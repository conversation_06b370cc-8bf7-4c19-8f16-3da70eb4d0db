package com.inditex.icdmdemg.infrastructure.coldstart.actuator.messagehelper;

import java.util.Map;
import java.util.Objects;

import com.inditex.aqsw.pipe.TracingResolverSessionId;
import com.inditex.aqsw.pipe.v1.Metadata.Builder;
import com.inditex.aqsw.pipe.v1.MetadataBuilderImpl;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component("coldStartMetadataBuilder")
public class CustomColdStartMetadataBuilder extends MetadataBuilderImpl {

  public CustomColdStartMetadataBuilder(@Value("${metadata.domain}") final String domain,
      @Value("${metadata.version:1}") final String version,
      @Qualifier("amigaTracingResolverSessionId") final TracingResolverSessionId tracingResolverSessionId) {
    super(domain, version, tracingResolverSessionId);
  }

  @Override
  public void extendMetadata(final Builder builder, final Map<String, Object> headers) {
    final var context = builder.getContext();

    if (headers == null || headers.isEmpty()) {
      return;
    }

    headers.forEach((key, value) -> {
      if (Objects.nonNull(value)) {
        context.put(key, value.toString());
      }
    });
  }
}
