package com.inditex.icdmdemg.infrastructure.distributioninner.database.repository;

import static com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerEntity_.budgetCycle;
import static com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerEntity_.productOrderId;
import static com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerEntity_.productVariantGroupId;
import static com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerEntity_.referenceId;
import static com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerEntity_.status;
import static com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerEntity_.useId;
import static com.inditex.icdmdemg.infrastructure.shared.audit.CompleteAuditEntity_.deletedAt;
import static com.inditex.icdmdemg.infrastructure.shared.jpa.GenericSpecification.beingNullInherited;
import static com.inditex.icdmdemg.infrastructure.shared.jpa.GenericSpecification.by;
import static com.inditex.icdmdemg.infrastructure.shared.jpa.GenericSpecification.byAny;

import static org.springframework.data.jpa.domain.Specification.allOf;

import java.util.List;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceId;
import com.inditex.icdmdemg.domain.distributioninner.entity.CompositeKeyInner;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.distributioninner.entity.SharedRawMaterialInner;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerEntity;

import org.springframework.data.jpa.domain.Specification;

interface DistributionInnerSpecification {

  static Specification<DistributionInnerEntity> byCriteria(
      final List<ReferenceId> referenceIds,
      final List<ProductOrderId> productOrderIds,
      final List<BudgetCycle> budgetCycles,
      final List<DistributionInnerStatus> statusList) {
    return allOf(
        beingNullInherited(deletedAt),
        byAny(referenceId, referenceIds.stream().map(ReferenceId::value).toList()),
        byAny(productOrderId, productOrderIds.stream().map(ProductOrderId::value).toList()),
        byAny(budgetCycle, budgetCycles.stream().map(BudgetCycle::value).toList()),
        byAny(status, statusList));
  }

  static Specification<DistributionInnerEntity> byCompositeKey(final CompositeKeyInner compositeKey) {
    return allOf(
        beingNullInherited(deletedAt),
        by(referenceId, compositeKey.referenceId().value()),
        by(useId, compositeKey.useId().value()),
        by(budgetCycle, compositeKey.budgetCycle().value()),
        by(productOrderId, compositeKey.productOrderId().value()),
        by(productVariantGroupId, compositeKey.productVariantGroupId().value()));
  }

  static Specification<DistributionInnerEntity> bySharedRawMaterial(final SharedRawMaterialInner sharedRawMaterial) {
    return allOf(
        beingNullInherited(deletedAt),
        by(referenceId, sharedRawMaterial.referenceId().value()),
        by(useId, sharedRawMaterial.useId().value()),
        by(budgetCycle, sharedRawMaterial.budgetCycle().value()));
  }
}
