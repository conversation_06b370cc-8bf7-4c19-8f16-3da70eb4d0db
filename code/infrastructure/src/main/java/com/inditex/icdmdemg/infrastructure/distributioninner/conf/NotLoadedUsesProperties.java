package com.inditex.icdmdemg.infrastructure.distributioninner.conf;

import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "di-availability")
public class NotLoadedUsesProperties {

  private List<String> externalUses = new ArrayList<>();

}
