package com.inditex.icdmdemg.infrastructure.distributionnominated.conf;

import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "dn-cancel-conf")
public class DistributionNominatedCancelProperties {

  private Map<String, OffsetDateTime> allowedUses = new HashMap<>();

}
