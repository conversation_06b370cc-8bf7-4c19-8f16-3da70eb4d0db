package com.inditex.icdmdemg.infrastructure.coldstart.actuator.messagehelper;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

import com.inditex.icdmdemg.icbcdemg.distribution.unified.Header;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.springframework.kafka.support.KafkaHeaders;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ItxKafkaHeaders {

  public static final String CONTENT_TYPE_HEADER = "contentType";

  public static final String ENVELOPE_ID = "itx_envelope_id";

  public static final String MESSAGE_ID = "itx_message_id";

  public static final String PRODUCER_ID = "itx_producer_id";

  public static final String PUBLICATION_TIMESTAMP = "itx_publication_timestamp";

  public static final String TOPIC = "itx_topic";

  private static final String CONTENT_TYPE_VALUE = "application/*+avro";

  private static final String ENVELOPE_ID_VALUE = "pipe-event:1.0";

  private static final String ICDMDEMG = "ICDMDEMG.";

  public static Header[] generateHeaders(final String aggregateId, final String topic) {
    return new Header[]{
        Header.of(KafkaHeaders.KEY, aggregateId),
        Header.of(CONTENT_TYPE_HEADER, CONTENT_TYPE_VALUE),
        Header.of(MESSAGE_ID, UUID.randomUUID().toString()),
        Header.of(ENVELOPE_ID, ENVELOPE_ID_VALUE),
        Header.of(PUBLICATION_TIMESTAMP, generateTimestamp()),
        Header.of(TOPIC, topic),
        Header.of(PRODUCER_ID, ICDMDEMG)
    };
  }

  private static String generateTimestamp() {
    return DateTimeFormatter.ISO_LOCAL_DATE_TIME.format(LocalDateTime.now(ZoneOffset.UTC));
  }
}
