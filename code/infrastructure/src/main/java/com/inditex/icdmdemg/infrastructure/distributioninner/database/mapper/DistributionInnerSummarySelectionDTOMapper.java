package com.inditex.icdmdemg.infrastructure.distributioninner.database.mapper;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.UseId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerSummaryDTO;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerSummarySelectionDTO;

import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@NullMarked
public class DistributionInnerSummarySelectionDTOMapper {

  public DistributionInnerSummaryDTO toDomain(final DistributionInnerSummarySelectionDTO distributionInnerSummarySelectionDTO) {
    return new DistributionInnerSummaryDTO(
        new ReferenceProductId(distributionInnerSummarySelectionDTO.referenceProductId()),
        new ReferenceId(distributionInnerSummarySelectionDTO.referenceId()),
        new UseId(distributionInnerSummarySelectionDTO.useId()),
        new BudgetCycle(distributionInnerSummarySelectionDTO.budgetCycle()),
        new RequestedQuantity(distributionInnerSummarySelectionDTO.requested()),
        new DistributedQuantity(distributionInnerSummarySelectionDTO.distributed()));
  }
}
