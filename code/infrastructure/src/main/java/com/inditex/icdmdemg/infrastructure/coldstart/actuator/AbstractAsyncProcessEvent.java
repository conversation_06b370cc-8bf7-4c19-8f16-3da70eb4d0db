package com.inditex.icdmdemg.infrastructure.coldstart.actuator;

import java.util.Iterator;
import java.util.UUID;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Stream;

import com.inditex.amigafwk.data.stream.bridge.AmigaStreamBridge;
import com.inditex.amigafwk.data.stream.bridge.AmigaStreamBridgeBuilder;
import com.inditex.aqsw.pipe.v1.Metadata;
import com.inditex.aqsw.pipe.v1.MetadataBuilder;
import com.inditex.icdmdemg.domain.shared.AggregateDomainEvent;
import com.inditex.icdmdemg.icbcdemg.distribution.unified.Header;
import com.inditex.icdmdemg.infrastructure.coldstart.actuator.messagehelper.ColdStartHeadersBuilder;
import com.inditex.icdmdemg.infrastructure.coldstart.actuator.messagehelper.ItxKafkaHeaders;
import com.inditex.iopcmmntsh.generator.IopAttributesGenerator;

import jakarta.persistence.EntityManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.specific.SpecificRecord;
import org.springframework.cloud.stream.binder.BinderException;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@RequiredArgsConstructor
public abstract class AbstractAsyncProcessEvent<S, E extends AggregateDomainEvent<UUID>, T extends SpecificRecord,
    P extends SpecificRecord> {

  private static final String AVRO_CONTENT_TYPE = "application/*+avro";

  protected final MetadataBuilder metadataBuilder;

  protected final IopAttributesGenerator iopAttributesGenerator;

  protected final AmigaStreamBridgeBuilder streamBridgeBuilder;

  protected final ColdStartHeadersBuilder coldStartHeadersBuilder;

  private final EntityManager entityManager;

  protected abstract Supplier<Stream<S>> getStreamSupplier();

  protected abstract Function<S, E> getEvent();

  protected abstract P mapperDomainEventToUnifiedLoadEvent(E event);

  protected abstract Class<T> getEnvelopeType();

  protected abstract T buildEnvelope(Metadata metadata, P payload);

  protected Message<T> createEventMessage(final E event, final String topic) {
    final var payload = this.iopAttributesGenerator.generateAttributes(event, this.mapperDomainEventToUnifiedLoadEvent(event));
    final var metadata = this.getCustomMetadata(payload, event);
    final var headers = ItxKafkaHeaders.generateHeaders(event.getAggregateId().toString(), topic);
    final T envelope = this.buildEnvelope(metadata, payload);

    return MessageBuilder.createMessage(envelope, new MessageHeaders(Header.asMap(headers, Header.Type.FOR_HEADERS)));
  }

  protected Metadata getCustomMetadata(final P payload, final E event) {
    final Header[] metadataHeaders = this.coldStartHeadersBuilder.contextDomainMetadataHeaders(event, payload.getClass());
    return this.metadataBuilder.buildMetadata(payload, Header.asMap(metadataHeaders, Header.Type.FOR_METADATA));
  }

  @Transactional(readOnly = true)
  public void processAndEmitEvents(final String topic) {
    log.debug("Starting event emission process for topic '{}'", topic);

    final AmigaStreamBridge<T> streamBridge =
        this.streamBridgeBuilder.withOutputContentType(AVRO_CONTENT_TYPE)
            .build(this.getEnvelopeType(), topic);

    try (final Stream<S> entityStream = this.getStreamSupplier().get()) {
      final Iterator<S> iterator = entityStream.iterator();
      while (iterator.hasNext()) {
        final S entity = iterator.next();
        if (!handleEntity(entity, streamBridge, topic)) {
          break;
        }
      }
    } catch (final Exception streamEx) {
      log.error("Error during stream processing setup or closure for topic '{}'", topic, streamEx);
    }

    log.debug("Finished event emission process for topic '{}'", topic);
  }

  private boolean handleEntity(final S entity,
      final AmigaStreamBridge<T> streamBridge,
      final String topic) {
    try {
      final E event = this.getEvent().apply(entity);
      final Message<T> message = this.createEventMessage(event, topic);
      streamBridge.send(message);
      log.debug("Successfully sent event for ID: {}", event.getAggregateId());
      return true;
    } catch (final BinderException binderEx) {
      log.error("BinderException sending to topic '{}'. Aborting.", topic, binderEx);
      return false;
    } catch (final Exception e) {
      log.error("Error processing or sending event for entity.", e);
      return true;
    } finally {
      this.entityManager.detach(entity);
    }
  }
}
