package com.inditex.icdmdemg.infrastructure.distributioninner.database.mapper;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.DistributionEndDate;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.DistributionStartDate;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.Id;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.TrackingCode;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerEntity;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerLineEntity;

import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@NullMarked
public class DistributionInnerLineEntityMapper {

  public List<DistributionInnerLine> toDomain(final List<DistributionInnerLineEntity> distributionInnerLineEntities) {
    return distributionInnerLineEntities.stream()
        .map(this::toDomain)
        .toList();
  }

  private DistributionInnerLine toDomain(final DistributionInnerLineEntity distributionInnerLineEntity) {
    return new DistributionInnerLine(
        new Id(distributionInnerLineEntity.id()),
        acceptNullElseMap(distributionInnerLineEntity.trackingCode(), TrackingCode::new),
        new TheoreticalQuantity(distributionInnerLineEntity.theoreticalQuantity()),
        new RequestedQuantity(distributionInnerLineEntity.requestedQuantity()),
        new DistributedQuantity(distributionInnerLineEntity.distributedQuantity()),
        acceptNullElseMap(distributionInnerLineEntity.distributionStartDate(), DistributionInnerLine.DistributionStartDate::new),
        acceptNullElseMap(distributionInnerLineEntity.distributionEndDate(), DistributionInnerLine.DistributionEndDate::new),
        new BasicAudit(distributionInnerLineEntity.createdAt(), distributionInnerLineEntity.updatedAt()));
  }

  public List<DistributionInnerLineEntity> toEntity(
      final UUID distributionInnerId,
      final List<DistributionInnerLine> distributionInnerLines) {
    return distributionInnerLines.stream()
        .map(distributionInnerLine -> this.toEntity(distributionInnerId, distributionInnerLine))
        .toList();
  }

  private DistributionInnerLineEntity toEntity(
      final UUID distributionInnerId,
      final DistributionInnerLine distributionInnerLine) {
    return new DistributionInnerLineEntity()
        .id(distributionInnerLine.id().value())
        .distributionInner(new DistributionInnerEntity().id(distributionInnerId))
        .trackingCode(acceptNullElseMap(distributionInnerLine.trackingCode(), TrackingCode::value))
        .theoreticalQuantity(distributionInnerLine.theoreticalQuantity().value())
        .requestedQuantity(distributionInnerLine.requestedQuantity().value())
        .distributedQuantity(distributionInnerLine.distributedQuantity().value())
        .distributionStartDate(acceptNullElseMap(distributionInnerLine.distributionStartDate(), DistributionStartDate::value))
        .distributionEndDate(acceptNullElseMap(distributionInnerLine.distributionEndDate(), DistributionEndDate::value))
        .createdAt(distributionInnerLine.audit().createdAt())
        .updatedAt(distributionInnerLine.audit().updatedAt());
  }

}
