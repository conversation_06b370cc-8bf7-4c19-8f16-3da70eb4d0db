package com.inditex.icdmdemg.infrastructure.commitmentuse.repository;

import static com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntity_.budgetId;
import static com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntity_.expectedDate;
import static com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntity_.materialOrderId;
import static com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntity_.materialOrderLineId;
import static com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntity_.materialReferenceId;
import static com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntity_.processedAt;
import static com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntity_.useId;
import static com.inditex.icdmdemg.infrastructure.shared.jpa.GenericSpecification.beingLessOrEqual;
import static com.inditex.icdmdemg.infrastructure.shared.jpa.GenericSpecification.beingNull;
import static com.inditex.icdmdemg.infrastructure.shared.jpa.GenericSpecification.byAnyAtLeastOne;
import static com.inditex.icdmdemg.infrastructure.shared.jpa.GenericSpecification.useRoot;

import static org.springframework.data.jpa.domain.Specification.allOf;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderIdAndOrderLineId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntity;
import com.inditex.icdmdemg.infrastructure.shared.jpa.CriteriaBuilderTupleIn.In;

import org.springframework.data.jpa.domain.Specification;

interface MaterialCommitmentUseSpecification {

  static Specification<MaterialCommitmentUseEntity> byAnyOrderIdOrderLineId(
      final List<MaterialCommitmentUseOrderIdAndOrderLineId> orderIdAndOrderLineIds) {
    return byAnyAtLeastOne(orderIdAndOrderLineIds, (tupable, root) -> new In()
        .element(root.get(materialOrderId), UUID.fromString(tupable.materialCommitmentOrderId().value()))
        .element(root.get(materialOrderLineId), UUID.fromString(tupable.materialCommitmentOrderLineId().value())));
  }

  static Specification<MaterialCommitmentUseEntity> byAnySharedRawMaterial(
      final List<MaterialCommitmentUseSharedRawMaterial> sharedRawMaterialList) {
    return byAnyAtLeastOne(sharedRawMaterialList, (srm, root) -> new In()
        .element(root.get(materialReferenceId), UUID.fromString(srm.materialCommitmentMaterialReferenceId().value()))
        .element(root.get(useId), UUID.fromString(srm.materialCommitmentUseId().value()))
        .element(root.get(budgetId), srm.materialCommitmentBudgetId().value()));
  }

  static Specification<MaterialCommitmentUseEntity> byProcessedAtIsNullAndExpectedDateIsLessThan(OffsetDateTime date) {
    return useRoot(root -> allOf(
        beingNull(processedAt),
        beingLessOrEqual(expectedDate, date)));
  }

}
