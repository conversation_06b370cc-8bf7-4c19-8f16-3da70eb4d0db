package com.inditex.icdmdemg.infrastructure.distributioninner.pipe.mapper;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import java.util.List;

import com.inditex.icbcdemg.distributioninner.unified.v1.DistributionInnerUnifiedPayload;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.TrackingCode;
import com.inditex.icdmdemg.shared.utils.TimeMapperUtils;

public interface DistributionInnerUnifiedPayloadMapper {

  static DistributionInnerUnifiedPayload toDistributionInnerUnifiedPayload(final DistributionInnerUnifiedEvent event) {
    return new DistributionInnerUnifiedPayload(
        event.distributionInner().getClass().getSimpleName(),
        null,
        null,
        null,
        null,
        toOperationEnum(event.type()),
        null,
        event.distributionInner().referenceId().value(),
        event.distributionInner().useId().value(),
        event.distributionInner().budgetCycle().value(),
        event.distributionInner().productOrderId().value(),
        event.distributionInner().productVariantGroupId().value(),
        event.distributionInner().theoreticalQuantity().value(),
        event.distributionInner().consumptionFactor().value(),
        event.distributionInner().requestedQuantity().value(),
        event.distributionInner().distributedQuantity().value(),
        toDistributionInnerLines(event.distributionInner().lines().value()),
        event.distributionInner().status().value(),
        acceptNullElseMap(event.distributionInner().audit().createdAt(), TimeMapperUtils::offsetDateTimeToIso8601),
        acceptNullElseMap(event.distributionInner().audit().updatedAt(), TimeMapperUtils::offsetDateTimeToIso8601),
        acceptNullElseMap(event.distributionInner().audit().deletedAt(), TimeMapperUtils::offsetDateTimeToIso8601));
  }

  private static com.inditex.icbcdemg.distributioninner.unified.v1.OperationEnum toOperationEnum(
      final DistributionInnerUnifiedEvent.EventType eventType) {
    return switch (eventType) {
      case CREATED_PENDING, CREATED_NON_DISTRIBUTABLE -> com.inditex.icbcdemg.distributioninner.unified.v1.OperationEnum.CREATE;
      case UPDATED_PENDING, UPDATED_NON_DISTRIBUTABLE, CORRECTED, DISTRIBUTED, PRODUCT_UPDATED, CANCELED, CLOSED ->
        com.inditex.icbcdemg.distributioninner.unified.v1.OperationEnum.UPDATE;
      case DELETED -> com.inditex.icbcdemg.distributioninner.unified.v1.OperationEnum.DELETE;
    };
  }

  private static List<com.inditex.icbcdemg.distributioninner.unified.v1.DistributionInnerLine> toDistributionInnerLines(
      final List<com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine> lines) {
    return lines.stream().map(DistributionInnerUnifiedPayloadMapper::toDistributionInnerLine).toList();
  }

  private static com.inditex.icbcdemg.distributioninner.unified.v1.DistributionInnerLine toDistributionInnerLine(
      final com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine distributionInnerLine) {
    return new com.inditex.icbcdemg.distributioninner.unified.v1.DistributionInnerLine(
        distributionInnerLine.id().value(),
        acceptNullElseMap(distributionInnerLine.trackingCode(),
            TrackingCode::value),
        distributionInnerLine.theoreticalQuantity().value(),
        distributionInnerLine.requestedQuantity().value(),
        distributionInnerLine.distributedQuantity().value(),
        acceptNullElseMap(distributionInnerLine.distributionStartDate(),
            distributionStartDate -> TimeMapperUtils.offsetDateTimeToIso8601(distributionStartDate.value())),
        acceptNullElseMap(distributionInnerLine.distributionEndDate(),
            distributionStartDate -> TimeMapperUtils.offsetDateTimeToIso8601(distributionStartDate.value())),
        TimeMapperUtils.offsetDateTimeToIso8601(distributionInnerLine.audit().createdAt()),
        TimeMapperUtils.offsetDateTimeToIso8601(distributionInnerLine.audit().updatedAt()));
  }
}
