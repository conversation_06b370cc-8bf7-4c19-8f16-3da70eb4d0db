package com.inditex.icdmdemg.infrastructure.commitmentuse.entity;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.inditex.icdmdemg.infrastructure.shared.audit.BasicAuditEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.IdClass;
import jakarta.persistence.Table;
import jakarta.persistence.Version;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Entity
@Table(name = "material_commitment_use", schema = "debtmg")
@NoArgsConstructor
@Accessors(chain = true, fluent = true)
@Data
@EqualsAndHashCode(callSuper = true)
@IdClass(MaterialCommitmentUseEntityId.class)
public class MaterialCommitmentUseEntity extends BasicAuditEntity<MaterialCommitmentUseEntity> {

  @Column(name = "id")
  private UUID id;

  @Column(name = "quantity")
  private BigDecimal quantity;

  @Column(name = "expected_date")
  private OffsetDateTime expectedDate;

  @Id
  @Column(name = "material_reference_id", nullable = false)
  private UUID materialReferenceId;

  @Id
  @Column(name = "use_id", nullable = false)
  private UUID useId;

  @Id
  @Column(name = "order_budget_id", nullable = false)
  private String budgetId;

  @Id
  @Column(name = "order_id", nullable = false)
  private UUID materialOrderId;

  @Id
  @Column(name = "order_line_id", nullable = false)
  private UUID materialOrderLineId;

  @Column(name = "status")
  private String status;

  @Column(name = "service_localization_type")
  private String serviceLocalizationType;

  @Column(name = "service_localization_id")
  private String serviceLocalizationId;

  @Column(name = "budget_cycle_change_requested_at", nullable = false)
  private OffsetDateTime budgetCycleChangeRequestedAt;

  @Column(name = "budget_cycle_change_executed_at", nullable = false)
  private OffsetDateTime budgetCycleChangeExecutedAt;

  @Column(name = "version", nullable = false)
  @Version
  private short version;

  @Column(name = "processed_at")
  private OffsetDateTime processedAt;
}
