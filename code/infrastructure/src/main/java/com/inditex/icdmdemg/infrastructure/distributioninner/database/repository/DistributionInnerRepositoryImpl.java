package com.inditex.icdmdemg.infrastructure.distributioninner.database.repository;

import static com.inditex.icdmdemg.infrastructure.distributioninner.database.repository.DistributionInnerSpecification.byCompositeKey;
import static com.inditex.icdmdemg.infrastructure.distributioninner.database.repository.DistributionInnerSpecification.byCriteria;
import static com.inditex.icdmdemg.infrastructure.distributioninner.database.repository.DistributionInnerSpecification.bySharedRawMaterial;
import static com.inditex.icdmdemg.shared.utils.Nullables.areEmpty;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.UseId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerSummaryDTO;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUseQuantitiesDTO;
import com.inditex.icdmdemg.domain.distributioninner.entity.CompositeKeyInner;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.distributioninner.entity.SharedRawMaterialInner;
import com.inditex.icdmdemg.infrastructure.distributioninner.conf.NotLoadedUsesProperties;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerEntity_;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.mapper.DistributionInnerEntityMapper;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.mapper.DistributionInnerSummarySelectionDTOMapper;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.mapper.DistributionInnerUseQuantitiesSelectionDTOMapper;
import com.inditex.icdmdemg.infrastructure.externaldistributions.repository.ExternalDistributionsProvider;
import com.inditex.icdmdemg.shared.utils.Nullables;

import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
@Transactional(readOnly = true)
@NullMarked
public class DistributionInnerRepositoryImpl implements DistributionInnerRepository {

  private final DistributionInnerJpaRepository distributionInnerJpaRepository;

  private final ExternalDistributionsProvider externalDistributionsProvider;

  private final DistributionInnerSummarySelectionDTOMapper distributionInnerSummarySelectionDTOMapper;

  private final DistributionInnerUseQuantitiesSelectionDTOMapper distributionInnerUseQuantitiesSelectionDTOMapper;

  private final DistributionInnerEntityMapper mapper;

  private final NotLoadedUsesProperties notLoadedUsesProperties;

  @Override
  public Optional<DistributionInner> findById(final DistributionInner.Id id) {
    return this.distributionInnerJpaRepository.findByIdAndDeletedAtIsNull(id.value())
        .map(this.mapper::toDomain);
  }

  @Override
  @Transactional
  public void save(final DistributionInner distributionInner) {
    this.distributionInnerJpaRepository.save(this.mapper.toEntity(distributionInner));
  }

  @Override
  public Slice<DistributionInner> findByCriteria(
      final List<ReferenceId> referenceIds,
      final List<ProductOrderId> productOrderIds,
      final List<BudgetCycle> budgetCycles,
      final List<DistributionInnerStatus> statusList,
      final Pageable pageable) {
    final var pageRequest = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by(DistributionInnerEntity_.CREATED_AT));
    final var filtered =
        this.distributionInnerJpaRepository.findAllSliced(byCriteria(referenceIds, productOrderIds, budgetCycles, statusList), pageRequest);
    return this.distributionInnerJpaRepository.findByEntities(filtered).map(this.mapper::toDomain);
  }

  @Override
  public Optional<DistributionInner> findByCompositeKey(final CompositeKeyInner compositeKey) {
    return this.distributionInnerJpaRepository.findAll(byCompositeKey(compositeKey)).stream().map(this.mapper::toDomain).findFirst();
  }

  @Override
  public List<DistributionInner> findByProductOrderId(final DistributionInner.ProductOrderId productOrderId) {
    final var filtered = this.distributionInnerJpaRepository.findByProductOrderIdAndDeletedAtIsNull(productOrderId.value());
    final var result = this.distributionInnerJpaRepository.findByEntities(filtered);
    return this.mapper.toDomain(result);
  }

  @Override
  public List<DistributionInner> findByProductVariantGroupIds(final List<ProductVariantGroupId> productVariantGroupIds) {
    final var filtered = this.distributionInnerJpaRepository.findByProductVariantGroupIdInAndDeletedAtIsNull(
        productVariantGroupIds.stream().map(ProductVariantGroupId::value).toList());
    final var result = this.distributionInnerJpaRepository.findByEntities(filtered);
    return this.mapper.toDomain(result);
  }

  @Override
  public List<DistributionInner> findByReferenceIds(final List<ReferenceId> referenceIds) {
    final var filtered = this.distributionInnerJpaRepository.findByReferenceIdInAndDeletedAtIsNull(
        referenceIds.stream().map(ReferenceId::value).toList());
    final var result = this.distributionInnerJpaRepository.findByEntities(filtered);
    return this.mapper.toDomain(result);
  }

  @Override
  public List<DistributionInner> findBySharedRawMaterial(final SharedRawMaterialInner sharedRawMaterial) {
    final var filtered = this.distributionInnerJpaRepository.findAll(bySharedRawMaterial(sharedRawMaterial));
    final var result = this.distributionInnerJpaRepository.findByEntities(filtered);
    return this.mapper.toDomain(result);
  }

  @Override
  public List<DistributionInnerSummaryDTO> findDistributionInnerSummaries(final List<ReferenceProductId> referenceProductIds,
      final List<ReferenceId> referenceIds, final List<BudgetCycle> budgetCycles, final List<DistributionInnerStatus> excludedStatus) {

    if (areEmpty(referenceProductIds, referenceIds, budgetCycles, excludedStatus)) {
      return List.of();
    }

    return this.distributionInnerJpaRepository.findDistributionInnerSummaries(
        Nullables.returnNullIfEmptyElseMap(referenceProductIds, ReferenceProductId::value),
        Nullables.returnNullIfEmptyElseMap(referenceIds, ReferenceId::value),
        Nullables.returnNullIfEmptyElseMap(budgetCycles, BudgetCycle::value),
        Nullables.returnNullIfEmptyElseMap(excludedStatus, DistributionInnerStatus::value)).stream().map(
            this.distributionInnerSummarySelectionDTOMapper::toDomain)
        .toList();
  }

  @Override
  public List<DistributionInnerUseQuantitiesDTO> findDistributionInnerUseQuantities(final List<UseId> useIds,
      final ReferenceId referenceId, final BudgetCycle budgetCycle) {
    final var partitionedByExclusion = this.getUsesPartitionedByLoadedOrNotInIop(useIds);

    final List<UseId> useIdsNotInIop = partitionedByExclusion.get(true);
    final List<UseId> useIdsInIop = partitionedByExclusion.get(false);

    final var useQuantitiesFromDatabase = this.getUseQuantitiesFromDatabase(referenceId, budgetCycle, useIdsInIop);

    final var useQuantitiesFromLegacy =
        this.externalDistributionsProvider.findDistributionOrdersQuantityFromLegacy(useIdsNotInIop, referenceId, budgetCycle);

    return Stream.concat(useQuantitiesFromDatabase.stream(), useQuantitiesFromLegacy.stream())
        .toList();
  }

  private List<DistributionInnerUseQuantitiesDTO> getUseQuantitiesFromDatabase(final ReferenceId referenceId, final BudgetCycle budgetCycle,
      final List<UseId> useIdsInIop) {
    if (areEmpty(useIdsInIop)) {
      return List.of();
    }
    return this.distributionInnerJpaRepository
        .findDistributionInnerQuantitiesGroupedByUse(referenceId.value(),
            Nullables.returnNullIfEmptyElseMap(useIdsInIop, UseId::value), budgetCycle.value())
        .stream().map(
            this.distributionInnerUseQuantitiesSelectionDTOMapper::toDomain)
        .toList();
  }

  private Map<Boolean, List<UseId>> getUsesPartitionedByLoadedOrNotInIop(final List<UseId> useIds) {
    final var excludedUseIds = this.notLoadedUsesProperties.getExternalUses();

    return useIds.stream()
        .collect(Collectors.partitioningBy(useId -> excludedUseIds.contains(useId.value().toString())));
  }

}
