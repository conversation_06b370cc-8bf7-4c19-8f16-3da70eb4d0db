package com.inditex.icdmdemg.infrastructure.consumermessage.repository;

import java.util.UUID;

import com.inditex.amigafwk.data.jpa.repositories.AmigaJpaRepository;
import com.inditex.icdmdemg.infrastructure.consumermessage.entity.ConsumerMessageEntity;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ConsumerMessageJpaRepository extends AmigaJpaRepository<ConsumerMessageEntity, UUID> {

  @Modifying(clearAutomatically = true, flushAutomatically = true)
  @Query(value = """
      INSERT INTO consumer_message (id, name, created_at)
      VALUES (:#{#message.id},:#{#message.name},:#{#message.createdAt})
      """, nativeQuery = true)
  void insert(@Param("message") ConsumerMessageEntity consumerMessageEntity);

}
