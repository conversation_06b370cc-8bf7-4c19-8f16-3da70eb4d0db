package com.inditex.icdmdemg.infrastructure.distributioninner.database.entity;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.inditex.icdmdemg.infrastructure.shared.audit.BasicAuditEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Entity()
@Table(name = "distribution_inner_line", schema = "debtmg")
@NoArgsConstructor
@Accessors(chain = true, fluent = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class DistributionInnerLineEntity extends BasicAuditEntity<DistributionInnerLineEntity> {

  @Id
  private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "distribution_inner_id")
  private DistributionInnerEntity distributionInner;

  @Column(name = "tracking_code")
  private String trackingCode;

  @Column(name = "theoretical_quantity", nullable = false)
  private BigDecimal theoreticalQuantity;

  @Column(name = "requested_quantity", nullable = false)
  private BigDecimal requestedQuantity;

  @Column(name = "distributed_quantity", nullable = false)
  private BigDecimal distributedQuantity;

  @Column(name = "distribution_start_date")
  private OffsetDateTime distributionStartDate;

  @Column(name = "distribution_end_date")
  private OffsetDateTime distributionEndDate;
}
