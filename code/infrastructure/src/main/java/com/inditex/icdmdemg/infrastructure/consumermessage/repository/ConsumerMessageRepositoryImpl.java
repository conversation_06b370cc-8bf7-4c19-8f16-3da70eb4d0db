package com.inditex.icdmdemg.infrastructure.consumermessage.repository;

import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.domain.consumermessage.ConsumerMessage;
import com.inditex.icdmdemg.domain.consumermessage.ConsumerMessageRepository;
import com.inditex.icdmdemg.domain.consumermessage.DuplicatedMessageException;
import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageId;
import com.inditex.icdmdemg.infrastructure.consumermessage.mapper.ConsumerMessageEntityMapper;

import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
@Transactional(readOnly = true)
@NullMarked
public class ConsumerMessageRepositoryImpl implements ConsumerMessageRepository {

  private final ConsumerMessageJpaRepository jpaRepository;

  private final ConsumerMessageEntityMapper mapper;

  @Override
  public Optional<ConsumerMessage> findById(final ConsumerMessageId id) {
    return this.jpaRepository.findById(this.getUUID(id)).map(this.mapper::toDomain);
  }

  @Override
  @Transactional(propagation = Propagation.MANDATORY)
  public void create(final ConsumerMessage message) throws DuplicatedMessageException {
    try {
      this.jpaRepository.insert(this.mapper.toEntity(message));
    } catch (final DataIntegrityViolationException e) {
      throw new DuplicatedMessageException(message);
    }
  }

  private UUID getUUID(final ConsumerMessageId id) {
    return UUID.fromString(id.value());
  }
}
