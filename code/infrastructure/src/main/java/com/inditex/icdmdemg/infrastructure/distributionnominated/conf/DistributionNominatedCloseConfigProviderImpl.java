package com.inditex.icdmdemg.infrastructure.distributionnominated.conf;

import java.time.OffsetDateTime;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedCloseConfigProvider;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

@Getter
@Component
@RequiredArgsConstructor
public class DistributionNominatedCloseConfigProviderImpl implements DistributionNominatedCloseConfigProvider {

  private final DistributionNominatedCancelProperties properties;

  @Override
  public Boolean shouldAdjustFor(@NonNull String useId, @NonNull OffsetDateTime createdAt) {
    return this.properties.getAllowedUses().getOrDefault(useId, OffsetDateTime.MAX).isBefore(createdAt);
  }
}
