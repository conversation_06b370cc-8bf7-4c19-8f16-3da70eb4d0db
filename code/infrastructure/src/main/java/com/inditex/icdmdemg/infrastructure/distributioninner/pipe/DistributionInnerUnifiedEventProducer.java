package com.inditex.icdmdemg.infrastructure.distributioninner.pipe;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.icbcdemg.distribution.unified.DistributionsInnerEventsProducer;
import com.inditex.icdmdemg.icbcdemg.distribution.unified.Header;
import com.inditex.icdmdemg.infrastructure.distributioninner.pipe.mapper.DistributionInnerUnifiedPayloadMapper;
import com.inditex.icdmdemg.infrastructure.shared.pipe.CustomMetadataBuilder;
import com.inditex.icdmdemg.shared.utils.TimeMapperUtils;
import com.inditex.iopcmmnt.ddd.core.EventProducer;
import com.inditex.iopcmmntsh.generator.IopAttributesGenerator;

import lombok.RequiredArgsConstructor;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DistributionInnerUnifiedEventProducer implements EventProducer<DistributionInnerUnifiedEvent> {

  private final IopAttributesGenerator iopAttributesGenerator;

  private final DistributionsInnerEventsProducer producer;

  @Override
  public void produce(final DistributionInnerUnifiedEvent distributionInnerUnifiedEvent) {
    final var payload = DistributionInnerUnifiedPayloadMapper.toDistributionInnerUnifiedPayload(distributionInnerUnifiedEvent);
    this.iopAttributesGenerator.generateAttributes(distributionInnerUnifiedEvent, payload);

    this.producer.distributionInnerEvent(
        payload,
        Header.of(KafkaHeaders.KEY, distributionInnerUnifiedEvent.getAggregateId().toString()),
        Header.forMetadata(CustomMetadataBuilder.HEADER_METADATA,
            new CustomMetadataBuilder.Metadata(
                TimeMapperUtils.instantToIso8601(distributionInnerUnifiedEvent.occurredOn()),
                distributionInnerUnifiedEvent.eventType())));
  }

}
