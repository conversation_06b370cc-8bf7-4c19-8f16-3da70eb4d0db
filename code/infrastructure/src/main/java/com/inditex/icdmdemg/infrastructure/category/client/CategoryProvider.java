package com.inditex.icdmdemg.infrastructure.category.client;

import java.util.List;

import com.inditex.icdmdemg.icmpurcent.rest.client.dto.CategoryDTO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@NullMarked
public class CategoryProvider {

  private final ReducedCategoryClient reducedCategoryClient;

  private final CategoryClient categoryClient;

  public List<String> getParents(final List<String> urns) {
    return this.reducedCategoryClient.call(urns);
  }

  public List<String> getEquivalent(final List<String> urns) {
    final var response = this.categoryClient.call(urns);
    return response.stream().flatMap(equivalentDTO -> equivalentDTO.getEquivalents()
        .stream().filter(categoryDTO -> "CAMPAIGN".contentEquals(categoryDTO.getType()))
        .map(CategoryDTO::getUrn))
        .toList();
  }
}
