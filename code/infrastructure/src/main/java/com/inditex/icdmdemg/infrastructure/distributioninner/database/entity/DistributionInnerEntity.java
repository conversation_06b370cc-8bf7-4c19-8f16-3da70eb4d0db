package com.inditex.icdmdemg.infrastructure.distributioninner.database.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.infrastructure.shared.audit.CompleteAuditEntity;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Entity()
@Table(name = "distribution_inner", schema = "debtmg")
@NoArgsConstructor
@Accessors(chain = true, fluent = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class DistributionInnerEntity extends CompleteAuditEntity<DistributionInnerEntity> {

  @Id
  private UUID id;

  @Column(name = "reference_id", nullable = false)
  private UUID referenceId;

  @Column(name = "use_id", nullable = false)
  private UUID useId;

  @Column(name = "budget_cycle", nullable = false)
  private String budgetCycle;

  @Column(name = "product_order_id", nullable = false)
  private UUID productOrderId;

  @Column(name = "reference_product_id", nullable = false)
  private UUID referenceProductId;

  @Column(name = "product_variant_group_id", nullable = false)
  private UUID productVariantGroupId;

  @Column(name = "consumption_factor", nullable = false)
  private BigDecimal consumptionFactor;

  @Column(name = "theoretical_quantity", nullable = false)
  private BigDecimal theoreticalQuantity;

  @Column(name = "requested_quantity", nullable = false)
  private BigDecimal requestedQuantity;

  @Column(name = "distributed_quantity", nullable = false)
  private BigDecimal distributedQuantity;

  @Column(name = "status", nullable = false)
  @Enumerated(EnumType.STRING)
  private DistributionInnerStatus status;

  @OneToMany(mappedBy = "distributionInner", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
  private List<DistributionInnerLineEntity> lines = new ArrayList<>();
}
