package com.inditex.icdmdemg.infrastructure.commitmentuse.entity;

import java.io.Serializable;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class MaterialCommitmentUseEntityId implements Serializable {
  private UUID materialOrderId;

  private UUID materialOrderLineId;

  private String budgetId;

  private UUID materialReferenceId;

  private UUID useId;

}
