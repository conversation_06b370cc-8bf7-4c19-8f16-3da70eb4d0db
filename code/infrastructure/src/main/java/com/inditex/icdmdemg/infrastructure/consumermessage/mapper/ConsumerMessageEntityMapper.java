package com.inditex.icdmdemg.infrastructure.consumermessage.mapper;

import java.util.UUID;

import com.inditex.icdmdemg.domain.consumermessage.ConsumerMessage;
import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageId;
import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageName;
import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageTimestamp;
import com.inditex.icdmdemg.infrastructure.consumermessage.entity.ConsumerMessageEntity;

import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@NullMarked
public class ConsumerMessageEntityMapper {

  public ConsumerMessage toDomain(final ConsumerMessageEntity consumerMessageEntity) {
    return new ConsumerMessage(
        new ConsumerMessageId(consumerMessageEntity.id().toString()),
        new ConsumerMessageName(consumerMessageEntity.name()),
        ConsumerMessageTimestamp.of(consumerMessageEntity.createdAt()));
  }

  public ConsumerMessageEntity toEntity(final ConsumerMessage consumerMessage) {
    return new ConsumerMessageEntity()
        .id(UUID.fromString(consumerMessage.id().value()))
        .name(consumerMessage.name().value())
        .createdAt(consumerMessage.createdAt().value());
  }

}
