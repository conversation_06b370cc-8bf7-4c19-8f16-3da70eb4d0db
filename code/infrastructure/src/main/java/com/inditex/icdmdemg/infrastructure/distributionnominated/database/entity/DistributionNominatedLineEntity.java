package com.inditex.icdmdemg.infrastructure.distributionnominated.database.entity;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.inditex.icdmdemg.infrastructure.shared.audit.BasicAuditEntity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Entity()
@Table(name = "distribution_nominated_line", schema = "debtmg")
@NoArgsConstructor
@Accessors(chain = true, fluent = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class DistributionNominatedLineEntity extends BasicAuditEntity<DistributionNominatedLineEntity> {

  @Id
  private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "distribution_nominated_id")
  private DistributionNominatedEntity distributionNominated;

  @Column(name = "commitment_order_id", nullable = false)
  private UUID commitmentOrderId;

  @Column(name = "commitment_order_line_id", nullable = false)
  private UUID commitmentOrderLineId;

  @Column(name = "commitment_order_supplier_id", nullable = false)
  private String commitmentOrderSupplierId;

  @Column(name = "theoretical_quantity", nullable = false)
  private BigDecimal theoreticalQuantity;

  @Column(name = "requested_quantity", nullable = false)
  private BigDecimal requestedQuantity;

  @Column(name = "distributed_quantity", nullable = false)
  private BigDecimal distributedQuantity;

  @Column(name = "alternative_reference_id")
  private UUID alternativeReferenceId;

  @Column(name = "alternative_requested_quantity", nullable = false)
  private BigDecimal alternativeRequestedQuantity;

  @Column(name = "alternative_theoretical_quantity", nullable = false)
  private BigDecimal alternativeTheoreticalQuantity;

  @Column(name = "distribution_start_date")
  private OffsetDateTime distributionStartDate;
}
