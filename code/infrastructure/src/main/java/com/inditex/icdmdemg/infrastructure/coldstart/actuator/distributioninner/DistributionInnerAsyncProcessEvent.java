package com.inditex.icdmdemg.infrastructure.coldstart.actuator.distributioninner;

import static com.inditex.icdmdemg.infrastructure.distributioninner.pipe.mapper.DistributionInnerUnifiedPayloadMapper.toDistributionInnerUnifiedPayload;

import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Stream;

import com.inditex.amigafwk.data.stream.bridge.AmigaStreamBridgeBuilder;
import com.inditex.aqsw.pipe.v1.Metadata;
import com.inditex.aqsw.pipe.v1.MetadataBuilder;
import com.inditex.icbcdemg.distributioninner.unified.v1.DistributionInnerUnifiedEnvelope;
import com.inditex.icbcdemg.distributioninner.unified.v1.DistributionInnerUnifiedPayload;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent.EventType;
import com.inditex.icdmdemg.infrastructure.coldstart.actuator.AbstractAsyncProcessEvent;
import com.inditex.icdmdemg.infrastructure.coldstart.actuator.messagehelper.ColdStartHeadersBuilder;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerEntity;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.mapper.DistributionInnerEntityMapper;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.repository.DistributionInnerJpaRepository;
import com.inditex.iopcmmntsh.generator.IopAttributesGenerator;

import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DistributionInnerAsyncProcessEvent
    extends AbstractAsyncProcessEvent<DistributionInnerEntity, DistributionInnerUnifiedEvent, DistributionInnerUnifiedEnvelope,
        DistributionInnerUnifiedPayload> {

  private final DistributionInnerEntityMapper mapper;

  private final DistributionInnerJpaRepository repository;

  public DistributionInnerAsyncProcessEvent(
      final DistributionInnerJpaRepository repository,
      final DistributionInnerEntityMapper mapper,
      @Qualifier("coldStartMetadataBuilder") final MetadataBuilder metadataBuilder,
      final IopAttributesGenerator iopAttributesGenerator,
      final ColdStartHeadersBuilder coldStartHeadersBuilder,
      final AmigaStreamBridgeBuilder streamBridgeBuilder,
      final EntityManager entityManager) {
    super(metadataBuilder, iopAttributesGenerator, streamBridgeBuilder, coldStartHeadersBuilder, entityManager);
    this.repository = repository;
    this.mapper = mapper;
  }

  @Override
  protected Supplier<Stream<DistributionInnerEntity>> getStreamSupplier() {
    return this.repository::findAllStream;
  }

  @Override
  protected Function<DistributionInnerEntity, DistributionInnerUnifiedEvent> getEvent() {
    return entity -> new DistributionInnerUnifiedEvent(
        this.mapper.toDomain(entity),
        entity.status().isNonDistributable() ? EventType.CREATED_NON_DISTRIBUTABLE : EventType.CREATED_PENDING);
  }

  @Override
  protected DistributionInnerUnifiedPayload mapperDomainEventToUnifiedLoadEvent(final DistributionInnerUnifiedEvent event) {
    return toDistributionInnerUnifiedPayload(event);
  }

  @Override
  protected Class<DistributionInnerUnifiedEnvelope> getEnvelopeType() {
    return DistributionInnerUnifiedEnvelope.class;
  }

  @Override
  protected DistributionInnerUnifiedEnvelope buildEnvelope(final Metadata metadata, final DistributionInnerUnifiedPayload payload) {
    return DistributionInnerUnifiedEnvelope.newBuilder()
        .setMetadata(metadata)
        .setPayload(payload)
        .build();
  }
}
