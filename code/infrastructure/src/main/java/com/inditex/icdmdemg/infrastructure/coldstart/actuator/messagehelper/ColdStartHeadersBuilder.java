package com.inditex.icdmdemg.infrastructure.coldstart.actuator.messagehelper;

import java.time.OffsetDateTime;
import java.util.UUID;

import com.inditex.icdmdemg.domain.shared.AggregateDomainEvent;
import com.inditex.icdmdemg.icbcdemg.distribution.unified.Header;
import com.inditex.icdmdemg.shared.utils.TimeMapperUtils;

import lombok.RequiredArgsConstructor;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class ColdStartHeadersBuilder {

  public static final String MESSAGE_SOURCE_ID = "message_source_id";

  public static final String PUBLICATION_HEADER_KEY = "publication_timestamp";

  public static final String AGGREGATE_ID_HEADER_KEY = "aggregate_id";

  public static final String PIPE_EVENT_TYPE_HEADER_KEY = "pipe_event_type";

  public static final String AGGREGATE_TYPE_HEADER_KEY = "aggregate_type";

  public static final String USER = "user";

  public static final String IMPERSONATED_BY = "impersonated_by";

  public static final String OCURRED_ON = "occurred_on";

  public Header[] contextDomainMetadataHeaders(final AggregateDomainEvent<UUID> event, final Class<?> eventClass) {

    final var now = OffsetDateTime.now().toString();

    return new Header[]{
        Header.forMetadata(MESSAGE_SOURCE_ID, UUID.randomUUID().toString()),
        Header.forMetadata(AGGREGATE_ID_HEADER_KEY, event.getAggregateId().toString()),
        Header.forMetadata(AGGREGATE_TYPE_HEADER_KEY, eventClass.getName()),
        Header.forMetadata(PIPE_EVENT_TYPE_HEADER_KEY, eventClass.getSimpleName()),
        Header.forMetadata(PUBLICATION_HEADER_KEY, now),
        Header.forMetadata(USER, event.user()),
        Header.forMetadata(IMPERSONATED_BY, event.user()),
        Header.forMetadata(OCURRED_ON, TimeMapperUtils.instantToIso8601(event.occurredOn())),
        Header.of(KafkaHeaders.KEY, event.getAggregateId().toString()),
    };
  }
}
