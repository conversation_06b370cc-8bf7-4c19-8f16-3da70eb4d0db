package com.inditex.icdmdemg.infrastructure.category.repository;

import java.util.List;

import com.inditex.icdmdemg.domain.category.CategoryRepository;
import com.inditex.icdmdemg.infrastructure.category.client.CategoryProvider;

import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@NullMarked
@RequiredArgsConstructor
public class CategoryRepositoryImpl implements CategoryRepository {

  private final CategoryProvider categoryProvider;

  @Override
  public List<String> getParents(final List<String> urns) {
    return this.categoryProvider.getParents(urns);
  }

  @Override
  public List<String> getEquivalent(final List<String> urns) {
    return this.categoryProvider.getEquivalent(urns);
  }
}
