package com.inditex.icdmdemg.infrastructure.distributionnominated.database.mapper;

import java.util.List;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycleChangePendingQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ConsumptionFactor;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductSupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.UseId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLines;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.infrastructure.distributionnominated.database.entity.DistributionNominatedEntity;
import com.inditex.icdmdemg.shared.utils.Nullables;

import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@NullMarked
public class DistributionNominatedEntityMapper {

  private final DistributionNominatedLineEntityMapper distributionNominatedLineMapper;

  public DistributionNominated toDomain(final DistributionNominatedEntity distributionNominatedEntity) {
    return new DistributionNominated(new Id(distributionNominatedEntity.id()), new ReferenceId(distributionNominatedEntity.referenceId()),
        new UseId(distributionNominatedEntity.useId()), new BudgetCycle(distributionNominatedEntity.budgetCycle()),
        Nullables.acceptNullElseMap(distributionNominatedEntity.referenceProductId(), ReferenceProductId::new),
        new ProductOrderId(distributionNominatedEntity.productOrderId()),
        new ProductSupplierId(distributionNominatedEntity.productOrderSupplierId()),
        new ProductVariantGroupId(distributionNominatedEntity.productVariantGroupId()),
        new TheoreticalQuantity(distributionNominatedEntity.theoreticalQuantity()),
        new ConsumptionFactor(distributionNominatedEntity.consumptionFactor()),
        new RequestedQuantity(distributionNominatedEntity.requestedQuantity()),
        new DistributedQuantity(distributionNominatedEntity.distributedQuantity()),
        new BudgetCycleChangePendingQuantity(distributionNominatedEntity.budgetCycleChangePendingQuantity()),
        distributionNominatedEntity.status(), distributionNominatedEntity.plan(),
        new DistributionNominatedLines(this.distributionNominatedLineMapper.toDomain(distributionNominatedEntity.lines())),
        this.extractDistributionNominatedAudit(distributionNominatedEntity));
  }

  public List<DistributionNominated> toDomain(final List<DistributionNominatedEntity> distributionNominatedEntities) {
    return distributionNominatedEntities.stream().map(this::toDomain).toList();
  }

  private CompleteAudit extractDistributionNominatedAudit(final DistributionNominatedEntity distributionNominatedEntity) {
    return new CompleteAudit(
        distributionNominatedEntity.createdBy(),
        distributionNominatedEntity.createdAt(),
        distributionNominatedEntity.updatedBy(),
        distributionNominatedEntity.updatedAt(),
        distributionNominatedEntity.deletedAt(),
        distributionNominatedEntity.version());
  }

  public DistributionNominatedEntity toEntity(final DistributionNominated distributionNominated) {
    return new DistributionNominatedEntity()
        .id(distributionNominated.getId().value())
        .referenceId(distributionNominated.referenceId().value())
        .useId(distributionNominated.useId().value())
        .budgetCycle(distributionNominated.budgetCycle().value())
        .referenceProductId(Nullables.acceptNullElseMap(distributionNominated.referenceProductId(), ReferenceProductId::value))
        .productOrderId(distributionNominated.productOrderId().value())
        .productOrderSupplierId(distributionNominated.productSupplierId().value())
        .productVariantGroupId(distributionNominated.productVariantGroupId().value())
        .theoreticalQuantity(distributionNominated.theoreticalQuantity().value())
        .consumptionFactor(distributionNominated.consumptionFactor().value())
        .requestedQuantity(distributionNominated.requestedQuantity().value())
        .distributedQuantity(distributionNominated.distributedQuantity().value())
        .budgetCycleChangePendingQuantity(distributionNominated.budgetCycleChangePendingQuantity().value())
        .lines(this.distributionNominatedLineMapper.toEntity(distributionNominated.getId().value(), distributionNominated.lines().value()))
        .status(distributionNominated.status())
        .plan(distributionNominated.plan())
        .version(distributionNominated.audit().version())
        .createdBy(distributionNominated.audit().createdBy())
        .createdAt(distributionNominated.audit().createdAt())
        .updatedBy(distributionNominated.audit().updatedBy())
        .updatedAt(distributionNominated.audit().updatedAt())
        .deletedAt(distributionNominated.audit().deletedAt());
  }

}
