package com.inditex.icdmdemg.infrastructure.commitmentuse.repository;

import static com.inditex.icdmdemg.infrastructure.commitmentuse.repository.MaterialCommitmentUseSpecification.byAnyOrderIdOrderLineId;
import static com.inditex.icdmdemg.infrastructure.commitmentuse.repository.MaterialCommitmentUseSpecification.byAnySharedRawMaterial;
import static com.inditex.icdmdemg.infrastructure.commitmentuse.repository.MaterialCommitmentUseSpecification.byProcessedAtIsNullAndExpectedDateIsLessThan;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse.MaterialCommitmentUseCompositeId;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseCollection;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderIdAndOrderLineId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntity;
import com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntityId;
import com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntity_;
import com.inditex.icdmdemg.infrastructure.commitmentuse.mapper.MaterialCommitmentUseEntityMapper;

import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
@Transactional(readOnly = true)
@NullMarked
public class MaterialCommitmentUseRepositoryImpl implements MaterialCommitmentUseRepository {

  private final MaterialCommitmentUseJpaRepository materialCommitmentUseJpaRepository;

  private final MaterialCommitmentUseEntityMapper materialCommitmentUseEntityMapper;

  @Transactional
  @Override
  public void delete(final MaterialCommitmentUse materialCommitmentUseToDelete) {
    final var id = new MaterialCommitmentUseEntityId(UUID.fromString(materialCommitmentUseToDelete.getOrderLine().orderId().value()),
        UUID.fromString(materialCommitmentUseToDelete.getOrderLine().orderLineId().value()),
        materialCommitmentUseToDelete.getOrderLine().budgetId().value(),
        UUID.fromString(materialCommitmentUseToDelete.getMaterialReferenceId().value()),
        UUID.fromString(materialCommitmentUseToDelete.getUseId().value()));
    this.materialCommitmentUseJpaRepository
        .findById(id)
        .ifPresent(this.materialCommitmentUseJpaRepository::delete);
  }

  @Transactional
  @Override
  public MaterialCommitmentUse save(final MaterialCommitmentUse materialCommitmentUseToSave) {
    final MaterialCommitmentUseEntity materialCommitmentUseEntitySaved =
        this.materialCommitmentUseJpaRepository.save(this.materialCommitmentUseEntityMapper.toEntity(materialCommitmentUseToSave));

    return this.materialCommitmentUseEntityMapper.toDomain(materialCommitmentUseEntitySaved);
  }

  @Transactional
  @Override
  public MaterialCommitmentUseCollection saveAll(final List<MaterialCommitmentUse> materialCommitmentUseListToSave) {
    if (materialCommitmentUseListToSave.isEmpty()) {
      return MaterialCommitmentUseCollection.empty();
    }
    return this.materialCommitmentUseJpaRepository.saveAll(this.materialCommitmentUseEntityMapper.toEntity(materialCommitmentUseListToSave))
        .stream().map(this.materialCommitmentUseEntityMapper::toDomain)
        .collect(Collectors.collectingAndThen(Collectors.toList(), MaterialCommitmentUseCollection::new));
  }

  @Override
  public MaterialCommitmentUseCollection findByAnyOrderIdAndOrderLineId(
      final List<MaterialCommitmentUseOrderIdAndOrderLineId> matComOrderIdAndLineIds) {
    if (matComOrderIdAndLineIds.isEmpty()) {
      return MaterialCommitmentUseCollection.empty();
    }
    return this.materialCommitmentUseJpaRepository.findAll(byAnyOrderIdOrderLineId(matComOrderIdAndLineIds))
        .stream()
        .map(this.materialCommitmentUseEntityMapper::toDomain)
        .collect(Collectors.collectingAndThen(Collectors.toList(), MaterialCommitmentUseCollection::new));
  }

  @Override
  public MaterialCommitmentUseCollection findByAnySharedRawMaterial(
      final List<MaterialCommitmentUseSharedRawMaterial> sharedRawMaterialList) {
    if (sharedRawMaterialList.isEmpty()) {
      return MaterialCommitmentUseCollection.empty();
    }
    return this.materialCommitmentUseJpaRepository
        .findAll(byAnySharedRawMaterial(sharedRawMaterialList))
        .stream()
        .map(this.materialCommitmentUseEntityMapper::toDomain)
        .collect(Collectors.collectingAndThen(Collectors.toList(), MaterialCommitmentUseCollection::new));
  }

  @Override
  public Slice<MaterialCommitmentUse> findByAnySharedRawMaterial(
      final List<MaterialCommitmentUseSharedRawMaterial> sharedRawMaterials,
      final Pageable pageable) {
    final var pageRequest = pageable.isPaged()
        ? PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(), Sort.by(MaterialCommitmentUseEntity_.CREATED_AT))
        : Pageable.unpaged();
    return this.materialCommitmentUseJpaRepository.findAllSliced(
        byAnySharedRawMaterial(sharedRawMaterials), pageRequest)
        .map(this.materialCommitmentUseEntityMapper::toDomain);
  }

  @Override
  public MaterialCommitmentUseCollection findNotProcessedAndExpectedDateIsLessThan(final OffsetDateTime date) {
    return this.materialCommitmentUseJpaRepository
        .findAll(byProcessedAtIsNullAndExpectedDateIsLessThan(date))
        .stream()
        .map(this.materialCommitmentUseEntityMapper::toDomain)
        .collect(Collectors.collectingAndThen(Collectors.toList(), MaterialCommitmentUseCollection::new));
  }

  @Override
  public MaterialCommitmentUseCollection findByAnyCompositeId(final List<MaterialCommitmentUseCompositeId> compositeIds) {
    final var ids = compositeIds.stream()
        .map(compositeId -> new MaterialCommitmentUseEntityId(
            UUID.fromString(compositeId.orderLine().orderId().value()),
            UUID.fromString(compositeId.orderLine().orderLineId().value()),
            compositeId.orderLine().budgetId().value(),
            UUID.fromString(compositeId.materialReferenceId().value()),
            UUID.fromString(compositeId.useId().value())))
        .toList();
    final var entitiesByCompositeIds = this.materialCommitmentUseJpaRepository.findAllById(ids);
    return new MaterialCommitmentUseCollection(this.materialCommitmentUseEntityMapper.toDomain(entitiesByCompositeIds));
  }

}
