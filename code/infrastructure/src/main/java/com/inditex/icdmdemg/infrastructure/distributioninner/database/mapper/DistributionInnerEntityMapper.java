package com.inditex.icdmdemg.infrastructure.distributioninner.database.mapper;

import java.util.List;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ConsumptionFactor;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.UseId;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLines;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerEntity;
import com.inditex.icdmdemg.shared.utils.Nullables;

import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@NullMarked
public class DistributionInnerEntityMapper {

  private final DistributionInnerLineEntityMapper distributionInnerLineEntityMapper;

  public DistributionInner toDomain(final DistributionInnerEntity distributionInnerEntity) {
    return new DistributionInner(new Id(distributionInnerEntity.id()), new ReferenceId(distributionInnerEntity.referenceId()),
        new UseId(distributionInnerEntity.useId()), new BudgetCycle(distributionInnerEntity.budgetCycle()),
        Nullables.acceptNullElseMap(distributionInnerEntity.referenceProductId(), ReferenceProductId::new),
        new ProductOrderId(distributionInnerEntity.productOrderId()),
        new ProductVariantGroupId(distributionInnerEntity.productVariantGroupId()),
        new TheoreticalQuantity(distributionInnerEntity.theoreticalQuantity()),
        new ConsumptionFactor(distributionInnerEntity.consumptionFactor()),
        new RequestedQuantity(distributionInnerEntity.requestedQuantity()),
        new DistributedQuantity(distributionInnerEntity.distributedQuantity()),
        distributionInnerEntity.status(), new DistributionInnerLines(
            this.distributionInnerLineEntityMapper.toDomain(distributionInnerEntity.lines())),
        this.extractDistributionInnerAudit(distributionInnerEntity));
  }

  public List<DistributionInner> toDomain(final List<DistributionInnerEntity> distributionInnerEntities) {
    return distributionInnerEntities.stream().map(this::toDomain).toList();
  }

  private CompleteAudit extractDistributionInnerAudit(final DistributionInnerEntity distributionInnerEntity) {
    return new CompleteAudit(
        distributionInnerEntity.createdBy(),
        distributionInnerEntity.createdAt(),
        distributionInnerEntity.updatedBy(),
        distributionInnerEntity.updatedAt(),
        distributionInnerEntity.deletedAt(),
        distributionInnerEntity.version());
  }

  public DistributionInnerEntity toEntity(final DistributionInner distributionInner) {
    return new DistributionInnerEntity()
        .id(distributionInner.getId().value())
        .referenceId(distributionInner.referenceId().value())
        .useId(distributionInner.useId().value())
        .budgetCycle(distributionInner.budgetCycle().value())
        .referenceProductId(Nullables.acceptNullElseMap(distributionInner.referenceProductId(), ReferenceProductId::value))
        .productOrderId(distributionInner.productOrderId().value())
        .productVariantGroupId(distributionInner.productVariantGroupId().value())
        .theoreticalQuantity(distributionInner.theoreticalQuantity().value())
        .consumptionFactor(distributionInner.consumptionFactor().value())
        .requestedQuantity(distributionInner.requestedQuantity().value())
        .distributedQuantity(distributionInner.distributedQuantity().value())
        .lines(this.distributionInnerLineEntityMapper.toEntity(distributionInner.getId().value(),
            distributionInner.lines().value()))
        .status(distributionInner.status())
        .version(distributionInner.audit().version())
        .createdBy(distributionInner.audit().createdBy())
        .createdAt(distributionInner.audit().createdAt())
        .updatedBy(distributionInner.audit().updatedBy())
        .updatedAt(distributionInner.audit().updatedAt())
        .deletedAt(distributionInner.audit().deletedAt());
  }

}
