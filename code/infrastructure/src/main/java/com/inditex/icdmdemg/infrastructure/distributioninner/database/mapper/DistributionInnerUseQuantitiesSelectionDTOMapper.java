package com.inditex.icdmdemg.infrastructure.distributioninner.database.mapper;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.UseId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUseQuantitiesDTO;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerUseQuantitiesSelectionDTO;

import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@NullMarked
public class DistributionInnerUseQuantitiesSelectionDTOMapper {

  public DistributionInnerUseQuantitiesDTO toDomain(final DistributionInnerUseQuantitiesSelectionDTO distributionInnerSummarySelectionDTO) {
    return new DistributionInnerUseQuantitiesDTO(
        new UseId(distributionInnerSummarySelectionDTO.useId()),
        new RequestedQuantity(distributionInnerSummarySelectionDTO.requested()),
        new DistributedQuantity(distributionInnerSummarySelectionDTO.distributed()));
  }
}
