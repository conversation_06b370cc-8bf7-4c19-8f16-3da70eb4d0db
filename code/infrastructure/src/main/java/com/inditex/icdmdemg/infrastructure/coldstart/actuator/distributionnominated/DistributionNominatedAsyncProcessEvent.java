package com.inditex.icdmdemg.infrastructure.coldstart.actuator.distributionnominated;

import static com.inditex.icdmdemg.infrastructure.distributionnominated.pipe.mapper.DistributionNominatedUnifiedPayloadMapper.toDistributionNominatedUnifiedPayload;

import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Stream;

import com.inditex.amigafwk.data.stream.bridge.AmigaStreamBridgeBuilder;
import com.inditex.aqsw.pipe.v1.Metadata;
import com.inditex.aqsw.pipe.v1.MetadataBuilder;
import com.inditex.icbcdemg.distributionnominated.unified.v1.DistributionNominatedUnifiedEnvelope;
import com.inditex.icbcdemg.distributionnominated.unified.v1.DistributionNominatedUnifiedPayload;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.infrastructure.coldstart.actuator.AbstractAsyncProcessEvent;
import com.inditex.icdmdemg.infrastructure.coldstart.actuator.messagehelper.ColdStartHeadersBuilder;
import com.inditex.icdmdemg.infrastructure.distributionnominated.database.entity.DistributionNominatedEntity;
import com.inditex.icdmdemg.infrastructure.distributionnominated.database.mapper.DistributionNominatedEntityMapper;
import com.inditex.icdmdemg.infrastructure.distributionnominated.database.repository.DistributionNominatedJpaRepository;
import com.inditex.iopcmmntsh.generator.IopAttributesGenerator;

import jakarta.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DistributionNominatedAsyncProcessEvent
    extends
    AbstractAsyncProcessEvent<DistributionNominatedEntity, DistributionNominatedUnifiedEvent, DistributionNominatedUnifiedEnvelope,
        DistributionNominatedUnifiedPayload> {

  private final DistributionNominatedEntityMapper mapper;

  private final DistributionNominatedJpaRepository repository;

  public DistributionNominatedAsyncProcessEvent(
      final DistributionNominatedJpaRepository repository,
      final DistributionNominatedEntityMapper mapper,
      @Qualifier("coldStartMetadataBuilder") final MetadataBuilder metadataBuilder,
      final IopAttributesGenerator iopAttributesGenerator,
      final ColdStartHeadersBuilder coldStartHeadersBuilder,
      final AmigaStreamBridgeBuilder streamBridgeBuilder,
      final EntityManager entityManager) {
    super(metadataBuilder, iopAttributesGenerator, streamBridgeBuilder, coldStartHeadersBuilder, entityManager);
    this.repository = repository;
    this.mapper = mapper;
  }

  @Override
  protected Supplier<Stream<DistributionNominatedEntity>> getStreamSupplier() {
    return this.repository::findAllStream;
  }

  @Override
  protected Function<DistributionNominatedEntity, DistributionNominatedUnifiedEvent> getEvent() {
    return entity -> new DistributionNominatedUnifiedEvent(this.mapper.toDomain(entity), EventType.CREATED);
  }

  @Override
  protected DistributionNominatedUnifiedPayload mapperDomainEventToUnifiedLoadEvent(final DistributionNominatedUnifiedEvent event) {
    return toDistributionNominatedUnifiedPayload(event);
  }

  @Override
  protected Class<DistributionNominatedUnifiedEnvelope> getEnvelopeType() {
    return DistributionNominatedUnifiedEnvelope.class;
  }

  @Override
  protected DistributionNominatedUnifiedEnvelope buildEnvelope(final Metadata metadata, final DistributionNominatedUnifiedPayload payload) {
    return DistributionNominatedUnifiedEnvelope.newBuilder()
        .setMetadata(metadata)
        .setPayload(payload)
        .build();
  }
}
