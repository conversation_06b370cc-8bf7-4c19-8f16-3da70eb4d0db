package com.inditex.icdmdemg.infrastructure.category.client;

import java.util.List;

import com.inditex.icdmdemg.domain.shared.Error.MessageError;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.icmpurcent.rest.client.api.ReducedCategoriesApi;
import com.inditex.icdmdemg.infrastructure.utils.RetryableTemplateProvider;

import io.github.resilience4j.circuitbreaker.CallNotPermittedException;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jspecify.annotations.NullMarked;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.retry.RecoveryCallback;
import org.springframework.retry.RetryCallback;
import org.springframework.retry.context.RetryContextSupport;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;

@Slf4j
@Component
@RequiredArgsConstructor
@NullMarked
public class ReducedCategoryClient {
  private static final String CIRCUIT_BREAKER = "icdmdemg-icmpurcent-reduced";

  private final ReducedCategoriesApi reducedCategoriesApi;

  private final RetryableTemplateProvider retryableTemplateProvider;

  @Cacheable(value = "parentsCategory")
  @CircuitBreaker(name = CIRCUIT_BREAKER, fallbackMethod = "getFallback")
  public List<String> call(final List<String> urns) {
    final RetryTemplate template = this.retryableTemplateProvider.getRetryTemplate();
    final RetryCallback<List<String>, RestClientException> provideValue =
        ctx -> this.reducedCategoriesApi.getReducedParentCategories(urns, null);
    final RecoveryCallback<List<String>> recoveryCallback = ctx -> this.recoverLog((RetryContextSupport) ctx);
    return template.execute(provideValue, recoveryCallback);
  }

  protected List<String> recoverLog(final RetryContextSupport context) {
    log.error("Retry failed with message '{}'", context);
    throw new ErrorException(new MessageError(context.toString()));
  }

  protected List<String> getFallback(final CallNotPermittedException e) {
    log.error("Failed with message '{}'", e.getMessage());
    throw e;
  }
}
