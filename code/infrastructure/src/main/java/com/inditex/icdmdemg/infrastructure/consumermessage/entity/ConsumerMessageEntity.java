package com.inditex.icdmdemg.infrastructure.consumermessage.entity;

import java.time.OffsetDateTime;
import java.util.UUID;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Entity
@Table(name = "consumer_message", schema = "debtmg")
@NoArgsConstructor
@Accessors(chain = true, fluent = true)
@Data
public class ConsumerMessageEntity {

  @Id
  @Column(name = "id")
  private UUID id;

  @Column(name = "name")
  private String name;

  @Column(name = "created_at", nullable = false)
  private OffsetDateTime createdAt;

}
