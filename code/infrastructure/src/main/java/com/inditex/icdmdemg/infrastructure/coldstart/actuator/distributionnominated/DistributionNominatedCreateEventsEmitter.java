package com.inditex.icdmdemg.infrastructure.coldstart.actuator.distributionnominated;

import java.util.concurrent.CompletableFuture;

import com.inditex.icdmdemg.infrastructure.coldstart.actuator.CreateEventEmitter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.utils.StringUtils;
import org.springframework.boot.actuate.endpoint.annotation.WriteOperation;
import org.springframework.boot.actuate.endpoint.web.annotation.WebEndpoint;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
@WebEndpoint(id = "distributionNominatedCreateEvents")
public class DistributionNominatedCreateEventsEmitter implements CreateEventEmitter {

  private final DistributionNominatedAsyncProcessEvent distributionNominatedAsyncProcessEvent;

  @Override
  @WriteOperation
  public ResponseEntity<String> emitCreateEvents(final String topic) {
    log.debug("Trigger received to start async emission of DistributionNominated events to topic '{}'", topic);

    if (StringUtils.isBlank(topic)) {
      return ResponseEntity.badRequest().body("Topic must not be null or blank.");
    }

    CompletableFuture.runAsync(() -> this.distributionNominatedAsyncProcessEvent.processAndEmitEvents(topic));
    log.debug("Async emission process for DistributionNominated events started successfully for topic '{}'", topic);

    return ResponseEntity.accepted().body("Asynchronous nominated event emission started.");
  }
}
