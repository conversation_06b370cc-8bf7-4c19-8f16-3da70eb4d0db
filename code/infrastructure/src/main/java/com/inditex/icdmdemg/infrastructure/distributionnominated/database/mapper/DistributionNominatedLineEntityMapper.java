package com.inditex.icdmdemg.infrastructure.distributionnominated.database.mapper;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.AlternativeReference;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.AlternativeReference.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributionStartDate;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.Id;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.infrastructure.distributionnominated.database.entity.DistributionNominatedEntity;
import com.inditex.icdmdemg.infrastructure.distributionnominated.database.entity.DistributionNominatedLineEntity;

import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@NullMarked
public class DistributionNominatedLineEntityMapper {

  public List<DistributionNominatedLine> toDomain(final List<DistributionNominatedLineEntity> distributionNominatedLineEntities) {
    return distributionNominatedLineEntities.stream()
        .map(this::toDomain)
        .toList();
  }

  private DistributionNominatedLine toDomain(final DistributionNominatedLineEntity distributionNominatedLineEntity) {
    return new DistributionNominatedLine(
        new Id(distributionNominatedLineEntity.id()),
        new CommitmentOrder(
            new CommitmentOrder.Id(distributionNominatedLineEntity.commitmentOrderId()),
            new CommitmentOrder.LineId(distributionNominatedLineEntity.commitmentOrderLineId()),
            new CommitmentOrder.SupplierId(distributionNominatedLineEntity.commitmentOrderSupplierId())),
        new TheoreticalQuantity(distributionNominatedLineEntity.theoreticalQuantity()),
        new RequestedQuantity(distributionNominatedLineEntity.requestedQuantity()),
        new DistributedQuantity(distributionNominatedLineEntity.distributedQuantity()),
        acceptNullElseMap(distributionNominatedLineEntity.alternativeReferenceId(), referenceId -> new AlternativeReference(
            new ReferenceId(referenceId),
            new AlternativeReference.RequestedQuantity(distributionNominatedLineEntity.alternativeRequestedQuantity()),
            new AlternativeReference.TheoreticalQuantity(distributionNominatedLineEntity.alternativeTheoreticalQuantity()))),
        acceptNullElseMap(distributionNominatedLineEntity.distributionStartDate(), DistributionStartDate::new),
        new BasicAudit(distributionNominatedLineEntity.createdAt(), distributionNominatedLineEntity.updatedAt()));
  }

  public List<DistributionNominatedLineEntity> toEntity(
      final UUID distributionNominatedId,
      final List<DistributionNominatedLine> distributionNominatedLines) {
    return distributionNominatedLines.stream()
        .map(distributionNominatedLine -> this.toEntity(distributionNominatedId, distributionNominatedLine))
        .toList();
  }

  private DistributionNominatedLineEntity toEntity(
      final UUID distributionNominatedId,
      final DistributionNominatedLine distributionNominatedLine) {
    return new DistributionNominatedLineEntity()
        .id(distributionNominatedLine.id().value())
        .distributionNominated(new DistributionNominatedEntity().id(distributionNominatedId))
        .commitmentOrderId(distributionNominatedLine.commitmentOrder().id().value())
        .commitmentOrderLineId(distributionNominatedLine.commitmentOrder().lineId().value())
        .commitmentOrderSupplierId(distributionNominatedLine.commitmentOrder().supplierId().value())
        .theoreticalQuantity(distributionNominatedLine.theoreticalQuantity().value())
        .requestedQuantity(distributionNominatedLine.requestedQuantity().value())
        .distributedQuantity(distributionNominatedLine.distributedQuantity().value())
        .alternativeReferenceId(
            acceptNullElseMap(distributionNominatedLine.alternativeReference(),
                alternativeReference -> alternativeReference.referenceId().value()))
        .alternativeRequestedQuantity(!Objects.isNull(distributionNominatedLine.alternativeReference())
            ? distributionNominatedLine.alternativeReference().requestedQuantity().value()
            : BigDecimal.ZERO)
        .alternativeTheoreticalQuantity(!Objects.isNull(distributionNominatedLine.alternativeReference())
            ? distributionNominatedLine.alternativeReference().theoreticalQuantity().value()
            : BigDecimal.ZERO)
        .distributionStartDate(acceptNullElseMap(distributionNominatedLine.distributionStartDate(), DistributionStartDate::value))
        .createdAt(distributionNominatedLine.audit().createdAt())
        .updatedAt(distributionNominatedLine.audit().updatedAt());
  }

}
