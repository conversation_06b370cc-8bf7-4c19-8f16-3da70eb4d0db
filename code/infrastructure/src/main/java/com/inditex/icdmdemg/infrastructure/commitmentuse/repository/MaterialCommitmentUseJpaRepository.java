package com.inditex.icdmdemg.infrastructure.commitmentuse.repository;

import com.inditex.amigafwk.data.jpa.repositories.AmigaJpaRepository;
import com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntity;
import com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntityId;
import com.inditex.icdmdemg.infrastructure.shared.jpa.SliceSpecificationExecutor;

import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface MaterialCommitmentUseJpaRepository extends AmigaJpaRepository<MaterialCommitmentUseEntity, MaterialCommitmentUseEntityId>,
    SliceSpecificationExecutor<MaterialCommitmentUseEntity>,
    JpaSpecificationExecutor<MaterialCommitmentUseEntity> {

}
