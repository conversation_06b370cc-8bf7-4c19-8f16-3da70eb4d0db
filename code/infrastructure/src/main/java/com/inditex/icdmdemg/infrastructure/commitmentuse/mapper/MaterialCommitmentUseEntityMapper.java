package com.inditex.icdmdemg.infrastructure.commitmentuse.mapper;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeExecutedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeRequestedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseMaterialReferenceId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLine;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLineId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseQuantity;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseServiceLocalizationId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseServiceLocalizationType;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseTimestamp;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseUseId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseVersion;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.infrastructure.commitmentuse.entity.MaterialCommitmentUseEntity;

import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@NullMarked
public class MaterialCommitmentUseEntityMapper {

  public MaterialCommitmentUse toDomain(final MaterialCommitmentUseEntity materialCommitmentUseEntity) {
    final var audit = BasicAudit.builder().createdAt(materialCommitmentUseEntity.createdAt())
        .updatedAt(materialCommitmentUseEntity.updatedAt()).build();
    return new MaterialCommitmentUse(
        new MaterialCommitmentUseId(materialCommitmentUseEntity.id().toString()),
        new MaterialCommitmentUseMaterialReferenceId(materialCommitmentUseEntity.materialReferenceId().toString()),
        new MaterialCommitmentUseUseId(materialCommitmentUseEntity.useId().toString()),
        acceptNullElseMap(materialCommitmentUseEntity.quantity(), MaterialCommitmentUseQuantity::new),
        acceptNullElseMap(materialCommitmentUseEntity.expectedDate(), MaterialCommitmentUseTimestamp::of),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(materialCommitmentUseEntity.materialOrderId().toString()),
            new MaterialCommitmentUseBudgetId(materialCommitmentUseEntity.budgetId()),
            new MaterialCommitmentUseOrderLineId(materialCommitmentUseEntity.materialOrderLineId().toString())),
        acceptNullElseMap(materialCommitmentUseEntity.status(), MaterialCommitmentUseStatus::new),
        acceptNullElseMap(materialCommitmentUseEntity.serviceLocalizationId(),
            MaterialCommitmentUseServiceLocalizationId::new),
        acceptNullElseMap(materialCommitmentUseEntity.serviceLocalizationType(), MaterialCommitmentUseServiceLocalizationType::new),
        acceptNullElseMap(materialCommitmentUseEntity.budgetCycleChangeRequestedAt(),
            MaterialCommitmentUseBudgetCycleChangeRequestedAt::new),
        acceptNullElseMap(materialCommitmentUseEntity.budgetCycleChangeExecutedAt(),
            MaterialCommitmentUseBudgetCycleChangeExecutedAt::new),
        new MaterialCommitmentUseVersion(materialCommitmentUseEntity.version()),
        audit,
        acceptNullElseMap(materialCommitmentUseEntity.processedAt(), MaterialCommitmentUseTimestamp::of));
  }

  public List<MaterialCommitmentUse> toDomain(final List<MaterialCommitmentUseEntity> materialCommitmentUseEntities) {
    return materialCommitmentUseEntities.stream().map(this::toDomain).toList();
  }

  public MaterialCommitmentUseEntity toEntity(final MaterialCommitmentUse materialCommitmentUse) {
    return new MaterialCommitmentUseEntity()
        .id(UUID.fromString(materialCommitmentUse.getId().value()))
        .quantity(acceptNullElseMap(materialCommitmentUse.getQuantity(), MaterialCommitmentUseQuantity::value))
        .expectedDate(acceptNullElseMap(materialCommitmentUse.getExpectedDate(), MaterialCommitmentUseTimestamp::value))
        .materialReferenceId(UUID.fromString(materialCommitmentUse.getMaterialReferenceId().value()))
        .useId(UUID.fromString(materialCommitmentUse.getUseId().value()))
        .budgetId(materialCommitmentUse.getOrderLine().budgetId().value())
        .materialOrderId(UUID.fromString(materialCommitmentUse.getOrderLine().orderId().value()))
        .materialOrderLineId(UUID.fromString(materialCommitmentUse.getOrderLine().orderLineId().value()))
        .status(acceptNullElseMap(materialCommitmentUse.getStatus(), MaterialCommitmentUseStatus::value))
        .serviceLocalizationType(
            acceptNullElseMap(materialCommitmentUse.getServiceLocalizationType(), MaterialCommitmentUseServiceLocalizationType::value))
        .serviceLocalizationId(
            acceptNullElseMap(materialCommitmentUse.getServiceLocalizationId(), MaterialCommitmentUseServiceLocalizationId::value))
        .budgetCycleChangeRequestedAt(acceptNullElseMap(materialCommitmentUse.getBudgetCycleChangeRequestedAt(),
            MaterialCommitmentUseBudgetCycleChangeRequestedAt::value))
        .budgetCycleChangeExecutedAt(acceptNullElseMap(materialCommitmentUse.getBudgetCycleChangeExecutedAt(),
            MaterialCommitmentUseBudgetCycleChangeExecutedAt::value))
        .version(materialCommitmentUse.getVersion().value())
        .createdAt(materialCommitmentUse.getAudit().createdAt())
        .updatedAt(materialCommitmentUse.getAudit().updatedAt())
        .processedAt(acceptNullElseMap(materialCommitmentUse.getProcessedAt(), MaterialCommitmentUseTimestamp::value));
  }

  public List<MaterialCommitmentUseEntity> toEntity(final List<MaterialCommitmentUse> materialCommitmentUses) {
    return materialCommitmentUses.stream().map(this::toEntity).toList();
  }
}
