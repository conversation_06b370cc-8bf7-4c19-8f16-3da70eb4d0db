package com.inditex.icdmdemg.infrastructure.distributioninner.database.repository;

import static com.inditex.icdmdemg.infrastructure.shared.jpa.GenericSpecification.byAnyAtLeastOne;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.amigafwk.data.jpa.repositories.AmigaJpaRepository;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerEntity;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerEntity_;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerSummarySelectionDTO;
import com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerUseQuantitiesSelectionDTO;
import com.inditex.icdmdemg.infrastructure.shared.jpa.FinderByEntities;
import com.inditex.icdmdemg.infrastructure.shared.jpa.SliceSpecificationExecutor;

import jakarta.persistence.QueryHint;
import org.hibernate.jpa.HibernateHints;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;
import org.springframework.data.repository.query.Param;

public interface DistributionInnerJpaRepository
    extends AmigaJpaRepository<DistributionInnerEntity, UUID>, SliceSpecificationExecutor<DistributionInnerEntity>,
    FinderByEntities<DistributionInnerEntity> {

  @EntityGraph(attributePaths = DistributionInnerEntity_.LINES)
  Optional<DistributionInnerEntity> findByIdAndDeletedAtIsNull(UUID id);

  @Override
  default List<DistributionInnerEntity> findByEntities(final List<DistributionInnerEntity> entities, final Sort sort) {
    final var filteredIds = entities.stream().map(DistributionInnerEntity::id).distinct().toList();
    return this.findBy(byAnyAtLeastOne(DistributionInnerEntity_.id, filteredIds),
        query -> query.project(DistributionInnerEntity_.LINES).sortBy(sort).all());
  }

  List<DistributionInnerEntity> findByProductOrderIdAndDeletedAtIsNull(UUID productOrderId);

  List<DistributionInnerEntity> findByProductVariantGroupIdInAndDeletedAtIsNull(List<UUID> productVariantGroupIds);

  List<DistributionInnerEntity> findByReferenceIdInAndDeletedAtIsNull(List<UUID> referenceIds);

  @Query(
      value = """
                  SELECT new com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerSummarySelectionDTO
                    (di.referenceProductId, di.referenceId,
                    di.useId, di.budgetCycle, SUM(di.requestedQuantity), SUM(di.distributedQuantity))
                  FROM DistributionInnerEntity di
                    WHERE (:referenceProductIds IS NULL OR di.referenceProductId IN :referenceProductIds)
                      AND (:budgetCycles IS NULL OR di.budgetCycle IN :budgetCycles)
                      AND (:referenceIds IS NULL OR di.referenceId IN :referenceIds)
                      AND (:excludedStatus IS NULL OR di.status NOT IN :excludedStatus)
                      AND di.deletedAt IS NULL
                  GROUP BY di.referenceProductId, di.referenceId, di.useId, di.budgetCycle
          """)
  List<DistributionInnerSummarySelectionDTO> findDistributionInnerSummaries(
      @Param("referenceProductIds") List<UUID> referenceProductIds,
      @Param("referenceIds") List<UUID> referenceIds,
      @Param("budgetCycles") List<String> budgetCycles,
      @Param("excludedStatus") List<String> excludedStatus);

  @Query(
      value = """
                  SELECT new
                  com.inditex.icdmdemg.infrastructure.distributioninner.database.entity.DistributionInnerUseQuantitiesSelectionDTO
                    (di.useId, SUM(dil.requestedQuantity), SUM(dil.distributedQuantity))
                  FROM DistributionInnerEntity di
                  INNER JOIN DistributionInnerLineEntity dil ON di.id = dil.distributionInner.id
                    WHERE di.budgetCycle = :budgetCycle
                      AND di.referenceId = :referenceId
                      AND (:useIds IS NULL OR di.useId IN :useIds)
                      AND di.status IN ('NON_DISTRIBUTABLE', 'IN_PROGRESS', 'PENDING')
                      AND dil.distributionEndDate IS NULL
                      AND di.deletedAt IS NULL
                  GROUP BY di.useId
          """)
  List<DistributionInnerUseQuantitiesSelectionDTO> findDistributionInnerQuantitiesGroupedByUse(
      @Param("referenceId") UUID referenceId,
      @Param("useIds") List<UUID> useIds,
      @Param("budgetCycle") String budgetCycle);

  @Query(
      value = """
            SELECT DISTINCT d FROM DistributionInnerEntity d
            LEFT JOIN FETCH d.lines
            WHERE d.deletedAt IS NULL
            ORDER BY d.id
          """)
  @QueryHints({
      @QueryHint(name = HibernateHints.HINT_FETCH_SIZE, value = "100"),
      @QueryHint(name = HibernateHints.HINT_CACHEABLE, value = "false"),
      @QueryHint(name = HibernateHints.HINT_READ_ONLY, value = "true")
  })
  Stream<DistributionInnerEntity> findAllStream();
}
