<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.inditex.icdmdemg</groupId>
    <artifactId>icdmdebtmgmt-clients</artifactId>
    <version>1.91.0-SNAPSHOT</version>
  </parent>

  <artifactId>icdmdebtmgmt-pipe-icbcdemg-distribution-unified</artifactId>
  <name>${project.groupId}:${project.artifactId}</name>

  <properties>
    <amiga.data.event.pipe.generator.api.package>${project.groupId}.icbcdemg.distribution.unified</amiga.data.event.pipe.generator.api.package>
    <amiga.service.event.pipe.generator.api.definition>asyncapi.yml</amiga.service.event.pipe.generator.api.definition>
    <amiga.data.event.pipe.generator.configKeyValueOptions>avro.imports=inner/v1/imports/001-distribution_inner_line.avsc,inner/v1/imports/100-distribution_inner_unified_payload.avsc,nominated/v1/imports/001-alternative_reference.avsc,nominated/v1/imports/002-distribution_nominated_line.avsc,nominated/v1/imports/100-distribution_nominated_unified_payload.avsc</amiga.data.event.pipe.generator.configKeyValueOptions>
    <avro.imports.resources>${avro.imports.directory}/event/pipe/v1/imports/pipe_definitions.avsc,${avro.imports.directory}/event/inner/v1/imports/001-distribution_inner_line.avsc,${avro.imports.directory}/event/inner/v1/imports/100-distribution_inner_unified_payload.avsc,${avro.imports.directory}/event/nominated/v1/imports/001-alternative_reference.avsc,${avro.imports.directory}/event/nominated/v1/imports/002-distribution_nominated_line.avsc,${avro.imports.directory}/event/nominated/v1/imports/100-distribution_nominated_unified_payload.avsc,${avro.imports.directory}</avro.imports.resources>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.inditex.amigafwk.service</groupId>
      <artifactId>service-starter-stream-pipe</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.api</groupId>
      <artifactId>icbcdemg-distribution-management-unified-events-event-stable</artifactId>
      <version>${icbcdemg-distribution-management-unified-events-event-stable.version}</version>
    </dependency>
    <dependency>
      <groupId>com.inditex.apiasync</groupId>
      <artifactId>lib-asyncapi-tools</artifactId>
    </dependency>
  </dependencies>

</project>
