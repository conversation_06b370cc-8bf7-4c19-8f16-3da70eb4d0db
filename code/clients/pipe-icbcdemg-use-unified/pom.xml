<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
<modelVersion>4.0.0</modelVersion>
<parent>
  <groupId>com.inditex.icdmdemg</groupId>
  <artifactId>icdmdebtmgmt-clients</artifactId>
  <version>1.91.0-SNAPSHOT</version>
</parent>

<artifactId>icdmdebtmgmt-pipe-icbcdemg-use-unified</artifactId>
<name>${project.groupId}:${project.artifactId}</name>

<properties>
  <amiga.data.event.pipe.generator.api.package>${project.groupId}.icbcdemg.use.unified</amiga.data.event.pipe.generator.api.package>
  <amiga.service.event.pipe.generator.api.definition>asyncapi.yml</amiga.service.event.pipe.generator.api.definition>
  <amiga.data.event.pipe.generator.configKeyValueOptions>avro.imports=v1/imports/001-use-names.avsc,v1/imports/002-use-conditions.avsc,v1/imports/100-use_unified_payload.avsc</amiga.data.event.pipe.generator.configKeyValueOptions>
  <avro.imports.resources>${avro.imports.directory}/event/pipe/v1/imports/pipe_definitions.avsc,${avro.imports.directory}/event/v1/imports/001-use-names.avsc,${avro.imports.directory}/event/v1/imports/002-use-conditions.avsc,${avro.imports.directory}/event/v1/imports/100-use_unified_payload.avsc,${avro.imports.directory}</avro.imports.resources>
</properties>

<dependencies>
  <dependency>
    <groupId>com.inditex.amigafwk.service</groupId>
    <artifactId>service-starter-stream-pipe</artifactId>
  </dependency>

  <dependency>
    <groupId>com.inditex.api</groupId>
    <artifactId>icbcdemg-use-management-unified-events-event-stable</artifactId>
    <version>${icbcdemg-use-management-unified-events-event-stable.version}</version>
  </dependency>

  <dependency>
    <groupId>com.inditex.apiasync</groupId>
    <artifactId>lib-asyncapi-tools</artifactId>
  </dependency>
</dependencies>

</project>