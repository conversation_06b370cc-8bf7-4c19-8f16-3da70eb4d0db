<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.inditex.icdmdemg</groupId>
    <artifactId>icdmdebtmgmt-clients</artifactId>
    <version>1.91.0-SNAPSHOT</version>
  </parent>

  <artifactId>icdmdebtmgmt-clients-modules</artifactId>
  <name>${project.groupId}:${project.artifactId}</name>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-pipe-icbcnegord-order-domain-message</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-pipe-icbcprodt-products-management-events</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-pipe-icbcprovis-domain-bc-provisioning-event</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-rest-icbcprovis</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-rest-siga-acl</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-pipe-icbcsacl-delivery-anti-corruption-layer-event</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-pipe-bcsupshipm-supplier-transport-bounded-context-events</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-pipe-icbcdemg-distribution-unified</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-rest-icbcprodt</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-rest-icmpurcent</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-pipe-icbcdemg-use-unified</artifactId>
      <version>${project.version}</version>
    </dependency>
  </dependencies>
</project>
