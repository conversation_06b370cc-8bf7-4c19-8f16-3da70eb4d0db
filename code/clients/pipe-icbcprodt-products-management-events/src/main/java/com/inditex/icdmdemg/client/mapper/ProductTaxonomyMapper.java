package com.inditex.icdmdemg.client.mapper;

import com.inditex.icdmdemg.application.product.command.ProjectProductTaxonomiesCommand.ProductTaxonomy;
import com.inditex.icdmdemg.shared.utils.CharSequenceMapper;
import com.inditex.icdmprodt.pipe.product.v9.ProductDomainEvent;

import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(uses = {CharSequenceMapper.class}, injectionStrategy = InjectionStrategy.CONSTRUCTOR, componentModel = "spring")
public interface ProductTaxonomyMapper {

  @Mapping(target = "productId", source = "entityId")
  ProductTaxonomy from(ProductDomainEvent productDomainEvent);
}
