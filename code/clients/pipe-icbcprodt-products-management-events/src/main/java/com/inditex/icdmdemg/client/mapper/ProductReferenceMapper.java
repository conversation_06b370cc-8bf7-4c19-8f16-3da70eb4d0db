package com.inditex.icdmdemg.client.mapper;

import java.util.List;

import com.inditex.icdmdemg.application.product.command.ProjectProductCommand.Attributes;
import com.inditex.icdmdemg.application.product.command.ProjectProductCommand.ProductReference;
import com.inditex.icdmprodt.pipe.product.v9.CategoryType;
import com.inditex.icdmprodt.pipe.product.v9.ProductReferenceDomainEvent;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper
public interface ProductReferenceMapper {

  String SUPPLIER_UNKNOWN = "urn:UNKNOWN:UNKNOWN";

  @Mapping(target = "productId", source = "id")
  @Mapping(target = "referenceId", source = "reference.id")
  @Mapping(target = "supplierId", source = "reference.supplier", qualifiedByName = "charSequenceToString")
  @Mapping(target = "campaignId", source = "reference.categoryTypes", qualifiedByName = "getCampaignFromCategoryTypes")
  @Mapping(target = "originMarketId", source = "reference.origin_market")
  @Mapping(target = "families", source = "reference.categoryTypes", qualifiedByName = "getFamiliesFromCategoryTypes")
  @Mapping(target = "attributes", source = "reference.attributes", qualifiedByName = "mapAttributes")
  ProductReference from(ProductReferenceDomainEvent productReferenceDomainEvent);

  @Named("mapAttributes")
  default Attributes mapAttributes(final CharSequence attributesJson) {
    final ObjectMapper objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    try {
      return objectMapper.readValue(attributesJson.toString(), Attributes.class);
    } catch (final JsonProcessingException e) {
      return new Attributes(null, null, null);
    }
  }

  @Named("charSequenceToString")
  default String charSequenceToString(final CharSequence charSequence) {
    return charSequence != null ? charSequence.toString() : SUPPLIER_UNKNOWN;
  }

  @Named("getCampaignFromCategoryTypes")
  default String getCampaignIdFromCategoryTypes(final List<CategoryType> categoryTypes) {
    return categoryTypes.stream()
        .filter(categoryType -> "CAMPAIGN".contentEquals(categoryType.getType()))
        .map(categoryType -> categoryType.getId().toString())
        .findFirst()
        .orElse(null);
  }

  @Named("getFamiliesFromCategoryTypes")
  default List<String> getFamiliesFromCategoryTypes(final List<CategoryType> categoryTypes) {
    return categoryTypes.stream()
        .filter(categoryType -> "CAMPAIGN".contentEquals(categoryType.getType()))
        .flatMap(categoryType -> categoryType.getCategories().stream().map(String::valueOf).filter(s -> s.contains("urn:FAMILY")))
        .toList();
  }

}
