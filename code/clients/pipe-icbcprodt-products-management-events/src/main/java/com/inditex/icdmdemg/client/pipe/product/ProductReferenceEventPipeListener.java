package com.inditex.icdmdemg.client.pipe.product;

import static java.util.Objects.isNull;

import static com.inditex.icdmdemg.client.pipe.product.ProductReferenceEventPipeListener.ProductEventType.PRODUCT_REFERENCE_DELETED_EVENT;

import java.util.Arrays;
import java.util.Optional;
import java.util.function.Consumer;

import com.inditex.icdmdemg.application.process.ProductProcessManager;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary;
import com.inditex.icdmdemg.application.product.command.DeleteProductProjectionCommand.ProductReferenceIdDeleted;
import com.inditex.icdmdemg.application.product.command.DeleteProductTaxonomiesCommand.DeletedProduct;
import com.inditex.icdmdemg.application.product.command.ProjectProductCommand.ProductReference;
import com.inditex.icdmdemg.client.mapper.ProductReferenceMapper;
import com.inditex.icdmdemg.client.mapper.ProductTaxonomyMapper;
import com.inditex.icdmdemg.client.mapper.ProductsOperationSummaryMapper;
import com.inditex.icdmdemg.shared.aop.LogPipeInput;
import com.inditex.icdmprodt.pipe.product.v9.ProductDomainEvent;
import com.inditex.icdmprodt.pipe.product.v9.ProductEventEnvelopeV9;
import com.inditex.icdmprodt.pipe.product.v9.ProductReferenceDomainEvent;
import com.inditex.icdmprodt.pipe.product.v9.ProductsOperationSummaryEvents;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NonNull;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component("product-event")
@LogPipeInput
public class ProductReferenceEventPipeListener implements Consumer<Message<ProductEventEnvelopeV9>> {

  public static final String REJECTED = "REJECTED";

  private final ProductProcessManager productProcessManager;

  private final ProductReferenceMapper productReferenceMapper;

  private final ProductTaxonomyMapper productTaxonomyMapper;

  private final ProductsOperationSummaryMapper productsOperationSummaryMapper;

  @Override
  public void accept(final Message<ProductEventEnvelopeV9> message) {
    final Object payload = message.getPayload().getPayload();

    switch (payload) {
      case final ProductDomainEvent productDomainEvent -> this.consume(productDomainEvent);
      case final ProductReferenceDomainEvent productReferenceDomainEvent -> this.consume(productReferenceDomainEvent);
      case final ProductsOperationSummaryEvents productsMergedDomainEvent -> this.consume(productsMergedDomainEvent);
      default -> {
        // do nothing
      }
    }
  }

  private void consume(final ProductReferenceDomainEvent productReferenceDomainEvent) {
    this.getEventToProcessFromMetadata(productReferenceDomainEvent)
        .ifPresent(event -> {
          switch (event) {
            case ProductEventType.PRODUCT_REFERENCE_ADDED_EVENT -> this.consumeCreated(productReferenceDomainEvent);
            case ProductEventType.PRODUCT_REFERENCE_UPDATED_EVENT -> this.consumeUpdated(productReferenceDomainEvent);
            case ProductEventType.PRODUCT_REFERENCE_DELETED_EVENT -> this.consumeDeleted(productReferenceDomainEvent);
            default -> {
              // do nothing
            }
          }
        });
  }

  private void consume(final ProductDomainEvent productDomainEvent) {
    this.getEventToProcessFromMetadata(productDomainEvent)
        .ifPresent(event -> {
          switch (event) {
            case ProductEventType.PRODUCT_CREATED_EVENT -> this.consumeProductCreated(productDomainEvent);
            case ProductEventType.PRODUCT_UPDATED_EVENT -> this.consumeProductUpdated(productDomainEvent);
            case ProductEventType.PRODUCT_DELETED_EVENT -> this.consumeProductDeleted(productDomainEvent);
            default -> {
              // do nothing
            }
          }
        });
  }

  private void consume(final ProductsOperationSummaryEvents productsOperationSummaryEvent) {
    productsOperationSummaryEvent.getOperations()
        .forEach(operation -> ProductEventType.fromValue(operation.getEvent())
            .ifPresent(eventType -> this.productProcessManager.execute(
                new MergeSplitSummary(
                    this.productsOperationSummaryMapper.toUpdateVariantInput(operation),
                    this.productsOperationSummaryMapper.toUpdateVariantGroupInput(operation),
                    eventType.getEventName()))));
  }

  private Optional<ProductEventType> getEventToProcessFromMetadata(final ProductReferenceDomainEvent productReferenceDomainEvent) {
    return ProductEventType.fromValue(productReferenceDomainEvent.getEvent())
        .map(event -> {
          if (this.eventIsALogicalDeletion(productReferenceDomainEvent, event)) {
            return PRODUCT_REFERENCE_DELETED_EVENT;
          }
          return event;
        });
  }

  private Optional<ProductEventType> getEventToProcessFromMetadata(final ProductDomainEvent productDomainEvent) {
    return ProductEventType.fromValue(productDomainEvent.getEvent());
  }

  private boolean eventIsALogicalDeletion(final ProductReferenceDomainEvent productReferenceDomainEvent, final ProductEventType event) {
    return (ProductEventType.PRODUCT_REFERENCE_ADDED_EVENT.equals(event)
        || ProductEventType.PRODUCT_REFERENCE_UPDATED_EVENT.equals(event))
        && !isNull(productReferenceDomainEvent.getReference().getMetadata().getDeletedAt());
  }

  private void consumeProductDeleted(final ProductDomainEvent productDomainEvent) {
    this.productProcessManager.execute(new DeletedProduct(productDomainEvent.getEntityId()));

  }

  private void consumeProductUpdated(final ProductDomainEvent productDomainEvent) {
    final var pt = this.productTaxonomyMapper.from(productDomainEvent);
    this.productProcessManager.execute(pt);
  }

  private void consumeProductCreated(final ProductDomainEvent productDomainEvent) {
    final var pt = this.productTaxonomyMapper.from(productDomainEvent);
    this.productProcessManager.execute(pt);

  }

  private void consumeDeleted(final ProductReferenceDomainEvent productReferenceDeletedDomainEvent) {
    this.productProcessManager.execute(new ProductReferenceIdDeleted(productReferenceDeletedDomainEvent.getReference().getId()));
  }

  private void consumeUpdated(final ProductReferenceDomainEvent msg) {
    final ProductReference productReference = this.productReferenceMapper.from(msg);
    this.productProcessManager.execute(productReference);
  }

  private void consumeCreated(final ProductReferenceDomainEvent msg) {
    if (this.isDiscardedReferenceEvent(msg)) {
      return;
    }
    final ProductReference productReference = this.productReferenceMapper.from(msg);
    this.productProcessManager.execute(productReference);
  }

  private boolean isDiscardedReferenceEvent(final ProductReferenceDomainEvent msg) {
    return REJECTED.contentEquals(msg.getReference().getStatus());
  }

  @Getter
  public enum ProductEventType {

    PRODUCT_CREATED_EVENT("ProductCreatedEvent"),

    PRODUCT_UPDATED_EVENT("ProductUpdatedEvent"),

    PRODUCT_DELETED_EVENT("ProductDeletedEvent"),

    PRODUCT_REFERENCE_ADDED_EVENT("ProductReferenceAddedEvent"),

    PRODUCT_REFERENCE_UPDATED_EVENT("ProductReferenceUpdatedEvent"),

    PRODUCT_REFERENCE_DELETED_EVENT("ProductReferenceDeletedEvent"),

    PRODUCT_MERGED_EVENT("ProductMergedEvent"),

    PRODUCT_SPLIT_EVENT("ProductSplitEvent");

    private final String eventName;

    ProductEventType(String name) {
      this.eventName = name;
    }

    public static Optional<ProductEventType> fromValue(@NonNull final CharSequence value) {
      return Arrays.stream(ProductEventType.values())
          .filter(eventType -> eventType.eventName.equals(value.toString()))
          .findFirst();
    }

  }
}
