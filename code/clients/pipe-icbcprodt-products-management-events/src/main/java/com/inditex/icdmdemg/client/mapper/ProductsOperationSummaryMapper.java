package com.inditex.icdmdemg.client.mapper;

import java.util.List;
import java.util.Objects;

import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateReferenceInput;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateReferenceInput.Variant;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateVariantGroupInput;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateVariantGroupInput.VariantGroup;
import com.inditex.icdmdemg.shared.utils.CharSequenceMapper;
import com.inditex.icdmprodt.pipe.product.v9.ProductsOperationSummaryEvent;
import com.inditex.icdmprodt.pipe.product.v9.TargetReference;
import com.inditex.icdmprodt.pipe.product.v9.TargetVariant;
import com.inditex.icdmprodt.pipe.product.v9.TargetVariantGroup;

import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(uses = {CharSequenceMapper.class}, injectionStrategy = InjectionStrategy.CONSTRUCTOR, componentModel = "spring")
public interface ProductsOperationSummaryMapper {

  @Mapping(target = "variantGroupId", source = "id")
  @Mapping(target = "variantGroupSourceId", source = "sourceId")
  @Mapping(target = "name", source = "name")
  VariantGroup mapSingleVariantGroup(TargetVariantGroup targetVariantGroup);

  @Mapping(target = "referenceId", source = "id")
  @Mapping(target = "referenceSourceId", source = "sourceId")
  Variant mapSingleVariant(TargetReference targetVariantGroup);

  default UpdateVariantGroupInput toUpdateVariantGroupInput(final ProductsOperationSummaryEvent productsOperationSummaryEvent) {
    return new UpdateVariantGroupInput(this.mapVariantGroups(productsOperationSummaryEvent.getTarget().getVariantGroups()));
  }

  default UpdateReferenceInput toUpdateVariantInput(final ProductsOperationSummaryEvent productsOperationSummaryEvent) {
    return new UpdateReferenceInput(
        productsOperationSummaryEvent.getTarget().getId(),
        productsOperationSummaryEvent.getTarget().getTaxonomy().toString(),
        this.mapVariants(productsOperationSummaryEvent.getTarget().getVariants()));
  }

  private List<VariantGroup> mapVariantGroups(final List<TargetVariantGroup> mergedVariantGroups) {
    return mergedVariantGroups.stream()
        .filter(originVariantGroup -> Objects.nonNull(originVariantGroup.source_id))
        .map(this::mapSingleVariantGroup)
        .toList();
  }

  private List<Variant> mapVariants(final List<TargetVariant> variant) {
    return variant.stream()
        .flatMap(targetVariant -> targetVariant.getReferences().stream()
            .filter(targetReference -> Objects.nonNull(targetReference.source_id))
            .map(this::mapSingleVariant))
        .toList();
  }
}
