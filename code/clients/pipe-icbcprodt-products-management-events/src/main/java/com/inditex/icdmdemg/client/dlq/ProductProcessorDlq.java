package com.inditex.icdmdemg.client.dlq;

import com.inditex.amigafwk.data.stream.dlqprocessing.DlqProcessorStrategy;
import com.inditex.icdmprodt.pipe.product.v9.ProductEventEnvelopeV9;

import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;

@Slf4j
public class ProductProcessorDlq implements DlqProcessorStrategy<ProductEventEnvelopeV9> {
  public static final String LOG_CONTEXT = "DLQ Processed: ";

  @Override
  public void process(final Message<ProductEventEnvelopeV9> message) {
    log.info(LOG_CONTEXT.concat(message.getPayload().toString()));
  }
}
