package com.inditex.icdmdemg.client.mapper;

import static com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateVariantGroupInput.VariantGroup;

import static org.assertj.core.api.Assertions.assertThat;

import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateReferenceInput;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateReferenceInput.Variant;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateVariantGroupInput;
import com.inditex.icdmdemg.shared.utils.CharSequenceMapperImpl;
import com.inditex.icdmprodt.pipe.product.v9.ProductsOperationSummaryEvent;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = {ProductsOperationSummaryMapperImpl.class, CharSequenceMapperImpl.class})
class ProductsOperationSummaryMapperTest {

  @Autowired
  private ProductsOperationSummaryMapper mapper;

  @Test
  void toUpdateVariantGroupInput() {
    final var event = Instancio.of(ProductsOperationSummaryEvent.class).create();
    final var expected = new UpdateVariantGroupInput(event.getTarget().getVariantGroups().stream()
        .map(variantGroup -> new VariantGroup(variantGroup.getId(), variantGroup.getSourceId(), variantGroup.getName().toString()))
        .toList());

    final var result = this.mapper.toUpdateVariantGroupInput(event);

    assertThat(result).isEqualTo(expected);
  }

  @Test
  void toUpdateVariantInput() {
    final var event = Instancio.of(ProductsOperationSummaryEvent.class).create();
    final var expected = new UpdateReferenceInput(
        event.getTarget().getId(),
        event.getTarget().getTaxonomy().toString(),
        event.getTarget().getVariants().stream().flatMap(variant -> variant.getReferences()
            .stream().map(targetReference -> new Variant(targetReference.getId(), targetReference.getSourceId()))).toList());

    final var result = this.mapper.toUpdateVariantInput(event);

    assertThat(result).isEqualTo(expected);
  }

}
