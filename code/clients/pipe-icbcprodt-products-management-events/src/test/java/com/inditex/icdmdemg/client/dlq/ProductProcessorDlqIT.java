package com.inditex.icdmdemg.client.dlq;

import static org.mockito.Mockito.doReturn;

import com.inditex.amigafwk.test.logcaptor.LogCaptor;
import com.inditex.amigafwk.test.logcaptor.LogCaptorExtension;
import com.inditex.icdmprodt.pipe.product.v9.ProductEventEnvelopeV9;

import ch.qos.logback.classic.Level;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;

@ExtendWith({MockitoExtension.class, LogCaptorExtension.class})
class ProductProcessorDlqIT {

  private final LogCaptor logCaptor;

  private final ProductProcessorDlq sut = new ProductProcessorDlq();

  ProductProcessorDlqIT(final LogCaptor logCaptor) {
    this.logCaptor = logCaptor;
  }

  @Test
  void should_log_the_payload(@Mock final Message<ProductEventEnvelopeV9> message) {
    final var payload = Instancio.create(ProductEventEnvelopeV9.class);
    doReturn(payload).when(message).getPayload();

    this.sut.process(message);

    this.logCaptor.verifyMessagesContaining(ProductProcessorDlq.LOG_CONTEXT + payload)
        .withLevel(Level.INFO)
        .areExactly(1);
  }

}
