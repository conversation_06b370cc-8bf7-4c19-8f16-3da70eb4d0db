package com.inditex.icdmdemg.client.pipe.product;

import static com.inditex.icdmdemg.client.pipe.product.ProductReferenceEventPipeListener.ProductEventType.PRODUCT_CREATED_EVENT;
import static com.inditex.icdmdemg.client.pipe.product.ProductReferenceEventPipeListener.ProductEventType.PRODUCT_DELETED_EVENT;
import static com.inditex.icdmdemg.client.pipe.product.ProductReferenceEventPipeListener.ProductEventType.PRODUCT_MERGED_EVENT;
import static com.inditex.icdmdemg.client.pipe.product.ProductReferenceEventPipeListener.ProductEventType.PRODUCT_REFERENCE_ADDED_EVENT;
import static com.inditex.icdmdemg.client.pipe.product.ProductReferenceEventPipeListener.ProductEventType.PRODUCT_REFERENCE_DELETED_EVENT;
import static com.inditex.icdmdemg.client.pipe.product.ProductReferenceEventPipeListener.ProductEventType.PRODUCT_REFERENCE_UPDATED_EVENT;
import static com.inditex.icdmdemg.client.pipe.product.ProductReferenceEventPipeListener.ProductEventType.PRODUCT_SPLIT_EVENT;
import static com.inditex.icdmdemg.client.pipe.product.ProductReferenceEventPipeListener.ProductEventType.PRODUCT_UPDATED_EVENT;
import static com.inditex.icdmdemg.client.pipe.product.ProductReferenceEventPipeListener.REJECTED;

import static org.instancio.Select.field;
import static org.instancio.Select.fields;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.lang.reflect.Field;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

import com.inditex.amigafwk.data.test.stream.AmigaStreamTest;
import com.inditex.amigafwk.data.test.stream.EnableTestSupportBinder;
import com.inditex.icdmdemg.application.process.ProductProcessManager;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateReferenceInput;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateReferenceInput.Variant;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateVariantGroupInput;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateVariantGroupInput.VariantGroup;
import com.inditex.icdmdemg.application.product.command.DeleteProductProjectionCommand.ProductReferenceIdDeleted;
import com.inditex.icdmdemg.application.product.command.DeleteProductTaxonomiesCommand.DeletedProduct;
import com.inditex.icdmdemg.client.mapper.ProductReferenceMapper;
import com.inditex.icdmdemg.client.mapper.ProductTaxonomyMapper;
import com.inditex.icdmdemg.client.mapper.ProductsOperationSummaryMapper;
import com.inditex.icdmprodt.pipe.product.v9.CategoryType;
import com.inditex.icdmprodt.pipe.product.v9.Metadata;
import com.inditex.icdmprodt.pipe.product.v9.ProductDomainEvent;
import com.inditex.icdmprodt.pipe.product.v9.ProductEventEnvelopeV9;
import com.inditex.icdmprodt.pipe.product.v9.ProductReferenceDomainEvent;
import com.inditex.icdmprodt.pipe.product.v9.ProductVariantDomainEvent;
import com.inditex.icdmprodt.pipe.product.v9.ProductVariantGroupDomainEvent;
import com.inditex.icdmprodt.pipe.product.v9.ProductsOperationSummaryEvent;
import com.inditex.icdmprodt.pipe.product.v9.ProductsOperationSummaryEvents;
import com.inditex.icdmprodt.pipe.product.v9.Reference;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.stream.binder.test.InputDestination;
import org.springframework.cloud.stream.config.BindingProperties;
import org.springframework.cloud.stream.config.BindingServiceProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@SpringBootTest(classes = ProductReferenceEventPipeListenerIT.Config.class)
@AmigaStreamTest
@EnableTestSupportBinder
@ActiveProfiles("test")
class ProductReferenceEventPipeListenerIT {

  private static final String REGEX_TOPIC = "iop.global.pre.icbcprodt.product.public.domain.v9";

  @MockitoBean
  private ProductProcessManager productProcessManager;

  @Autowired
  private ProductTaxonomyMapper productTaxonomyMapper;

  @Autowired
  private ProductReferenceMapper productReferenceMapper;

  @Autowired
  private ProductsOperationSummaryMapper productsOperationSummaryMapper;

  @Autowired
  private BindingServiceProperties bindingServiceProperties;

  @Autowired
  private InputDestination inputDestination;

  @Test
  void should_delegate_productEvent_whenStateIsRejected_is_discarded() {
    final Reference reference = this.getReference(REJECTED, null);
    final var productReferenceAddedDomainEvent = Instancio.of(ProductReferenceDomainEvent.class)
        .set(fields(this::isReference), reference)
        .create();
    final var productEventEnvelope = new ProductEventEnvelopeV9();
    productEventEnvelope.setPayload(productReferenceAddedDomainEvent);
    final var message = MessageBuilder.withPayload(productEventEnvelope).build();

    this.sendMessages(message);

    verifyNoInteractions(this.productProcessManager);
  }

  @ParameterizedTest
  @ValueSource(strings = {"IN_PROGRESS", "COMPLETED"})
  void should_delegate_productEvent_whenStateIsInProgress_to_handler(final String referenceStatus) {
    final Reference reference = this.getReference(referenceStatus, null);
    final var productReferenceAddedDomainEvent = Instancio.of(ProductReferenceDomainEvent.class)
        .set(fields(this::isReference), reference)
        .set(fields(this::isEvent), PRODUCT_REFERENCE_ADDED_EVENT.getEventName())
        .create();
    final var productEventEnvelope = new ProductEventEnvelopeV9();
    productEventEnvelope.setPayload(productReferenceAddedDomainEvent);
    final var message = MessageBuilder.withPayload(productEventEnvelope).build();

    this.sendMessages(message);

    verify(this.productProcessManager).execute(this.productReferenceMapper.from(productReferenceAddedDomainEvent));
  }

  @Test
  void should_delegate_productDomainCreatedEvent_to_handler() {
    final var productReferenceUpdatedDomainEvent = Instancio.of(ProductDomainEvent.class)
        .set(fields(this::isEvent), PRODUCT_CREATED_EVENT.getEventName())
        .create();
    final var productEventEnvelope = new ProductEventEnvelopeV9();
    productEventEnvelope.setPayload(productReferenceUpdatedDomainEvent);
    final var message = MessageBuilder.withPayload(productEventEnvelope).build();

    this.sendMessages(message);

    verify(this.productProcessManager).execute(this.productTaxonomyMapper.from(productReferenceUpdatedDomainEvent));
  }

  @Test
  void should_delegate_productDomainUpdatedEvent_to_handler() {
    final var productReferenceUpdatedDomainEvent = Instancio.of(ProductDomainEvent.class)
        .set(fields(this::isEvent), PRODUCT_UPDATED_EVENT.getEventName())
        .create();
    final var productEventEnvelope = new ProductEventEnvelopeV9();
    productEventEnvelope.setPayload(productReferenceUpdatedDomainEvent);
    final var message = MessageBuilder.withPayload(productEventEnvelope).build();

    this.sendMessages(message);

    verify(this.productProcessManager).execute(this.productTaxonomyMapper.from(productReferenceUpdatedDomainEvent));
  }

  @Test
  void should_delegate_productDomainDeletedEvent_to_handler() {
    final var productReferenceUpdatedDomainEvent = Instancio.of(ProductDomainEvent.class)
        .set(fields(this::isEvent), PRODUCT_DELETED_EVENT.getEventName())
        .create();
    final var productEventEnvelope = new ProductEventEnvelopeV9();
    productEventEnvelope.setPayload(productReferenceUpdatedDomainEvent);
    final var message = MessageBuilder.withPayload(productEventEnvelope).build();

    this.sendMessages(message);

    verify(this.productProcessManager, never()).execute(this.productTaxonomyMapper.from(productReferenceUpdatedDomainEvent));
    verify(this.productProcessManager).execute(any(DeletedProduct.class));
  }

  @Test
  void should_delegate_productReferenceUpdatedEvent_to_handler() {
    final Reference reference = this.getReference("IN_PROGRESS", null);
    final var productReferenceUpdatedDomainEvent = Instancio.of(ProductReferenceDomainEvent.class)
        .set(fields(this::isReference), reference)
        .set(fields(this::isEvent), PRODUCT_REFERENCE_UPDATED_EVENT.getEventName())
        .create();
    final var productEventEnvelope = new ProductEventEnvelopeV9();
    productEventEnvelope.setPayload(productReferenceUpdatedDomainEvent);
    final var message = MessageBuilder.withPayload(productEventEnvelope).build();

    this.sendMessages(message);

    verify(this.productProcessManager).execute(this.productReferenceMapper.from(productReferenceUpdatedDomainEvent));
  }

  @Test
  void should_delegate_productReferenceAddedEvent_logical_deletion_to_handler() {
    final Reference reference = this.getReference("IN_PROGRESS", OffsetDateTime.now().toString());
    final var productReferenceUpdatedDomainEvent = Instancio.of(ProductReferenceDomainEvent.class)
        .set(fields(this::isReference), reference)
        .set(fields(this::isEvent), PRODUCT_REFERENCE_ADDED_EVENT.getEventName())
        .create();
    final var productEventEnvelope = new ProductEventEnvelopeV9();
    productEventEnvelope.setPayload(productReferenceUpdatedDomainEvent);
    final var message = MessageBuilder.withPayload(productEventEnvelope).build();

    this.sendMessages(message);

    verify(this.productProcessManager).execute(new ProductReferenceIdDeleted(productReferenceUpdatedDomainEvent.getReference().getId()));
  }

  @Test
  void should_delegate_productReferenceUpdatedEvent_logical_deletion_to_handler() {
    final Reference reference = this.getReference("IN_PROGRESS", OffsetDateTime.now().toString());
    final var productReferenceUpdatedDomainEvent = Instancio.of(ProductReferenceDomainEvent.class)
        .set(fields(this::isReference), reference)
        .set(fields(this::isEvent), PRODUCT_REFERENCE_UPDATED_EVENT.getEventName())
        .create();
    final var productEventEnvelope = new ProductEventEnvelopeV9();
    productEventEnvelope.setPayload(productReferenceUpdatedDomainEvent);
    final var message = MessageBuilder.withPayload(productEventEnvelope).build();

    this.sendMessages(message);

    verify(this.productProcessManager).execute(new ProductReferenceIdDeleted(productReferenceUpdatedDomainEvent.getReference().getId()));
  }

  private boolean isReference(final Field field) {
    return field.getName().equalsIgnoreCase("reference");
  }

  @Test
  void should_delegate_productReferenceDeletedEvent_to_handler() {
    final var productReferenceDeletedDomainEvent = Instancio.of(ProductReferenceDomainEvent.class)
        .set(fields(this::isEvent), PRODUCT_REFERENCE_DELETED_EVENT.getEventName())
        .create();
    final var productEventEnvelope = new ProductEventEnvelopeV9();
    productEventEnvelope.setPayload(productReferenceDeletedDomainEvent);
    final var message = MessageBuilder.withPayload(productEventEnvelope).build();

    this.sendMessages(message);

    verify(this.productProcessManager).execute(new ProductReferenceIdDeleted(productReferenceDeletedDomainEvent.getReference().getId()));
  }

  @Test
  void should_delegate_productMergedEvent_to_handler() {
    final var productsMergedDomainEvent = Instancio.of(ProductsOperationSummaryEvents.class)
        .set(fields(this::isEvent), PRODUCT_MERGED_EVENT.getEventName())
        .create();
    final var productEventEnvelope = new ProductEventEnvelopeV9();
    productEventEnvelope.setPayload(productsMergedDomainEvent);
    final var message = MessageBuilder.withPayload(productEventEnvelope).build();

    this.sendMessages(message);

    productsMergedDomainEvent.getOperations().forEach(operation -> {
      final var referenceExpected = this.castToUpdateReference(operation);
      final var variantGroupExpected = this.castToUpdateVariantGroup(operation);
      verify(this.productProcessManager)
          .execute(new MergeSplitSummary(referenceExpected, variantGroupExpected, PRODUCT_MERGED_EVENT.getEventName()));
    });
  }

  @Test
  void should_delegate_productSplitEvent_to_handler() {
    final var productsSplitDomainEvent = Instancio.of(ProductsOperationSummaryEvents.class)
        .set(fields(this::isEvent), PRODUCT_SPLIT_EVENT.getEventName())
        .create();
    final var productEventEnvelope = new ProductEventEnvelopeV9();
    productEventEnvelope.setPayload(productsSplitDomainEvent);
    final var message = MessageBuilder.withPayload(productEventEnvelope).build();

    this.sendMessages(message);

    productsSplitDomainEvent.getOperations().forEach(operation -> {
      final var referenceExpected = this.castToUpdateReference(operation);
      final var variantGroupExpected = this.castToUpdateVariantGroup(operation);
      verify(this.productProcessManager)
          .execute(new MergeSplitSummary(referenceExpected, variantGroupExpected, PRODUCT_SPLIT_EVENT.getEventName()));
    });
  }

  private boolean isEvent(final Field field) {
    return field.getName().equalsIgnoreCase("event");
  }

  private UpdateVariantGroupInput castToUpdateVariantGroup(final ProductsOperationSummaryEvent productsOperationSummaryEvent) {
    final List<VariantGroup> variantGroups = productsOperationSummaryEvent.getTarget().getVariantGroups().stream()
        .map(variantGroup -> new VariantGroup(variantGroup.getId(), variantGroup.getSourceId(), variantGroup.getName().toString()))
        .toList();
    return new UpdateVariantGroupInput(variantGroups);
  }

  private UpdateReferenceInput castToUpdateReference(final ProductsOperationSummaryEvent productsOperationSummaryEvent) {
    final List<Variant> variants =
        productsOperationSummaryEvent.getTarget().getVariants().stream().flatMap(variant -> variant.getReferences()
            .stream().map(targetReference -> new Variant(targetReference.getId(), targetReference.getSourceId()))).toList();

    return new UpdateReferenceInput(
        productsOperationSummaryEvent.getTarget().getId(),
        productsOperationSummaryEvent.getTarget().getTaxonomy().toString(),
        variants);
  }

  private Reference getReference(final String referenceStatus, final String deletedAt) {
    return Instancio.of(Reference.class)
        .set(fields(field -> field.getName().equalsIgnoreCase("category_types")), List.of(
            Instancio.of(CategoryType.class)
                .set(fields(field -> field.getName().equalsIgnoreCase("type")), "CAMPAIGN")
                .create()))
        .set(fields(field -> field.getName().equalsIgnoreCase("attributes")),
            "{\"color\": \"" + UUID.randomUUID() + "\", \"quality\": 123}")
        .set(fields(field -> field.getName().equalsIgnoreCase("status")), referenceStatus)
        .set(fields(field -> field.getName().equalsIgnoreCase("metadata")), this.getMetadata(deletedAt))
        .create();
  }

  private Metadata getMetadata(final String deletedAt) {
    return Instancio.of(Metadata.class).set(field("deleted_at"), deletedAt).create();
  }

  private void sendMessages(final Message<ProductEventEnvelopeV9> message) {
    this.bindingServiceProperties.getBindings().values().stream()
        .map(BindingProperties::getDestination)
        .filter(destination -> destination.matches(REGEX_TOPIC))
        .forEach(destination -> this.inputDestination.send(message, destination));
  }

  @ParameterizedTest
  @EnumSource(NotImplementedEvent.class)
  void should_not_send_commands_with_not_implemented_event_listeners(final NotImplementedEvent notImplementedEvent) {
    final var event = Instancio.create(notImplementedEvent.eventClass);
    final var productEventEnvelope = new ProductEventEnvelopeV9();
    productEventEnvelope.setPayload(event);
    final var message = MessageBuilder.withPayload(productEventEnvelope).build();

    this.sendMessages(message);

    verifyNoInteractions(this.productProcessManager);
  }

  enum NotImplementedEvent {
    PRODUCT_DOMAIN_EVENT(ProductDomainEvent.class, "any"),
    PRODUCT_VARIANT_GROUP_DOMAIN_EVENT(ProductVariantGroupDomainEvent.class, "any"),
    PRODUCT_VARIANT_DOMAIN_EVENT(ProductVariantDomainEvent.class, "any"),
    PRODUCT_REFERENCE_DOMAIN_EVENT(ProductReferenceDomainEvent.class, "any"),
    PRODUCTS_OPERATION_SUMMARY_EVENT(ProductsOperationSummaryEvent.class, "any");

    private final Class<?> eventClass;

    private final String eventType;

    NotImplementedEvent(final Class<?> eventClass, final String eventType) {
      this.eventClass = eventClass;
      this.eventType = eventType;
    }

  }

  @Configuration
  @ComponentScan(
      basePackages = {"com.inditex.icdmdemg.icbcprodt.products_management.event.pipe", "com.inditex.icdmdemg.client.mapper",
          "com.inditex.icdmdemg.client.pipe.product", "com.inditex.icdmdemg.shared.utils"})
  public static class Config {
  }

}
