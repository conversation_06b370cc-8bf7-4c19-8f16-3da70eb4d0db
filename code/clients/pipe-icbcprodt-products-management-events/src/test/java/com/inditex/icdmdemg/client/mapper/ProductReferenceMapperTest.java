package com.inditex.icdmdemg.client.mapper;

import static com.inditex.icdmdemg.client.mapper.ProductReferenceMapper.SUPPLIER_UNKNOWN;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.instancio.Select.field;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.product.command.ProjectProductCommand.Attributes;
import com.inditex.icdmdemg.application.product.command.ProjectProductCommand.ProductReference;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmprodt.pipe.product.v9.CategoryType;
import com.inditex.icdmprodt.pipe.product.v9.ProductReferenceDomainEvent;
import com.inditex.icdmprodt.pipe.product.v9.Reference;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class ProductReferenceMapperTest {

  private ProductReferenceMapperImpl sut;

  @BeforeAll
  void setUp() {
    this.sut = new ProductReferenceMapperImpl();
  }

  @Test
  void should_map_from_reference_domain_event() {
    final String attributes =
        """
            {
               "model":5555,
               "quality":333,
               "size":"4c8925a2-2134-4216-ad5b-865c02e5b406",
               "color":"bff3e590-37f7-409f-8650-130fbb4617ff",
               "owners":["urn:BUYERCODE:569c2cb0-419e-466e-b305-ef56a353991b","urn:BUYERCODE:569c2cb0-419e-466e-b305-ef56a353991a"]
            }
            """;

    final var family1 = UUID.randomUUID();
    final var family2 = UUID.randomUUID();
    final var categories =
        List.of(UrnConstantsEnum.FAMILY.prefixWithUrn(family1),
            (CharSequence) UrnConstantsEnum.FAMILY.prefixWithUrn(family2));

    final var categoryType = Instancio.of(CategoryType.class)
        .set(field("type"), "CAMPAIGN")
        .set(field("categories"), categories)
        .create();

    final Reference reference = Instancio.of(Reference.class)
        .set(field("attributes"), attributes)
        .set(field("category_types"), List.of(categoryType))
        .create();

    final var productReferenceAddedDomainEvent = Instancio.of(ProductReferenceDomainEvent.class)
        .set(field("reference"), reference)
        .create();

    final ProductReference expected =
        new ProductReference(productReferenceAddedDomainEvent.getId().toString(),
            productReferenceAddedDomainEvent.getReference().getId().toString(),
            productReferenceAddedDomainEvent.getReference().getSupplier().toString(),
            productReferenceAddedDomainEvent.getReference().getCategoryTypes().get(0).getId().toString(),
            productReferenceAddedDomainEvent.getReference().getOriginMarket(),
            List.of(UrnConstantsEnum.FAMILY.prefixWithUrn(family1), UrnConstantsEnum.FAMILY.prefixWithUrn(family2)),
            new Attributes(UUID.fromString("bff3e590-37f7-409f-8650-130fbb4617ff"), 333,
                List.of("urn:BUYERCODE:569c2cb0-419e-466e-b305-ef56a353991b", "urn:BUYERCODE:569c2cb0-419e-466e-b305-ef56a353991a")));

    final ProductReference result = this.sut.from(productReferenceAddedDomainEvent);

    assertThat(result).isEqualTo(expected);
  }

  @Test
  void should_map_from_domain_event_when_color_or_quality_not_exist_in_attributes() {
    final String attributes =
        """
            {
               "model":5555,
               "size":"4c8925a2-2134-4216-ad5b-865c02e5b406"
            }
            """;
    final var categoryType = Instancio.of(CategoryType.class)
        .set(field("type"), "CAMPAIGN")
        .create();
    final Reference reference = Instancio.of(Reference.class)
        .set(field("attributes"), attributes)
        .set(field("category_types"), List.of(categoryType))
        .create();
    final var productReferenceAddedDomainEvent = Instancio.of(ProductReferenceDomainEvent.class)
        .set(field("reference"), reference)
        .create();
    final ProductReference expectedRequest =
        new ProductReference(productReferenceAddedDomainEvent.getId().toString(),
            productReferenceAddedDomainEvent.getReference().getId().toString(),
            productReferenceAddedDomainEvent.getReference().getSupplier().toString(),
            productReferenceAddedDomainEvent.getReference().getCategoryTypes().get(0).getId().toString(),
            productReferenceAddedDomainEvent.getReference().getOriginMarket(),
            List.of(),
            new Attributes(null, null, null));

    final ProductReference result = this.sut.from(productReferenceAddedDomainEvent);

    assertThat(expectedRequest).isEqualTo(result);
  }

  @Test
  void should_map_from_domain_event_when_supplier_not_exist() {
    final String attributes =
        """
            {
               "model":5555,
               "quality":333,
               "size":"4c8925a2-2134-4216-ad5b-865c02e5b406",
               "color":"bff3e590-37f7-409f-8650-130fbb4617ff",
               "owners":["urn:BUYERCODE:569c2cb0-419e-466e-b305-ef56a353991b","urn:BUYERCODE:569c2cb0-419e-466e-b305-ef56a353991a"]
            }
            """;

    final var categoryType = Instancio.of(CategoryType.class)
        .set(field("type"), "CAMPAIGN")
        .create();

    final Reference reference = Instancio.of(Reference.class)
        .set(field("attributes"), attributes)
        .set(field("category_types"), List.of(categoryType))
        .set(field("supplier"), null)
        .create();

    final var productReferenceAddedDomainEvent = Instancio.of(ProductReferenceDomainEvent.class)
        .set(field("reference"), reference)
        .create();

    final ProductReference expected =
        new ProductReference(productReferenceAddedDomainEvent.getId().toString(),
            productReferenceAddedDomainEvent.getReference().getId().toString(),
            SUPPLIER_UNKNOWN,
            productReferenceAddedDomainEvent.getReference().getCategoryTypes().get(0).getId().toString(),
            productReferenceAddedDomainEvent.getReference().getOriginMarket(),
            List.of(),
            new Attributes(UUID.fromString("bff3e590-37f7-409f-8650-130fbb4617ff"), 333,
                List.of("urn:BUYERCODE:569c2cb0-419e-466e-b305-ef56a353991b", "urn:BUYERCODE:569c2cb0-419e-466e-b305-ef56a353991a")));

    final ProductReference result = this.sut.from(productReferenceAddedDomainEvent);

    assertThat(result).isEqualTo(expected);
  }

}
