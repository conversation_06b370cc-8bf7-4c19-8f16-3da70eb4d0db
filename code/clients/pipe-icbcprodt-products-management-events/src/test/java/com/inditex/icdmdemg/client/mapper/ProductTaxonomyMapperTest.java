package com.inditex.icdmdemg.client.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import com.inditex.icdmdemg.application.product.command.ProjectProductTaxonomiesCommand.ProductTaxonomy;
import com.inditex.icdmdemg.shared.utils.CharSequenceMapperImpl;
import com.inditex.icdmprodt.pipe.product.v9.ProductDomainEvent;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = {ProductTaxonomyMapperImpl.class, CharSequenceMapperImpl.class})
class ProductTaxonomyMapperTest {
  @Autowired
  private ProductTaxonomyMapper sut;

  @Test
  void should_map_from_product_domain_event() {
    final var productDomainEvent = Instancio.create(ProductDomainEvent.class);
    final var expected = new ProductTaxonomy(productDomainEvent.getEntityId(), productDomainEvent.getTaxonomy().toString());

    final var result = this.sut.from(productDomainEvent);

    assertThat(result).isEqualTo(expected);
  }
}
