<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.inditex.icdmdemg</groupId>
    <artifactId>icdmdebtmgmt-clients</artifactId>
    <version>1.91.0-SNAPSHOT</version>
  </parent>

  <artifactId>icdmdebtmgmt-pipe-icbcprovis-domain-bc-provisioning-event</artifactId>
  <name>${project.groupId}:${project.artifactId}</name>

  <properties>
    <avro.imports.excludes>**/quantityUseAssignmentToOrder/**/*.avsc,
      **/useAssignmentToOrder/v1/**/*.avsc,
      **/useAssignmentToOrder/v2/**/*.avsc,
      **/useAssignmentToOrder/v3/**/*.avsc</avro.imports.excludes>
    <avro.sources.excludes>**/quantityUseAssignmentToOrder/**/*.avsc,
      **/useAssignmentToOrder/v1/**/*.avsc,
      **/useAssignmentToOrder/v2/**/*.avsc,
      **/useAssignmentToOrder/v3/**/*.avsc</avro.sources.excludes>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.inditex.amigafwk.service</groupId>
      <artifactId>service-starter-stream-pipe</artifactId>
    </dependency>

    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-application</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.api</groupId>
      <artifactId>icbcprovis-event-domain-bc-provisioning-event-experimental</artifactId>
      <version>${icbcprovis-event-domain-bc-provisioning-event.version}</version>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.apiasync</groupId>
      <artifactId>lib-asyncapi-tools</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.test</groupId>
      <artifactId>test-starter-instancio</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.data</groupId>
      <artifactId>data-starter-test-stream</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.inditex.icdmdemg</groupId>
      <artifactId>icdmdebtmgmt-shared</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.test</groupId>
      <artifactId>test-starter-logcaptor</artifactId>
      <scope>test</scope>
    </dependency>

  </dependencies>

</project>
