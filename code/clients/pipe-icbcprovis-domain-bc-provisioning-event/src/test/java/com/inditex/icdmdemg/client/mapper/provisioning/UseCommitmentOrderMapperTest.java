package com.inditex.icdmdemg.client.mapper.provisioning;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.OrderUse;
import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.Use;
import com.inditex.icdmdemg.shared.utils.TimeMapperUtils;
import com.inditex.iop.use.assignment.order.v4.OrderAssignment;
import com.inditex.iop.use.assignment.order.v4.UseAssignmentToOrderV4;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class UseCommitmentOrderMapperTest {

  private final UseCommitmentOrderMapper sut = new UseCommitmentOrderMapper();

  @Test
  void should_map_from_event() {
    final String serviceDate = OffsetDateTime.MIN.toString();
    final String budgetCycleChangeDate = OffsetDateTime.MIN.toString();
    final OrderAssignment orderAssignment = Instancio.of(OrderAssignment.class)
        .set(field("budget_cycle_change_date"), budgetCycleChangeDate).create();
    final UseAssignmentToOrderV4 event =
        Instancio.of(UseAssignmentToOrderV4.class).set(field("order_assignations"), List.of(orderAssignment)).create();
    event.setServiceDate(serviceDate);

    final OrderUse result = this.sut.fromUseAssignmentToOrder(event);

    assertThat(result)
        .usingRecursiveComparison()
        .ignoringCollectionOrder()
        .isEqualTo(this.castToObject(event));
  }

  private OrderUse castToObject(final UseAssignmentToOrderV4 event) {
    return new OrderUse(
        String.valueOf(event.getId()),
        String.valueOf(event.getOrderId()),
        String.valueOf(event.getOrderLineId()),
        String.valueOf(event.getBudgetCycle()),
        String.valueOf(event.getServiceLocalizationId()),
        event.getServiceLocalizationType().toString(),
        TimeMapperUtils.offsetDateTimeFromString(event.getServiceDate().toString()),
        event.getOrderAssignations().stream()
            .flatMap(assignment -> assignment.getUses().stream()
                .findFirst()
                .map(orderUse -> new Use(
                    acceptNullElseMap(assignment.getProductReferenceId(), String::valueOf),
                    acceptNullElseMap(assignment.getReferenceLineStatus(), String::valueOf),
                    acceptNullElseMap(assignment.getBudgetCycleChangeDate(), TimeMapperUtils::offsetDateTimeFromString),
                    acceptNullElseMap(orderUse.getUseId(), String::valueOf),
                    acceptNullElseMap(assignment.getQuantity(), BigDecimal::valueOf)))
                .stream())
            .toList());
  }
}
