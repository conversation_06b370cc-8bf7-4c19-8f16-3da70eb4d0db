package com.inditex.icdmdemg.client.pipe.provisioning;

import static org.instancio.Select.fields;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

import com.inditex.amigafwk.data.test.stream.AmigaStreamTest;
import com.inditex.amigafwk.data.test.stream.EnableTestSupportBinder;
import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.OrderUse;
import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.Use;
import com.inditex.icdmdemg.application.process.UseCommitmentProcessManager;
import com.inditex.icdmdemg.application.process.UseCommitmentProcessManager.UseCommitmentProcessManagerInput;
import com.inditex.icdmdemg.client.mapper.provisioning.UseCommitmentOrderMapper;
import com.inditex.iop.use.assignment.order.v4.OrderType;
import com.inditex.iop.use.assignment.order.v4.UseAssignmentToOrderEnvelopeV4;
import com.inditex.iop.use.assignment.order.v4.UseAssignmentToOrderV4;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.stream.binder.test.InputDestination;
import org.springframework.cloud.stream.config.BindingProperties;
import org.springframework.cloud.stream.config.BindingServiceProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@SpringBootTest(classes = UseCommitmentEventPipeListenerIT.Config.class)
@AmigaStreamTest
@EnableTestSupportBinder
@ActiveProfiles("test")
class UseCommitmentEventPipeListenerIT {

  private static final String REGEX_TOPIC = "iop.global.pre.icbcprovis.use-assignment-order.public.load.v4";

  @MockitoBean
  private UseCommitmentProcessManager processManager;

  @Autowired
  private UseCommitmentOrderMapper useCommitmentOrderMapper;

  @Autowired
  private BindingServiceProperties bindingServiceProperties;

  @Autowired
  private InputDestination inputDestination;

  @Test
  void should_delegate_useAssignmentEvent_whenStateIsOrdinary_is_discarded() {
    final UseAssignmentToOrderV4 useAssignmentToOrder = Instancio.create(UseAssignmentToOrderV4.class);
    useAssignmentToOrder.setOrderType(OrderType.ORDINARY);
    final var event = new UseAssignmentToOrderEnvelopeV4();
    event.setPayload(useAssignmentToOrder);
    final var message = MessageBuilder.withPayload(event).build();

    this.sendMessages(message);

    verifyNoInteractions(this.processManager);
  }

  @Test
  void should_not_delegate_useAssignmentEvent_whenLocalizationIdnull_is_discarded() {
    final UseAssignmentToOrderV4 useAssignmentToOrder = Instancio.create(UseAssignmentToOrderV4.class);
    useAssignmentToOrder.setServiceLocalizationId(null);
    final var event = new UseAssignmentToOrderEnvelopeV4();
    event.setPayload(useAssignmentToOrder);
    final var message = MessageBuilder.withPayload(event).build();

    this.sendMessages(message);

    verifyNoInteractions(this.processManager);
  }

  @Test
  void should_not_consume_payload_different_from_useAssignmentToOrder() {
    final UseAssignmentToOrderV4 useAssignmentToOrder = Instancio.create(UseAssignmentToOrderV4.class);
    useAssignmentToOrder.setOrderType(OrderType.ORDINARY);
    final var event = new UseAssignmentToOrderEnvelopeV4();
    final var message = MessageBuilder.withPayload(event).build();

    this.sendMessages(message);

    verifyNoInteractions(this.processManager);
  }

  @Test
  void should_delegate_useAssignmentEvent_whenStateIsCommitment_to_handle() {
    final UseAssignmentToOrderV4 useAssignmentToOrder = Instancio.of(UseAssignmentToOrderV4.class)
        .generate(fields(field -> field.getName().contains("date")),
            generators -> generators.temporal().instant().future().asString())
        .create();
    useAssignmentToOrder.setOrderType(OrderType.COMMITMENT);
    final var event = new UseAssignmentToOrderEnvelopeV4();
    event.setPayload(useAssignmentToOrder);
    final var message = MessageBuilder.withPayload(event).build();

    this.sendMessages(message);

    verify(this.processManager).execute(this.getExpectedInput(useAssignmentToOrder));
  }

  private UseCommitmentProcessManagerInput getExpectedInput(final UseAssignmentToOrderV4 useAssignmentToOrder) {
    return new UseCommitmentProcessManagerInput(
        new OrderUse(
            useAssignmentToOrder.getId().toString(),
            String.valueOf(useAssignmentToOrder.getOrderId()),
            String.valueOf(useAssignmentToOrder.getOrderLineId()),
            useAssignmentToOrder.getBudgetCycle().toString(),
            useAssignmentToOrder.getServiceLocalizationId().toString(),
            useAssignmentToOrder.getServiceLocalizationType().name(),
            OffsetDateTime.parse(useAssignmentToOrder.getServiceDate()),
            useAssignmentToOrder.getOrderAssignations().stream()
                .flatMap(assignment -> assignment.getUses().stream()
                    .findFirst()
                    .map(orderUse -> new Use(
                        assignment.getProductReferenceId().toString(),
                        assignment.getReferenceLineStatus().toString(),
                        OffsetDateTime.parse(assignment.getBudgetCycleChangeDate()),
                        orderUse.getUseId().toString(),
                        BigDecimal.valueOf(assignment.getQuantity())))
                    .stream())
                .toList()));
  }

  private void sendMessages(final Message<UseAssignmentToOrderEnvelopeV4> message) {
    this.bindingServiceProperties.getBindings().values().stream()
        .map(BindingProperties::getDestination)
        .filter(destination -> destination.matches(REGEX_TOPIC))
        .forEach(destination -> this.inputDestination.send(message, destination));
  }

  @Configuration
  @ComponentScan(
      basePackages = {"com.inditex.icdmdemg.client.pipe.provisioning", "com.inditex.icdmdemg.client.mapper.provisioning"})
  public static class Config {

  }
}
