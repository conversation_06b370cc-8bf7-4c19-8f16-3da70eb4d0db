package com.inditex.icdmdemg.client.mapper.provisioning;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import java.math.BigDecimal;

import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.OrderUse;
import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.Use;
import com.inditex.icdmdemg.shared.utils.TimeMapperUtils;
import com.inditex.iop.use.assignment.order.v4.UseAssignmentToOrderV4;

import org.springframework.stereotype.Component;

@Component
public class UseCommitmentOrderMapper {

  public OrderUse fromUseAssignmentToOrder(final UseAssignmentToOrderV4 useAssignmentToOrder) {
    return new OrderUse(
        acceptNullElseMap(useAssignmentToOrder.getId(), String::valueOf),
        String.valueOf(useAssignmentToOrder.getOrderId()),
        String.valueOf(useAssignmentToOrder.getOrderLineId()),
        acceptNullElseMap(useAssignmentToOrder.getBudgetCycle(), String::valueOf),
        acceptNullElseMap(useAssignmentToOrder.getServiceLocalizationId(), String::valueOf),
        acceptNullElseMap(useAssignmentToOrder.getServiceLocalizationType(), String::valueOf),
        acceptNullElseMap(useAssignmentToOrder.getServiceDate(), TimeMapperUtils::offsetDateTimeFromString),
        useAssignmentToOrder.getOrderAssignations().stream()
            .flatMap(assignment -> assignment.getUses().stream()
                .findFirst()
                .map(orderUse -> new Use(
                    acceptNullElseMap(assignment.getProductReferenceId(), String::valueOf),
                    acceptNullElseMap(assignment.getReferenceLineStatus(), String::valueOf),
                    acceptNullElseMap(assignment.getBudgetCycleChangeDate(), TimeMapperUtils::offsetDateTimeFromString),
                    acceptNullElseMap(orderUse.getUseId(), String::valueOf),
                    acceptNullElseMap(assignment.getQuantity(), BigDecimal::valueOf)))
                .stream())
            .toList());
  }

}
