package com.inditex.icdmdemg.client.pipe.provisioning;

import java.util.Objects;
import java.util.function.Consumer;

import com.inditex.icdmdemg.application.process.UseCommitmentProcessManager;
import com.inditex.icdmdemg.application.process.UseCommitmentProcessManager.UseCommitmentProcessManagerInput;
import com.inditex.icdmdemg.client.mapper.provisioning.UseCommitmentOrderMapper;
import com.inditex.icdmdemg.shared.aop.LogPipeInput;
import com.inditex.iop.use.assignment.order.v4.UseAssignmentToOrderEnvelopeV4;
import com.inditex.iop.use.assignment.order.v4.UseAssignmentToOrderV4;

import lombok.RequiredArgsConstructor;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component("use-assignment-to-order-v4")
@LogPipeInput
public class UseCommitmentEventPipeListener implements Consumer<Message<UseAssignmentToOrderEnvelopeV4>> {

  public static final String COMMITMENT = "COMMITMENT";

  private final UseCommitmentOrderMapper useCommitmentOrderMapper;

  private final UseCommitmentProcessManager processManager;

  @Override
  public void accept(final Message<UseAssignmentToOrderEnvelopeV4> useAssignmentToOrderEnvelopeV4Message) {
    final Object payload = useAssignmentToOrderEnvelopeV4Message.getPayload().getPayload();

    if (payload instanceof final UseAssignmentToOrderV4 useAssignmentToOrder) {
      this.consume(useAssignmentToOrder);
    }
  }

  private void consume(final UseAssignmentToOrderV4 msg) {
    if (this.isDiscardedEvent(msg)) {
      return;
    }
    final var orderUse = this.useCommitmentOrderMapper.fromUseAssignmentToOrder(msg);
    this.processManager.execute(new UseCommitmentProcessManagerInput(orderUse));
  }

  private boolean isDiscardedEvent(final UseAssignmentToOrderV4 msg) {
    return !COMMITMENT.equalsIgnoreCase(msg.getOrderType().name())
        || Objects.isNull(msg.getOrderId())
        || Objects.isNull(msg.getOrderLineId())
        || Objects.isNull(msg.getServiceLocalizationId());
  }
}
