package com.inditex.icdmdemg.client.dlq;

import com.inditex.amigafwk.data.stream.dlqprocessing.DlqProcessorStrategy;
import com.inditex.iop.use.assignment.order.v4.UseAssignmentToOrderEnvelopeV4;

import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;

@Slf4j
public class UseAssignmentProcessorDlq implements DlqProcessorStrategy<UseAssignmentToOrderEnvelopeV4> {
  public static final String LOG_CONTEXT = "DLQ Processed: ";

  @Override
  public void process(final Message<UseAssignmentToOrderEnvelopeV4> message) {
    log.info(LOG_CONTEXT.concat(message.getPayload().toString()));
  }

}
