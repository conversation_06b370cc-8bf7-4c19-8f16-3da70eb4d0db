package com.inditex.icdmdemg.client.mapper;

import java.time.OffsetDateTime;

import com.inditex.bcsupshipm.event.domain.v4.CommitmentsPackingModifiedV2;
import com.inditex.bcsupshipm.event.domain.v4.CommitmentsShipmentCancelledV2;
import com.inditex.bcsupshipm.event.domain.v4.CommitmentsShipmentCreatedV3;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCancelledCommand.ShipmentCancelled;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCreatedCommand.ShipmentCreated;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentModifiedCommand.ShipmentModified;
import com.inditex.icdmdemg.shared.utils.TimeMapperUtils;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper
public interface CommitmentShipmentMapper {

  @Mapping(target = "id", source = "id")
  @Mapping(target = "distributionNominatedLineId", source = "transport_id")
  @Mapping(target = "quantity", source = "packing.quantity.value")
  @Mapping(target = "measurementUnitId", source = "packing.quantity.unit")
  @Mapping(target = "sentDate", source = "sent_date")
  ShipmentCreated fromShipmentCreated(CommitmentsShipmentCreatedV3 shipmentCreated);

  @Mapping(target = "id", source = "id")
  @Mapping(target = "distributionNominatedLineId", source = "transport_id")
  @Mapping(target = "quantity", source = "packing.quantity.value")
  @Mapping(target = "measurementUnitId", source = "packing.quantity.unit")
  ShipmentModified fromShipmentModified(CommitmentsPackingModifiedV2 packingModified);

  @Mapping(target = "id", source = "id")
  @Mapping(target = "distributionNominatedLineId", source = "transport_id")
  ShipmentCancelled fromShipmentCancelled(CommitmentsShipmentCancelledV2 shipmentCancelled);

  default String mapCharSequenceToString(final CharSequence value) {
    return String.valueOf(value);
  }

  default OffsetDateTime mapCharSequenceToOffsetDateTime(final CharSequence value) {
    return TimeMapperUtils.offsetDateTimeFromString(value);
  }

}
