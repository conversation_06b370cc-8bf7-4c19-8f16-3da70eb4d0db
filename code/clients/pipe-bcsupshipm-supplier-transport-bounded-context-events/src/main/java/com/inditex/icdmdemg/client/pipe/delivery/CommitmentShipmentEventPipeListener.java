package com.inditex.icdmdemg.client.pipe.delivery;

import java.time.OffsetDateTime;
import java.util.Optional;
import java.util.function.Function;
import java.util.function.Supplier;

import com.inditex.bcsupshipm.event.domain.v4.CommitmentsPackingModifiedV2;
import com.inditex.bcsupshipm.event.domain.v4.CommitmentsShipmentCancelledV2;
import com.inditex.bcsupshipm.event.domain.v4.CommitmentsShipmentCreatedV3;
import com.inditex.bcsupshipm.event.domain.v4.ShipmentEnvelopeV4;
import com.inditex.icdmdemg.application.process.CommitmentShipmentProcessManager;
import com.inditex.icdmdemg.application.shared.idempotent.EnableIdempotentConsuming;
import com.inditex.icdmdemg.application.shared.idempotent.IdempotentConsumer;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCancelledCommand.ShipmentCancelled;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCreatedCommand.ShipmentCreated;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentModifiedCommand.ShipmentModified;
import com.inditex.icdmdemg.client.mapper.CommitmentShipmentMapper;
import com.inditex.icdmdemg.domain.consumermessage.ConsumerMessage;
import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageId;
import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageName;
import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageTimestamp;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component("commitment-shipment-event")
@EnableIdempotentConsuming
@Slf4j
public class CommitmentShipmentEventPipeListener implements IdempotentConsumer<ShipmentEnvelopeV4> {

  private final CommitmentShipmentMapper commitmentShipmentMapper;

  private final CommitmentShipmentProcessManager processManager;

  @Override
  public void accept(final Message<ShipmentEnvelopeV4> shipmentEnvelopeV2Message) {
    final Object payload = shipmentEnvelopeV2Message.getPayload().getPayload();

    if (payload instanceof final CommitmentsShipmentCreatedV3 processCreated) {
      this.mapCommand(() -> this.commitmentShipmentMapper.fromShipmentCreated(processCreated))
          .ifPresent(shipmentCreated -> this.processManager.execute((ShipmentCreated) shipmentCreated));
      return;
    }

    if (payload instanceof final CommitmentsPackingModifiedV2 commitmentsPackingModifiedMessage) {
      this.mapCommand(() -> this.commitmentShipmentMapper.fromShipmentModified(commitmentsPackingModifiedMessage))
          .ifPresent(shipmentModified -> this.processManager.execute((ShipmentModified) shipmentModified));
      return;
    }

    if (payload instanceof final CommitmentsShipmentCancelledV2 shipmentCancelledMessage) {
      this.mapCommand(() -> this.commitmentShipmentMapper.fromShipmentCancelled(shipmentCancelledMessage))
          .ifPresent(shipmentCancelled -> this.processManager.execute((ShipmentCancelled) shipmentCancelled));
    }
  }

  @Override
  public Function<Message<ShipmentEnvelopeV4>, ConsumerMessage> buildConsumerMessage() {
    return message -> {
      final var metadata = message.getPayload().getMetadata();
      return new ConsumerMessage(
          ConsumerMessageId.from(metadata.getId()),
          ConsumerMessageName.from(metadata.getName()),
          ConsumerMessageTimestamp.of(OffsetDateTime.now()));
    };
  }

  private Optional<?> mapCommand(final Supplier<?> mappingFunction) {
    try {
      return Optional.ofNullable(mappingFunction.get());
    } catch (final Exception e) {
      log.error("Error mapping object", e);
      return Optional.empty();
    }
  }
}
