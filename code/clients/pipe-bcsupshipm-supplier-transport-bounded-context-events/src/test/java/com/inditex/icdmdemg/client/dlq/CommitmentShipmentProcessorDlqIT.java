package com.inditex.icdmdemg.client.dlq;

import static org.mockito.Mockito.doReturn;

import com.inditex.amigafwk.test.logcaptor.LogCaptor;
import com.inditex.amigafwk.test.logcaptor.LogCaptorExtension;
import com.inditex.bcsupshipm.event.domain.v4.ShipmentEnvelopeV4;

import ch.qos.logback.classic.Level;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;

@ExtendWith({MockitoExtension.class, LogCaptorExtension.class})
class CommitmentShipmentProcessorDlqIT {

  private final LogCaptor logCaptor;

  private final CommitmentShipmentProcessorDlq sut = new CommitmentShipmentProcessorDlq();

  CommitmentShipmentProcessorDlqIT(final LogCaptor logCaptor) {
    this.logCaptor = logCaptor;
  }

  @Test
  void should_log_the_payload(@Mock final Message<ShipmentEnvelopeV4> message) {
    final var payload = Instancio.create(ShipmentEnvelopeV4.class);

    doReturn(payload).when(message).getPayload();

    this.sut.process(message);

    this.logCaptor.verifyMessagesContaining(CommitmentShipmentProcessorDlq.LOG_CONTEXT + payload)
        .withLevel(Level.INFO)
        .areExactly(1);
  }
}
