package com.inditex.icdmdemg.client.pipe.delivery;

import static org.instancio.Select.fields;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

import com.inditex.amigafwk.data.test.stream.AmigaStreamTest;
import com.inditex.amigafwk.data.test.stream.EnableTestSupportBinder;
import com.inditex.aqsw.pipe.v1.Metadata;
import com.inditex.bcsupshipm.event.domain.v4.CommitmentsPackingModifiedV2;
import com.inditex.bcsupshipm.event.domain.v4.CommitmentsShipmentCancelledV2;
import com.inditex.bcsupshipm.event.domain.v4.CommitmentsShipmentCreatedV3;
import com.inditex.bcsupshipm.event.domain.v4.ShipmentEnvelopeV4;
import com.inditex.icdmdemg.application.process.CommitmentShipmentProcessManager;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCancelledCommand.ShipmentCancelled;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCreatedCommand.ShipmentCreated;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentModifiedCommand.ShipmentModified;
import com.inditex.icdmdemg.client.mapper.CommitmentShipmentMapper;
import com.inditex.icdmdemg.domain.consumermessage.ConsumerMessageRepository;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.stream.binder.test.InputDestination;
import org.springframework.cloud.stream.config.BindingProperties;
import org.springframework.cloud.stream.config.BindingServiceProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@SpringBootTest(classes = CommitmentShipmentEventPipeListenerIT.Config.class)
@AmigaStreamTest
@EnableTestSupportBinder
@ActiveProfiles("test")
@EnableAspectJAutoProxy
class CommitmentShipmentEventPipeListenerIT {

  private static final String REGEX_TOPIC = "iop.global.pre.itbcenv.shipment.public.domain.v4";

  @MockitoBean
  private CommitmentShipmentProcessManager processManager;

  @Autowired
  private CommitmentShipmentMapper commitmentShipmentMapper;

  @MockitoBean
  private ConsumerMessageRepository messageRepository;

  @Autowired
  private BindingServiceProperties bindingServiceProperties;

  @Autowired
  private InputDestination inputDestination;

  @Test
  void should_delegate_shipmentCreatedEvent_to_handler() {
    final var shipmentCreated = Instancio.of(CommitmentsShipmentCreatedV3.class)
        .generate(fields(field -> field.getName().contains("date")),
            generators -> generators.temporal().instant().future().asString())
        .create();
    final var message = this.getMessageFromPayload(shipmentCreated);

    this.sendMessages(message);

    final ShipmentCreated expectedShipmentCreated = new ShipmentCreated(
        shipmentCreated.getId().toString(),
        shipmentCreated.getTransportId().toString(),
        BigDecimal.valueOf(shipmentCreated.getPacking().getQuantity().getValue()),
        shipmentCreated.getPacking().getQuantity().getUnit().toString(),
        OffsetDateTime.parse(shipmentCreated.getSentDate()));
    verify(this.processManager).execute(expectedShipmentCreated);
  }

  @Test
  void should_continue_when_mapping_shipmentCreated_fails() {
    final var shipmentCreated = Instancio.create(CommitmentsShipmentCreatedV3.class);
    final var message = this.getMessageFromPayload(shipmentCreated);

    this.sendMessages(message);

    verifyNoInteractions(this.processManager);
  }

  @Test
  void should_delegate_shipmentModified_to_handler() {
    final var shipmentModified = Instancio.of(CommitmentsPackingModifiedV2.class).create();
    final var message = this.getMessageFromPayload(shipmentModified);

    this.sendMessages(message);

    final ShipmentModified expectedShipmentModified = new ShipmentModified(
        shipmentModified.getId().toString(),
        shipmentModified.getTransportId().toString(),
        BigDecimal.valueOf(shipmentModified.getPacking().getQuantity().getValue()),
        shipmentModified.getPacking().getQuantity().getUnit().toString());
    verify(this.processManager).execute(expectedShipmentModified);
  }

  @Test
  void should_delegate_shipmentCancelled_to_handler() {
    final var shipmentCancelled = Instancio.of(CommitmentsShipmentCancelledV2.class).create();
    final var message = this.getMessageFromPayload(shipmentCancelled);

    this.sendMessages(message);

    final ShipmentCancelled expectedShipmentCancelled = new ShipmentCancelled(
        shipmentCancelled.getId().toString(),
        shipmentCancelled.getTransportId().toString());
    verify(this.processManager).execute(expectedShipmentCancelled);
  }

  private void sendMessages(final Message<?> message) {
    this.bindingServiceProperties.getBindings().values().stream()
        .map(BindingProperties::getDestination)
        .filter(destination -> destination.matches(REGEX_TOPIC))
        .forEach(destination -> this.inputDestination.send(message, destination));
  }

  private Message<ShipmentEnvelopeV4> getMessageFromPayload(final Object payload) {
    final var shipmentEnvelope = new ShipmentEnvelopeV4();
    shipmentEnvelope.setPayload(payload);
    shipmentEnvelope.setMetadata(this.getMetadata());
    return MessageBuilder.withPayload(shipmentEnvelope).build();
  }

  private Metadata getMetadata() {
    return Metadata.newBuilder().setId("id").setName("name").setDomain("iop").setVersion("2").setTimestamp("timestamp")
        .setAction("action").build();
  }

  @Configuration
  @ComponentScan(basePackages = {"com.inditex.icdmdemg.client.pipe.delivery", "com.inditex.icdmdemg.client.mapper",
      "com.inditex.icdmdemg.application.shared.idempotent"})
  public static class Config {

  }
}
