package com.inditex.icdmdemg.client.mapper;

import static com.inditex.icdmdemg.shared.utils.TimeMapperUtils.offsetDateTimeFromString;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.fields;

import java.math.BigDecimal;

import com.inditex.bcsupshipm.event.domain.v4.CommitmentsPackingModifiedV2;
import com.inditex.bcsupshipm.event.domain.v4.CommitmentsShipmentCancelledV2;
import com.inditex.bcsupshipm.event.domain.v4.CommitmentsShipmentCreatedV3;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCancelledCommand.ShipmentCancelled;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCreatedCommand.ShipmentCreated;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentModifiedCommand.ShipmentModified;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.mapstruct.factory.Mappers;

@TestInstance(Lifecycle.PER_CLASS)
class CommitmentShipmentMapperTest {

  private CommitmentShipmentMapper sut;

  @BeforeAll
  void setUp() {
    this.sut = Mappers.getMapper(CommitmentShipmentMapper.class);
  }

  @Test
  void should_map_from_shipmentCreated() {
    final var shipmentCreated = Instancio.of(CommitmentsShipmentCreatedV3.class)
        .generate(fields(field -> field.getName().contains("date")),
            generators -> generators.temporal().instant().future().asString())
        .create();

    final ShipmentCreated result = this.sut.fromShipmentCreated(shipmentCreated);

    assertThat(result).isNotNull();
    assertThat(result.id()).isEqualTo(shipmentCreated.getId());
    assertThat(result.distributionNominatedLineId()).isEqualTo(shipmentCreated.getTransportId());
    assertThat(result.quantity()).isEqualTo(BigDecimal.valueOf(shipmentCreated.getPacking().getQuantity().getValue()));
    assertThat(result.measurementUnitId()).isEqualTo(shipmentCreated.getPacking().getQuantity().getUnit());
    assertThat(result.sentDate()).isEqualTo(offsetDateTimeFromString(shipmentCreated.getSentDate()));
  }

  @Test
  void should_map_from_shipmentModified() {
    final var shipmentModified = Instancio.of(CommitmentsPackingModifiedV2.class).create();

    final ShipmentModified result = this.sut.fromShipmentModified(shipmentModified);

    assertThat(result).isNotNull();
    assertThat(result.id()).isEqualTo(shipmentModified.getId());
    assertThat(result.distributionNominatedLineId()).isEqualTo(shipmentModified.getTransportId());
    assertThat(result.quantity()).isEqualTo(BigDecimal.valueOf(shipmentModified.getPacking().getQuantity().getValue()));
    assertThat(result.measurementUnitId()).isEqualTo(shipmentModified.getPacking().getQuantity().getUnit());
  }

  @Test
  void should_map_from_shipmentCancelled() {
    final var shipmentCancelled = Instancio.of(CommitmentsShipmentCancelledV2.class).create();

    final ShipmentCancelled result = this.sut.fromShipmentCancelled(shipmentCancelled);

    assertThat(result).isNotNull();
    assertThat(result.id()).isEqualTo(shipmentCancelled.getId());
  }
}
