<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.inditex.icdmdemg</groupId>
    <artifactId>icdmdebtmgmt-clients</artifactId>
    <version>1.91.0-SNAPSHOT</version>
  </parent>

  <artifactId>icdmdebtmgmt-pipe-bcsupshipm-supplier-transport-bounded-context-events</artifactId>
  <name>${project.groupId}:${project.artifactId}</name>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <asyncapi.generator.configOptions>avro.imports=common/v3/imports/0_domain_packing_quantity.avsc,common/v3/imports/1_commitments_packing.avsc,common/v3/imports/1_finishings_packing.avsc,reception/v3/imports/1_generic_shipment_info.avsc,reception/v3/imports/2_commitments_reception_cancelled.avsc,reception/v3/imports/2_commitments_reception_created.avsc,reception/v3/imports/2_finishings_in_reception_cancelled.avsc,reception/v3/imports/2_finishings_in_reception_created.avsc,reception/v3/imports/2_finishings_out_reception_cancelled.avsc,reception/v3/imports/2_finishings_out_reception_created.avsc,shipment/v3/imports/2_commitments_arrival_date_modified.avsc,shipment/v3/imports/2_commitments_shipment_cancelled.avsc,shipment/v3/imports/2_commitments_shipment_created.avsc,shipment/v3/imports/2_commitments_packing_modified.avsc,shipment/v3/imports/2_finishings_in_arrival_date_modified.avsc,shipment/v3/imports/2_finishings_in_shipment_cancelled.avsc,shipment/v3/imports/2_finishings_in_packing_modified.avsc,shipment/v3/imports/2_finishings_in_shipment_created.avsc,shipment/v3/imports/2_finishings_out_arrival_date_modified.avsc,shipment/v3/imports/2_finishings_out_packing_modified.avsc,shipment/v3/imports/2_finishings_out_shipment_cancelled.avsc,shipment/v3/imports/2_finishings_out_shipment_created.avsc,common/v4/imports/0_domain_packing_quantity.avsc,common/v4/imports/1_commitments_packing.avsc,common/v4/imports/1_finishings_packing.avsc,common/v4/imports/1_tracking_info.avsc,shipment/v4/imports/2_commitments_arrival_date_modified.avsc,shipment/v4/imports/2_commitments_shipment_cancelled.avsc,shipment/v4/imports/2_commitments_shipment_created.avsc,shipment/v4/imports/2_commitments_packing_modified.avsc,shipment/v4/imports/2_finishings_in_arrival_date_modified.avsc,shipment/v4/imports/2_finishings_in_shipment_cancelled.avsc,shipment/v4/imports/2_finishings_in_packing_modified.avsc,shipment/v4/imports/2_finishings_in_shipment_created.avsc,shipment/v4/imports/2_finishings_out_arrival_date_modified.avsc,shipment/v4/imports/2_finishings_out_packing_modified.avsc,shipment/v4/imports/2_finishings_out_shipment_cancelled.avsc,shipment/v4/imports/2_finishings_out_shipment_created.avsc,shipment/v4/imports/2_samples_shipment_cancelled.avsc,shipment/v4/imports/2_samples_shipment_created.avsc,shipment/v4/imports/2_samples_shipment_modified.avsc</asyncapi.generator.configOptions>
    <avro.imports.resources>${avro.imports.directory}/event/pipe/v1/imports/pipe_definitions.avsc,${avro.imports.directory}/event/common/v3/imports/0_domain_packing_quantity.avsc,${avro.imports.directory}/event/common/v3/imports/1_commitments_packing.avsc,${avro.imports.directory}/event/common/v3/imports/1_finishings_packing.avsc,${avro.imports.directory}/event/reception/v3/imports/1_generic_shipment_info.avsc,${avro.imports.directory}/event/reception/v3/imports/2_commitments_reception_cancelled.avsc,${avro.imports.directory}/event/reception/v3/imports/2_commitments_reception_created.avsc,${avro.imports.directory}/event/reception/v3/imports/2_finishings_in_reception_cancelled.avsc,${avro.imports.directory}/event/reception/v3/imports/2_finishings_in_reception_created.avsc,${avro.imports.directory}/event/reception/v3/imports/2_finishings_out_reception_cancelled.avsc,${avro.imports.directory}/event/reception/v3/imports/2_finishings_out_reception_created.avsc,${avro.imports.directory}/event/shipment/v3/imports/2_commitments_arrival_date_modified.avsc,${avro.imports.directory}/event/shipment/v3/imports/2_commitments_shipment_cancelled.avsc,${avro.imports.directory}/event/shipment/v3/imports/2_commitments_shipment_created.avsc,${avro.imports.directory}/event/shipment/v3/imports/2_commitments_packing_modified.avsc,${avro.imports.directory}/event/shipment/v3/imports/2_finishings_in_arrival_date_modified.avsc,${avro.imports.directory}/event/shipment/v3/imports/2_finishings_in_shipment_cancelled.avsc,${avro.imports.directory}/event/shipment/v3/imports/2_finishings_in_packing_modified.avsc,${avro.imports.directory}/event/shipment/v3/imports/2_finishings_in_shipment_created.avsc,${avro.imports.directory}/event/shipment/v3/imports/2_finishings_out_arrival_date_modified.avsc,${avro.imports.directory}/event/shipment/v3/imports/2_finishings_out_packing_modified.avsc,${avro.imports.directory}/event/shipment/v3/imports/2_finishings_out_shipment_cancelled.avsc,${avro.imports.directory}/event/shipment/v3/imports/2_finishings_out_shipment_created.avsc,${avro.imports.directory}/event/common/v4/imports/0_domain_packing_quantity.avsc,${avro.imports.directory}/event/common/v4/imports/1_commitments_packing.avsc,${avro.imports.directory}/event/common/v4/imports/1_finishings_packing.avsc,${avro.imports.directory}/event/common/v4/imports/1_tracking_info.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_commitments_arrival_date_modified.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_commitments_shipment_cancelled.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_commitments_shipment_created.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_commitments_packing_modified.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_finishings_in_arrival_date_modified.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_finishings_in_shipment_cancelled.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_finishings_in_packing_modified.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_finishings_in_shipment_created.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_finishings_out_arrival_date_modified.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_finishings_out_packing_modified.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_finishings_out_shipment_cancelled.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_finishings_out_shipment_created.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_samples_shipment_cancelled.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_samples_shipment_created.avsc,${avro.imports.directory}/event/shipment/v4/imports/2_samples_shipment_modified.avsc</avro.imports.resources>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.inditex.amigafwk.service</groupId>
      <artifactId>service-starter-stream-pipe</artifactId>
    </dependency>

    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-application</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.api</groupId>
      <artifactId>bcsupshipm-supplier-transport-message-event-stable</artifactId>
      <version>${bcsupshipm-supplier-transport-message-event-stable.version}</version>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.apiasync</groupId>
      <artifactId>lib-asyncapi-tools</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.icdmdemg</groupId>
      <artifactId>icdmdebtmgmt-shared</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.data</groupId>
      <artifactId>data-starter-test-stream-pipe</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.test</groupId>
      <artifactId>test-starter-instancio</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.test</groupId>
      <artifactId>test-starter-logcaptor</artifactId>
      <scope>test</scope>
    </dependency>

  </dependencies>

</project>