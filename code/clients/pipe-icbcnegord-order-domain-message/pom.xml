<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.inditex.icdmdemg</groupId>
    <artifactId>icdmdebtmgmt-clients</artifactId>
    <version>1.91.0-SNAPSHOT</version>
  </parent>

  <artifactId>icdmdebtmgmt-pipe-icbcnegord-order-domain-message</artifactId>
  <name>${project.groupId}:${project.artifactId}</name>

  <properties>
    <avro.imports.resources>${avro.imports.directory}/event/pipe/v1/imports/pipe_definitions.avsc,${avro.imports.directory}/event/v1/imports/0-attributes.avsc,${avro.imports.directory}/event/v1/imports/0-order-operation-enum.avsc,${avro.imports.directory}/event/v1/imports/0-destination.avsc,${avro.imports.directory}/event/v1/imports/0-order-status-key.avsc,${avro.imports.directory}/event/v1/imports/0-value-type.avsc,${avro.imports.directory}/event/v1/imports/0-order-context.avsc,${avro.imports.directory}/event/v1/imports/0-order-deleted-data.avsc,${avro.imports.directory}/event/v1/imports/0-order-status-data.avsc,${avro.imports.directory}/event/v1/imports/1-cost.avsc,${avro.imports.directory}/event/v1/imports/1-cost-shipment-service-line.avsc,${avro.imports.directory}/event/v1/imports/1-draft-product-purchase-condition.avsc,${avro.imports.directory}/event/v1/imports/1-draft-product-quantity-detail.avsc,${avro.imports.directory}/event/v1/imports/1-purchase-condition.avsc,${avro.imports.directory}/event/v1/imports/1-purchase-condition-shipment-service-line.avsc,${avro.imports.directory}/event/v1/imports/1-quantity-detail.avsc,${avro.imports.directory}/event/v1/imports/1-reference-detail.avsc,${avro.imports.directory}/event/v1/imports/1-status.avsc,${avro.imports.directory}/event/v1/imports/2-draft-product-line-information.avsc,${avro.imports.directory}/event/v1/imports/2-finish-service-line-information.avsc,${avro.imports.directory}/event/v1/imports/2-product-line-information.avsc,${avro.imports.directory}/event/v1/imports/2-shipment-service-line-information.avsc,${avro.imports.directory}/event/v1/imports/3-order-line.avsc,${avro.imports.directory}/event/v1/imports/4-order-unified.avsc,${avro.imports.directory}</avro.imports.resources>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.inditex.amigafwk.service</groupId>
      <artifactId>service-starter-stream-pipe</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.icdmdemg</groupId>
      <artifactId>icdmdebtmgmt-shared</artifactId>
    </dependency>

    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-application</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.api</groupId>
      <artifactId>icbcnegord-order-unified-message-event-stable</artifactId>
      <version>${icbcnegord-order-unified-message-event-stable.version}</version>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.apiasync</groupId>
      <artifactId>lib-asyncapi-tools</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.data</groupId>
      <artifactId>data-starter-test-stream</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.test</groupId>
      <artifactId>test-starter-logcaptor</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.test</groupId>
      <artifactId>test-starter-instancio</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

</project>
