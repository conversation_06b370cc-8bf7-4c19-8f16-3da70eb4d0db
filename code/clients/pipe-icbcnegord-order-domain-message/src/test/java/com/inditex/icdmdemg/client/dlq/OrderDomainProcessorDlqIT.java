package com.inditex.icdmdemg.client.dlq;

import static org.mockito.Mockito.doReturn;

import com.inditex.amigafwk.test.logcaptor.LogCaptor;
import com.inditex.amigafwk.test.logcaptor.LogCaptorExtension;
import com.inditex.icdmnegord.order.event.unified.v1.OrderUnified;
import com.inditex.icdmnegord.order.event.unified.v1.OrderUnifiedEnvelopeV1;

import ch.qos.logback.classic.Level;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;

@ExtendWith({MockitoExtension.class, LogCaptorExtension.class})
class OrderDomainProcessorDlqIT {

  private final LogCaptor logCaptor;

  private final OrderDomainProcessorDlq sut = new OrderDomainProcessorDlq();

  OrderDomainProcessorDlqIT(final LogCaptor logCaptor) {
    this.logCaptor = logCaptor;
  }

  @Test
  void should_log_the_payload(@Mock final Message<OrderUnifiedEnvelopeV1> message) {
    final var payload = Instancio.create(OrderUnifiedEnvelopeV1.class);
    payload.setPayload(Instancio.create(OrderUnified.class));

    doReturn(payload).when(message).getPayload();

    this.sut.process(message);

    this.logCaptor.verifyMessagesContaining(OrderDomainProcessorDlq.LOG_CONTEXT + payload)
        .withLevel(Level.INFO)
        .areExactly(1);
  }

}
