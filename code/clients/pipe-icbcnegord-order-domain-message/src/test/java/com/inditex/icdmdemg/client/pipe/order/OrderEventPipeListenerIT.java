package com.inditex.icdmdemg.client.pipe.order;

import static com.inditex.icdmdemg.client.pipe.order.OrderEventPipeListener.OrderEventType.ORDER_CREATED_EVENT;
import static com.inditex.icdmdemg.client.pipe.order.OrderEventPipeListener.OrderEventType.ORDER_DELETED_EVENT;
import static com.inditex.icdmdemg.client.pipe.order.OrderEventPipeListener.OrderEventType.ORDER_PUBLISHED_EVENT;
import static com.inditex.icdmdemg.client.pipe.order.OrderEventPipeListener.OrderEventType.ORDER_STATUS_UPDATED_EVENT;
import static com.inditex.icdmdemg.client.pipe.order.OrderEventPipeListener.OrderEventType.ORDER_UPDATED_EVENT;

import static org.instancio.Select.field;
import static org.instancio.Select.fields;
import static org.mockito.Mockito.verify;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;

import com.inditex.amigafwk.data.test.stream.AmigaStreamTest;
import com.inditex.amigafwk.data.test.stream.EnableTestSupportBinder;
import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderCreatedOrUpdated;
import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderLine;
import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderQuantityDetail;
import com.inditex.icdmdemg.application.order.command.ProjectOrderDeletedCommand.ProductOrderDeleted;
import com.inditex.icdmdemg.application.order.command.PublishedOrderCommand.PublishedOrder;
import com.inditex.icdmdemg.application.order.command.UpdateOrderStatusCommand.StatusOrderUpdated;
import com.inditex.icdmdemg.application.process.OrderProcessManager;
import com.inditex.icdmdemg.client.mapper.OrderMapper;
import com.inditex.icdmnegord.order.event.unified.v1.OrderLine;
import com.inditex.icdmnegord.order.event.unified.v1.OrderLineTypeEnum;
import com.inditex.icdmnegord.order.event.unified.v1.OrderUnified;
import com.inditex.icdmnegord.order.event.unified.v1.OrderUnifiedEnvelopeV1;
import com.inditex.icdmnegord.order.event.unified.v1.ProductLineInfo;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.stream.binder.test.InputDestination;
import org.springframework.cloud.stream.config.BindingProperties;
import org.springframework.cloud.stream.config.BindingServiceProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@SpringBootTest(classes = OrderEventPipeListenerIT.Config.class)
@AmigaStreamTest
@EnableTestSupportBinder
@ActiveProfiles("test")
class OrderEventPipeListenerIT {

  private static final String REGEX_TOPIC = "iop.global.pre.icdmnegord.order.public.unified.v1";

  @MockitoBean
  private OrderProcessManager processManager;

  @Autowired
  private OrderMapper orderMapper;

  @Autowired
  private BindingServiceProperties bindingServiceProperties;

  @Autowired
  private InputDestination inputDestination;

  @Test
  void should_delegate_OrderCreatedEvent_to_handler() {
    final var orderCreated = Instancio.of(OrderUnified.class)
        .set(field(OrderUnified.class, "event"), ORDER_CREATED_EVENT.getEventName())
        .set(field(OrderLine.class, "type"), OrderLineTypeEnum.PRODUCT_LINE)
        .set(field(OrderLine.class, "line_info"), this.getRandomProductLineInfo())
        .create();
    final var orderDomainEnvelope = this.getNewOrderUnifiedEnvelope();
    orderDomainEnvelope.setPayload(orderCreated);
    final var message = MessageBuilder.withPayload(orderDomainEnvelope).build();

    this.sendMessages(message);

    final ProductOrderCreatedOrUpdated order = new ProductOrderCreatedOrUpdated(
        orderCreated.getEntityId().toString(),
        orderCreated.getStatus().getStatusKey().name(),
        orderCreated.getSupplier().toString(),
        this.getExpectedProductOrderLines(orderCreated.getOrderLines()));
    verify(this.processManager).executeCreate(order);
  }

  @Test
  void should_delegate_OrderUpdatedEvent_to_handler() {
    final var orderUpdated = Instancio.of(OrderUnified.class)
        .set(field(OrderUnified.class, "event"), ORDER_UPDATED_EVENT.getEventName())
        .set(field(OrderLine.class, "type"), OrderLineTypeEnum.PRODUCT_LINE)
        .set(field(OrderLine.class, "line_info"), this.getRandomProductLineInfo())
        .create();
    final var envelope = this.getNewOrderUnifiedEnvelope();
    envelope.setPayload(orderUpdated);
    final var message = MessageBuilder.withPayload(envelope).build();

    this.sendMessages(message);

    final ProductOrderCreatedOrUpdated order = new ProductOrderCreatedOrUpdated(
        orderUpdated.getEntityId().toString(),
        orderUpdated.getStatus().getStatusKey().name(),
        orderUpdated.getSupplier().toString(),
        this.getExpectedProductOrderLines(orderUpdated.getOrderLines()));
    verify(this.processManager).executeUpdate(order);
  }

  @Test
  void should_delegate_OrderDeletedEvent_to_handler() {
    final var orderDeleted = Instancio.of(OrderUnified.class)
        .set(field(OrderUnified.class, "event"), ORDER_DELETED_EVENT.getEventName())
        .create();
    final var orderDomainEnvelope = this.getNewOrderUnifiedEnvelope();
    orderDomainEnvelope.setPayload(orderDeleted);
    final var message = MessageBuilder.withPayload(orderDomainEnvelope).build();

    this.sendMessages(message);

    final ProductOrderDeleted order = new ProductOrderDeleted(orderDeleted.getEntityId().toString());
    verify(this.processManager).execute(order);
  }

  @Test
  void should_delegate_OrderStatusUpdated_to_handler() {
    final var orderStatusUpdated = Instancio.of(OrderUnified.class)
        .set(field(OrderUnified.class, "event"), ORDER_STATUS_UPDATED_EVENT.getEventName())
        .create();
    final var orderDomainEnvelope = this.getNewOrderUnifiedEnvelope();
    orderDomainEnvelope.setPayload(orderStatusUpdated);
    final var message = MessageBuilder.withPayload(orderDomainEnvelope).build();

    this.sendMessages(message);

    final StatusOrderUpdated statusOrderUpdated = new StatusOrderUpdated(
        orderStatusUpdated.getEntityId().toString(),
        orderStatusUpdated.getStatus().getStatusKey().name());
    verify(this.processManager).execute(statusOrderUpdated);
  }

  @Test
  void should_delegate_OrderPublished_to_handler() {
    final var orderPublished = Instancio.of(OrderUnified.class)
        .set(field(OrderUnified.class, "event"), ORDER_PUBLISHED_EVENT.getEventName())
        .create();
    final var orderDomainEnvelope = this.getNewOrderUnifiedEnvelope();
    orderDomainEnvelope.setPayload(orderPublished);
    final var message = MessageBuilder.withPayload(orderDomainEnvelope).build();

    this.sendMessages(message);

    final PublishedOrder publishedOrder = new PublishedOrder(orderPublished.getEntityId().toString());
    verify(this.processManager).execute(publishedOrder);
  }

  private ProductLineInfo getRandomProductLineInfo() {
    return Instancio.of(ProductLineInfo.class)
        .generate(fields(field -> field.getName().contains("date")),
            generators -> generators.temporal().offsetDateTime().future().asString())
        .create();
  }

  private List<ProductOrderLine> getExpectedProductOrderLines(final List<OrderLine> orderLines) {
    return orderLines.stream().map(orderLine -> {

      final ProductLineInfo productLineInfo = (ProductLineInfo) orderLine.getLineInfo();

      final List<ProductOrderQuantityDetail> actualQuantityDetails = productLineInfo.getQuantityDetails().stream()
          .map(quantityDetail -> new ProductOrderQuantityDetail(
              quantityDetail.getQuantity(),
              quantityDetail.getReferenceId().toString()))
          .toList();

      return new ProductOrderLine(
          orderLine.getLineId().toString(),
          productLineInfo.measuring_units_id.toString(),
          OffsetDateTime.of(LocalDateTime.parse(productLineInfo.getServiceDate(), DateTimeFormatter.ISO_OFFSET_DATE_TIME),
              ZoneOffset.UTC),
          orderLine.getBudgetCycle().toString(),
          actualQuantityDetails);

    }).toList();
  }

  private void sendMessages(final Message<?> message) {
    this.bindingServiceProperties.getBindings().values().stream()
        .map(BindingProperties::getDestination)
        .filter(destination -> destination.matches(REGEX_TOPIC))
        .forEach(destination -> this.inputDestination.send(message, destination));
  }

  private OrderUnifiedEnvelopeV1 getNewOrderUnifiedEnvelope() {
    return new OrderUnifiedEnvelopeV1();
  }

  @Configuration
  @ComponentScan(basePackages = {"com.inditex.icdmdemg.client.pipe.order", "com.inditex.icdmdemg.client.mapper"})
  public static class Config {

  }
}
