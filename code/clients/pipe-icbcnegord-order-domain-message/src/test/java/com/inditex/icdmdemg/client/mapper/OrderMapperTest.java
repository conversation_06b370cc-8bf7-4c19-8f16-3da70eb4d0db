package com.inditex.icdmdemg.client.mapper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.instancio.Select.fields;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderCreatedOrUpdated;
import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderLine;
import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderQuantityDetail;
import com.inditex.icdmdemg.application.order.command.ProjectOrderDeletedCommand.ProductOrderDeleted;
import com.inditex.icdmdemg.shared.utils.RandomValue;
import com.inditex.icdmnegord.order.event.unified.v1.OrderLine;
import com.inditex.icdmnegord.order.event.unified.v1.OrderLineTypeEnum;
import com.inditex.icdmnegord.order.event.unified.v1.OrderUnified;
import com.inditex.icdmnegord.order.event.unified.v1.ProductLineInfo;
import com.inditex.icdmnegord.order.event.unified.v1.PurchaseCondition;
import com.inditex.icdmnegord.order.event.unified.v1.QuantityDetail;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class OrderMapperTest {

  private OrderMapper sut;

  @BeforeAll
  void setUp() {
    this.sut = new OrderMapper();
  }

  @Test
  void should_map_from_orderCreated() {
    final var orderCreated = Instancio.of(OrderUnified.class)
        .set(field(OrderLine.class, "type"), OrderLineTypeEnum.PRODUCT_LINE)
        .set(field(OrderLine.class, "line_info"),
            getRandomProductLineInfo())
        .create();

    final ProductOrderCreatedOrUpdated result = this.sut.fromOrderCreated(orderCreated);

    final var expectedProductOrderLines = this.getExpectedProductOrderLines(orderCreated.getOrderLines());

    assertThat(result.orderLines()).isNotNull();
    assertThat(orderCreated.getEntityId()).hasToString(result.id());
    assertThat(orderCreated.getOrderLines()).hasSize(result.orderLines().size());
    assertThat(result.orderLines())
        .usingRecursiveComparison()
        .ignoringCollectionOrder()
        .isEqualTo(expectedProductOrderLines);
  }

  @Test
  void should_map_from_orderDeleted() {
    final var orderDeleted = Instancio.create(OrderUnified.class);

    final ProductOrderDeleted result = this.sut.fromOrderDeleted(orderDeleted);

    assertThat(result)
        .isNotNull()
        .returns(orderDeleted.getEntityId().toString(), ProductOrderDeleted::id);
  }

  @Test
  void should_map_from_orderUpdated() {
    final var orderUpdated = Instancio.of(OrderUnified.class)
        .set(field(OrderLine.class, "type"), OrderLineTypeEnum.PRODUCT_LINE)
        .set(field(OrderLine.class, "line_info"),
            getRandomProductLineInfo())
        .create();

    final ProductOrderCreatedOrUpdated result = this.sut.fromOrderUpdated(orderUpdated);

    final var expectedProductOrderLines = this.getExpectedProductOrderLines(orderUpdated.getOrderLines());

    assertThat(result.orderLines()).isNotNull();
    assertThat(orderUpdated.getEntityId()).hasToString(result.id());
    assertThat(orderUpdated.getOrderLines()).hasSameSizeAs(result.orderLines());
    assertThat(result.orderLines())
        .usingRecursiveComparison()
        .ignoringCollectionOrder()
        .isEqualTo(expectedProductOrderLines);
  }

  @Test
  void should_filter_invalid_product_order_line() {
    final CharSequence nullServiceDate = null;
    final var orderCreated = Instancio.of(OrderUnified.class)
        .set(field(OrderLine.class, "type"), OrderLineTypeEnum.PRODUCT_LINE)
        .set(field(OrderLine.class, "line_info"),
            new ProductLineInfo(new PurchaseCondition(), OffsetDateTime.MIN.toString(), nullServiceDate,
                OffsetDateTime.MIN.toString(), UUID.randomUUID(),
                List.of(new QuantityDetail(RandomValue.randomPositiveInteger(), UUID.randomUUID())), null))
        .create();

    final ProductOrderCreatedOrUpdated result = this.sut.fromOrderCreated(orderCreated);

    assertThat(result.orderLines()).isEmpty();
  }

  private static ProductLineInfo getRandomProductLineInfo() {
    return Instancio.of(ProductLineInfo.class)
        .generate(fields(field -> field.getName().contains("date")),
            generators -> generators.temporal().offsetDateTime().future().asString())
        .create();
  }

  private List<ProductOrderLine> getExpectedProductOrderLines(final List<OrderLine> orderLines) {
    return orderLines.stream().map(orderLine -> {

      final ProductLineInfo productLineInfo = (ProductLineInfo) orderLine.getLineInfo();

      final List<ProductOrderQuantityDetail> actualQuantityDetails = productLineInfo.getQuantityDetails().stream()
          .map(quantityDetail -> new ProductOrderQuantityDetail(
              quantityDetail.getQuantity(),
              quantityDetail.getReferenceId().toString()))
          .toList();

      return new ProductOrderLine(
          orderLine.getLineId().toString(),
          productLineInfo.measuring_units_id.toString(),
          OffsetDateTime.of(LocalDateTime.parse(productLineInfo.getServiceDate(), DateTimeFormatter.ISO_OFFSET_DATE_TIME),
              ZoneOffset.UTC),
          orderLine.getBudgetCycle().toString(),
          actualQuantityDetails);

    }).toList();
  }

}
