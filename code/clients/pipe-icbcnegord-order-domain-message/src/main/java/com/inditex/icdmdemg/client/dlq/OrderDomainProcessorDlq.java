package com.inditex.icdmdemg.client.dlq;

import com.inditex.amigafwk.data.stream.dlqprocessing.DlqProcessorStrategy;
import com.inditex.icdmnegord.order.event.unified.v1.OrderUnifiedEnvelopeV1;

import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;

@Slf4j
public class OrderDomainProcessorDlq implements DlqProcessorStrategy<OrderUnifiedEnvelopeV1> {
  public static final String LOG_CONTEXT = "DLQ Processed: ";

  @Override
  public void process(final Message<OrderUnifiedEnvelopeV1> message) {
    log.info(LOG_CONTEXT.concat(message.getPayload().toString()));
  }
}
