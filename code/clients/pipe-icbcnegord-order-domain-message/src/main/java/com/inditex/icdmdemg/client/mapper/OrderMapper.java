package com.inditex.icdmdemg.client.mapper;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderCreatedOrUpdated;
import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderLine;
import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderQuantityDetail;
import com.inditex.icdmdemg.application.order.command.ProjectOrderDeletedCommand.ProductOrderDeleted;
import com.inditex.icdmdemg.application.order.command.PublishedOrderCommand.PublishedOrder;
import com.inditex.icdmdemg.application.order.command.UpdateOrderStatusCommand.StatusOrderUpdated;
import com.inditex.icdmnegord.order.event.unified.v1.OrderLine;
import com.inditex.icdmnegord.order.event.unified.v1.OrderLineTypeEnum;
import com.inditex.icdmnegord.order.event.unified.v1.OrderUnified;
import com.inditex.icdmnegord.order.event.unified.v1.ProductLineInfo;
import com.inditex.icdmnegord.order.event.unified.v1.QuantityDetail;

import org.springframework.stereotype.Component;

@Component
public class OrderMapper {

  public ProductOrderCreatedOrUpdated fromOrderCreated(final OrderUnified orderCreated) {
    return new ProductOrderCreatedOrUpdated(
        orderCreated.getEntityId().toString(),
        orderCreated.getStatus().getStatusKey().name(),
        orderCreated.getSupplier().toString(),
        this.fromOrderLine(orderCreated.getOrderLines()));

  }

  private List<ProductOrderLine> fromOrderLine(final List<OrderLine> orderLines) {
    return Optional.ofNullable(orderLines)
        .map(lines -> lines.stream()
            .filter(this::isValidOrderLine)
            .filter(this::isValidProductLineInfo)
            .map(this::fromOrderLineInfo)
            .toList())
        .orElse(List.of());
  }

  private boolean isValidOrderLine(final OrderLine orderLine) {
    return orderLine.getLineInfo() != null && OrderLineTypeEnum.PRODUCT_LINE.equals(orderLine.getType());
  }

  private List<ProductOrderQuantityDetail> fromQuantityDetails(final List<QuantityDetail> quantityDetails) {
    return Optional.ofNullable(quantityDetails)
        .map(details -> details.stream()
            .map(quantityDetail -> new ProductOrderQuantityDetail(quantityDetail.getQuantity(),
                quantityDetail.getReferenceId().toString()))
            .toList())
        .orElse(List.of());
  }

  private OffsetDateTime mapToOffsetDateTime(final CharSequence charSequence) {
    return charSequence == null ? null
        : OffsetDateTime.of(LocalDateTime.parse(charSequence, DateTimeFormatter.ISO_OFFSET_DATE_TIME), ZoneOffset.UTC);
  }

  private ProductOrderLine fromOrderLineInfo(final OrderLine orderLine) {
    final ProductLineInfo productLineInfo = (ProductLineInfo) orderLine.getLineInfo();
    final List<ProductOrderQuantityDetail> quantityDetails = this.fromQuantityDetails(productLineInfo.getQuantityDetails());
    return new ProductOrderLine(
        orderLine.getLineId().toString(),
        acceptNullElseMap(productLineInfo.getMeasuringUnitsId(), UUID::toString),
        this.mapToOffsetDateTime(productLineInfo.getServiceDate()),
        acceptNullElseMap(orderLine.getBudgetCycle(), CharSequence::toString),
        quantityDetails);
  }

  public ProductOrderCreatedOrUpdated fromOrderUpdated(final OrderUnified orderUpdated) {
    return new ProductOrderCreatedOrUpdated(
        orderUpdated.getEntityId().toString(),
        orderUpdated.getStatus().getStatusKey().name(),
        orderUpdated.getSupplier().toString(),
        this.fromOrderLine(orderUpdated.getOrderLines()));
  }

  private boolean isValidProductLineInfo(final OrderLine orderLine) {
    final ProductLineInfo productLineInfo = (ProductLineInfo) orderLine.getLineInfo();
    return !Objects.isNull(productLineInfo.service_date);
  }

  public ProductOrderDeleted fromOrderDeleted(final OrderUnified orderDeleted) {
    return new ProductOrderDeleted(orderDeleted.getEntityId().toString());
  }

  public StatusOrderUpdated fromOrderStatusUpdated(final OrderUnified orderStatusUpdated) {
    return new StatusOrderUpdated(orderStatusUpdated.getEntityId().toString(), orderStatusUpdated.getStatus().getStatusKey().name());
  }

  public PublishedOrder fromOrderStatusPublished(final OrderUnified orderPublished) {
    return new PublishedOrder(orderPublished.getEntityId().toString());
  }
}
