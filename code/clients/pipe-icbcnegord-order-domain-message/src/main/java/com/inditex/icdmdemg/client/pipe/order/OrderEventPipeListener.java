package com.inditex.icdmdemg.client.pipe.order;

import java.util.Arrays;
import java.util.Optional;
import java.util.function.Consumer;

import com.inditex.icdmdemg.application.process.OrderProcessManager;
import com.inditex.icdmdemg.client.mapper.OrderMapper;
import com.inditex.icdmdemg.shared.aop.LogPipeInput;
import com.inditex.icdmnegord.order.event.unified.v1.OrderUnified;
import com.inditex.icdmnegord.order.event.unified.v1.OrderUnifiedEnvelopeV1;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NonNull;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component("order-sent-event-v1")
@LogPipeInput
public class OrderEventPipeListener implements Consumer<Message<OrderUnifiedEnvelopeV1>> {

  private final OrderProcessManager processManager;

  private final OrderMapper orderMapper;

  @Override
  public void accept(final Message<OrderUnifiedEnvelopeV1> message) {
    final var payload = message.getPayload().getPayload();
    if (payload instanceof OrderUnified orderUnified) {
      this.consume(orderUnified);
    }
  }

  private void consume(final OrderUnified orderUnified) {
    OrderEventType.fromValue(orderUnified.getEvent())
        .ifPresent(event -> {
          switch (event) {
            case ORDER_CREATED_EVENT -> this.consumeCreated(orderUnified);
            case ORDER_UPDATED_EVENT -> this.consumeUpdated(orderUnified);
            case ORDER_DELETED_EVENT -> this.consumeDeleted(orderUnified);
            case ORDER_STATUS_UPDATED_EVENT -> this.consumeStatusUpdated(orderUnified);
            case ORDER_PUBLISHED_EVENT -> this.consumePublished(orderUnified);
            default -> {
              // do nothing
            }
          }
        });
  }

  private void consumePublished(final OrderUnified orderPublished) {
    final var publishedOrder = this.orderMapper.fromOrderStatusPublished(orderPublished);
    this.processManager.execute(publishedOrder);
  }

  private void consumeStatusUpdated(final OrderUnified orderStatusUpdated) {
    final var statusUpdated = this.orderMapper.fromOrderStatusUpdated(orderStatusUpdated);
    this.processManager.execute(statusUpdated);
  }

  private void consumeDeleted(final OrderUnified orderDeleted) {
    final var order = this.orderMapper.fromOrderDeleted(orderDeleted);
    this.processManager.execute(order);
  }

  private void consumeUpdated(final OrderUnified orderUpdated) {
    if (orderUpdated.getSupplier() == null) {
      return;
    }
    final var order = this.orderMapper.fromOrderUpdated(orderUpdated);
    this.processManager.executeUpdate(order);
  }

  private void consumeCreated(final OrderUnified orderCreated) {
    if (orderCreated.getSupplier() == null) {
      return;
    }
    final var order = this.orderMapper.fromOrderCreated(orderCreated);
    this.processManager.executeCreate(order);
  }

  @Getter
  public enum OrderEventType {

    ORDER_CREATED_EVENT("OrderCreated"),

    ORDER_UPDATED_EVENT("OrderUpdated"),

    ORDER_STATUS_UPDATED_EVENT("OrderStatusUpdated"),

    ORDER_PUBLISHED_EVENT("OrderPublished"),

    ORDER_DELETED_EVENT("OrderDeleted");

    private final String eventName;

    OrderEventType(final String name) {
      this.eventName = name;
    }

    public static Optional<OrderEventType> fromValue(@NonNull final CharSequence value) {
      return Arrays.stream(OrderEventType.values())
          .filter(eventType -> eventType.eventName.equals(value.toString()))
          .findFirst();
    }

  }
}
