<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.inditex.icdmdemg</groupId>
    <artifactId>icdmdebtmgmt-clients</artifactId>
    <version>1.91.0-SNAPSHOT</version>
  </parent>

  <artifactId>icdmdebtmgmt-rest-icmpurcent</artifactId>
  <name>${project.groupId}:${project.artifactId}</name>

  <properties>
    <amiga.common.rest.generator.api.package>${project.groupId}.icmpurcent.rest.client.api</amiga.common.rest.generator.api.package>
    <amiga.common.rest.generator.model.package>${project.groupId}.icmpurcent.rest.client.dto</amiga.common.rest.generator.model.package>
    <amiga.common.rest.generator.invoker.package>${project.groupId}.icmpurcent.rest.client.invoker</amiga.common.rest.generator.invoker.package>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-starter-rest-client</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.api</groupId>
      <artifactId>icbcpurent-purchase-categories-rest-stable</artifactId>
      <version>${icmpurcent-purchase-categories-rest-stable.version}</version>
    </dependency>
  </dependencies>

</project>
