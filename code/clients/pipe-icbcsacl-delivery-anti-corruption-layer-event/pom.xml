<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.inditex.icdmdemg</groupId>
    <artifactId>icdmdebtmgmt-clients</artifactId>
    <version>1.91.0-SNAPSHOT</version>
  </parent>

  <artifactId>icdmdebtmgmt-pipe-icbcsacl-delivery-anti-corruption-layer-event</artifactId>
  <name>${project.groupId}:${project.artifactId}</name>

  <properties>
    <amiga.data.event.pipe.generator.api.package>${project.groupId}.icbcsacl.icacsiga</amiga.data.event.pipe.generator.api.package>
    <amiga.service.event.pipe.generator.api.definition>asyncapi.yml</amiga.service.event.pipe.generator.api.definition>
    <avro.imports.excludes>v1/**/*.avsc</avro.imports.excludes>
    <avro.sources.excludes>**/v1/**/*.avsc</avro.sources.excludes>
    <avro.imports.resources>${avro.imports.directory}/event/pipe/v1/imports/pipe_definitions.avsc,${avro.imports.directory}/event/processupdates/v3/imports/001-distribution_inner.avsc,${avro.imports.directory}/event/processupdates/v3/imports/process_completed.avsc,${avro.imports.directory}/event/processupdates/v3/imports/process_created.avsc,${avro.imports.directory}/event/processupdates/v3/imports/process_started.avsc,${avro.imports.directory}/event/processupdates/v3/imports/process_updated.avsc,${avro.imports.directory}</avro.imports.resources>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.inditex.amigafwk.service</groupId>
      <artifactId>service-starter-stream-pipe</artifactId>
    </dependency>

    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-application</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.api</groupId>
      <artifactId>icbcsacl-event-siga-anti-corruption-layer-event-stable</artifactId>
      <version>${icbcsacl-event-siga-anti-corruption-layer-event-stable.version}</version>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.apiasync</groupId>
      <artifactId>lib-asyncapi-tools</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.icdmdemg</groupId>
      <artifactId>icdmdebtmgmt-shared</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.data</groupId>
      <artifactId>data-starter-test-stream-pipe</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.test</groupId>
      <artifactId>test-starter-instancio</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.test</groupId>
      <artifactId>test-starter-logcaptor</artifactId>
      <scope>test</scope>
    </dependency>

  </dependencies>

</project>
