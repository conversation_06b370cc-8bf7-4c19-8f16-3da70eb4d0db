package com.inditex.icdmdemg.client.pipe.delivery;

import java.util.function.Consumer;

import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessCompleted;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessCreated;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessStarted;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessUpdated;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessUpdatesEnvelopeV3;
import com.inditex.icdmdemg.application.process.ShipmentWarehouseProcessManager;
import com.inditex.icdmdemg.client.mapper.ShipmentWarehouseMapper;
import com.inditex.icdmdemg.shared.aop.LogPipeInput;

import lombok.RequiredArgsConstructor;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component("process-updates-response-global")
@LogPipeInput
public class ShipmentWarehouseEventPipeListener implements Consumer<Message<ProcessUpdatesEnvelopeV3>> {

  private final ShipmentWarehouseMapper shipmentWarehouseMapper;

  private final ShipmentWarehouseProcessManager shipmentWarehouseProcessManager;

  @Override
  public void accept(final Message<ProcessUpdatesEnvelopeV3> processUpdatesEnvelopeMessage) {
    final Object payload = processUpdatesEnvelopeMessage.getPayload().getPayload();

    switch (payload) {
      case final ProcessCreated processCreated ->
        this.shipmentWarehouseProcessManager.execute(this.shipmentWarehouseMapper.from(processCreated));
      case final ProcessStarted processStarted ->
        this.shipmentWarehouseProcessManager.execute(this.shipmentWarehouseMapper.from(processStarted));
      case final ProcessUpdated processUpdated ->
        this.shipmentWarehouseProcessManager.execute(this.shipmentWarehouseMapper.from(processUpdated));
      case final ProcessCompleted processCompleted ->
        this.shipmentWarehouseProcessManager.execute(this.shipmentWarehouseMapper.from(processCompleted));
      default -> {
        // Empty default for events we dont process
      }
    }
  }

}
