package com.inditex.icdmdemg.client.mapper;

import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessCompleted;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessCreated;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessStarted;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessUpdated;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCompletedCommand.ShipmentWarehouseCompleted;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCreatedCommand.ShipmentWarehouseCreated;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseStartedCommand.ShipmentWarehouseStarted;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseUpdatedCommand.ShipmentWarehouseUpdated;
import com.inditex.icdmdemg.shared.utils.CharSequenceMapper;

import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(uses = {CharSequenceMapper.class}, injectionStrategy = InjectionStrategy.CONSTRUCTOR)
public interface ShipmentWarehouseMapper {
  @Mapping(target = "distributionInnerId", source = "distributionInner.orderId")
  @Mapping(target = "distributionInnerLineId", source = "distributionInner.orderLineId")
  @Mapping(target = "trackingCode", source = "distributionTrackingCode")
  ShipmentWarehouseCreated from(ProcessCreated processCreated);

  @Mapping(target = "quantity", source = "globalDistributedQuantity")
  ShipmentWarehouseUpdated from(ProcessUpdated processUpdated);

  ShipmentWarehouseStarted from(ProcessStarted processStarted);

  ShipmentWarehouseCompleted from(ProcessCompleted processCompleted);
}
