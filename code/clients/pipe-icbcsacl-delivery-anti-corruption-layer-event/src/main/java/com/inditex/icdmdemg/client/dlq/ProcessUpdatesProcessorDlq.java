package com.inditex.icdmdemg.client.dlq;

import com.inditex.amigafwk.data.stream.dlqprocessing.DlqProcessorStrategy;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessUpdatesEnvelopeV3;

import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.Message;

@Slf4j
public class ProcessUpdatesProcessorDlq implements DlqProcessorStrategy<ProcessUpdatesEnvelopeV3> {
  public static final String LOG_CONTEXT = "DLQ Processed: ";

  @Override
  public void process(final Message<ProcessUpdatesEnvelopeV3> message) {
    log.info(LOG_CONTEXT.concat(message.getPayload().toString()));
  }
}
