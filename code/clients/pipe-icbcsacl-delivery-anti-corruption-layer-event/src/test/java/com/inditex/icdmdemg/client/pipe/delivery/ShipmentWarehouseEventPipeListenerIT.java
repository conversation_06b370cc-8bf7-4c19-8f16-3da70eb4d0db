package com.inditex.icdmdemg.client.pipe.delivery;

import static org.instancio.Select.fields;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import com.inditex.amigafwk.data.test.stream.AmigaStreamTest;
import com.inditex.amigafwk.data.test.stream.EnableTestSupportBinder;
import com.inditex.aqsw.pipe.v1.Metadata;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessCompleted;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessCreated;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessStarted;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessUpdated;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessUpdatesEnvelopeV3;
import com.inditex.icdmdemg.application.process.ShipmentWarehouseProcessManager;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCompletedCommand.ShipmentWarehouseCompleted;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCreatedCommand.ShipmentWarehouseCreated;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseStartedCommand.ShipmentWarehouseStarted;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseUpdatedCommand.ShipmentWarehouseUpdated;
import com.inditex.icdmdemg.client.mapper.ShipmentWarehouseMapper;
import com.inditex.icdmdemg.domain.consumermessage.ConsumerMessageRepository;
import com.inditex.icdmdemg.domain.consumermessage.DuplicatedMessageException;
import com.inditex.icdmdemg.shared.utils.RandomValue;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cloud.stream.binder.test.InputDestination;
import org.springframework.cloud.stream.config.BindingProperties;
import org.springframework.cloud.stream.config.BindingServiceProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@SpringBootTest(classes = ShipmentWarehouseEventPipeListenerIT.Config.class)
@AmigaStreamTest
@EnableTestSupportBinder
@ActiveProfiles("test")
@EnableAspectJAutoProxy
class ShipmentWarehouseEventPipeListenerIT {

  private static final String REGEX_TOPIC = "comercial.global.pre.icacsiga.processupdates.public.load.v3";

  @Autowired
  private ShipmentWarehouseMapper shipmentWarehouseMapper;

  @MockitoBean
  private ShipmentWarehouseProcessManager shipmentWarehouseProcessManager;

  @MockitoBean
  private ConsumerMessageRepository messageRepository;

  @Autowired
  private BindingServiceProperties bindingServiceProperties;

  @Autowired
  private InputDestination inputDestination;

  @Test
  void should_do_nothing_with_unexpected_payload() throws DuplicatedMessageException {
    final var payload = RandomValue.randomString();
    final var message = this.getMessageFromPayload(payload);

    this.sendMessages(message);

    verifyNoInteractions(this.shipmentWarehouseProcessManager);
  }

  @Test
  void should_delegate_ProcessCreatedEvent_to_handler() throws DuplicatedMessageException {
    final var processCreated = Instancio.create(ProcessCreated.class);
    final var message = this.getMessageFromPayload(processCreated);

    this.sendMessages(message);

    verify(this.shipmentWarehouseProcessManager).execute(any(ShipmentWarehouseCreated.class));
  }

  @Test
  void should_delegate_ProcessUpdated_to_handler() throws DuplicatedMessageException {
    final var processUpdated = Instancio.of(ProcessUpdated.class)
        .generate(fields(field -> field.getName().contains("date")),
            generators -> generators.temporal().instant().future().asString())
        .create();
    final var message = this.getMessageFromPayload(processUpdated);

    this.sendMessages(message);

    verify(this.shipmentWarehouseProcessManager).execute(any(ShipmentWarehouseUpdated.class));
  }

  @Test
  void should_delegate_ProcessCompleted_to_handler() throws DuplicatedMessageException {
    final var processUpdated = Instancio.of(ProcessCompleted.class)
        .generate(fields(field -> field.getName().contains("date")),
            generators -> generators.temporal().instant().future().asString())
        .create();
    final var message = this.getMessageFromPayload(processUpdated);

    this.sendMessages(message);

    verify(this.shipmentWarehouseProcessManager).execute(any(ShipmentWarehouseCompleted.class));
  }

  @Test
  void should_delegate_ProcessStarted_to_handler() throws DuplicatedMessageException {
    final var processStarted = Instancio.of(ProcessStarted.class)
        .generate(fields(field -> field.getName().contains("date")),
            generators -> generators.temporal().instant().future().asString())
        .create();
    final var message = this.getMessageFromPayload(processStarted);

    this.sendMessages(message);

    verify(this.shipmentWarehouseProcessManager).execute(any(ShipmentWarehouseStarted.class));
  }

  private void sendMessages(final Message<?> message) {
    this.bindingServiceProperties.getBindings().values().stream()
        .map(BindingProperties::getDestination)
        .filter(destination -> destination.matches(REGEX_TOPIC))
        .forEach(destination -> this.inputDestination.send(message, destination));
  }

  private Message<ProcessUpdatesEnvelopeV3> getMessageFromPayload(final Object payload) {
    final var processUpdatesEnvelope = new ProcessUpdatesEnvelopeV3();
    processUpdatesEnvelope.setPayload(payload);
    processUpdatesEnvelope.setMetadata(this.getMetadata());
    return MessageBuilder.withPayload(processUpdatesEnvelope).build();
  }

  private Metadata getMetadata() {
    return Metadata.newBuilder().setId("id").setName("name").setDomain("iop").setVersion("1").setTimestamp("timestamp")
        .setAction("action").build();
  }

  @Configuration
  @ComponentScan(basePackages = {"com.inditex.icdmdemg.client.pipe.delivery", "com.inditex.icdmdemg.client.mapper",
      "com.inditex.icdmdemg.shared.utils"})
  public static class Config {

  }
}
