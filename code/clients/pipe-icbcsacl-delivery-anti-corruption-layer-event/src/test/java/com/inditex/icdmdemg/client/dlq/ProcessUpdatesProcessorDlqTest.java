package com.inditex.icdmdemg.client.dlq;

import static org.mockito.Mockito.doReturn;

import com.inditex.amigafwk.test.logcaptor.LogCaptor;
import com.inditex.amigafwk.test.logcaptor.LogCaptorExtension;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessUpdatesEnvelopeV3;

import ch.qos.logback.classic.Level;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;

@ExtendWith({MockitoExtension.class, LogCaptorExtension.class})
class ProcessUpdatesProcessorDlqTest {

  private final LogCaptor logCaptor;

  private final ProcessUpdatesProcessorDlq sut = new ProcessUpdatesProcessorDlq();

  ProcessUpdatesProcessorDlqTest(final LogCaptor logCaptor) {
    this.logCaptor = logCaptor;
  }

  @Test
  void should_log_the_payload(@Mock final Message<ProcessUpdatesEnvelopeV3> message) {
    final var payload = Instancio.create(ProcessUpdatesEnvelopeV3.class);

    doReturn(payload).when(message).getPayload();

    this.sut.process(message);

    this.logCaptor.verifyMessagesContaining(ProcessUpdatesProcessorDlq.LOG_CONTEXT + payload)
        .withLevel(Level.INFO)
        .areExactly(1);
  }

}
