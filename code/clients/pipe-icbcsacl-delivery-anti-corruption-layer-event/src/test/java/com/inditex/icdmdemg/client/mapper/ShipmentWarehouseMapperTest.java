package com.inditex.icdmdemg.client.mapper;

import static com.inditex.icdmdemg.shared.utils.TimeMapperUtils.offsetDateTimeFromString;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.fields;

import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessCompleted;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessCreated;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessStarted;
import com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessUpdated;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCompletedCommand.ShipmentWarehouseCompleted;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseStartedCommand.ShipmentWarehouseStarted;
import com.inditex.icdmdemg.shared.utils.CharSequenceMapperImpl;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest(classes = {ShipmentWarehouseMapperImpl.class, CharSequenceMapperImpl.class})
class ShipmentWarehouseMapperTest {
  @Autowired
  private ShipmentWarehouseMapper sut;

  @Test
  void should_map_from_processCreated() {
    final var processCreated = Instancio.create(ProcessCreated.class);

    final var result = this.sut.from(processCreated);

    assertThat(result).isNotNull();
    assertThat(result.trackingCode()).isEqualTo(processCreated.getDistributionTrackingCode().toString());
    assertThat(result.distributionInnerId()).isEqualTo(processCreated.getDistributionInner().getOrderId());
    assertThat(result.distributionInnerLineId()).isEqualTo(processCreated.getDistributionInner().getOrderLineId());
  }

  @Test
  void should_map_from_processUpdated() {
    final var processUpdated = Instancio.of(ProcessUpdated.class)
        .generate(fields(field -> field.getName().contains("date")),
            generators -> generators.temporal().instant().future().asString())
        .create();

    final var result = this.sut.from(processUpdated);

    assertThat(result).isNotNull();
    assertThat(result.distributionTrackingCode()).isEqualTo(processUpdated.getDistributionTrackingCode().toString());
    assertThat(result.quantity().doubleValue()).isEqualTo(processUpdated.getGlobalDistributedQuantity());
    assertThat(result.distributionLastUpdateDate()).isEqualTo(offsetDateTimeFromString(processUpdated.getDistributionLastUpdateDate()));
  }

  @Test
  void should_map_from_processStarted() {
    final var processStarted = Instancio.of(ProcessStarted.class)
        .generate(fields(field -> field.getName().contains("date")),
            generators -> generators.temporal().instant().future().asString())
        .create();

    final ShipmentWarehouseStarted result = this.sut.from(processStarted);

    assertThat(result).isNotNull();
    assertThat(result.distributionTrackingCode()).isEqualTo(processStarted.getDistributionTrackingCode().toString());
    assertThat(result.distributionStartDate()).isEqualTo(offsetDateTimeFromString(processStarted.getDistributionStartDate()));
  }

  @Test
  void should_map_from_processCompleted() {
    final var processStarted = Instancio.of(ProcessCompleted.class)
        .generate(fields(field -> field.getName().contains("date")),
            generators -> generators.temporal().instant().future().asString())
        .create();

    final ShipmentWarehouseCompleted result = this.sut.from(processStarted);

    assertThat(result).isNotNull();
    assertThat(result.distributionTrackingCode()).isEqualTo(processStarted.getDistributionTrackingCode().toString());
    assertThat(result.distributionEndDate()).isEqualTo(offsetDateTimeFromString(processStarted.getDistributionEndDate()));
  }

}
