<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.inditex.icdmdemg</groupId>
    <artifactId>icdmdebtmgmt</artifactId>
    <version>1.91.0-SNAPSHOT</version>
    <relativePath>..</relativePath>
  </parent>

  <artifactId>icdmdebtmgmt-boot</artifactId>
  <packaging>jar</packaging>

  <name>${project.groupId}:${project.artifactId}</name>
  <description />

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-api-rest</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-application</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-infrastructure</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-clients-modules</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-starter-oauth2-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-starter-config-now</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-starter-metrics-prometheus</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-starter-resilience</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.service</groupId>
      <artifactId>service-starter-aaa-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-starter-cache-caffeine</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-scheduling</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.iopcmmntddd</groupId>
      <artifactId>lib-iopcmmntddd-starter-spring-glue</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.data</groupId>
      <artifactId>data-starter-scs-outbox-jdbc</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.iopcmmntsh</groupId>
      <artifactId>lib-iopcmmntstreamheaders-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-grpc-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.graphql</groupId>
      <artifactId>spring-graphql-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-webflux</artifactId>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <finalName>${project.artifactId}-${project.version}</finalName>
    <plugins />
  </build>

  <reporting />
</project>
