package com.inditex.icdmdemg.config;

import com.inditex.amigafwk.common.scheduling.annotations.DynamicScheduled;
import com.inditex.icdmdemg.application.commitmentuse.service.MaterialCommitmentUseExpiredProcessor;
import com.inditex.icdmdemg.application.taxonomy.TaxonomyUpdaterProcessor;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

@Configuration
@EnableScheduling
@RequiredArgsConstructor
@Slf4j
public class SchedulingConfig {

  private static final String SCHEDULED_JOB_EXECUTION_STARTED = "Scheduled job execution {} started.";

  private final MaterialCommitmentUseExpiredProcessor materialCommitmentUseExpiredProcessor;

  private final TaxonomyUpdaterProcessor taxonomyUpdaterProcessor;

  @DynamicScheduled(name = "process-expired-material-commitment-use")
  @SchedulerLock(name = "process-expired-material-commitment-use", lockAtMostFor = "PT1M")
  public void processMaterialCommitmentUse() {
    log.info(SCHEDULED_JOB_EXECUTION_STARTED, "process-expired-material-commitment-use");
    this.materialCommitmentUseExpiredProcessor.processExpiredExpectedDate();
  }

  @DynamicScheduled(name = "update-taxonomies")
  @SchedulerLock(name = "update-taxonomies", lockAtMostFor = "PT1M")
  public void processTaxonomyUpdate() {
    log.info(SCHEDULED_JOB_EXECUTION_STARTED, "update-taxonomies");
    this.taxonomyUpdaterProcessor.updateTaxonomies();
  }
}
