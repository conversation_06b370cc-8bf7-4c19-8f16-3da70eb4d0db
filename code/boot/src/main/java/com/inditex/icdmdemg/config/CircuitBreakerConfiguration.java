package com.inditex.icdmdemg.config;

import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import jakarta.annotation.PostConstruct;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@AllArgsConstructor
public class CircuitBreakerConfiguration {

  private final CircuitBreakerRegistry circuitBreakerRegistry;

  @PostConstruct
  public void registerEventConsumer() {
    this.circuitBreakerRegistry.getAllCircuitBreakers().forEach(circuitBreaker -> circuitBreaker.getEventPublisher()
        .onStateTransition(event -> log.info(event.toString())));
  }

}
