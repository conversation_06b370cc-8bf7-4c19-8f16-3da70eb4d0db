package com.inditex.icdmdemg.config;

import java.util.Objects;

import com.inditex.amigafwk.data.core.jdbc.annotation.AmigaJdbcDatasource;
import com.inditex.amigafwk.data.jpa.annotations.AmigaEnableJpaRepositories;

import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.boot.orm.jpa.EntityManagerFactoryBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.transaction.PlatformTransactionManager;

@Configuration
@AmigaEnableJpaRepositories(
    basePackages = "com.inditex.icdmdemg.infrastructure",
    entityManagerFactoryRef = "entityManagerFactory",
    transactionManagerRef = "transactionManager")
public class DatabaseConfiguration {

  @Primary
  @AmigaJdbcDatasource(value = "postgresql", beanName = "dataSource")
  public DataSource dataSource() {
    return DataSourceBuilder.create().build();
  }

  @Bean
  @Primary
  public LocalContainerEntityManagerFactoryBean entityManagerFactory(@Qualifier("dataSource") final DataSource dataSource,
      final EntityManagerFactoryBuilder builder) {
    return builder
        .dataSource(dataSource)
        .persistenceUnit("icdmdemg")
        .packages("com.inditex.icdmdemg.infrastructure")
        .jta(false)
        .build();
  }

  @Bean
  public PlatformTransactionManager transactionManager(
      @Qualifier("entityManagerFactory") final LocalContainerEntityManagerFactoryBean entityManagerFactory) {
    return new JpaTransactionManager(Objects.requireNonNull(entityManagerFactory.getObject()));
  }

  @Bean(name = "debtNamedJdbcTemplate")
  public NamedParameterJdbcTemplate debtNamedJdbcTemplate(@Qualifier("dataSource") final DataSource dataSource) {
    return new NamedParameterJdbcTemplate(dataSource);
  }

}
