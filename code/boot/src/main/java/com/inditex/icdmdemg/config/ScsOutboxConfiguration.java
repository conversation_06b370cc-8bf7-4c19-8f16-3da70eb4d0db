package com.inditex.icdmdemg.config;

import com.inditex.scsoutbox.serialization.KafkaAvroSerialization;
import com.inditex.scsoutbox.serialization.SerializationEngine;

import io.confluent.kafka.serializers.KafkaAvroDeserializer;
import io.confluent.kafka.serializers.KafkaAvroSerializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class ScsOutboxConfiguration {

  @Bean
  public SerializationEngine kafkaAvroSerializationEngine(
      final KafkaAvroSerializer serializer,
      final KafkaAvroDeserializer deserializer) {
    return new KafkaAvroSerialization(serializer, deserializer);
  }

}
