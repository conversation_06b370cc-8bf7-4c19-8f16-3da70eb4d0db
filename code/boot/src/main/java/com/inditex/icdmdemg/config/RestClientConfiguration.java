package com.inditex.icdmdemg.config;

import com.inditex.amigafwk.common.rest.client.RestClient;
import com.inditex.amigafwk.common.rest.client.annotation.AmigaRestClient;
import com.inditex.amigafwk.common.rest.client.builder.RestClientBuilder;
import com.inditex.icdmdemg.icbcprodt.rest.client.api.TaxonomiesApi;
import com.inditex.icdmdemg.icbcprovis.rest.client.api.ProductTaxonomiesApi;
import com.inditex.icdmdemg.icmpurcent.rest.client.api.CategoriesApi;
import com.inditex.icdmdemg.icmpurcent.rest.client.api.ReducedCategoriesApi;
import com.inditex.icdmdemg.siga.rest.client.api.DistributionOrdersApi;
import com.inditex.icdmdemg.siga.rest.client.invoker.ApiClient;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RestClientConfiguration {

  @AmigaRestClient("siga-client")
  public RestClient sigaClient(final RestClientBuilder builder) {
    return builder.build();
  }

  @Bean
  public ApiClient sigaApiClient(
      final @Qualifier("sigaClient") RestClient restClient) {
    final var apiClient = new ApiClient(restClient);
    apiClient.setBasePath(restClient.getBasePath());
    return apiClient;
  }

  @Bean
  public DistributionOrdersApi distributionOrdersApi(final ApiClient apiClient) {
    return new DistributionOrdersApi(apiClient);
  }

  @AmigaRestClient("icbcprovis-client")
  public RestClient icbcprovisClient(final RestClientBuilder builder) {
    return builder.build();
  }

  @Bean
  public com.inditex.icdmdemg.icbcprovis.rest.client.invoker.ApiClient icbcprovisApiClient(
      final @Qualifier("icbcprovisClient") RestClient restClient) {
    final var apiClient = new com.inditex.icdmdemg.icbcprovis.rest.client.invoker.ApiClient(restClient);
    apiClient.setBasePath(restClient.getBasePath());
    return apiClient;
  }

  @Bean
  public ProductTaxonomiesApi productTaxonomiesApi(final com.inditex.icdmdemg.icbcprovis.rest.client.invoker.ApiClient apiClient) {
    return new ProductTaxonomiesApi(apiClient);
  }

  @AmigaRestClient(value = "icbcprodt-client")
  public RestClient icbcprodtRestClient(final RestClientBuilder builder) {
    return builder.build();
  }

  @Bean
  public com.inditex.icdmdemg.icbcprodt.rest.client.invoker.ApiClient icbcprodtApiClient(
      @Qualifier("icbcprodtRestClient") final RestClient restClient) {
    final var apiClient = new com.inditex.icdmdemg.icbcprodt.rest.client.invoker.ApiClient(restClient);
    apiClient.setBasePath(restClient.getBasePath());
    return apiClient;
  }

  @Bean
  public TaxonomiesApi taxonomiesApi(final com.inditex.icdmdemg.icbcprodt.rest.client.invoker.ApiClient icbcprodtApiClient) {
    return new TaxonomiesApi(icbcprodtApiClient);
  }

  @AmigaRestClient(value = "icmpurcent-client")
  public RestClient icmpurcentRestClient(final RestClientBuilder builder) {
    return builder.build();
  }

  @Bean
  public com.inditex.icdmdemg.icmpurcent.rest.client.invoker.ApiClient icmpurcentApiClient(
      @Qualifier("icmpurcentRestClient") final RestClient restClient) {
    final var apiClient = new com.inditex.icdmdemg.icmpurcent.rest.client.invoker.ApiClient(restClient);
    apiClient.setBasePath(restClient.getBasePath());
    return apiClient;
  }

  @Bean
  public CategoriesApi categoriesApi(final com.inditex.icdmdemg.icmpurcent.rest.client.invoker.ApiClient icmpurcenttApiClient) {
    return new CategoriesApi(icmpurcenttApiClient);
  }

  @Bean
  public ReducedCategoriesApi reducedCategoriesApi(
      final com.inditex.icdmdemg.icmpurcent.rest.client.invoker.ApiClient icmpurcenttApiClient) {
    return new ReducedCategoriesApi(icmpurcenttApiClient);
  }

}
