package com.inditex.icdmdemg.config;

import com.inditex.amigafwk.data.core.jdbc.annotation.AmigaJdbcDatasource;

import javax.sql.DataSource;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

@Configuration
public class ProvisDatabaseConfiguration {

  @AmigaJdbcDatasource(value = "provis", beanName = "dataSourceProvis")
  public DataSource provisDataSource() {
    return DataSourceBuilder.create().build();
  }

  @Bean(name = "provisNamedJdbcTemplate")
  public NamedParameterJdbcTemplate provisNamedJdbcTemplate(@Qualifier("dataSourceProvis") final DataSource dataSourceProvis) {
    return new NamedParameterJdbcTemplate(dataSourceProvis);
  }

}
