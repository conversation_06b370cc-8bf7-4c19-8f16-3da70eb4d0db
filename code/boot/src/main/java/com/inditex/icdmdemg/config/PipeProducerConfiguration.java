package com.inditex.icdmdemg.config;

import com.inditex.aqsw.pipe.TracingResolverSessionId;
import com.inditex.aqsw.pipe.v1.MetadataBuilder;
import com.inditex.icdmdemg.infrastructure.shared.pipe.CustomMetadataBuilder;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
class PipeProducerConfiguration {

  @Bean
  MetadataBuilder metadataBuilder(
      @Value("${metadata.domain}") final String domain,
      @Value("${metadata.version:1}") final String version,
      @Qualifier("amigaTracingResolverSessionId") final TracingResolverSessionId amigaTracingResolverSessionId) {
    return new CustomMetadataBuilder(domain, version, amigaTracingResolverSessionId);
  }

}
