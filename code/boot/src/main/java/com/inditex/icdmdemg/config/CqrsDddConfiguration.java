package com.inditex.icdmdemg.config;

import java.util.Set;

import com.inditex.iopcmmnt.cqrs.core.BaseCommandHandler;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;
import com.inditex.iopcmmnt.cqrs.core.QueryBus;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;
import com.inditex.iopcmmnt.glue.spring.CqrsAutoConfiguration;
import com.inditex.iopcmmnt.glue.spring.cqrs.SpringCommandBus;
import com.inditex.iopcmmnt.glue.spring.cqrs.SpringQueryBus;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
@EnableAutoConfiguration(exclude = CqrsAutoConfiguration.class)
public class CqrsDddConfiguration {

  @Primary
  @Bean
  @SuppressWarnings("rawtypes")
  public QueryBus queryBus(final Set<QueryHandler> queryhandlers) {
    return new SpringQueryBus(queryhandlers);
  }

  @Bean("notTransactionalCommandBus")
  @SuppressWarnings({"rawtypes", "unchecked"})
  public CommandBus notTransactionalCommandBus(final Set<BaseCommandHandler> commands) {
    return new SpringCommandBus(commands);
  }

}
