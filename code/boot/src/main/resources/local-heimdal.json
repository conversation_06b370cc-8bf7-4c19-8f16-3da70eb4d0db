{"username100": {"metadata": {"roles": ["admin"]}, "permissions": ["Debt.Read", "Debt.Write", "DistributionNominated.Read", "DistributionNominated.Write", "Distributions.Admin", "DistributionInner.Read", "DistributionInner.Write", "Use.Read", "Use.Write"], "userData": {"login": "username100", "name": "Nombre100", "mail": "<EMAIL>", "description": "Usuario 100", "employeeId": "employeeId100", "employeeNumber": "employeeNumber100", "company": "inditex", "department": "technology"}, "attributes": []}, "username200": {"metadata": {"roles": ["authorized"]}, "permissions": [], "userData": {"login": "username200", "name": "Nombre200", "mail": "<EMAIL>", "description": "Usuario 200", "employeeId": "employeeId200", "employeeNumber": "employeeNumber200", "company": "inditex", "department": "technology"}, "attributes": []}}