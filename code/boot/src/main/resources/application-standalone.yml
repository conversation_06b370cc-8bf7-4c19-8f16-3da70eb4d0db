logging:
  level:
    root: INFO
    com.inditex.icdmdemg.client.pipe: DEBUG
    org:
      hibernate:
        SQL: DEBUG
        type: TRACE
        orm.jdbc.bind: TRACE
#      springframework:
#        orm.jpa: DEBUG
#        jdbc.core: TRACE
amiga:
  common:
    oauth2-client:
      enabled: false
    rest:
      logging:
        is-production: false
      client:
        http:
          connect-timeout: 10s
          read-timeout: 40s
        icbcprovis-client:
          base-url: http://${qa.trafficparrot.rest.host:localhost}:${qa.trafficparrot.rest.port:31320}/icdmpvmgmt
          auth-method: NONE
        siga-client:
          base-url: http://${qa.trafficparrot.rest.host:localhost}:${qa.trafficparrot.rest.port:31320}/icacsiga
          auth-method: NONE
        icbcprodt-client:
          base-url: http://${qa.trafficparrot.rest.host:localhost}:${qa.trafficparrot.rest.port:31320}/icbcprodt
          auth-method: NONE
        icmpurcent-client:
          base-url: http://${qa.trafficparrot.rest.host:localhost}:${qa.trafficparrot.rest.port:31320}/icmpurcent
          auth-method: NONE
    cache:
      caffeine:
        enabled: false
        cache-names: equivalentCategories, parentsCategory
        per-cache-conf:
          equivalentCategories:
            expire-after-write: PT24H # 1 day
          parentsCategory:
            expire-after-write: PT24H # 1 day
    dynamic-scheduling:
      enabled: true
      tasks:
        process-expired-material-commitment-use:
          enabled: true
          cron: "0 0 3 * * *"
        update-taxonomies:
          enabled: true
          cron: "0 0 3 * * *"
  server:
    context-path: /icdmdemg
    port: 8080
  service:
    aaa:
      oidc:
        local-test:
          enabled: true
      local:
        enable-local-test: true
      paths:
        app-public-path: /swagger-ui**,/itx-openapi.js,/amiga/**
      web:
        form:
          type: OIDC
    web:
      api:
        production: false
  data:
    jdbc:
      query-timeout: 5s
      datasource:
        postgresql:
          jdbc-url: jdbc:postgresql://${container.postgres-debt.host:localhost}:${container.postgres-debt.port:32010}/${container.postgres-debt.db:postgres}?currentSchema=debtmg
          driver-class-name: org.postgresql.Driver
          username: ${container.postgres-debt.user:postgres}
          password: ${container.postgres-debt.password:postgres-pwd}
        provis:
          jdbc-url: jdbc:postgresql://${container.postgres-provis.host:localhost}:${container.postgres-provis.port:32011}/${container.postgres-provis.db:postgres}
          driver-class-name: org.postgresql.Driver
          username: ${container.postgres-provis.user:postgres}
          password: ${container.postgres-provis.password:postgres-pwd}
    function:
      definition: order-sent-event-v1;product-event;use-assignment-to-order-v4;m-bom-event;process-updates-response-global;commitment-shipment-event;
    stream:
      scs-outbox:
        bindings:
          inclusions: "distribution-nominated-event-out-0,distribution-inner-event-out-0,use-event-out-0"
        publication:
          scheduler:
            fixed-rate: 5000
            initial-delay: 2000
          archive:
            enabled: true
            json-payload-enabled: false
      binders:
        kafka-pipe:
          environment:
            spring:
              cloud:
                stream:
                  kafka:
                    bindings:
                      distribution-nominated-event-out-0:
                        producer:
                          sync: true
                      distribution-inner-event-out-0:
                        producer:
                          sync: true
                      use-event-out-0:
                        producer:
                          sync: true
                      order-sent-event-v1-in-0:
                        consumer:
                          enable-dlq: true
                          dlq-name: iop.global.pre.icbcdemg.order.error.v1
                          dlq-partitions: 1
                      use-assignment-to-order-v4-in-0:
                        consumer:
                          enable-dlq: true
                          dlq-name: iop.global.pre.icbcdemg.use.error.v1
                          dlq-partitions: 1
                      product-event-in-0:
                        consumer:
                          enable-dlq: true
                          dlq-name: iop.global.pre.icbcdemg.product.error.v1
                          dlq-partitions: 1
                      process-updates-response-global-in-0:
                        consumer:
                          enable-dlq: true
                          dlq-name: iop.global.pre.icbcdemg.processupdates.error.v1
                          dlq-partitions: 1
                      commitment-shipment-event-in-0:
                        consumer:
                          enable-dlq: true
                          dlq-name: iop.global.pre.icbcdemg.shipment.error.v1
                          dlq-partitions: 1
      # dlq-processor: order-domain-processor;use-assignment-processor;mbom-processor;product-processor;process-updates-response-processor;
      output-bindings: distribution-nominated-event-out-0;distribution-inner-event-out-0;use-event-out-0;

      schema:
        avro:
          schema-imports:
            - classpath*:/pipe/**/imports/*.avsc
            - classpath*:/v1/imports/*.avsc
            - classpath*:/v2/imports/*.avsc
            - classpath*:/nominated/v1/imports/*.avsc
            - classpath*:/inner/v1/imports/*.avsc
          schema-sources:
            - classpath*:/v1/*.avsc
            - classpath*:/v2/*.avsc
            - classpath*:/nominated/v1/*.avsc
            - classpath*:/inner/v1/*.avsc
        kafka-brokers: ${qa.kafka.host:localhost}:${qa.kafka.port:30810}
        registry-client:
          endpoint: http://${qa.schemaregistry.host:localhost}:${qa.schemaregistry.port:31210}
      bindings:
        order-domain-processor-in-0:
          destination: iop.global.pre.icbcdemg.order.error.v1
          group: ${metadata.jirakey}-local
          consumer:
            dlq-processor:
              max-processed-messages: 5000
              deserialize-to: com.inditex.icdmnegord.order.event.unified.v1.OrderUnifiedEnvelopeV1
              strategy:
                strategy-class: com.inditex.icdmdemg.client.dlq.OrderDomainProcessorDlq
        use-assignment-processor-in-0:
          destination: iop.global.pre.icbcdemg.use.error.v1
          group: ${metadata.jirakey}-local
          consumer:
            dlq-processor:
              max-processed-messages: 5000
              deserialize-to: com.inditex.iop.use.assignment.order.v4.UseAssignmentToOrderEnvelopeV4
              strategy:
                strategy-class: com.inditex.icdmdemg.client.dlq.UseAssignmentProcessorDlq
        product-processor-in-0:
          destination: iop.global.pre.icbcdemg.product.error.v1
          group: ${metadata.jirakey}-local
          consumer:
            dlq-processor:
              max-processed-messages: 5000
              deserialize-to: com.inditex.icdmprodt.pipe.product.v9.ProductEventEnvelopeV9
              strategy:
                strategy-class: com.inditex.icdmdemg.client.dlq.ProductProcessorDlq
        process-updates-response-processor-in-0:
          destination: iop.global.pre.icbcdemg.processupdates.error.v1
          group: ${metadata.jirakey}-local
          consumer:
            dlq-processor:
              max-processed-messages: 5000
              deserialize-to: com.inditex.comercial.icacsiga.processupdates.load.v3.ProcessUpdatesEnvelopeV3
              strategy:
                strategy-class: com.inditex.icdmdemg.client.dlq.ProcessUpdatesProcessorDlq
        commitment-shipment-processor-in-0:
          destination: iop.global.pre.icbcdemg.shipment.error.v1
          group: ${metadata.jirakey}-local
          consumer:
            dlq-processor:
              max-processed-messages: 5000
              deserialize-to: com.inditex.bcsupshipm.event.domain.v4.ShipmentEnvelopeV4
              strategy:
                strategy-class: com.inditex.icdmdemg.client.dlq.CommitmentShipmentProcessorDlq
        order-sent-event-v1-in-0:
          destination: iop.global.pre.icdmnegord.order.public.unified.v1
          group: ${metadata.jirakey}-local
          consumer:
            max-attempts: 1
        product-event-in-0:
          destination: iop.global.pre.icbcprodt.product.public.domain.v9
          group: ${metadata.jirakey}-local
          consumer:
            max-attempts: 1
        use-assignment-to-order-v4-in-0:
          destination: iop.global.pre.icbcprovis.use-assignment-order.public.load.v4
          group: ${metadata.jirakey}-local
          consumer:
            max-attempts: 1
        process-updates-response-global-in-0:
          destination: comercial.global.pre.icacsiga.processupdates.public.load.v3
          group: ${metadata.jirakey}-local
          consumer:
            max-attempts: 1
        commitment-shipment-event-in-0:
          destination: iop.global.pre.itbcenv.shipment.public.domain.v4
          group: ${metadata.jirakey}-local
          consumer:
            max-attempts: 1
        distribution-nominated-event-out-0:
          entity-version: 1.0.0
          destination: iop.global.pre.icbcdemg.distributionnominated.public.unified.v1
          content-type: application/*+avro
          registered-schema: classpath:/nominated/v1/distribution_unified_envelope.avsc
          header-injection:
            headers:
              itx_envelope_id: pipe-event:1.0
        distribution-inner-event-out-0:
          entity-version: 1.0.0
          destination: iop.global.pre.icbcdemg.distributioninner.public.unified.v1
          content-type: application/*+avro
          registered-schema: classpath:/inner/v1/distribution_inner_unified_envelope.avsc
          header-injection:
            headers:
              itx_envelope_id: pipe-event:1.0
        use-event-out-0:
          entity-version: 1.0.0
          destination: iop.global.pre.icbcdemg.use.public.unified.v1
          content-type: application/*+avro
          registered-schema: classpath:/v1/use_unified_envelope.avsc
          header-injection:
            headers:
              itx_envelope_id: pipe-event:1.0

metadata:
  jirakey: icdmdemg

retryable-conf:
  max-attempts: 1

product-reference:
  uncoloured:
    id: "59b26ca9-1146-4e0e-bf53-f6e01b6670dd"

dn-cancel-conf:
  allowed-uses:
    a8568fd5-029e-4c2a-87c0-90c04aa6cdd5: '2022-02-01T00:00:00Z'
    00000000-0000-0000-0000-000000000003: '2022-03-01T00:00:00Z'

di-availability:
  external-uses:
    - "0195ee2e-e8b1-7e76-9271-d20dcb2c0c85"
    - "0195ee2f-8fe6-7b85-a033-5b0c1d758d81"
    - "0195ee31-dc3b-7e5a-a249-d7d837cba100"
    - "01963398-d493-7890-8e28-17ba2a6465ad"
    - "83f79cf6-4f80-4130-ad33-686c06b84748"

mcu:
  budget-cycle-change-executed-mark:
    uses:
      - "9d5250ef-ba41-4c8d-9caf-759f8773ec1c"
    until-at: "2025-05-21T00:00:00Z"
