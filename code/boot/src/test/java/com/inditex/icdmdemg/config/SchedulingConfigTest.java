package com.inditex.icdmdemg.config;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.inditex.icdmdemg.application.commitmentuse.service.MaterialCommitmentUseExpiredProcessor;
import com.inditex.icdmdemg.application.taxonomy.TaxonomyUpdaterProcessor;

import org.junit.jupiter.api.Test;

class SchedulingConfigTest {

  private final MaterialCommitmentUseExpiredProcessor materialCommitmentUseExpiredProcessor = mock();

  private final TaxonomyUpdaterProcessor taxonomyUpdaterProcessor = mock();

  private final SchedulingConfig sut = new SchedulingConfig(
      this.materialCommitmentUseExpiredProcessor,
      this.taxonomyUpdaterProcessor);

  @Test
  void should_invoke_schedule_material_tasks() {
    this.sut.processMaterialCommitmentUse();

    verify(this.materialCommitmentUseExpiredProcessor).processExpiredExpectedDate();
  }

  @Test
  void should_invoke_schedule_taxonomy_tasks() {
    this.sut.processTaxonomyUpdate();

    verify(this.taxonomyUpdaterProcessor).updateTaxonomies();
  }
}
