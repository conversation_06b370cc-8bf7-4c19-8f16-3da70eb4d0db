package com.inditex.icdmdemg;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

/**
 * <pre>
 * 1) crear túnel azure: azpro
 * 2) crear túnel SSH:
 * ssh -L 5555:iopcompclo-psqlf-weu1-pro-001-icdmdbt.postgres.database.azure.com:5432 jumpboxdev@localhost -p 9922
 * ssh -L 5555:iopcompclo-psqlf-weu1-pre-101-icdmdbt.postgres.database.azure.com:5432 jumpboxdev@localhost -p 2222
 *
 * 3) Cambiar el HOST a PRE o PRO en la constante ENDPOINT_URL
 * PRE: apigw-pre.apps.purchaseweuocp1pre.paas01weu.iopcompclo-pre.azcl.inditex.com
 * PRO: apigw.apps.purchaseweuocp1.paas01weu.iopcompclo.azcl.inditex.com
 * 4) Rellenar el AUTH_TOKEN
 * 4) ejecutar con los datos de conexión jdbc a localhost:5555
 * </pre>
 */
public class NominatedProvisionSynchronization {
  private static final Logger log = LoggerFactory.getLogger(NominatedProvisionSynchronization.class);

  private static final String URL = "******************************************";

  private static final String USER = "postgres";

  private static final String PASSWORD = "postgres-pwd";

  public static final String HOST = "localhost";

  private static final String ENDPOINT_URL = "https://" + HOST + "/icdmdemg/v1/calculate-nominated-provision";

  private static final String AUTH_TOKEN =
      "token";

  private static final int BATCH_SIZE = 100;

  public static void main(String[] args) {
    final var rows = fetchRowsFromDB();
    if (rows.isEmpty()) {
      log.error("Not found rows in DB");
      return;
    }
    sendBatchedRequests(rows);
  }

  private static List<Map<String, Object>> fetchRowsFromDB() {
    final List<Map<String, Object>> rows = new ArrayList<>();
    final String sql = """
        select material_reference_id, use_id, order_budget_id
        from debtmg.material_commitment_use
        group by material_reference_id, use_id, order_budget_id
        """;
    try (final Connection conn = DriverManager.getConnection(URL, USER, PASSWORD);
        final PreparedStatement stmtSelectMcu = conn.prepareStatement(sql);
        final ResultSet rs = stmtSelectMcu.executeQuery()) {
      while (rs.next()) {
        final Map<String, Object> row = new HashMap<>();
        row.put("reference_id", rs.getObject("material_reference_id"));
        row.put("use_id", rs.getObject("use_id"));
        row.put("budget_cycle", rs.getObject("order_budget_id"));
        rows.add(row);
      }
    } catch (final SQLException e) {
      log.error("Error query data", e);
    }
    return rows;
  }

  private static void sendBatchedRequests(List<Map<String, Object>> rows) {
    final RestTemplate restTemplate = new RestTemplate();
    final int totalRows = rows.size();
    for (int i = 0; i < totalRows; i += BATCH_SIZE) {
      final var batch = rows.subList(i, Math.min(i + BATCH_SIZE, totalRows));
      log.info("Sending bath of {} rows", batch.size());
      final String jsonPayload = convertToJson(batch);
      sendPostRequest(restTemplate, jsonPayload);
    }
  }

  private static String convertToJson(List<Map<String, Object>> batch) {
    log.info("Converting to Json");
    final StringBuilder json = new StringBuilder("{\"sharedRawMaterials\":[");
    for (final Map<String, Object> row : batch) {
      json.append(String.format("{\"referenceId\":\"%s\",\"useId\":\"%s\",\"budgetCycle\":\"%s\"},",
          row.get("reference_id"), row.get("use_id"), row.get("budget_cycle")));
    }
    if (json.length() > 1) {
      json.deleteCharAt(json.length() - 1); // Remove last comma
    }
    json.append("]}");
    return json.toString();
  }

  private static void sendPostRequest(RestTemplate restTemplate, String jsonPayload) {
    log.info("Sending request");
    final HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setBearerAuth(AUTH_TOKEN);
    final HttpEntity<String> request = new HttpEntity<>(jsonPayload, headers);

    final ResponseEntity<String> response = restTemplate.postForEntity(ENDPOINT_URL, request, String.class);
    log.info("Response Code: {}", response.getStatusCode());
  }
}
