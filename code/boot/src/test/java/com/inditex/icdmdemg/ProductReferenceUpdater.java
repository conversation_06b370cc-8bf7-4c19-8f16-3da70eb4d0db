package com.inditex.icdmdemg;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.NumberFormat;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <pre>
 * 1) crear túnel azure: azpro
 * 2) crear túnel SSH:
 * ssh -L 5555:iopcompclo-psqlf-weu1-pro-001-icdmdbt.postgres.database.azure.com:5432 jumpboxdev@localhost -p 9922
 * ssh -L 5555:iopcompclo-psqlf-weu1-pre-101-icdmdbt.postgres.database.azure.com:5432 jumpboxdev@localhost -p 2222
 *
 * 3) ejecutar con el CSV (en code/target) y los datos de conexión jdbc a localhost:5555
 * </pre>
 */
public class ProductReferenceUpdater {

  private static final Logger log = LoggerFactory.getLogger(ProductReferenceUpdater.class);

  private static final String URL = "******************************************";

  private static final String USER = "postgres";

  private static final String PASSWORD = "postgres-pwd";

  private static final String CSV_DELIMITER = ",";

  private static final int CSV_NUMBER_OF_COLUMNS = 10;

  public static final int CSV_POSITION_OLD_REFERENCE = 6;

  public static final int CSV_POSITION_NEW_REFERENCE = 9;

  private static final NumberFormat NUMBER_FORMATTER = NumberFormat.getInstance();

  private static final String TABLE_TEMP = "temp_references";

  private static final boolean DELETE_TABLE_TEMP = false;

  private static final String TABLE_DN = "distribution_nominated";

  private static final String TABLE_DI = "distribution_inner";

  private static final String TABLE_NOMINATED_PROVISION = "nominated_provision";

  private static final String TABLE_COMMITMENT = "material_commitment_use";

  private static final String TABLE_ORDER = "product_order";

  private static final String TABLE_ORDER_LINE = "order_line";

  private static final String FILE_NAME = "references.csv";

  private static final String UPDATED_BY = "manual_ref_update";

  public static void main(final String[] args) {
    final long start = System.nanoTime();
    final String targetDir = System.getProperty("user.dir") + File.separator + "code" + File.separator + "target" + File.separator + "%s";
    final String csvFile = targetDir.formatted(FILE_NAME);
    String line;

    // Contador de lineas procesadas y no procesadas
    int totalLinesProcessed = 0;
    int totalLinesUnprocessed = 0;

    try (final Connection conn = DriverManager.getConnection(URL, USER, PASSWORD);
        final Statement statement = conn.createStatement();
        final BufferedReader br = new BufferedReader(new FileReader(csvFile))) {

      conn.setAutoCommit(false);

      final String sqlUpdateDN =
          """
              UPDATE debtmg.%s t
              SET reference_id = refs.target_id, updated_at = NOW(), updated_by = '%s'
              FROM debtmg.%s refs
              WHERE t.reference_id = refs.source_id
              """
              .formatted(TABLE_DN, UPDATED_BY, TABLE_TEMP);

      final String sqlUpdateDI =
          """
              UPDATE debtmg.%s t
              SET reference_id = refs.target_id, updated_at = NOW(), updated_by = '%s'
              FROM debtmg.%s refs
              WHERE t.reference_id = refs.source_id
              """
              .formatted(TABLE_DI, UPDATED_BY, TABLE_TEMP);

      final String sqlUpdateNominatedProvision =
          """
              UPDATE debtmg.%s t
              SET reference_id = refs.target_id, updated_at = NOW(), updated_by = '%s'
              FROM debtmg.%s refs
              WHERE t.reference_id = refs.source_id
              """
              .formatted(TABLE_NOMINATED_PROVISION, UPDATED_BY, TABLE_TEMP);

      final String sqlUpdateCommitment =
          """
              UPDATE debtmg.%s t
              SET material_reference_id = refs.target_id, updated_at = NOW()
              FROM debtmg.%s refs
              WHERE t.material_reference_id = refs.source_id
              """
              .formatted(TABLE_COMMITMENT, TABLE_TEMP);

      final String sqlUpdateOrderLine =
          """
              UPDATE debtmg.%s t
              SET product_reference_id = refs.target_id
              FROM debtmg.%s refs
              WHERE t.product_reference_id = refs.source_id
              """
              .formatted(TABLE_ORDER_LINE, TABLE_TEMP);

      final String sqlUpdateOrder =
          """
              UPDATE debtmg.%s t
              SET updated_at = NOW()
              FROM debtmg.%s refs, debtmg.%s ol
              WHERE t.id = ol.order_id AND ol.product_reference_id = refs.source_id
              """
              .formatted(TABLE_ORDER, TABLE_TEMP, TABLE_ORDER_LINE);

      final String sqlInsertRefs = "INSERT INTO debtmg.%s (source_id, target_id) VALUES (?, ?)".formatted(TABLE_TEMP);

      // Borrar tabla temporal
      statement.execute("DROP TABLE IF EXISTS debtmg.%s;".formatted(TABLE_TEMP));

      // Crear tabla temporal
      statement.execute("CREATE TABLE debtmg.%s (source_id UUID PRIMARY KEY, target_id UUID);".formatted(TABLE_TEMP));

      try (
          final PreparedStatement stmtInsertRefs = conn.prepareStatement(sqlInsertRefs);
          final PreparedStatement stmtUpdateDN = conn.prepareStatement(sqlUpdateDN);
          final PreparedStatement stmtUpdateDI = conn.prepareStatement(sqlUpdateDI);
          final PreparedStatement stmtUpdateNominatedProvision = conn.prepareStatement(sqlUpdateNominatedProvision);
          final PreparedStatement stmtUpdateCommitment = conn.prepareStatement(sqlUpdateCommitment);
          final PreparedStatement stmtUpdateOrder = conn.prepareStatement(sqlUpdateOrder);
          final PreparedStatement stmtUpdateOrderLine = conn.prepareStatement(sqlUpdateOrderLine)) {

        boolean isFirstLine = true; // Controla la primera línea (cabeceras)

        while ((line = br.readLine()) != null) {
          if (isFirstLine) {
            isFirstLine = false;
            continue;
          }
          final String[] data = line.split(CSV_DELIMITER);

          if (data.length != CSV_NUMBER_OF_COLUMNS) {
            log.warn("Línea con menos campos de los necesarios: {}", line);
            totalLinesUnprocessed++;
            continue;
          }

          if (anyRequiredFieldIsBlank(data)) {
            log.warn("Campos requeridos vacíos en línea: {}", line);
            totalLinesUnprocessed++;
            continue;
          }

          final UUID oldReference = UUID.fromString(data[CSV_POSITION_OLD_REFERENCE]);
          final UUID newReference = UUID.fromString(data[CSV_POSITION_NEW_REFERENCE]);

          stmtInsertRefs.setObject(1, oldReference);
          stmtInsertRefs.setObject(2, newReference);
          stmtInsertRefs.addBatch();

          totalLinesProcessed++;
        }

        stmtInsertRefs.executeBatch();
        conn.commit();
        log.info("Carga de CSV completada correctamente.");

        final int cont_DN = stmtUpdateDN.executeUpdate();
        final int cont_DI = stmtUpdateDI.executeUpdate();
        final int cont_NP = stmtUpdateNominatedProvision.executeUpdate();
        final int cont_MCU = stmtUpdateCommitment.executeUpdate();
        final int cont_O = stmtUpdateOrder.executeUpdate();
        final int cont_OL = stmtUpdateOrderLine.executeUpdate();

        conn.commit();
        log.info("Datos actualizados correctamente en todas las tablas: \n{} DNs \n{} DIs \n{} NPs \n{} MCUs \n{} Orders \n{} OrderLines",
            cont_DN, cont_DI, cont_NP, cont_MCU, cont_O, cont_OL);
      } catch (final SQLException e) {
        conn.rollback();
        log.error("Error al insertar los datos en la base de datos", e);
      }

      if (DELETE_TABLE_TEMP) {
        statement.execute("DROP TABLE debtmg.%s;".formatted(TABLE_TEMP));
      }

    } catch (final Exception e) {
      log.error("Error al leer el archivo", e);
    }

    log.info("Resumen: \n Líneas: [{}] \n Procesadas: [{}] \n No procesadas: [{}]", totalLinesProcessed + totalLinesUnprocessed,
        totalLinesProcessed, totalLinesUnprocessed);
    printElapsed(start);
  }

  private static boolean anyRequiredFieldIsBlank(final String[] data) {
    return data[CSV_POSITION_OLD_REFERENCE].isBlank()
        || data[CSV_POSITION_NEW_REFERENCE].isBlank();
  }

  private static void printElapsed(final long start) {
    final long elapsed = System.nanoTime() - start;
    log.info("Tiempo procesamiento (nanos): {}", NUMBER_FORMATTER.format(elapsed));
  }

}
