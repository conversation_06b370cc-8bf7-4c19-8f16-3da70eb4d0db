package com.inditex.icdmdemg;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <pre>
 * 0) ¡¡¡ IMPORTANTE !!! en PROVIS hay que copiar la tabla product_reference de DEUDA
 * 1) crear túnel azure: azpro
 * 2) crear túneles SSH:
 *  - PRO:
 * ssh -L 5555:iopcompclo-psqlf-weu1-pro-101-icdmpv.postgres.database.azure.com:5432 jumpboxdev@localhost -p 9922
 *  - PRE:
 * ssh -L 5555:iopcompclo-psqlf-weu1-pre-101-icdmpv.postgres.database.azure.com:5432 jumpboxdev@localhost -p 2222
 *  - PREINT:
 * ssh -L 5555:iopcompclo-psqlf-weu1-pre-201-icdmpv.postgres.database.azure.com:5432 jumpboxdev@localhost -p 2222
 *
 * 3) ejecutar con los datos de conexión jdbc a localhost:5555 (¡¡¡ OJO !!! el nombre de la BBDD es icdmpvmgmt en los entornos)
 * </pre>
 */
public class ProductSynchronizationProvis {

  private static final Logger log = LoggerFactory.getLogger(ProductSynchronizationProvis.class);

  private static final String URL = "******************************************";

  private static final String USER = "postgres";

  private static final String PASSWORD = "postgres-pwd";

  private static final String UPDATED_BY = "manual_merge_product";

  public static final String UPDATE_ORDER_USE = """
            UPDATE tbl_order_use_assignment AS toua
            SET product_id = pr.product_id, updated_at = now(), updated_by = '%s'
            FROM tbl_order_assignment AS toa
            JOIN product_reference AS pr ON toa.product_reference_id = pr.reference_id
            WHERE toua.id = toa.order_use_assignment_id AND toua.product_id <> pr.product_id
      """;

  public static final String UPDATE_PRODUCT_USE = """
            UPDATE tbl_use_product AS tup
            SET product_id = pr.product_id, updated_at = now(), updated_by = '%s'
            FROM product_reference AS pr
            WHERE tup.reference_id = pr.reference_id AND tup.product_id <> pr.product_id
      """;

  public static void main(final String[] args) {
    final String updateOrderUse = UPDATE_ORDER_USE.formatted(UPDATED_BY);
    final String updateProductUse = UPDATE_PRODUCT_USE.formatted(UPDATED_BY);
    try (final Connection conn = DriverManager.getConnection(URL, USER, PASSWORD)) {

      conn.setAutoCommit(false);

      try (final PreparedStatement stmtUpdateOrderUse = conn.prepareStatement(updateOrderUse);
          final PreparedStatement stmtUpdateProductUse = conn.prepareStatement(updateProductUse)) {

        final int cont_ous = stmtUpdateOrderUse.executeUpdate();
        final int cont_pus = stmtUpdateProductUse.executeUpdate();

        conn.commit();
        log.info("Datos actualizados correctamente en PROVIS: {} OUs - {} PUs", cont_ous, cont_pus);
      } catch (final SQLException e) {
        conn.rollback();
        log.error("Error al actualizar los datos en la base de datos de PROVIS", e);
      }
    } catch (final SQLException e) {
      log.error("Error al conectar a la base de datos de PROVIS", e);
    }
  }
}
