package com.inditex.icdmdemg;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <pre>
 * 1) crear túnel azure: azpro
 * 2) crear túneles SSH:
 *  - PRO:
 * ssh -L 5555:iopcompclo-psqlf-weu1-pro-001-icdmdbt.postgres.database.azure.com:5432 jumpboxdev@localhost -p 9922
 *  - PRE:
 * ssh -L 5555:iopcompclo-psqlf-weu1-pre-001-icdmdbt.postgres.database.azure.com:5432 jumpboxdev@localhost -p 2222
 *  - PREINT:
 * ssh -L 5555:iopcompclo-psqlf-weu1-pre-101-icdmdbt.postgres.database.azure.com:5432 jumpboxdev@localhost -p 2222
 *
 * 3) ejecutar con los datos de conexión jdbc a localhost:5555
 * </pre>
 */
public class ProductSynchronizationDebt {

  private static final Logger log = LoggerFactory.getLogger(ProductSynchronizationDebt.class);

  private static final String URL = "******************************************";

  private static final String USER = "postgres";

  private static final String PASSWORD = "postgres-pwd";

  private static final String UPDATED_BY = "manual_merge_product";

  public static final String UPDATE_DISTRIBUTION = """
            UPDATE debtmg.%s as distrib
            SET reference_product_id = pr.product_id, updated_at = now(), updated_by = '%s'
            FROM debtmg.product_reference as pr
            WHERE distrib.reference_id = pr.reference_id AND distrib.reference_product_id <> pr.product_id
      """;

  public static final String UPDATE_NOMINATED_PROVISION = """
            UPDATE debtmg.nominated_provision AS np
            SET product_id = pr.product_id, updated_at = now(), updated_by = '%s'
            FROM debtmg.product_reference AS pr
            WHERE np.reference_id = pr.reference_id AND np.product_id <> pr.product_id;
      """;

  public static void main(final String[] args) {
    final String updateDistributionNominated = UPDATE_DISTRIBUTION.formatted("distribution_nominated", UPDATED_BY);
    final String updateDistributionInner = UPDATE_DISTRIBUTION.formatted("distribution_inner", UPDATED_BY);
    final String updateNominatedProvision = UPDATE_NOMINATED_PROVISION.formatted(UPDATED_BY);

    try (final Connection conn = DriverManager.getConnection(URL, USER, PASSWORD)) {

      conn.setAutoCommit(false);

      try (final PreparedStatement stmtUpdateDistributionNominated = conn.prepareStatement(updateDistributionNominated);
          final PreparedStatement stmtUpdateDistributionInner = conn.prepareStatement(updateDistributionInner);
          final PreparedStatement stmtUpdateNominatedProvision = conn.prepareStatement(updateNominatedProvision)) {

        final int cont_dns = stmtUpdateDistributionNominated.executeUpdate();
        final int cont_dis = stmtUpdateDistributionInner.executeUpdate();
        final int cont_nps = stmtUpdateNominatedProvision.executeUpdate();

        conn.commit();
        log.info("Datos actualizados correctamente en DEUDA: {} DNs - {} DIs - {} NPs", cont_dns, cont_dis, cont_nps);
      } catch (final SQLException e) {
        conn.rollback();
        log.error("Error al actualizar los datos en la base de datos de DEUDA", e);
      }
    } catch (final SQLException e) {
      log.error("Error al conectar a la base de datos de DEUDA", e);
    }
  }

}
