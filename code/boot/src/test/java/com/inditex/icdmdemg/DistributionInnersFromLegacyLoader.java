package com.inditex.icdmdemg;

import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo.Status;
import com.inditex.icdmdemg.shared.utils.NumericUtils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <pre>
 * 1) crear túnel azure: azpro
 * 2) crear túnel SSH:
 * ssh -L 5555:iopcompclo-psqlf-weu1-pro-001-icdmdbt.postgres.database.azure.com:5432 jumpboxdev@localhost -p 9922
 * ssh -L 5555:iopcompclo-psqlf-weu1-pre-101-icdmdbt.postgres.database.azure.com:5432 jumpboxdev@localhost -p 2222
 * ssh -L 5555:iopcompclo-psqlf-weu1-pre-001-icdmdbt.postgres.database.azure.com:5432 jumpboxdev@localhost -p 2222
 *
 * 3) ejecutar con el CSV (en code/target) y los datos de conexión jdbc a localhost:5555
 * </pre>
 */
public class DistributionInnersFromLegacyLoader {

  private static final Logger log = LoggerFactory.getLogger(DistributionInnersFromLegacyLoader.class);

  private static final String URL = "******************************************";

  private static final String USER = "postgres";

  private static final String PASSWORD = "postgres-pwd";

  private static final String CSV_DELIMITER = ",";

  private static final int CSV_NUMBER_OF_COLUMNS = 16;

  private static final NumberFormat NUMBER_FORMATTER = NumberFormat.getInstance();

  private static final String TABLE_DI = "distribution_inner";

  private static final String TABLE_DI_LINE = "distribution_inner_line";

  private static final String FILE_NAME = "archivo_di.csv";

  private static final String CREATED_BY = "manual_004_v01";

  private static final String TABLE_SHIPMENT_WAREHOUSE = "shipment_warehouse";

  private static final Pattern URN_BUDGET_CYCLE_PATTERN = Pattern.compile(
      "^urn:BUDGETCYCLE:[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");

  public static void main(final String[] args) {
    final long start = System.nanoTime();
    final String targetDir = System.getProperty("user.dir") + File.separator + "code" + File.separator + "target" + File.separator + "%s";
    final String csvFile = targetDir.formatted(FILE_NAME);
    String line;

    // Map para almacenar ids agrupados por los campos clave
    final Map<String, UUID> distributionInnerMap = new HashMap<>();
    // Sumas acumuladas para cada distribution_inner
    final Map<UUID, Double> requestedQuantitySums = new HashMap<>();
    final Map<UUID, Double> distributedQuantitySums = new HashMap<>();

    // Contador de lineas procesadas y no procesadas
    int totalLinesProcessed = 0;
    int totalLinesUnprocessed = 0;

    try (final Connection conn = DriverManager.getConnection(URL, USER, PASSWORD);
        final BufferedReader br = new BufferedReader(new FileReader(csvFile))) {

      conn.setAutoCommit(false);

      // Consultar si existe un registro en distribution_inner
      final String checkExistingDistributionInner = """
          SELECT id, requested_quantity, distributed_quantity
          FROM debtmg.%s
          WHERE reference_id = ? AND use_id = ? AND budget_cycle = ? AND product_order_id = ? AND product_variant_group_id = ?
          """.formatted(TABLE_DI);

      // Consultar si existe un registro en shipment_warehouse
      final String checkExistingWarehouseProjectionTrackingCode = """
          SELECT id
          FROM debtmg.%s
          WHERE distribution_tracking_code = ?
          """.formatted(TABLE_SHIPMENT_WAREHOUSE);

      // Consultar si existe un registro en distribution_inner_line
      final String checkExistingDistributionInnerLineWithoutTrackingCode = """
          SELECT id
          FROM debtmg.%s
          WHERE distribution_inner_id = ? and tracking_code IS NULL
          """.formatted(TABLE_DI_LINE);

      // Insert para distribution_inner
      final String insertDistributionInner = """
          INSERT INTO debtmg.%s
          (id, reference_id, use_id, budget_cycle, product_order_id, product_variant_group_id,
          theoretical_quantity, consumption_factor, requested_quantity, distributed_quantity, status,
          "version", created_by, created_at, updated_by, updated_at, deleted_at,
          reference_product_id)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0, '%s', NOW(), '%s', NOW(), NULL, ?)
          """.formatted(TABLE_DI, CREATED_BY, CREATED_BY);

      // Insert para distribution_inner_line
      final String insertDistributionInnerLine = """
          INSERT INTO debtmg.%s
          (id, distribution_inner_id, requested_quantity, distributed_quantity, tracking_code,
           distribution_start_date, created_at, updated_at, theoretical_quantity, distribution_end_date)
          VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW(), ?, ?)
          """.formatted(TABLE_DI_LINE);

      // Actualizar requested_quantity y distributed_quantity en distribution_inner
      final String sqlUpdateRequestedQuantity =
          "UPDATE debtmg.%s SET requested_quantity = ?, distributed_quantity = ?, updated_at = NOW(), updated_by = '%s' WHERE id = ?"
              .formatted(TABLE_DI, CREATED_BY);

      // Actualizar theoretical_quantity en la linea
      final String sqlUpdateTheoreticalQuantityInLine = """
          UPDATE debtmg.%s AS line
          SET theoretical_quantity =
              CASE
                  WHEN line.requested_quantity = 0 OR root.requested_quantity = 0 THEN 0
                  ELSE
                      LEAST(
                          GREATEST(
                              (line.requested_quantity * 100.0) / root.requested_quantity,
                              0
                          ),
                          100
                      )
              END
          FROM debtmg.%s AS root
          WHERE line.distribution_inner_id = root.id
          AND root.id = ?
          """.formatted(TABLE_DI_LINE, TABLE_DI);

      // Actualizar status cuando no esta CLOSED NI CANCELED
      final String sqlUpdateInProgressStatus = """
              UPDATE debtmg.%s AS root
              SET status =
                  CASE
                      WHEN EXISTS (
                          SELECT 1
                          FROM debtmg.%s AS line
                          WHERE line.distribution_inner_id = root.id
                          AND line.tracking_code IS NOT NULL
                          AND line.distribution_start_date IS NULL
                      ) THEN 'PENDING'
                      WHEN EXISTS (
                          SELECT 1
                          FROM debtmg.%s AS line
                          WHERE line.distribution_inner_id = root.id
                          AND line.tracking_code IS NOT NULL
                          AND line.distribution_start_date IS NOT NULL
                          AND EXISTS (
                              SELECT 1
                              FROM debtmg.%s AS check_line
                              WHERE check_line.distribution_inner_id = root.id
                              AND check_line.distribution_end_date IS NULL
                          )
                      ) THEN 'IN_PROGRESS'
                      WHEN NOT EXISTS (
                          SELECT 1
                          FROM debtmg.%s AS line
                          WHERE line.distribution_inner_id = root.id
                          AND line.distribution_end_date IS NULL
                      ) THEN 'SENT'
                      ELSE 'NON_DISTRIBUTABLE'
                  END
              WHERE root.status NOT IN ('CLOSED', 'CANCELED')
              AND root.id = ?
          """.formatted(TABLE_DI, TABLE_DI_LINE, TABLE_DI_LINE, TABLE_DI_LINE, TABLE_DI_LINE);

      // Inserts para la proyeccion de siga
      final String insertShipmentWarehouse =
          """
              INSERT INTO debtmg.%s
              (id, distribution_tracking_code, distribution_inner_id, distribution_inner_line_id, distributed_quantity, distribution_start_date,
              distribution_last_update_date, "version", created_at, updated_at, distribution_end_date)
              VALUES (?, ?, ?, ?, ?, ?, ?, 0, NOW(), NOW(), ?)
              """
              .formatted(TABLE_SHIPMENT_WAREHOUSE);

      try (final PreparedStatement stmtCheckExisting = conn.prepareStatement(checkExistingDistributionInner);
          final PreparedStatement stmtCheckExistingTrackingCode = conn.prepareStatement(checkExistingWarehouseProjectionTrackingCode);
          final PreparedStatement stmtCheckExistingLineWithoutTrackingCode =
              conn.prepareStatement(checkExistingDistributionInnerLineWithoutTrackingCode);
          final PreparedStatement stmtInner = conn.prepareStatement(insertDistributionInner);
          final PreparedStatement stmtInnerLine = conn.prepareStatement(insertDistributionInnerLine);
          final PreparedStatement stmtUpdateRequestedQuantity = conn.prepareStatement(sqlUpdateRequestedQuantity);
          final PreparedStatement stmtUpdateInProgressStatus = conn.prepareStatement(sqlUpdateInProgressStatus);
          final PreparedStatement stmtUpdateTheoreticalQuantityInLine = conn.prepareStatement(sqlUpdateTheoreticalQuantityInLine);
          final PreparedStatement stmtInsertShipmentWarehouse = conn.prepareStatement(insertShipmentWarehouse)) {

        boolean isFirstLine = true; // Controla la primera línea (cabeceras)

        while ((line = br.readLine()) != null) {
          if (isFirstLine) {
            isFirstLine = false;
            continue;
          }
          final String[] data = line.replaceAll("[\"']", "")
              .split(CSV_DELIMITER, -1);

          if (data.length != CSV_NUMBER_OF_COLUMNS) {
            log.warn("Línea con menos campos de los necesarios: {}", line);
            totalLinesUnprocessed++;
            continue;
          }

          if (anyRequiredFieldIsBlank(data)) {
            log.warn("Campos requeridos vacíos en línea: {}", line);
            totalLinesUnprocessed++;
            continue;
          }

          UUID distributionInnerId = toUUID(data[0]);
          final UUID distributionInnerLineId = toUUID(data[1]);
          final UUID referenceId = toUUID(data[2]);
          final UUID useId = toUUID(data[3]);
          final String budgetCycle = data[4];
          final UUID productOrderId = toUUID(data[5]);
          final String productOrderStatus = data[6];
          final UUID productVariantGroupId = toUUID(data[7]);
          final double unitsOrderColorPt = Double.parseDouble(data[8]);
          final double consumptionFactor = Double.parseDouble(data[9]);
          final UUID referenceProductId = toUUID(data[10]);
          final var lineTrackingCode =
              data[11].isBlank() || "null".equalsIgnoreCase(data[11]) || Integer.parseInt(data[11]) <= 0 ? null : data[11];
          final double lineRequestedQuantity = Double.parseDouble(data[12]);
          final double lineDistributedQuantity = data[13].isBlank() ? 0 : Double.parseDouble(data[13]);
          final var lineDistributionStartDate =
              data[14].isBlank() ? Timestamp.from(Instant.now()) : fromStringUTCToTimestamp(data[14]);
          final var lineDistributionEndDate =
              data[15].isBlank() ? null : fromStringUTCToTimestamp(data[14]);

          if (lineRequestedQuantity == 0) {
            log.warn("Línea con requested_quantity 0: {}", line);
            totalLinesUnprocessed++;
            continue;
          }

          if (!isValidBudgetCycle(budgetCycle)) {
            log.warn("Línea con budgetCycle URN invalido: {}", line);
            totalLinesUnprocessed++;
            continue;
          }
          if (!isValidProductOrderStatus(productOrderStatus)) {
            log.warn("Línea con productOrderStatus invalido: {}", line);
            totalLinesUnprocessed++;
            continue;
          }

          if (nonNull(lineTrackingCode)) {
            // Checkeamos si existe el tracking code
            stmtCheckExistingTrackingCode.setObject(1, lineTrackingCode);
            final ResultSet rsTracking = stmtCheckExistingTrackingCode.executeQuery();

            if (rsTracking.next()) {
              log.warn("Tracking code ya existe en la proyección: {}", line);
              totalLinesUnprocessed++;
              rsTracking.close();
              continue;
            }
          }

          // Generar clave única para mapear distribución compra propia
          final String key = generateLineKey(referenceId, useId, budgetCycle, productOrderId, productVariantGroupId);

          // Verificar si ya existe en el mapa (o en la tabla)
          if (distributionInnerMap.containsKey(key)) {
            distributionInnerId = distributionInnerMap.get(key);
            log.warn("Distribución con key {} ya existe en el CSV. Seteando el ID de distribution_inner_id a {}", key, distributionInnerId);
          } else {

            // Consultar en la base de datos
            stmtCheckExisting.setObject(1, referenceId);
            stmtCheckExisting.setObject(2, useId);
            stmtCheckExisting.setString(3, budgetCycle);
            stmtCheckExisting.setObject(4, productOrderId);
            stmtCheckExisting.setObject(5, productVariantGroupId);

            final ResultSet rs = stmtCheckExisting.executeQuery();
            if (rs.next()) {
              // Si existe, obtenemos el ID y actualizamos el mapa de cantidades acumuladas con el valor que tiene el existing
              distributionInnerId = (UUID) rs.getObject("id");
              log.warn("Distribución con key {} ya existe en la BBDD. Seteando el ID de distribution_inner_id a {}", key,
                  distributionInnerId);
              requestedQuantitySums.put(distributionInnerId, rs.getDouble("requested_quantity"));
              distributedQuantitySums.put(distributionInnerId, rs.getDouble("distributed_quantity"));

              if (isNull(lineTrackingCode)) {
                // Checkeamos si existe la linea viva en ese root (sin tracking code)
                stmtCheckExistingLineWithoutTrackingCode.setObject(1, distributionInnerId);
                final ResultSet rsLine = stmtCheckExistingLineWithoutTrackingCode.executeQuery();

                if (rsLine.next()) {
                  log.warn("Línea sin tracking code ya existe: {}", line);
                  totalLinesUnprocessed++;
                  rsLine.close();
                  continue;
                }
              }

            } else {
              // Calcular el estado de distribution_inner (CLOSED, CANCELED, PENDING)
              final String distributionStatus =
                  DistributionInnerStatus.of(new ProductOrderStatusInfo(Status.valueOf(productOrderStatus), true),
                      true).value();
              // Si no existe, creamos una nueva fila en distribution_inner
              stmtInner.setObject(1, distributionInnerId);
              stmtInner.setObject(2, referenceId);
              stmtInner.setObject(3, useId);
              stmtInner.setString(4, budgetCycle);
              stmtInner.setObject(5, productOrderId);
              stmtInner.setObject(6, productVariantGroupId);
              final var theoreticalQuantity =
                  NumericUtils.roundUpScale2(BigDecimal.valueOf(consumptionFactor).multiply(BigDecimal.valueOf(unitsOrderColorPt)));
              stmtInner.setBigDecimal(7, theoreticalQuantity);
              stmtInner.setDouble(8, consumptionFactor);
              stmtInner.setDouble(9, 0); // requested_quantity
              stmtInner.setDouble(10, 0); // distributed_quantity
              stmtInner.setString(11, distributionStatus);
              stmtInner.setObject(12, referenceProductId);

              stmtInner.executeUpdate(); // Ejecutar la inserción
              requestedQuantitySums.put(distributionInnerId, 0.0); // Inicializa la suma en 0
              distributedQuantitySums.put(distributionInnerId, 0.0); // Inicializa la suma en 0
            }
            rs.close();
            distributionInnerMap.put(key, distributionInnerId); // Guardar en el mapa
          }

          // Actualiza la suma de requested_quantity para la línea correspondiente
          requestedQuantitySums.compute(distributionInnerId, (uuid, requested) -> requested + lineRequestedQuantity);
          distributedQuantitySums.compute(distributionInnerId, (uuid, distributed) -> distributed + lineDistributedQuantity);

          final var calculatedStartDate = (nonNull(lineTrackingCode) && lineDistributedQuantity > 0)
              ? lineDistributionStartDate
              : null;
          final var calculatedEndDate = nonNull(calculatedStartDate) ? lineDistributionEndDate : null;

          // Insertar en distribution_inner_line
          stmtInnerLine.setObject(1, distributionInnerLineId);
          stmtInnerLine.setObject(2, distributionInnerId); // ID de DI root
          stmtInnerLine.setDouble(3, lineRequestedQuantity);
          stmtInnerLine.setDouble(4, lineDistributedQuantity);
          stmtInnerLine.setObject(5, lineTrackingCode);
          stmtInnerLine.setTimestamp(6, calculatedStartDate);
          stmtInnerLine.setObject(7, 0); // theoretical_quantity
          stmtInnerLine.setObject(8, calculatedEndDate); // end_date

          stmtInnerLine.addBatch(); // Añadir al batch

          if (nonNull(lineTrackingCode)) {
            // Insertar en proyeccion siga
            final var shipmentWarehouseId = UUID.randomUUID();
            stmtInsertShipmentWarehouse.setObject(1, shipmentWarehouseId);
            stmtInsertShipmentWarehouse.setObject(2, lineTrackingCode);
            stmtInsertShipmentWarehouse.setObject(3, distributionInnerId);
            stmtInsertShipmentWarehouse.setObject(4, distributionInnerLineId);
            stmtInsertShipmentWarehouse.setObject(5, lineDistributedQuantity); // distributed_quantity
            stmtInsertShipmentWarehouse.setObject(6, calculatedStartDate); // start_date
            stmtInsertShipmentWarehouse.setObject(7, calculatedStartDate); // last_update_date
            stmtInsertShipmentWarehouse.setObject(8, calculatedEndDate); // end_date

            stmtInsertShipmentWarehouse.addBatch();

          }
          totalLinesProcessed++;
        }

        // Ejecutar batch y commit
        stmtInnerLine.executeBatch();

        // Actualiza `requested_quantity` y distributed_quantity en `distribution_inner` con las sumas acumuladas
        for (final Map.Entry<UUID, Double> entry : requestedQuantitySums.entrySet()) {
          stmtUpdateRequestedQuantity.setDouble(1, entry.getValue());
          stmtUpdateRequestedQuantity.setDouble(2, distributedQuantitySums.get(entry.getKey()));
          stmtUpdateRequestedQuantity.setObject(3, entry.getKey());
          stmtUpdateRequestedQuantity.addBatch();

          // EJECUTAR UPDATE DE THEORETICAL QUANTITY DE LAS LINEAS DE ESTE ROOT
          stmtUpdateTheoreticalQuantityInLine.setObject(1, entry.getKey());
          stmtUpdateTheoreticalQuantityInLine.addBatch();

          // Actualiza status de ese root
          stmtUpdateInProgressStatus.setObject(1, entry.getKey());
          stmtUpdateInProgressStatus.addBatch();
        }

        stmtUpdateRequestedQuantity.executeBatch();
        stmtUpdateTheoreticalQuantityInLine.executeBatch();
        stmtUpdateInProgressStatus.executeBatch();
        stmtInsertShipmentWarehouse.executeBatch();

        conn.commit();
        log.info("Datos insertados correctamente en ambas tablas.");
      } catch (final SQLException e) {
        conn.rollback();
        log.error("Error al insertar los datos en la base de datos", e);
      }
    } catch (final Exception e) {
      log.error("Error al leer el archivo", e);
    }

    log.info("Resumen: \n Líneas: [{}] \n Procesadas: [{}] \n No procesadas: [{}]", totalLinesProcessed + totalLinesUnprocessed,
        totalLinesProcessed, totalLinesUnprocessed);
    printElapsed(start);
  }

  private static boolean anyRequiredFieldIsBlank(final String[] data) {
    return data[0].isBlank()
        || data[1].isBlank() || data[2].isBlank() || data[3].isBlank() ||
        data[4].isBlank() || data[5].isBlank() || data[6].isBlank() || data[7].isBlank() ||
        data[8].isBlank() || data[9].isBlank() || data[10].isBlank() ||
        data[12].isBlank();
  }

  private static String generateLineKey(final UUID referenceId, final UUID useId, final String budgetCycle, final UUID productOrderId,
      final UUID productVariantGroupId) {
    return Stream.of(referenceId, useId, budgetCycle, productOrderId, productVariantGroupId)
        .map(Object::toString)
        .collect(Collectors.joining("#"));
  }

  public static boolean isValidBudgetCycle(final String input) {
    return URN_BUDGET_CYCLE_PATTERN.matcher(input).matches();
  }

  private static boolean isValidProductOrderStatus(final String productOrderStatus) {
    return productOrderStatus.equals("DRAFT") || productOrderStatus.equals("FORMALIZED") || productOrderStatus.equals("CLOSED");
  }

  private static UUID toUUID(final String data) {
    return UUID.fromString(data.trim());
  }

  private static Timestamp fromStringUTCToTimestamp(final String date) {
    final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    final LocalDateTime localDateTime = LocalDateTime.parse(date, formatter);

    return Timestamp.from(localDateTime.toInstant(ZoneOffset.UTC));
  }

  private static void printElapsed(final long start) {
    final long elapsed = System.nanoTime() - start;
    log.info("Tiempo procesamiento (nanos): {}", NUMBER_FORMATTER.format(elapsed));
  }

}
