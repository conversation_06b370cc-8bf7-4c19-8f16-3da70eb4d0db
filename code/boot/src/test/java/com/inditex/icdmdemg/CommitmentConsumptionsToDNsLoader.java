package com.inditex.icdmdemg;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedPlan;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedStatus;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo.Status;
import com.inditex.icdmdemg.shared.utils.FasterxmlUuidGenerator;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <pre>
 * 1) crear túnel azure: azpro
 * 2) crear túnel SSH:
 * ssh -L 5555:iopcompclo-psqlf-weu1-pro-001-icdmdbt.postgres.database.azure.com:5432 jumpboxdev@localhost -p 9922
 * ssh -L 5555:iopcompclo-psqlf-weu1-pre-101-icdmdbt.postgres.database.azure.com:5432 jumpboxdev@localhost -p 2222
 *
 * 3) ejecutar con el CSV (en code/target) y los datos de conexión jdbc a localhost:5555
 * </pre>
 */
public class CommitmentConsumptionsToDNsLoader {

  private static final Logger log = LoggerFactory.getLogger(CommitmentConsumptionsToDNsLoader.class);

  private static final String URL = "******************************************";

  private static final String USER = "postgres";

  private static final String PASSWORD = "postgres-pwd";

  private static final String CSV_DELIMITER = ",";

  private static final int CSV_NUMBER_OF_COLUMNS = 17;

  private static final UuidGenerator UUID_GENERATOR = new FasterxmlUuidGenerator();

  private static final NumberFormat NUMBER_FORMATTER = NumberFormat.getInstance();

  private static final String TABLE_DN = "distribution_nominated";

  private static final String TABLE_DN_LINE = "distribution_nominated_line";

  private static final String FILE_NAME = "archivo.csv";

  private static final String CREATED_BY = "manual";

  private static final String PLAN = DistributionNominatedPlan.PRESELECTED.name();

  public static void main(final String[] args) {
    final long start = System.nanoTime();
    final String targetDir = System.getProperty("user.dir") + File.separator + "code" + File.separator + "target" + File.separator + "%s";
    final String csvFile = targetDir.formatted(FILE_NAME);
    String line;

    // Map para almacenar ids agrupados por los campos clave
    final Map<String, UUID> distributionNominatedMap = new HashMap<>();
    // Sumas acumuladas para cada distribution_nominated
    final Map<UUID, Double> requestedQuantitySums = new HashMap<>();
    // Contador de lineas procesadas y no procesadas
    int totalLinesProcessed = 0;
    int totalLinesUnprocessed = 0;

    try (final Connection conn = DriverManager.getConnection(URL, USER, PASSWORD);
        final BufferedReader br = new BufferedReader(new FileReader(csvFile))) {

      conn.setAutoCommit(false);

      // Consultar si existe un registro en distribution_nominated
      final String checkExistingDistributionNominated = """
          SELECT id
          FROM debtmg.%s
          WHERE reference_id = ? AND use_id = ? AND budget_cycle = ? AND product_order_id = ? AND product_variant_group_id = ?
          """.formatted(TABLE_DN);

      // Consultar si existe un registro en distribution_nominated_line
      final String checkExistingDistributionNominatedLine = """
          SELECT id
          FROM debtmg.%s
          WHERE distribution_nominated_id = ? AND commitment_order_id = ? AND commitment_order_line_id = ?
          """.formatted(TABLE_DN_LINE);

      // Insert para distribution_nominated
      final String insertDistributionNominated = """
          INSERT INTO debtmg.%s
          (id, reference_id, use_id, budget_cycle, product_order_id, product_variant_group_id,
          theoretical_quantity, consumption_factor, requested_quantity, distributed_quantity, status,
          "version", created_by, created_at, updated_by, updated_at, deleted_at, product_order_supplier_id,
          reference_product_id, plan)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 0, ?, 0, '%s', NOW(), '%s', NOW(), NULL, ?, ?, '%s')
          """.formatted(TABLE_DN, CREATED_BY, CREATED_BY, PLAN);

      // Insert para distribution_nominated_line
      final String insertDistributionNominatedLine = """
          INSERT INTO debtmg.%s
          (id, distribution_nominated_id, commitment_order_id, commitment_order_line_id,
          theoretical_quantity, requested_quantity, distributed_quantity, created_at, updated_at,
          alternative_reference_id, alternative_requested_quantity, alternative_theoretical_quantity,
          commitment_order_supplier_id)
          VALUES (?, ?, ?, ?, ?, ?, 0, NOW(), NOW(), NULL, 0, 0, ?)
          """.formatted(TABLE_DN_LINE);

      // Actualizar requested_quantity en distribution_nominated
      final String sqlUpdateRequestedQuantity = "UPDATE debtmg.%s SET requested_quantity = ? WHERE id = ?".formatted(TABLE_DN);

      try (final PreparedStatement stmtCheckExisting = conn.prepareStatement(checkExistingDistributionNominated);
          final PreparedStatement stmtCheckExistingLine = conn.prepareStatement(checkExistingDistributionNominatedLine);
          final PreparedStatement stmtNominated = conn.prepareStatement(insertDistributionNominated);
          final PreparedStatement stmtNominatedLine = conn.prepareStatement(insertDistributionNominatedLine);
          final PreparedStatement stmtUpdateRequestedQuantity = conn.prepareStatement(sqlUpdateRequestedQuantity)) {

        boolean isFirstLine = true; // Controla la primera línea (cabeceras)

        while ((line = br.readLine()) != null) {
          if (isFirstLine) {
            isFirstLine = false;
            continue;
          }
          final String[] data = line.split(CSV_DELIMITER);

          // Verifica si hay algún campo vacío y asegura que tenga 16 campos
          if (data.length != CSV_NUMBER_OF_COLUMNS || Stream.of(data).anyMatch(String::isBlank)) {
            log.warn("Línea incorrecta: {}", line);
            totalLinesUnprocessed++;
            continue;
          }

          final UUID lineId = UUID.fromString(data[0]);
          final UUID referenceId = UUID.fromString(data[1]);
          final UUID useId = UUID.fromString(data[2]);
          final String budgetCycle = data[3];
          final UUID productOrderId = UUID.fromString(data[4]);
          final String estadoPedido = data[5];
          final UUID productVariantGroupId = UUID.fromString(data[6]);
          final double unidadesPedidoColor = Double.parseDouble(data[7]);
          final double consumoUnitario = Double.parseDouble(data[8]);
          final UUID commitmentOrderId = UUID.fromString(data[9]);
          final UUID commitmentOrderLineId = UUID.fromString(data[10]);
          final double consumoTeorico = Double.parseDouble(data[11]);
          final double cantidadDn = Double.parseDouble(data[12]);
          final String commitmentOrderSupplierId = data[13];
          final String productOrderSupplierId = data[14];
          final boolean productOrderPublished = Boolean.parseBoolean(data[15]);
          final UUID referenceProductId = UUID.fromString(data[16]);

          // Generar clave única para mapear distribución nominada
          final String key = generateLineKey(referenceId, useId, budgetCycle, productOrderId, productVariantGroupId);
          final UUID distributionNominatedId;

          // Verificar si ya existe en el mapa (o en la tabla)
          if (distributionNominatedMap.containsKey(key)) {
            distributionNominatedId = distributionNominatedMap.get(key);
          } else {
            // Calcular el estado de distribution_nominated
            final String distributionStatus =
                DistributionNominatedStatus.of(new ProductOrderStatusInfo(Status.valueOf(estadoPedido), productOrderPublished),
                    new DistributedQuantity(BigDecimal.ZERO)).value();

            // Consultar en la base de datos
            stmtCheckExisting.setObject(1, referenceId);
            stmtCheckExisting.setObject(2, useId);
            stmtCheckExisting.setString(3, budgetCycle);
            stmtCheckExisting.setObject(4, productOrderId);
            stmtCheckExisting.setObject(5, productVariantGroupId);

            final ResultSet rs = stmtCheckExisting.executeQuery();
            if (rs.next()) {
              // Si existe, obtenemos el ID
              distributionNominatedId = (UUID) rs.getObject("id");

              // Validar si ya existe línea con el mismo compromiso en el DN
              stmtCheckExistingLine.setObject(1, distributionNominatedId);
              stmtCheckExistingLine.setObject(2, commitmentOrderId);
              stmtCheckExistingLine.setObject(3, commitmentOrderLineId);

              final ResultSet rsLine = stmtCheckExistingLine.executeQuery();
              if (rsLine.next()) {
                log.warn("Línea ya existe: {}", line);
                totalLinesUnprocessed++;
                rsLine.close();
                continue;
              }
            } else {
              // Si no existe, creamos una nueva fila en distribution_nominated
              distributionNominatedId = UUID_GENERATOR.generate();
              stmtNominated.setObject(1, distributionNominatedId);
              stmtNominated.setObject(2, referenceId);
              stmtNominated.setObject(3, useId);
              stmtNominated.setString(4, budgetCycle);
              stmtNominated.setObject(5, productOrderId);
              stmtNominated.setObject(6, productVariantGroupId);
              final var theoreticalQuantity =
                  BigDecimal.valueOf(consumoUnitario).multiply(BigDecimal.valueOf(unidadesPedidoColor)).doubleValue();
              stmtNominated.setDouble(7, theoreticalQuantity);
              stmtNominated.setDouble(8, consumoUnitario);
              stmtNominated.setDouble(9, 0); // requested_quantity
              stmtNominated.setString(10, distributionStatus);
              stmtNominated.setString(11, productOrderSupplierId);
              stmtNominated.setObject(12, referenceProductId);

              stmtNominated.executeUpdate(); // Ejecutar la inserción
            }
            rs.close();
            distributionNominatedMap.put(key, distributionNominatedId); // Guardar en el mapa
            requestedQuantitySums.put(distributionNominatedId, 0.0); // Inicializa la suma en 0
          }

          // Actualiza la suma de requested_quantity para la línea correspondiente
          requestedQuantitySums.compute(distributionNominatedId, (uuid, requested) -> requested + cantidadDn);

          // Insertar en distribution_nominated_line
          stmtNominatedLine.setObject(1, lineId);
          stmtNominatedLine.setObject(2, distributionNominatedId); // ID de distribution_nominated
          stmtNominatedLine.setObject(3, commitmentOrderId);
          stmtNominatedLine.setObject(4, commitmentOrderLineId);
          stmtNominatedLine.setDouble(5, consumoTeorico);
          stmtNominatedLine.setDouble(6, cantidadDn);
          stmtNominatedLine.setObject(7, commitmentOrderSupplierId);

          stmtNominatedLine.addBatch(); // Añadir al batch
          totalLinesProcessed++;
        }

        // Actualiza `requested_quantity` en `distribution_nominated` con las sumas acumuladas
        for (final Map.Entry<UUID, Double> entry : requestedQuantitySums.entrySet()) {
          stmtUpdateRequestedQuantity.setDouble(1, entry.getValue());
          stmtUpdateRequestedQuantity.setObject(2, entry.getKey());
          stmtUpdateRequestedQuantity.addBatch();
        }

        stmtUpdateRequestedQuantity.executeBatch();

        // Ejecutar batch y commit
        stmtNominatedLine.executeBatch();
        conn.commit();
        log.info("Datos insertados correctamente en ambas tablas.");
      } catch (final SQLException e) {
        conn.rollback();
        log.error("Error al insertar los datos en la base de datos", e);
      }
    } catch (final Exception e) {
      log.error("Error al leer el archivo", e);
    }

    log.info("Resumen: \n Líneas: [{}] \n Procesadas: [{}] \n No procesadas: [{}]", totalLinesProcessed + totalLinesUnprocessed,
        totalLinesProcessed, totalLinesUnprocessed);
    printElapsed(start);
  }

  private static String generateLineKey(final UUID referenceId, final UUID useId, final String budgetCycle, final UUID productOrderId,
      final UUID productVariantGroupId) {
    return Stream.of(referenceId, useId, budgetCycle, productOrderId, productVariantGroupId)
        .map(Object::toString)
        .collect(Collectors.joining("#"));
  }

  private static void printElapsed(final long start) {
    final long elapsed = System.nanoTime() - start;
    log.info("Tiempo procesamiento (nanos): {}", NUMBER_FORMATTER.format(elapsed));
  }

}
