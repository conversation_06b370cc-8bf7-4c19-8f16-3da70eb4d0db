package bulk;

import java.io.File;
import java.io.FileWriter;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.stream.IntStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DistributionInnerGenerator {

  private static final Logger log = LoggerFactory.getLogger(DistributionInnerGenerator.class);

  // para cargar los ficheros en postgres:
  // NOSONAR ejecutar: COPY debtmg.distribution_nominated FROM '/ruta/a/tbl_use_product.csv' DELIMITER ',' CSV HEADER;

  // para cargar los fichero en dbeaver:
  // click derecho en la tabla -> import data from file -> seleccionar el archivo -> next -> next -> next -> finish
  // marcar Use bulk load, desmarcar Use Transactions

  public static void main(final String[] args) {
    final String targetDir = System.getProperty("user.dir") + File.separator + "code" + File.separator + "target" + File.separator + "%s";
    final String distributionInnerPath = targetDir.formatted("tbl_distribution_inner.csv");

    // Número de Dns a generar
    final int dnRecordsNumber = 1000;

    try (final FileWriter writer = new FileWriter(distributionInnerPath)) {
      writer.append(
          "id,reference_id,use_id,budget_cycle,reference_product_id,product_order_id,product_variant_group_id,theoretical_quantity,"
              + "consumption_factor,requested_quantity,distributed_quantity,status,version,created_by,created_at,updated_by,updated_at,deleted_at")
          .append("\n");

      final List<String> budgets = IntStream.range(0, 10).mapToObj(x -> "urn:BUDGETCYCLE:" + UUID.randomUUID()).toList();

      for (int i = 0; i < dnRecordsNumber; i++) {
        final String referenceProductId = uuid();
        for (int j = 0; j < ((int) (Math.random() * 20) + 1); j++) {
          final String budgetCycle = idFrom(budgets);
          final String id = uuid(); // Generar UUID para el ID
          final String referenceId = uuid();
          final String useId = uuid();
          final String productOrderId = uuid();
          final String productVariantGroupId = uuid();
          final String theoreticalQuantity = String.valueOf((int) (Math.random() * 1000) + 1);
          final String consumptionFactor = String.valueOf((int) (Math.random() * 100) + 1);
          final String requestedQuantity = String.valueOf((int) (Math.random() * 1000) + 1);
          final String distributedQuantity = String.valueOf((int) (Math.random() * 1000) + 1);
          final String status = "PENDING";
          final String version = "1";
          final String createdBy = "system";
          final String createdAt = nowDate();
          final String updatedBy = "system";
          final String updatedAt = nowDate();
          final String deletedAt = "";
          final String productOrderSupplierId = uuid();

          writer.append(String.join(",", id, referenceId, useId, budgetCycle, referenceProductId, productOrderId, productVariantGroupId,
              theoreticalQuantity, consumptionFactor, requestedQuantity, distributedQuantity, status, version, createdBy, createdAt,
              updatedBy,
              updatedAt, deletedAt))
              .append("\n");

          i++;
          if (i % 1000 == 0) {
            log.info("Generated {} records", i);
          }
        }
      }
      log.info("{} generated successfully.", distributionInnerPath);
    } catch (final Exception e) {
      log.error("Error generating tables", e);
    }
  }

  private static String idFrom(final List<String> ids) {
    return ids.get(new Random().nextInt(ids.size()));
  }

  private static String uuid() {
    return UUID.randomUUID().toString();
  }

  private static String nowDate() {
    return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
  }

}
