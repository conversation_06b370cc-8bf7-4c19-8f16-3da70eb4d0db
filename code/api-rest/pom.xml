<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.inditex.icdmdemg</groupId>
    <artifactId>icdmdebtmgmt</artifactId>
    <version>1.91.0-SNAPSHOT</version>
    <relativePath>..</relativePath>
  </parent>

  <artifactId>icdmdebtmgmt-api-rest</artifactId>
  <packaging>jar</packaging>

  <name>${project.groupId}:${project.artifactId}</name>
  <description>REST services definition module</description>
  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-domain</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.service</groupId>
      <artifactId>service-starter-rest</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.service</groupId>
      <artifactId>service-starter-aaa-oidc</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.icdmdemg</groupId>
      <artifactId>icdmdebtmgmt-application</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.test</groupId>
      <artifactId>test-starter-instancio</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.security</groupId>
      <artifactId>spring-security-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-hateoas</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.service</groupId>
      <artifactId>service-starter-aaa-userdetails-heimdal</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.api</groupId>
      <artifactId>icbcdemg-distribution-management-apis-rest-stable</artifactId>
      <version>${icbcdemg-distribution-management-apis-rest-stable.version}</version>
    </dependency>
    <dependency>
      <groupId>com.inditex.api</groupId>
      <artifactId>icbcdemg-distribution-admin-apis-rest-stable</artifactId>
      <version>${icbcdemg-distribution-admin-apis-rest-stable.version}</version>
    </dependency>
    <dependency>
      <groupId>com.inditex.api</groupId>
      <artifactId>icbcdemg-use-management-apis-rest-stable</artifactId>
      <version>${icbcdemg-use-management-api-rest-stable.version}</version>
    </dependency>
  </dependencies>

  <build>
    <finalName>${project.artifactId}-${project.version}</finalName>
    <plugins />
  </build>

  <reporting />
</project>
