package com.inditex.icdmdemg.apirest.mapper;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import java.util.List;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine;
import com.inditex.icdmdemg.dto.DistributionInnerDTO;
import com.inditex.icdmdemg.dto.DistributionInnerLineDTO;

import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

@Component
public class DistributionInnerDTOMapper {

  public DistributionInnerDTO toDto(@NonNull final DistributionInner distributionInner) {
    return new DistributionInnerDTO(
        distributionInner.getId().value(),
        distributionInner.referenceId().value(),
        distributionInner.useId().value(),
        distributionInner.budgetCycle().value(),
        distributionInner.productOrderId().value(),
        distributionInner.productVariantGroupId().value(),
        distributionInner.requestedQuantity().value(),
        distributionInner.distributedQuantity().value(),
        distributionInner.status().value(),
        distributionInner.theoreticalQuantity().value(),
        distributionInner.consumptionFactor().value(),
        this.getDistributionNominatedLineDTOs(distributionInner.lines().value()),
        distributionInner.pendingAssigned().value(),
        distributionInner.audit().createdAt(),
        distributionInner.audit().updatedAt());
  }

  private List<DistributionInnerLineDTO> getDistributionNominatedLineDTOs(final List<DistributionInnerLine> domainLines) {
    return domainLines.stream()
        .map(line -> {
          final var lineDTO = new DistributionInnerLineDTO(
              line.id().value(),
              line.requestedQuantity().value(),
              line.distributedQuantity().value(),
              line.audit().createdAt(),
              line.audit().updatedAt());
          lineDTO.setTrackingCode(acceptNullElseMap(line.trackingCode(), DistributionInnerLine.TrackingCode::value));
          lineDTO.setDistributionStartDate(
              acceptNullElseMap(line.distributionStartDate(), DistributionInnerLine.DistributionStartDate::value));
          lineDTO.setDistributionEndDate(acceptNullElseMap(line.distributionEndDate(), DistributionInnerLine.DistributionEndDate::value));
          return lineDTO;
        })
        .toList();
  }

}
