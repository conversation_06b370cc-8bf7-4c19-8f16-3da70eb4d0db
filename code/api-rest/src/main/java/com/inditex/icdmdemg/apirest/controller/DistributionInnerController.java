package com.inditex.icdmdemg.apirest.controller;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.apirest.config.UserContext;
import com.inditex.icdmdemg.apirest.mapper.DistributionInnerDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.DistributionInnerRequestDtoMapper;
import com.inditex.icdmdemg.apirest.pagination.PageMapper;
import com.inditex.icdmdemg.application.distributioninner.command.CloseDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributioninner.command.CreateDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributioninner.command.CreateDistributionInnerCommand.InnerRequest;
import com.inditex.icdmdemg.application.distributioninner.command.CreateDistributionInnerResult;
import com.inditex.icdmdemg.application.distributioninner.command.DeleteDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributioninner.command.PatchDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributioninner.command.RevertClosedDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributioninner.command.SendToDistributionDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributioninner.query.GetDistributionInnerByCriteriaQuery;
import com.inditex.icdmdemg.application.distributioninner.query.GetDistributionInnerByIdQuery;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.dto.DistributionInnerDTO;
import com.inditex.icdmdemg.dto.DistributionInnerRequestDTO;
import com.inditex.icdmdemg.dto.DistributionInnerRequestPatchDTO;
import com.inditex.icdmdemg.dto.DistributionsInnerPostResponseDTO;
import com.inditex.icdmdemg.dto.DistributionsInnerResponseDTO;
import com.inditex.icdmdemg.service.InnerApi;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;
import com.inditex.iopcmmnt.cqrs.core.QueryBus;

import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class DistributionInnerController implements InnerApi {

  private final QueryBus queryBus;

  private final DistributionInnerRequestDtoMapper requestMapper;

  private final DistributionInnerDTOMapper dtoMapper;

  private final PageMapper<DistributionInner, DistributionInnerDTO> pageMapper;

  private final UserContext userContext;

  private final CommandBus commandBus;

  @Override
  @PreAuthorize("hasAuthority('DistributionInner.Read')")
  public ResponseEntity<DistributionInnerDTO> distributionsInnerGetById(final UUID distributionInnerId) {
    final var distributionInner =
        this.queryBus.ask(new GetDistributionInnerByIdQuery(distributionInnerId)).responseOrThrowIfError();
    return ResponseEntity.ok(this.dtoMapper.toDto(distributionInner));
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionInner.Read')")
  public ResponseEntity<DistributionsInnerResponseDTO> distributionsInnerGet(final List<UUID> referenceIds,
      final List<UUID> productOrderIds, final List<String> budgetCycles, final List<String> status, final Integer page,
      final Integer size) {
    final var distributions = this.queryBus.ask(
        new GetDistributionInnerByCriteriaQuery(referenceIds, productOrderIds, budgetCycles, status, page, size));
    final var paginated = this.pageMapper.paginated(distributions, this.dtoMapper::toDto, true);
    return ResponseEntity.ok(
        new DistributionsInnerResponseDTO()
            .data(paginated.data())
            .pagination(paginated.pagination().toPaginationDto()));
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionInner.Write')")
  public ResponseEntity<DistributionsInnerPostResponseDTO> distributionsInnerPost(
      final DistributionInnerRequestDTO distributionInnerRequestDTO) {
    final InnerRequest request = this.requestMapper.toDomain(distributionInnerRequestDTO, this.userContext.getCurrentUser());
    final CreateDistributionInnerResult result = this.commandBus.execute(new CreateDistributionInnerCommand(request));
    final var distributionInnerId = result.distributionInnerId().value();
    return ResponseEntity.status(HttpStatus.CREATED).body((new DistributionsInnerPostResponseDTO()
        .distributionInnerId(distributionInnerId)));
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionInner.Write')")
  public ResponseEntity<Void> distributionsInnerDelete(final UUID distributionInnerId) {
    final var request = new DeleteDistributionInnerCommand.DeleteInnerRequest(distributionInnerId, this.userContext.getCurrentUser());
    this.commandBus.execute(new DeleteDistributionInnerCommand(request));
    return ResponseEntity.noContent().build();
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionInner.Write')")
  public ResponseEntity<Void> distributionsInnerPatch(final UUID distributionInnerId,
      final DistributionInnerRequestPatchDTO distributionInnerRequestPatchDTO) {
    this.commandBus
        .execute(new PatchDistributionInnerCommand(distributionInnerId, distributionInnerRequestPatchDTO.getTheoreticalQuantity(),
            distributionInnerRequestPatchDTO.getRequestedQuantity(), distributionInnerRequestPatchDTO.getConsumptionFactor(),
            this.userContext.getCurrentUser()));
    return ResponseEntity.noContent().build();
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionInner.Write')")
  public ResponseEntity<Void> distributionsInnerSendToDistribution(final UUID distributionInnerId) {
    this.commandBus.execute(new SendToDistributionDistributionInnerCommand(distributionInnerId, this.userContext.getCurrentUser()));
    return ResponseEntity.noContent().build();
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionInner.Write')")
  public ResponseEntity<Void> closeDistributionsInner(final UUID distributionInnerId) {
    this.commandBus.execute(new CloseDistributionInnerCommand(distributionInnerId, this.userContext.getCurrentUser()));
    return ResponseEntity.noContent().build();
  }

  @Override
  public ResponseEntity<Void> revertClosedDistributionInner(UUID distributionInnerId) {
    this.commandBus.execute(new RevertClosedDistributionInnerCommand(distributionInnerId, this.userContext.getCurrentUser()));
    return ResponseEntity.noContent().build();
  }
}
