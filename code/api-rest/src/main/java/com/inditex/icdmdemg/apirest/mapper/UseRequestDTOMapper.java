package com.inditex.icdmdemg.apirest.mapper;

import java.util.List;

import com.inditex.icdmdemg.application.use.command.CreateUseCommand.UseRequest;
import com.inditex.icdmdemg.application.use.command.CreateUseCommand.UseRequest.Condition;
import com.inditex.icdmdemg.application.use.command.PatchUseCommand.AssignablePatchRequest;
import com.inditex.icdmdemg.application.use.command.UseRequestNames;
import com.inditex.icdmdemg.application.use.command.UseRequestNames.UseRequestName;
import com.inditex.icdmdemg.dto.AssignablePatchDTO;
import com.inditex.icdmdemg.dto.NamePairsDTO;
import com.inditex.icdmdemg.dto.UseRequestDTO;
import com.inditex.icdmdemg.shared.utils.LocaleUtils;

import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

@Component
public class UseRequestDTOMapper {

  public UseRequest toDomain(@NonNull final UseRequestDTO useRequestDTO, @NonNull final String triggeredBy) {

    return new UseRequest(
        this.getNamesAndTranslations(useRequestDTO),
        List.copyOf(useRequestDTO.getAssignable()),
        useRequestDTO.getPurchasePurpose().getTaxonomy(),
        useRequestDTO.getPurchasePurpose().getCustomer(),
        List.copyOf(useRequestDTO.getPurchasePurpose().getPurchaseTypes()),
        this.getUseRequestConditions(useRequestDTO),
        triggeredBy);
  }

  private UseRequestNames getNamesAndTranslations(final UseRequestDTO useRequestDTO) {
    return this.toUseRequestNames(useRequestDTO.getNames());
  }

  private List<Condition> getUseRequestConditions(final UseRequestDTO useRequestDTO) {
    return useRequestDTO.getPurchasePurpose().getConditionParameters().stream().map(
        condition -> new Condition(condition.getCondition(), condition.getParameterConditionName(),
            condition.getParameterConditionValues()))
        .toList();
  }

  public UseRequestNames toUseRequestNames(final List<NamePairsDTO> names) {
    return new UseRequestNames(names.stream()
        .map(name -> new UseRequestName(LocaleUtils.createLocale(name.getLocale()), name.getTranslation()))
        .toList());
  }

  public List<String> toPatchRequestAssignable(final AssignablePatchDTO assignablePatchDTO) {
    return assignablePatchDTO == null ? null : AssignablePatchRequest.valueOf(assignablePatchDTO.getValue()).getStringList();
  }
}
