package com.inditex.icdmdemg.apirest.config;

import com.inditex.amigafwk.service.aaa.userdetails.heimdal.HeimdalUser;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
public class HeimdalUserContext implements UserContext {

  public String getCurrentUser() {
    final Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null && authentication.isAuthenticated()) {
      if (authentication.getPrincipal() instanceof HeimdalUser heimdalUser) {
        return heimdalUser.getUsername();
      }
      return authentication.getName();
    }
    return ANONYMOUS_USER;
  }

}
