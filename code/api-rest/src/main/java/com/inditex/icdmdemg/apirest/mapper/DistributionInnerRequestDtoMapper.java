package com.inditex.icdmdemg.apirest.mapper;

import com.inditex.icdmdemg.application.distributioninner.command.CreateDistributionInnerCommand.InnerRequest;
import com.inditex.icdmdemg.dto.DistributionInnerRequestDTO;

import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

@Component
public class DistributionInnerRequestDtoMapper {

  public InnerRequest toDomain(
      @NonNull final DistributionInnerRequestDTO distributionInnerRequestDTO,
      @NonNull final String triggeredBy) {
    return new InnerRequest(
        distributionInnerRequestDTO.getReferenceId(),
        distributionInnerRequestDTO.getUseId(),
        distributionInnerRequestDTO.getBudgetCycle(),
        distributionInnerRequestDTO.getProductOrderId(),
        distributionInnerRequestDTO.getProductVariantGroupId(),
        distributionInnerRequestDTO.getTheoreticalQuantity(),
        distributionInnerRequestDTO.getRequestedQuantity(),
        distributionInnerRequestDTO.getConsumptionFactor(),
        distributionInnerRequestDTO.getSendToDistribution(),
        triggeredBy);
  }

}
