package com.inditex.icdmdemg.apirest.pagination;

import com.inditex.icdmdemg.dto.PaginationDTO;
import com.inditex.icdmdemg.dto.PaginationLinksDTO;
import com.inditex.icdmdemg.shared.utils.Nullables;

import org.springframework.hateoas.Link;

public record Pagination(Integer page, Integer size, PaginationLinks links) {

  public PaginationDTO toPaginationDto() {
    return new PaginationDTO()
        .page(this.page)
        .size(this.size)
        .links(new PaginationLinksDTO()
            .self(Nullables.acceptNullElseMap(this.links.self(), Link::getHref))
            .prev(Nullables.acceptNullElseMap(this.links.prev(), Link::getHref))
            .next(Nullables.acceptNullElseMap(this.links.next(), Link::getHref))
            .first(Nullables.acceptNullElseMap(this.links.first(), Link::getHref)));
  }
}
