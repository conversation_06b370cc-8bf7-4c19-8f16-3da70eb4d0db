package com.inditex.icdmdemg.apirest.controller;

import java.util.UUID;

import com.inditex.icdmdemg.apirest.config.UserContext;
import com.inditex.icdmdemg.apirest.mapper.DistributionInnerAdminRequestDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.DistributionNominatedAdminRequestDTOMapper;
import com.inditex.icdmdemg.application.commitmentuse.command.CalculateNominatedProvisionCommand;
import com.inditex.icdmdemg.application.distributioninner.command.SendAdminEventDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.RegularizeDistributionNominatedBudgetCycleChangeCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.SendAdminEventDistributionNominatedCommand;
import com.inditex.icdmdemg.application.process.ShipmentWarehouseProcessManager;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCreatedCommand.ShipmentWarehouseCreated;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseStartedCommand.ShipmentWarehouseStarted;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseUpdatedCommand.ShipmentWarehouseUpdated;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.dto.CalculateNominatedProvisionRequestDTO;
import com.inditex.icdmdemg.dto.DistributionInnerAdminPostRequestDTO;
import com.inditex.icdmdemg.dto.DistributionNominatedAdminPostRequestDTO;
import com.inditex.icdmdemg.dto.DistributionsInnerAdminPostResponseDTO;
import com.inditex.icdmdemg.dto.DistributionsNominatedAdminPostResponseDTO;
import com.inditex.icdmdemg.dto.RegularizeBudgetCycleChangeRequestDTO;
import com.inditex.icdmdemg.dto.ShipmentWarehouseDeliveryPostRequestDTO;
import com.inditex.icdmdemg.dto.ShipmentWarehousePatchRequestDTO;
import com.inditex.icdmdemg.dto.ShipmentWarehousePostRequestDTO;
import com.inditex.icdmdemg.service.DistributionAdminApi;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class DistributionAdminController implements DistributionAdminApi {

  private final CommandBus commandBus;

  private final DistributionNominatedAdminRequestDTOMapper distributionNominatedAdminRequestDTOMapper;

  private final DistributionInnerAdminRequestDTOMapper distributionInnerAdminRequestDTOMapper;

  private final ShipmentWarehouseProcessManager shipmentWarehouseProcessManager;

  private final UserContext userContext;

  @Override
  @PreAuthorize("hasAuthority('Distributions.Admin')")
  public ResponseEntity<DistributionsNominatedAdminPostResponseDTO> distributionsNominatedAdminPost(final UUID distributionNominatedId,
      final DistributionNominatedAdminPostRequestDTO distributionNominatedAdminPostRequestDTO) {
    final var request = this.distributionNominatedAdminRequestDTOMapper.toDomain(distributionNominatedId,
        distributionNominatedAdminPostRequestDTO);
    final var result = this.commandBus.execute(new SendAdminEventDistributionNominatedCommand(request));
    return ResponseEntity.status(HttpStatus.CREATED).body(new DistributionsNominatedAdminPostResponseDTO()
        .distributionNominatedId(result.distributionNominatedId().value()));
  }

  @Override
  @PreAuthorize("hasAuthority('Distributions.Admin')")
  public ResponseEntity<Void> calculateNominatedProvisionPost(
      final CalculateNominatedProvisionRequestDTO calculateNominatedProvisionRequestDTO) {
    final var sharedRawMaterials = calculateNominatedProvisionRequestDTO.getSharedRawMaterials().stream()
        .map(dto -> new SharedRawMaterialNominated(dto.getReferenceId(), dto.getUseId(), dto.getBudgetCycle()))
        .toList();
    this.commandBus.execute(new CalculateNominatedProvisionCommand(sharedRawMaterials, this.userContext.getCurrentUser()));
    return ResponseEntity.noContent().build();
  }

  @Override
  @PreAuthorize("hasAuthority('Distributions.Admin')")
  public ResponseEntity<DistributionsInnerAdminPostResponseDTO> distributionsInnerAdminPost(final UUID distributionInnerId,
      final DistributionInnerAdminPostRequestDTO distributionInnerAdminPostRequestDTO) {
    final var request = this.distributionInnerAdminRequestDTOMapper.toDomain(distributionInnerId,
        distributionInnerAdminPostRequestDTO);
    final var result = this.commandBus.execute(new SendAdminEventDistributionInnerCommand(request));
    return ResponseEntity.status(HttpStatus.CREATED).body(new DistributionsInnerAdminPostResponseDTO()
        .distributionInnerId(result.distributionInnerId().value()));
  }

  @Override
  @PreAuthorize("hasAuthority('Distributions.Admin')")
  public ResponseEntity<Void> shipmentWarehouseDeliveryPost(final String distributionTrackingCode,
      final ShipmentWarehouseDeliveryPostRequestDTO shipmentWarehouseDeliveryPostRequestDTO) {
    final var shipmentWarehouseUpdated =
        new ShipmentWarehouseUpdated(distributionTrackingCode, shipmentWarehouseDeliveryPostRequestDTO.getQuantity(),
            shipmentWarehouseDeliveryPostRequestDTO.getDistributionLastUpdateDate());
    this.shipmentWarehouseProcessManager.execute(shipmentWarehouseUpdated);
    return ResponseEntity.noContent().build();
  }

  @Override
  @PreAuthorize("hasAuthority('Distributions.Admin')")
  public ResponseEntity<Void> shipmentWarehousePost(final ShipmentWarehousePostRequestDTO shipmentWarehousePostRequestDTO) {
    final var shipmentWarehouseCreated =
        new ShipmentWarehouseCreated(shipmentWarehousePostRequestDTO.getDistributionInnerId(),
            shipmentWarehousePostRequestDTO.getDistributionInnerLineId(), shipmentWarehousePostRequestDTO.getDistributionTrackingCode());
    this.shipmentWarehouseProcessManager.execute(shipmentWarehouseCreated);
    return ResponseEntity.noContent().build();
  }

  @Override
  @PreAuthorize("hasAuthority('Distributions.Admin')")
  public ResponseEntity<Void> shipmentWarehousePatch(final String distributionTrackingCode,
      final ShipmentWarehousePatchRequestDTO shipmentWarehousePatchRequestDTO) {
    final var shipmentWarehouseStarted =
        new ShipmentWarehouseStarted(distributionTrackingCode,
            shipmentWarehousePatchRequestDTO.getDistributionStartDate());
    this.shipmentWarehouseProcessManager.execute(shipmentWarehouseStarted);
    return ResponseEntity.noContent().build();
  }

  @Override
  @PreAuthorize("hasAuthority('Distributions.Admin')")
  public ResponseEntity<Void> regularizeBudgetCycleChangePost(
      final RegularizeBudgetCycleChangeRequestDTO regularizeBudgetCycleChangeRequestDTO) {
    final var sharedRawMaterials = regularizeBudgetCycleChangeRequestDTO.getSharedRawMaterials().stream()
        .map(dto -> new SharedRawMaterialNominated(dto.getReferenceId(), dto.getUseId(), dto.getBudgetCycle()))
        .toList();
    this.commandBus
        .execute(new RegularizeDistributionNominatedBudgetCycleChangeCommand(sharedRawMaterials, this.userContext.getCurrentUser()));
    return ResponseEntity.noContent().build();
  }
}
