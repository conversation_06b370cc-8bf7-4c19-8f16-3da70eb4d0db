package com.inditex.icdmdemg.apirest.mapper;

import com.inditex.icdmdemg.application.distributionnominated.query.GetAvailableCommitmentsQuery;
import com.inditex.icdmdemg.dto.CommitmentAvailabilityDTO;

import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

@Component
public class AvailableCommitmentDTOMapper {

  public CommitmentAvailabilityDTO toDto(final GetAvailableCommitmentsQuery.@NonNull AvailableCommitment availableCommitment) {
    return new CommitmentAvailabilityDTO(
        availableCommitment.commitmentOrderId(),
        availableCommitment.commitmentOrderLineId(),
        availableCommitment.expectedDate(),
        availableCommitment.locationId(),
        availableCommitment.availableQuantity());
  }

}
