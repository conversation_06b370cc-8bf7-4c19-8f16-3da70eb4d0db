package com.inditex.icdmdemg.apirest.controller;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.apirest.config.UserContext;
import com.inditex.icdmdemg.apirest.mapper.UseDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.UseRequestDTOMapper;
import com.inditex.icdmdemg.apirest.pagination.PageMapper;
import com.inditex.icdmdemg.application.use.command.CreateUseCommand;
import com.inditex.icdmdemg.application.use.command.CreateUseCommand.UseRequest;
import com.inditex.icdmdemg.application.use.command.CreateUseResult;
import com.inditex.icdmdemg.application.use.command.PatchUseCommand;
import com.inditex.icdmdemg.application.use.query.GetUseByIdsQuery;
import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.dto.UseDTO;
import com.inditex.icdmdemg.dto.UsePatchRequestDTO;
import com.inditex.icdmdemg.dto.UsePostResponseDTO;
import com.inditex.icdmdemg.dto.UseRequestDTO;
import com.inditex.icdmdemg.dto.UsesResponseDTO;
import com.inditex.icdmdemg.service.UseApi;
import com.inditex.icdmdemg.shared.utils.LocaleUtils;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;
import com.inditex.iopcmmnt.cqrs.core.QueryBus;

import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class UseController implements UseApi {

  private final UseRequestDTOMapper useRequestDTOMapper;

  private final UseDTOMapper useDTOMapper;

  private final UserContext userContext;

  private final CommandBus commandBus;

  private final QueryBus queryBus;

  private final PageMapper<Use, UseDTO> pageMapper;

  @Override
  @PreAuthorize("hasAuthority('Use.Write')")
  public ResponseEntity<UsePostResponseDTO> usePost(final UseRequestDTO useRequestDTO) {

    final UseRequest request = this.useRequestDTOMapper.toDomain(useRequestDTO, this.userContext.getCurrentUser());

    final CreateUseResult result = this.commandBus.execute(new CreateUseCommand(request));
    final var useId = result.useId().value();

    return ResponseEntity.status(HttpStatus.CREATED).body(new UsePostResponseDTO().useId(useId));
  }

  @Override
  @PreAuthorize("hasAuthority('Use.Read')")
  public ResponseEntity<UsesResponseDTO> usesGet(
      final @RequestHeader(value = "Accept-Language", required = false) String acceptLanguage,
      final List<UUID> useIds, final Integer page, final Integer size) {

    final var uses = this.queryBus.ask(new GetUseByIdsQuery(useIds, page, size));
    final var locale = LocaleUtils.createLocale(acceptLanguage);

    final var paginated = this.pageMapper.paginated(uses, it -> this.useDTOMapper.toDto(it, locale), true);
    return ResponseEntity.ok(
        new UsesResponseDTO()
            .data(paginated.data())
            .pagination(paginated.pagination().toPaginationDto()));
  }

  @Override
  @PreAuthorize("hasAuthority('Use.Write')")
  public ResponseEntity<Void> usePatch(final UUID useId, final UsePatchRequestDTO usePatchRequestDTO) {

    final var requestNames = this.useRequestDTOMapper.toUseRequestNames(usePatchRequestDTO.getNames());
    final var assignableFromRequest = this.useRequestDTOMapper.toPatchRequestAssignable(usePatchRequestDTO.getAssignable());

    this.commandBus.execute(
        new PatchUseCommand(useId, requestNames, assignableFromRequest,
            this.userContext.getCurrentUser()));

    return ResponseEntity.noContent().build();
  }

}
