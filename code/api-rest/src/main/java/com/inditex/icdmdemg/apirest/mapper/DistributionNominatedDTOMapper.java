package com.inditex.icdmdemg.apirest.mapper;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import java.math.BigDecimal;
import java.util.List;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.dto.DistributionNominatedDTO;
import com.inditex.icdmdemg.dto.DistributionNominatedLineDTO;
import com.inditex.icdmdemg.dto.DistributionNominatedPlanDTO;

import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

@Component
public class DistributionNominatedDTOMapper {

  public DistributionNominatedDTO toDto(@NonNull final DistributionNominated distributionNominated) {
    final var distributedQuantity = new DistributedQuantity(distributionNominated.requestedQuantity().value()).value();
    final var pendingAssigned = this.calculatePendingAssigned(
        distributionNominated.requestedQuantity().value(),
        distributedQuantity);

    return new DistributionNominatedDTO(
        distributionNominated.getId().value(),
        distributionNominated.referenceId().value(),
        distributionNominated.useId()
            .value(),
        distributionNominated.budgetCycle()
            .value(),
        distributionNominated.productOrderId().value(),
        distributionNominated.productVariantGroupId()
            .value(),
        distributionNominated.requestedQuantity().value(),
        distributedQuantity,
        distributionNominated.status().value(),
        distributionNominated.theoreticalQuantity().value(),
        distributionNominated.consumptionFactor().value(),
        this.getDistributionNominatedLineDTOs(distributionNominated.lines().value()),
        this.getDistributionNominatedPlanDTO(distributionNominated),
        pendingAssigned,
        distributionNominated.audit().createdAt(),
        distributionNominated.audit().updatedAt());
  }

  private DistributionNominatedPlanDTO getDistributionNominatedPlanDTO(final DistributionNominated distributionNominated) {
    return DistributionNominatedPlanDTO.fromValue(distributionNominated.plan().value());
  }

  private List<DistributionNominatedLineDTO> getDistributionNominatedLineDTOs(final List<DistributionNominatedLine> domainLines) {
    return domainLines.stream()
        .map(line -> {
          final var lineDTO = new DistributionNominatedLineDTO(
              line.id().value(),
              line.commitmentOrder().id().value(),
              line.commitmentOrder().lineId().value(),
              line.requestedQuantity().value(),
              new DistributedQuantity(line.requestedQuantity().value()).value(),
              line.audit().createdAt(),
              line.audit().updatedAt());
          lineDTO.setDistributionStartDate(
              acceptNullElseMap(line.distributionStartDate(), DistributionNominatedLine.DistributionStartDate::value));
          return lineDTO;
        })
        .toList();
  }

  private BigDecimal calculatePendingAssigned(final BigDecimal requestedQuantity, final BigDecimal distributedQuantity) {
    return requestedQuantity.subtract(distributedQuantity).max(BigDecimal.ZERO);
  }
}
