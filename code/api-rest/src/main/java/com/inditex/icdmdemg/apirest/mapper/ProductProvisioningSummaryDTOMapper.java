package com.inditex.icdmdemg.apirest.mapper;

import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.DistributionQuantity;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.OrderQuantity;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.Product;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.ProductProvisioningSummaryResponse;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.Provision;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.Reference;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.StockLocationQuantities;
import com.inditex.icdmdemg.dto.DistributionQuantityDTO;
import com.inditex.icdmdemg.dto.OrderQuantityDTO;
import com.inditex.icdmdemg.dto.ProductProvisionDTO;
import com.inditex.icdmdemg.dto.ProductProvisioningSummaryDTO;
import com.inditex.icdmdemg.dto.ProvisionDetailDTO;
import com.inditex.icdmdemg.dto.ReferenceProvisionDTO;
import com.inditex.icdmdemg.dto.StockLocationQuantityDTO;

import org.springframework.stereotype.Component;

@Component
public class ProductProvisioningSummaryDTOMapper {

  public ProductProvisioningSummaryDTO toDto(final ProductProvisioningSummaryResponse productProvisioningSummaryResponse) {
    return new ProductProvisioningSummaryDTO(productProvisioningSummaryResponse.products().stream().map(this::mapToDto).toList());
  }

  private ProductProvisionDTO mapToDto(final Product product) {
    return new ProductProvisionDTO(
        product.productId(),
        product.references().stream().map(this::mapToDto).toList());
  }

  private ReferenceProvisionDTO mapToDto(final Reference reference) {
    return new ReferenceProvisionDTO(
        reference.referenceId(),
        reference.budgetCycle(),
        reference.useId(),
        this.mapToDto(reference.inner()),
        this.mapToDto(reference.nominated()));
  }

  private ProvisionDetailDTO mapToDto(final Provision provision) {
    return new ProvisionDetailDTO(
        provision.stockLocationQuantities().stream().map(this::mapToDto).toList(),
        this.mapToDto(provision.orderQuantity()),
        this.mapToDto(provision.distributionQuantity()));
  }

  private StockLocationQuantityDTO mapToDto(final StockLocationQuantities stockLocationQuantities) {
    return new StockLocationQuantityDTO(
        stockLocationQuantities.locationId(),
        stockLocationQuantities.locationType(),
        stockLocationQuantities.stock());
  }

  private OrderQuantityDTO mapToDto(final OrderQuantity orderQuantity) {
    return new OrderQuantityDTO(
        orderQuantity.ordered(),
        orderQuantity.pending(),
        orderQuantity.entered());
  }

  private DistributionQuantityDTO mapToDto(final DistributionQuantity distributionQuantity) {
    return new DistributionQuantityDTO(
        distributionQuantity.requested(),
        distributionQuantity.distributed(),
        distributionQuantity.pendingAssigned());
  }

}
