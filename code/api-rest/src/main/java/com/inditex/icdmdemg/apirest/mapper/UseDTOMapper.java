package com.inditex.icdmdemg.apirest.mapper;

import java.util.Locale;
import java.util.Set;

import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues.ConditionValue;
import com.inditex.icdmdemg.dto.ConditionParametersDTO;
import com.inditex.icdmdemg.dto.PurchasePurposeDTO;
import com.inditex.icdmdemg.dto.UseDTO;

import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

@Component
public class UseDTOMapper {

  public UseDTO toDto(@NonNull final Use use, final Locale locale) {
    return new UseDTO(
        use.getId().value(),
        use.names().getNameTranslated(locale),
        use.assignable() == null ? Set.of() : Set.copyOf(use.assignable().getStringList()),
        this.buildPurchasePurposeDto(use),
        use.audit().createdAt(),
        use.audit().updatedAt());
  }

  private PurchasePurposeDTO buildPurchasePurposeDto(final Use use) {
    return new PurchasePurposeDTO(
        use.taxonomy().value(),
        use.customer().value(),
        Set.copyOf(use.purchaseType().getStringList()),
        use.conditions().value().stream()
            .map(it -> new ConditionParametersDTO(
                it.condition().name(),
                it.name().name(),
                it.values().value().stream().map(ConditionValue::value).toList()))
            .toList());
  }
}
