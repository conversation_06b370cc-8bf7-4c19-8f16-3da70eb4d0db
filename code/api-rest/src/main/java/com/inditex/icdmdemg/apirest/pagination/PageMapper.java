package com.inditex.icdmdemg.apirest.pagination;

import java.util.function.Function;

import com.inditex.icdmdemg.shared.utils.Nullables;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.data.web.SlicedResourcesAssembler;
import org.springframework.hateoas.EntityModel;
import org.springframework.hateoas.IanaLinkRelations;
import org.springframework.hateoas.SlicedModel;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PageMapper<T, R> {
  private final SlicedResourcesAssembler<T> slicedResourcesAssembler;

  public Paginated<R> paginated(final Slice<T> slice, final Function<T, R> apiDtoMapper) {
    this.slicedResourcesAssembler.setForceFirstRel(true);
    final var sliced = this.slicedResourcesAssembler.toModel(slice, entity -> EntityModel.of(apiDtoMapper.apply(entity)));
    return new Paginated<>(
        sliced.getContent().stream().map(EntityModel::getContent).toList(),
        this.buildPaginationFromPage(sliced));
  }

  public Paginated<R> paginated(final Slice<T> slice, final Function<T, R> apiDtoMapper, final boolean hideSort) {
    if (hideSort) {
      final var sortHiddenSlice = new SliceImpl<>(slice.getContent(), PageRequest.of(slice.getNumber(), slice.getSize()), slice.hasNext());
      return this.paginated(sortHiddenSlice, apiDtoMapper);
    }
    return this.paginated(slice, apiDtoMapper);
  }

  private Pagination buildPaginationFromPage(final SlicedModel<EntityModel<R>> models) {
    return new Pagination(
        Nullables.acceptNullElseMap(models.getMetadata(), md -> Math.toIntExact(md.getNumber())),
        Nullables.acceptNullElseMap(models.getMetadata(), md -> Math.toIntExact(md.getSize())),
        this.buildLinks(models));
  }

  private PaginationLinks buildLinks(final SlicedModel<EntityModel<R>> models) {
    return new PaginationLinks(
        models.getLinks().getRequiredLink(IanaLinkRelations.SELF),
        models.getLinks().getLink(IanaLinkRelations.PREV).orElse(null),
        models.getLinks().getLink(IanaLinkRelations.NEXT).orElse(null),
        models.getLinks().getRequiredLink(IanaLinkRelations.FIRST));
  }
}
