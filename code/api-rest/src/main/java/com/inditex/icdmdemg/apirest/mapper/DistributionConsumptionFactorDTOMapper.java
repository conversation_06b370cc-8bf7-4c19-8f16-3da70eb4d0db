package com.inditex.icdmdemg.apirest.mapper;

import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery.DistributionConsumptionFactor;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery.DistributionConsumptionFactorResponse;
import com.inditex.icdmdemg.dto.DistributionsConsumptionFactorDTO;
import com.inditex.icdmdemg.dto.DistributionsConsumptionFactorDTO.DistributionTypeEnum;
import com.inditex.icdmdemg.dto.DistributionsConsumptionFactorResponseDTO;

import org.springframework.stereotype.Component;

@Component
public class DistributionConsumptionFactorDTOMapper {

  public DistributionsConsumptionFactorResponseDTO toDto(
      final DistributionConsumptionFactorResponse distributionConsumptionFactorResponse) {
    return new DistributionsConsumptionFactorResponseDTO(
        distributionConsumptionFactorResponse.distributionsConsumptionFactor().stream().map(this::mapToDto).toList());
  }

  private DistributionsConsumptionFactorDTO mapToDto(final DistributionConsumptionFactor distributionConsumptionFactor) {
    return new DistributionsConsumptionFactorDTO(
        distributionConsumptionFactor.referenceId(),
        distributionConsumptionFactor.variantGroupId(),
        distributionConsumptionFactor.consumptionFactor(),
        DistributionTypeEnum.fromValue(distributionConsumptionFactor.distributionType()));
  }

}
