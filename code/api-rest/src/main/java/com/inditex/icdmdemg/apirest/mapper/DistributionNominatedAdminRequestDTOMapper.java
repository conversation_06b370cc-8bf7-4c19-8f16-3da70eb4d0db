package com.inditex.icdmdemg.apirest.mapper;

import java.util.UUID;

import com.inditex.icdmdemg.application.distributionnominated.command.SendAdminEventDistributionNominatedCommand.SendEventRequest;
import com.inditex.icdmdemg.dto.DistributionNominatedAdminPostRequestDTO;

import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

@Component
public class DistributionNominatedAdminRequestDTOMapper {

  public SendEventRequest toDomain(@NonNull final UUID distributionNominatedId,
      @NonNull final DistributionNominatedAdminPostRequestDTO distributionNominatedAdminPostRequestDTO) {
    return new SendEventRequest(distributionNominatedId,
        distributionNominatedAdminPostRequestDTO.getEventTypeToSend());
  }

}
