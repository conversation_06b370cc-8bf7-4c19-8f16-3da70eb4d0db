package com.inditex.icdmdemg.apirest.mapper;

import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailability;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityResponse;
import com.inditex.icdmdemg.dto.UseAvailabilityDTO;
import com.inditex.icdmdemg.dto.UseAvailabilityDetailDTO;
import com.inditex.icdmdemg.dto.UsesAvailabilityResponseDTO;

import org.springframework.stereotype.Component;

@Component
public class UsesAvailabilityResponseDTOMapper {

  public UsesAvailabilityResponseDTO toDto(final UsesAvailabilityResponse usesAvailabilityResponse) {
    final var usesAvailabilityResponseDTO = new UsesAvailabilityResponseDTO();
    return usesAvailabilityResponseDTO.data(usesAvailabilityResponse.data().stream().map(this::fromUsesAvailabilityData).toList());
  }

  private UseAvailabilityDTO fromUsesAvailabilityData(final UsesAvailability usesAvailability) {
    return new UseAvailabilityDTO(
        usesAvailability.useId(),
        usesAvailability.useName(),
        usesAvailability.purchaseTypes(),
        usesAvailability.assignable(),
        new UseAvailabilityDetailDTO(usesAvailability.nominated().totalQuantity()),
        new UseAvailabilityDetailDTO(usesAvailability.inner().totalQuantity()));
  }
}
