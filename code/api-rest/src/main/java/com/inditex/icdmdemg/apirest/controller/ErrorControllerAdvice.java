package com.inditex.icdmdemg.apirest.controller;

import com.inditex.amigafwk.service.rest.core.properties.WebApiConfigProperties;
import com.inditex.amigafwk.service.webmvc.exception.ProblemDetailsHandler;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.Error.MessageError;
import com.inditex.icdmdemg.domain.shared.Error.NotFound;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;

import jakarta.validation.ConstraintViolationException;
import jakarta.validation.ValidationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ProblemDetail;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.WebRequest;

@ControllerAdvice
public class ErrorControllerAdvice extends ProblemDetailsHandler {

  public static final String RESOURCE_NOT_FOUND = "Resource not found with id %s";

  public static final String BAD_REQUEST = "Bad request: %s";

  public ErrorControllerAdvice(final WebApiConfigProperties webApiConfigProperties) {
    super(webApiConfigProperties);
  }

  @ExceptionHandler(ValidationException.class)
  public ResponseEntity<Object> handleConstraintViolationException(final ConstraintViolationException ex, final WebRequest request) {
    final var httpStatus = HttpStatus.BAD_REQUEST;
    final var violations = ex.getConstraintViolations()
        .stream()
        .map(constraintViolation -> new FieldError(
            constraintViolation.getRootBeanClass().getName(),
            constraintViolation.getPropertyPath().toString(),
            constraintViolation.getMessage()))
        .toList();
    final var problemDetail = ProblemDetail.forStatusAndDetail(
        httpStatus,
        String.format(BAD_REQUEST, violations));
    return this.handleExceptionInternal(ex, problemDetail, new HttpHeaders(), httpStatus, request);
  }

  @ExceptionHandler(ErrorException.class)
  public ResponseEntity<Object> handleDomainErrorException(final ErrorException ex, final WebRequest request) {
    if (ex.error() instanceof MessageError) {
      return this.handleMessageError(ex, request);
    }
    if (ex.error() instanceof NotFound) {
      return this.handleNotFoundError(ex, request);
    }
    if (ex.error() instanceof BadRequest) {
      return this.handleBadRequestError(ex, request);
    }
    return this.handleGenericError(ex, request);
  }

  private ResponseEntity<Object> handleMessageError(final ErrorException ex, final WebRequest request) {
    final var httpStatus = HttpStatus.BAD_REQUEST;
    final var problemDetail = ProblemDetail.forStatusAndDetail(httpStatus, ((MessageError) ex.error()).reason());
    return this.handleExceptionInternal(ex, problemDetail, new HttpHeaders(), httpStatus, request);
  }

  private ResponseEntity<Object> handleNotFoundError(final ErrorException ex, final WebRequest request) {
    final var httpStatus = HttpStatus.NOT_FOUND;
    final var problemDetail = ProblemDetail.forStatusAndDetail(
        httpStatus,
        String.format(RESOURCE_NOT_FOUND, ((NotFound) ex.error()).notFoundResourceId()));
    return this.handleExceptionInternal(ex, problemDetail, new HttpHeaders(), httpStatus, request);
  }

  private ResponseEntity<Object> handleBadRequestError(final ErrorException ex, final WebRequest request) {
    final var httpStatus = HttpStatus.BAD_REQUEST;
    final var problemDetail = ProblemDetail.forStatusAndDetail(
        httpStatus,
        String.format(BAD_REQUEST, ((BadRequest) ex.error()).reason()));
    return this.handleExceptionInternal(ex, problemDetail, new HttpHeaders(), httpStatus, request);
  }

  private ResponseEntity<Object> handleGenericError(final ErrorException ex, final WebRequest request) {
    final var httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
    final var problemDetail = ProblemDetail.forStatus(httpStatus);
    return this.handleExceptionInternal(ex, problemDetail, new HttpHeaders(), httpStatus, request);
  }

}
