package com.inditex.icdmdemg.apirest.mapper;

import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityV2;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityV2Response;
import com.inditex.icdmdemg.dto.UseAvailabilityDetailDTO;
import com.inditex.icdmdemg.dto.UseAvailabilityV2DTO;
import com.inditex.icdmdemg.dto.UsesAvailabilityResponseV2DTO;

import org.jspecify.annotations.NullMarked;
import org.springframework.stereotype.Component;

@Component
@NullMarked
public class UsesAvailabilityResponseV2DTOMapper {

  public UsesAvailabilityResponseV2DTO toDto(final UsesAvailabilityV2Response usesAvailabilityResponse) {
    final var usesAvailabilityResponseDTO = new UsesAvailabilityResponseV2DTO();
    return usesAvailabilityResponseDTO.data(usesAvailabilityResponse.data().stream().map(this::fromUsesAvailabilityData).toList());
  }

  private UseAvailabilityV2DTO fromUsesAvailabilityData(final UsesAvailabilityV2 usesAvailability) {
    return new UseAvailabilityV2DTO(
        usesAvailability.useId(),
        usesAvailability.useName(),
        usesAvailability.purchaseTypes(),
        usesAvailability.assignable(),
        usesAvailability.matchPurchasePurpose(),
        new UseAvailabilityDetailDTO(usesAvailability.nominated().totalQuantity()),
        new UseAvailabilityDetailDTO(usesAvailability.inner().totalQuantity()));
  }
}
