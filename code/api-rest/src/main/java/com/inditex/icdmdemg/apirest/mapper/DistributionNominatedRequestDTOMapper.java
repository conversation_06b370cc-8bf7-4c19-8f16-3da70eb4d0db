package com.inditex.icdmdemg.apirest.mapper;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionnominated.command.CreateDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.CreateDistributionNominatedCommand.NominatedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.DeleteDistributionNominatedCommand.DeleteNominatedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PatchDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.PatchDistributionNominatedCommand.PatchNominatedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.AutoRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.PreselectedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.PreselectedRequest.LineSelectionRequest;
import com.inditex.icdmdemg.dto.DistributionNominatedRequestDTO;
import com.inditex.icdmdemg.dto.DistributionNominatedRequestPatchDTO;
import com.inditex.icdmdemg.dto.LineDTO;
import com.inditex.icdmdemg.dto.PlanDTO;

import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

@Component
public class DistributionNominatedRequestDTOMapper {

  public CreateDistributionNominatedCommand toCreateCommand(@NonNull final DistributionNominatedRequestDTO distributionNominatedRequestDTO,
      @NonNull final String triggeredBy) {
    return new CreateDistributionNominatedCommand(
        this.toCreateRequest(distributionNominatedRequestDTO, triggeredBy),
        this.toPlanRequest(distributionNominatedRequestDTO.getPlan()));
  }

  public PatchDistributionNominatedCommand toPatchCommand(@NonNull final UUID distributionNominatedId,
      @NonNull final DistributionNominatedRequestPatchDTO distributionNominatedRequestDTO,
      @NonNull final String triggeredBy) {
    return new PatchDistributionNominatedCommand(
        this.toModifyRequest(distributionNominatedId, distributionNominatedRequestDTO, triggeredBy),
        this.toPlanRequest(distributionNominatedRequestDTO.getPlan()));
  }

  public DeleteNominatedRequest toDomainDelete(final UUID distributionNominatedId, final String triggeredBy) {
    return new DeleteNominatedRequest(distributionNominatedId, triggeredBy);
  }

  private PatchNominatedRequest toModifyRequest(@NonNull final UUID distributionNominatedId,
      @NonNull final DistributionNominatedRequestPatchDTO distributionNominatedRequestDTO,
      @NonNull final String triggeredBy) {
    return new PatchNominatedRequest(distributionNominatedId,
        distributionNominatedRequestDTO.getTheoreticalQuantity(),
        distributionNominatedRequestDTO.getRequestedQuantity(),
        distributionNominatedRequestDTO.getConsumptionFactor(), triggeredBy);
  }

  private NominatedRequest toCreateRequest(
      @NonNull final DistributionNominatedRequestDTO distributionNominatedRequestDTO,
      @NonNull final String triggeredBy) {
    return new NominatedRequest(
        distributionNominatedRequestDTO.getReferenceId(),
        distributionNominatedRequestDTO.getUseId(),
        distributionNominatedRequestDTO.getBudgetCycle(),
        distributionNominatedRequestDTO.getProductOrderId(),
        distributionNominatedRequestDTO.getProductVariantGroupId(),
        distributionNominatedRequestDTO.getTheoreticalQuantity(),
        distributionNominatedRequestDTO.getRequestedQuantity(),
        distributionNominatedRequestDTO.getConsumptionFactor(),
        triggeredBy);
  }

  private PlanRequest toPlanRequest(final PlanDTO planDTO) {
    return Objects.isNull(planDTO)
        ? new AutoRequest()
        : new PreselectedRequest(this.lineSelectionsFromCreateLineDTOs(planDTO.getLines()));
  }

  private List<LineSelectionRequest> lineSelectionsFromCreateLineDTOs(final List<LineDTO> lineDTOs) {
    return lineDTOs.stream()
        .map(lineDTO -> new LineSelectionRequest(
            lineDTO.getCommitmentOrderId(),
            lineDTO.getCommitmentOrderLineId(),
            lineDTO.getRequestedQuantity()))
        .toList();
  }

}
