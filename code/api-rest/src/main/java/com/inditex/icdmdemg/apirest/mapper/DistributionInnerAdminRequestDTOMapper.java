package com.inditex.icdmdemg.apirest.mapper;

import java.util.UUID;

import com.inditex.icdmdemg.application.distributioninner.command.SendAdminEventDistributionInnerCommand.SendEventRequest;
import com.inditex.icdmdemg.dto.DistributionInnerAdminPostRequestDTO;

import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

@Component
public class DistributionInnerAdminRequestDTOMapper {

  public SendEventRequest toDomain(@NonNull final UUID distributionInnerId,
      @NonNull final DistributionInnerAdminPostRequestDTO distributionInnerAdminPostRequestDTO) {
    return new SendEventRequest(distributionInnerId,
        distributionInnerAdminPostRequestDTO.getEventTypeToSend());
  }

}
