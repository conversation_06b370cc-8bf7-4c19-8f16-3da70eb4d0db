package com.inditex.icdmdemg.apirest.controller;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.apirest.mapper.DistributionConsumptionFactorDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.DistributionSummaryDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.ProductProvisioningSummaryDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.UsesAvailabilityResponseDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.UsesAvailabilityResponseV2DTOMapper;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery.DistributionConsumptionFactorRequest;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery.DistributionSummaryRequest;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.ProductProvisioningSummaryRequest;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query;
import com.inditex.icdmdemg.dto.DistributionsConsumptionFactorResponseDTO;
import com.inditex.icdmdemg.dto.DistributionsSummaryDTO;
import com.inditex.icdmdemg.dto.ProductProvisioningSummaryDTO;
import com.inditex.icdmdemg.dto.UsesAvailabilityResponseDTO;
import com.inditex.icdmdemg.dto.UsesAvailabilityResponseV2DTO;
import com.inditex.icdmdemg.service.SummaryApi;
import com.inditex.iopcmmnt.cqrs.core.QueryBus;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class DistributionSummaryController implements SummaryApi {

  private final QueryBus queryBus;

  private final DistributionSummaryDTOMapper distributionSummaryDTOMapper;

  private final ProductProvisioningSummaryDTOMapper productProvisioningSummaryDTOMapper;

  private final DistributionConsumptionFactorDTOMapper distributionConsumptionFactorDTOMapper;

  private final UsesAvailabilityResponseDTOMapper usesAvailabilityResponseDTOMapper;

  private final UsesAvailabilityResponseV2DTOMapper usesAvailabilityResponseV2DTOMapper;

  @Override
  @PreAuthorize("hasAuthority('DistributionNominated.Read') and hasAuthority('DistributionInner.Read')")
  public ResponseEntity<DistributionsSummaryDTO> distributionsSummaryGet(final UUID referenceId, final String budgetCycle,
      final UUID useId) {
    final var request = new DistributionSummaryRequest(referenceId, useId, budgetCycle);
    final var response = this.queryBus.ask(new GetDistributionSummaryQuery(request)).responseOrThrowIfError();
    return ResponseEntity.ok(this.distributionSummaryDTOMapper.toDto(response));
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionNominated.Read') and hasAuthority('DistributionInner.Read')")
  public ResponseEntity<ProductProvisioningSummaryDTO> productProvisioningSummaryGet(final List<UUID> productIds,
      final List<UUID> referenceIds, final List<String> budgetCycles) {
    final var request = ProductProvisioningSummaryRequest.of(productIds, referenceIds, budgetCycles);
    final var response = this.queryBus.ask(new GetProductProvisioningSummaryQuery(request)).responseOrThrowIfError();
    return ResponseEntity.ok(this.productProvisioningSummaryDTOMapper.toDto(response));
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionNominated.Read') and hasAuthority('DistributionInner.Read')")
  public ResponseEntity<DistributionsConsumptionFactorResponseDTO> consumptionFactorDistributionGet(
      final List<UUID> productOrderIds) {
    final var request = DistributionConsumptionFactorRequest.of(productOrderIds);
    final var response = this.queryBus.ask(new GetDistributionConsumptionFactorQuery(request)).responseOrThrowIfError();
    return ResponseEntity.ok(this.distributionConsumptionFactorDTOMapper.toDto(response));
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionNominated.Read') and hasAuthority('DistributionInner.Read')")
  public ResponseEntity<UsesAvailabilityResponseDTO> usesAvailabilityGet(final UUID referenceId, final String budgetCycle,
      final UUID useId) {
    final var request = GetUsesAvailabilityQuery.of(referenceId, useId, budgetCycle);
    final var response = this.queryBus.ask(new GetUsesAvailabilityQuery(request)).responseOrThrowIfError();
    return ResponseEntity.ok(this.usesAvailabilityResponseDTOMapper.toDto(response));
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionNominated.Read') and hasAuthority('DistributionInner.Read')")
  public ResponseEntity<UsesAvailabilityResponseV2DTO> usesAvailabilityGetV2(final UUID referenceId, final String budgetCycle,
      final UUID productReferenceId, final UUID productOrderId, final String acceptLanguage, final Boolean filterNoAvailability,
      final List<String> purchaseType) {
    final var request = GetUsesAvailabilityV2Query.of(referenceId, productReferenceId, budgetCycle, productOrderId, filterNoAvailability,
        purchaseType, acceptLanguage);
    final var response = this.queryBus.ask(new GetUsesAvailabilityV2Query(request)).responseOrThrowIfError();
    return ResponseEntity.ok(this.usesAvailabilityResponseV2DTOMapper.toDto(response));
  }

}
