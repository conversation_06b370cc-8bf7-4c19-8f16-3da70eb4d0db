package com.inditex.icdmdemg.apirest.controller;

import java.util.UUID;

import com.inditex.icdmdemg.application.product.query.GetAlternativeProductReferenceQuery;
import com.inditex.icdmdemg.dto.AlternativeReferenceIdDTO;
import com.inditex.icdmdemg.service.ProductApi;
import com.inditex.iopcmmnt.cqrs.core.QueryBus;

import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class ProductsController implements ProductApi {
  private final QueryBus queryBus;

  @Override
  @PreAuthorize("hasAuthority('DistributionNominated.Read')")
  public ResponseEntity<AlternativeReferenceIdDTO> alternativeProductReference(final UUID referenceId) {
    final var referenceIdResponse = this.queryBus.ask(new GetAlternativeProductReferenceQuery(referenceId)).responseOrThrowIfError();
    return ResponseEntity.ok(new AlternativeReferenceIdDTO().referenceId(referenceIdResponse.alternativeReferenceId()));
  }
}
