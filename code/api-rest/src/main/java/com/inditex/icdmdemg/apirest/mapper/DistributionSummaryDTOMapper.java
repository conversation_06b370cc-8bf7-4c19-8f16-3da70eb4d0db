package com.inditex.icdmdemg.apirest.mapper;

import java.util.List;

import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery.DistributionAllocated;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery.DistributionSummaryResponse;
import com.inditex.icdmdemg.dto.DistributionsInnerSummaryDTO;
import com.inditex.icdmdemg.dto.DistributionsNominatedSummaryDTO;
import com.inditex.icdmdemg.dto.DistributionsSummaryDTO;

import org.springframework.stereotype.Component;

@Component
public class DistributionSummaryDTOMapper {

  public DistributionsSummaryDTO toDto(final DistributionSummaryResponse distributionSummaryResponse) {
    return new DistributionsSummaryDTO(
        distributionSummaryResponse.referenceId(),
        distributionSummaryResponse.budgetCycle(),
        distributionSummaryResponse.useId(),
        this.toDto(distributionSummaryResponse.distributionNominatesAllocated()),
        this.toDtoInner(distributionSummaryResponse.distributionInners()));
  }

  private DistributionsNominatedSummaryDTO toDto(final DistributionAllocated distributionNominated) {
    return new DistributionsNominatedSummaryDTO(
        distributionNominated.productOrderId(),
        distributionNominated.productVariantGroupId(),
        distributionNominated.requestedQuantity(),
        distributionNominated.distributedQuantity());
  }

  private List<DistributionsNominatedSummaryDTO> toDto(final List<DistributionAllocated> distributionNominates) {
    return distributionNominates.stream().map(this::toDto).toList();
  }

  private DistributionsInnerSummaryDTO toDtoInner(final DistributionAllocated distributionInner) {
    return new DistributionsInnerSummaryDTO(
        distributionInner.productOrderId(),
        distributionInner.productVariantGroupId(),
        distributionInner.requestedQuantity(),
        distributionInner.distributedQuantity());
  }

  private List<DistributionsInnerSummaryDTO> toDtoInner(final List<DistributionAllocated> distributionInners) {
    return distributionInners.stream().map(this::toDtoInner).toList();
  }
}
