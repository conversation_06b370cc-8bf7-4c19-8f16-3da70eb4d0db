package com.inditex.icdmdemg.apirest.controller;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.apirest.config.UserContext;
import com.inditex.icdmdemg.apirest.mapper.AvailableCommitmentDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.DistributionNominatedDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.DistributionNominatedRequestDTOMapper;
import com.inditex.icdmdemg.apirest.pagination.PageMapper;
import com.inditex.icdmdemg.application.distributionnominated.query.GetAvailableCommitmentsQuery;
import com.inditex.icdmdemg.application.distributionnominated.query.GetAvailableCommitmentsQuery.AvailableCommitment;
import com.inditex.icdmdemg.application.distributionnominated.query.GetDistributionNominatedByCriteriaQuery;
import com.inditex.icdmdemg.application.distributionnominated.query.GetDistributionNominatedByIdQuery;
import com.inditex.icdmdemg.application.process.DistributionNominatedProcessManager;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.dto.CommitmentAvailabilityDTO;
import com.inditex.icdmdemg.dto.CommitmentsAvailabilityResponseDTO;
import com.inditex.icdmdemg.dto.DistributionNominatedDTO;
import com.inditex.icdmdemg.dto.DistributionNominatedRequestDTO;
import com.inditex.icdmdemg.dto.DistributionNominatedRequestPatchDTO;
import com.inditex.icdmdemg.dto.DistributionsNominatedPostResponseDTO;
import com.inditex.icdmdemg.dto.DistributionsNominatedResponseDTO;
import com.inditex.icdmdemg.service.NominatedApi;
import com.inditex.iopcmmnt.cqrs.core.QueryBus;

import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class DistributionNominatedController implements NominatedApi {

  private final QueryBus queryBus;

  private final DistributionNominatedProcessManager processManager;

  private final DistributionNominatedDTOMapper mapper;

  private final AvailableCommitmentDTOMapper availableCommitmentDTOMapper;

  private final DistributionNominatedRequestDTOMapper distributionNominatedRequestDTOMapper;

  private final PageMapper<DistributionNominated, DistributionNominatedDTO> pageMapper;

  private final PageMapper<AvailableCommitment, CommitmentAvailabilityDTO> commitmentAvailabilityDTOPageMapper;

  private final UserContext userContext;

  @Override
  @PreAuthorize("hasAuthority('DistributionNominated.Write')")
  public ResponseEntity<DistributionsNominatedPostResponseDTO> distributionsNominatedPost(
      final DistributionNominatedRequestDTO distributionNominatedRequestDTO) {
    final var command = this.distributionNominatedRequestDTOMapper
        .toCreateCommand(distributionNominatedRequestDTO, this.userContext.getCurrentUser());
    final var distributionNominatedId = this.processManager.createDistributionNominated(command);
    return ResponseEntity.status(HttpStatus.CREATED).body(new DistributionsNominatedPostResponseDTO()
        .distributionNominatedId(distributionNominatedId));
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionNominated.Write')")
  public ResponseEntity<Void> distributionsNominatedDelete(
      final UUID distributionNominatedId) {
    final var request =
        this.distributionNominatedRequestDTOMapper.toDomainDelete(distributionNominatedId, this.userContext.getCurrentUser());
    this.processManager.deleteDistributionNominated(request);
    return ResponseEntity.noContent().build();
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionNominated.Write')")
  public ResponseEntity<Void> distributionsNominatedPatch(final UUID distributionNominatedId,
      final DistributionNominatedRequestPatchDTO distributionNominatedRequestPatchDTO) {
    final var command = this.distributionNominatedRequestDTOMapper.toPatchCommand(distributionNominatedId,
        distributionNominatedRequestPatchDTO, this.userContext.getCurrentUser());
    this.processManager.patchDistributionNominated(command);
    return ResponseEntity.noContent().build();
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionNominated.Read')")
  public ResponseEntity<DistributionNominatedDTO> distributionsNominatedGetById(final UUID distributionNominatedId) {
    final var distributionNominated =
        this.queryBus.ask(new GetDistributionNominatedByIdQuery(distributionNominatedId)).responseOrThrowIfError();
    return ResponseEntity.ok(this.mapper.toDto(distributionNominated));
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionNominated.Read')")
  public ResponseEntity<DistributionsNominatedResponseDTO> distributionsNominatedGet(final List<UUID> referenceIds,
      final List<UUID> productOrderIds, final List<UUID> commitmentOrderIds, final List<String> budgetCycles, final UUID lineId,
      final List<String> status, final Integer page, final Integer size) {
    final var distributions = this.queryBus.ask(
        new GetDistributionNominatedByCriteriaQuery(referenceIds, lineId, productOrderIds, commitmentOrderIds, budgetCycles, status, page,
            size));
    final var paginated = this.pageMapper.paginated(distributions, this.mapper::toDto, true);
    return ResponseEntity.ok(
        new DistributionsNominatedResponseDTO()
            .data(paginated.data())
            .pagination(paginated.pagination().toPaginationDto()));
  }

  @Override
  @PreAuthorize("hasAuthority('DistributionNominated.Read')")
  public ResponseEntity<CommitmentsAvailabilityResponseDTO> commitmentsAvailabilityGet(final UUID referenceId, final String budgetCycle,
      final UUID useId, final Integer page, final Integer size) {
    final var commitments =
        this.queryBus.ask(new GetAvailableCommitmentsQuery(referenceId, budgetCycle, useId, page, size));
    final var paginated =
        this.commitmentAvailabilityDTOPageMapper.paginated(commitments, this.availableCommitmentDTOMapper::toDto, true);

    return ResponseEntity.ok(new CommitmentsAvailabilityResponseDTO()
        .data(paginated.data())
        .pagination(paginated.pagination().toPaginationDto()));
  }
}
