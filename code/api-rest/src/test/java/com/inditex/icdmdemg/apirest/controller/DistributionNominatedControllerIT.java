package com.inditex.icdmdemg.apirest.controller;

import static org.instancio.Select.field;
import static org.instancio.Select.fields;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

import com.inditex.amigafwk.service.aaa.userdetails.heimdal.HeimdalUser;
import com.inditex.amigafwk.service.aaa.userdetails.heimdal.model.HeimdalUserDetails;
import com.inditex.amigafwk.service.rest.core.properties.WebApiConfigProperties;
import com.inditex.icdmdemg.apirest.config.HeimdalUserContext;
import com.inditex.icdmdemg.apirest.config.JacksonOffsetDateTimeMapperConfiguration;
import com.inditex.icdmdemg.apirest.config.UserContext;
import com.inditex.icdmdemg.apirest.mapper.AvailableCommitmentDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.DistributionNominatedDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.DistributionNominatedRequestDTOMapper;
import com.inditex.icdmdemg.apirest.pagination.PageMapper;
import com.inditex.icdmdemg.application.distributionnominated.command.CreateDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.CreateDistributionNominatedCommand.NominatedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.DeleteDistributionNominatedCommand.DeleteNominatedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PatchDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.PatchDistributionNominatedCommand.PatchNominatedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.AutoRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.PreselectedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.PreselectedRequest.LineSelectionRequest;
import com.inditex.icdmdemg.application.distributionnominated.query.GetAvailableCommitmentsQuery;
import com.inditex.icdmdemg.application.distributionnominated.query.GetAvailableCommitmentsQuery.AvailableCommitment;
import com.inditex.icdmdemg.application.distributionnominated.query.GetDistributionNominatedByCriteriaQuery;
import com.inditex.icdmdemg.application.distributionnominated.query.GetDistributionNominatedByIdQuery;
import com.inditex.icdmdemg.application.process.DistributionNominatedProcessManager;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.dto.CommitmentAvailabilityDTO;
import com.inditex.icdmdemg.dto.DistributionNominatedDTO;
import com.inditex.icdmdemg.dto.DistributionNominatedRequestDTO;
import com.inditex.icdmdemg.dto.DistributionNominatedRequestPatchDTO;
import com.inditex.icdmdemg.dto.LineDTO;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.iopcmmnt.cqrs.core.QueryBus;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.SliceImpl;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(SpringExtension.class)
@WebMvcTest(controllers = DistributionNominatedController.class)
@ContextConfiguration(classes = {DistributionNominatedController.class, DistributionNominatedRequestDTOMapper.class,
    PageMapper.class, DistributionNominatedDTOMapper.class, AvailableCommitmentDTOMapper.class,
    ErrorControllerAdvice.class, WebApiConfigProperties.class, JacksonOffsetDateTimeMapperConfiguration.class,
    HeimdalUserContext.class, DistributionNominatedControllerIT.Config.class})
class DistributionNominatedControllerIT {

  @Autowired
  private MockMvc mockMvc;

  @Autowired
  private DistributionNominatedController controller;

  @Autowired
  private ErrorControllerAdvice controllerAdvice;

  @MockitoBean
  private QueryBus queryBus;

  @MockitoBean
  private DistributionNominatedProcessManager processManager;

  @Autowired
  private DistributionNominatedRequestDTOMapper distributionNominatedRequestDTOMapper;

  @Autowired
  private AvailableCommitmentDTOMapper availableCommitmentDTOMapper;

  @Autowired
  private DistributionNominatedDTOMapper distributionNominatedDTOMapper;

  @Autowired
  private PageMapper<DistributionNominated, DistributionNominatedDTO> pageMapper;

  @Autowired
  private PageMapper<AvailableCommitment, CommitmentAvailabilityDTO> commitmentAvailabilityDTOPageMapper;

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private UserContext userContext;

  @BeforeEach
  void setUp() {
    this.mockMvc = MockMvcBuilders.standaloneSetup(this.controller).setControllerAdvice(this.controllerAdvice)
        .setMessageConverters(new MappingJackson2HttpMessageConverter(this.objectMapper)).build();
  }

  @Test
  @WithUserDetails
  void distributionsNominatedPost_shouldReturnAutomaticCreated() throws Exception {
    final var distributionNominatedId = UUID.randomUUID();
    final var distributionNominatedRequestDTO = Instancio.of(DistributionNominatedRequestDTO.class)
        .set(fields(field -> field.getName().equalsIgnoreCase("budgetCycle")),
            UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID()))
        .set(fields(field -> field.getName().equalsIgnoreCase("plan")), null)
        .create();
    final NominatedRequest nominatedRequest = new NominatedRequest(
        distributionNominatedRequestDTO.getReferenceId(),
        distributionNominatedRequestDTO.getUseId(),
        distributionNominatedRequestDTO.getBudgetCycle(),
        distributionNominatedRequestDTO.getProductOrderId(),
        distributionNominatedRequestDTO.getProductVariantGroupId(),
        distributionNominatedRequestDTO.getTheoreticalQuantity(),
        distributionNominatedRequestDTO.getRequestedQuantity(),
        distributionNominatedRequestDTO.getConsumptionFactor(),
        this.userContext.getCurrentUser());
    final var command = new CreateDistributionNominatedCommand(nominatedRequest, new AutoRequest());

    doReturn(distributionNominatedId).when(this.processManager).createDistributionNominated(command);

    this.mockMvc
        .perform(post("/v1/distributions-nominated", distributionNominatedRequestDTO)
            .content(this.objectMapper.writeValueAsString(distributionNominatedRequestDTO))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated())
        .andExpect(jsonPath("distributionNominatedId").value(String.valueOf(distributionNominatedId)));

    verify(this.processManager).createDistributionNominated(command);
  }

  @Test
  @WithUserDetails
  void distributionsNominatedPost_shouldReturnPreselectedCreated() throws Exception {
    final var distributionNominatedId = UUID.randomUUID();
    final var distributionNominatedRequestDTO = Instancio.of(DistributionNominatedRequestDTO.class)
        .set(fields(field -> field.getName().equalsIgnoreCase("budgetCycle")),
            UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID()))
        .create();
    final NominatedRequest nominatedRequest = new NominatedRequest(distributionNominatedRequestDTO.getReferenceId(),
        distributionNominatedRequestDTO.getUseId(), distributionNominatedRequestDTO.getBudgetCycle(),
        distributionNominatedRequestDTO.getProductOrderId(),
        distributionNominatedRequestDTO.getProductVariantGroupId(),
        distributionNominatedRequestDTO.getTheoreticalQuantity(),
        distributionNominatedRequestDTO.getRequestedQuantity(),
        distributionNominatedRequestDTO.getConsumptionFactor(), this.userContext.getCurrentUser());
    final var command = new CreateDistributionNominatedCommand(nominatedRequest, new PreselectedRequest(
        this.lineSelectionsRequestFromLineDTOs(distributionNominatedRequestDTO.getPlan().getLines())));

    doReturn(distributionNominatedId).when(this.processManager).createDistributionNominated(command);

    this.mockMvc
        .perform(post("/v1/distributions-nominated", distributionNominatedRequestDTO)
            .content(this.objectMapper.writeValueAsString(distributionNominatedRequestDTO))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated())
        .andExpect(jsonPath("distributionNominatedId").value(String.valueOf(distributionNominatedId)));

    verify(this.processManager).createDistributionNominated(command);
  }

  @Test
  @WithUserDetails
  void distributionsNominatedAutomaticPatch_shouldReturnNoContent() throws Exception {
    final var distributionNominatedId = UUID.randomUUID();
    final var distributionNominatedRequestPatchDTO = Instancio.of(DistributionNominatedRequestPatchDTO.class)
        .set(field("plan"), null)
        .create();

    final PatchNominatedRequest patchNominatedRequest = new PatchNominatedRequest(distributionNominatedId,
        distributionNominatedRequestPatchDTO.getTheoreticalQuantity(),
        distributionNominatedRequestPatchDTO.getRequestedQuantity(),
        distributionNominatedRequestPatchDTO.getConsumptionFactor(), this.userContext.getCurrentUser());
    final var command = new PatchDistributionNominatedCommand(patchNominatedRequest, new AutoRequest());

    this.mockMvc
        .perform(patch("/v1/distributions-nominated/{distributionNominatedId}", distributionNominatedId)
            .contentType(MediaType.APPLICATION_JSON)
            .content(this.objectMapper.writeValueAsString(distributionNominatedRequestPatchDTO)))
        .andExpect(status().isNoContent());

    verify(this.processManager).patchDistributionNominated(command);
  }

  @Test
  @WithUserDetails
  void distributionsNominatedPreselectedPatch_shouldReturnNoContent() throws Exception {
    final var distributionNominatedId = UUID.randomUUID();
    final var distributionNominatedRequestPatchDTO = Instancio.of(DistributionNominatedRequestPatchDTO.class)
        .create();

    final PatchNominatedRequest patchNominatedRequest = new PatchNominatedRequest(distributionNominatedId,
        distributionNominatedRequestPatchDTO.getTheoreticalQuantity(),
        distributionNominatedRequestPatchDTO.getRequestedQuantity(),
        distributionNominatedRequestPatchDTO.getConsumptionFactor(), this.userContext.getCurrentUser());
    final var command = new PatchDistributionNominatedCommand(patchNominatedRequest, new PreselectedRequest(
        this.lineSelectionsRequestFromLineDTOs(distributionNominatedRequestPatchDTO.getPlan().getLines())));

    this.mockMvc
        .perform(patch("/v1/distributions-nominated/{distributionNominatedId}", distributionNominatedId)
            .contentType(MediaType.APPLICATION_JSON)
            .content(this.objectMapper.writeValueAsString(distributionNominatedRequestPatchDTO)))
        .andExpect(status().isNoContent());

    verify(this.processManager).patchDistributionNominated(command);
  }

  @Test
  @WithUserDetails
  void distributionsNominatedPatch_shouldReturnBadRequest_whenIsCanceled() throws Exception {
    final var distributionNominatedId = UUID.randomUUID();
    final var distributionNominatedRequestPatchDTO = Instancio.create(DistributionNominatedRequestPatchDTO.class);

    doThrow(new ErrorException(
        new BadRequest("Distribution nominated cannot be modified since its status is canceled")))
            .when(this.processManager).patchDistributionNominated(any());

    this.mockMvc
        .perform(patch("/v1/distributions-nominated/{distributionNominatedId}", distributionNominatedId)
            .contentType(MediaType.APPLICATION_JSON)
            .content(this.objectMapper.writeValueAsString(distributionNominatedRequestPatchDTO)))
        .andExpect(status().isBadRequest());
  }

  @Test
  @WithUserDetails
  void distributionsNominatedDelete_shouldReturnNoContent() throws Exception {
    final var distributionNominatedId = UUID.randomUUID();

    final DeleteNominatedRequest deleteNominatedRequest = new DeleteNominatedRequest(distributionNominatedId,
        this.userContext.getCurrentUser());

    this.mockMvc.perform(delete("/v1/distributions-nominated/{distributionNominatedId}", distributionNominatedId)
        .contentType(MediaType.APPLICATION_JSON)).andExpect(status().isNoContent());

    verify(this.processManager).deleteDistributionNominated(deleteNominatedRequest);
  }

  @Test
  @WithUserDetails
  void distributionsNominatedGetById_shouldReturnDistributionNominated() throws Exception {
    final var distributionNominatedId = UUID.randomUUID();
    final var getByIdNominatedResult = Instancio.create(DistributionNominated.class);

    doReturn(Response.ofResponse(getByIdNominatedResult)).when(this.queryBus)
        .ask(new GetDistributionNominatedByIdQuery(distributionNominatedId));

    this.mockMvc
        .perform(get("/v1/distributions-nominated/{distributionNominatedId}", distributionNominatedId)
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("id").value(getByIdNominatedResult.getId().value().toString()))
        .andExpect(jsonPath("referenceId").value(getByIdNominatedResult.referenceId().value().toString()))
        .andExpect(jsonPath("useId").value(getByIdNominatedResult.useId().value().toString()))
        .andExpect(jsonPath("budgetCycle").value(getByIdNominatedResult.budgetCycle().value()))
        .andExpect(jsonPath("productOrderId").value(getByIdNominatedResult.productOrderId().value().toString()))
        .andExpect(jsonPath("productVariantGroupId")
            .value(getByIdNominatedResult.productVariantGroupId().value().toString()))
        .andExpect(jsonPath("requestedQuantity")
            .value(getByIdNominatedResult.requestedQuantity().value().doubleValue()))
        .andExpect(jsonPath("distributedQuantity")
            .value(getByIdNominatedResult.requestedQuantity().value().doubleValue()))
        .andExpect(jsonPath("status").value(getByIdNominatedResult.status().name()))
        .andExpect(jsonPath("theoreticalQuantity")
            .value(getByIdNominatedResult.theoreticalQuantity().value().doubleValue()))
        .andExpect(jsonPath("consumptionFactor")
            .value(getByIdNominatedResult.consumptionFactor().value().doubleValue()))
        // jsonPath list DistributionNominatedLineDTO
        .andExpect(jsonPath("lines[0].id")
            .value(getByIdNominatedResult.lines().value().getFirst().id().value().toString()))
        .andExpect(jsonPath("lines[0].commitmentOrderId")
            .value(getByIdNominatedResult.lines().value().getFirst().commitmentOrder().id().value().toString()))
        .andExpect(jsonPath("lines[0].commitmentOrderLineId").value(
            getByIdNominatedResult.lines().value().getFirst().commitmentOrder().lineId().value().toString()))
        .andExpect(jsonPath("lines[0].requestedQuantity")
            .value(getByIdNominatedResult.lines().value().getFirst().requestedQuantity().value().doubleValue()))
        .andExpect(jsonPath("lines[0].distributedQuantity").value(
            getByIdNominatedResult.lines().value().getFirst().requestedQuantity().value().doubleValue()))
        .andExpect(jsonPath("lines[0].distributionStartDate").value(getByIdNominatedResult.lines().value()
            .getFirst().distributionStartDate().value().format(DateTimeFormatter.ISO_DATE_TIME)))
        .andExpect(jsonPath("lines[0].createdAt").value(getByIdNominatedResult.lines().value().getFirst().audit()
            .createdAt().format(DateTimeFormatter.ISO_DATE_TIME)))
        .andExpect(jsonPath("lines[0].updatedAt").value(getByIdNominatedResult.lines().value().getFirst().audit()
            .updatedAt().format(DateTimeFormatter.ISO_DATE_TIME)))
        // jsonPath pendingAssigned
        .andExpect(jsonPath("pendingAssigned").value(0.00));

    verify(this.queryBus).ask(new GetDistributionNominatedByIdQuery(distributionNominatedId));
  }

  @Test
  @WithUserDetails
  void distributionsNominatedGetByCriteria_shouldReturnDistributionNominated() throws Exception {
    final var pageable = PageRequest.of(0, 50);
    final var getByCriteriaNominatedResult = Instancio.create(DistributionNominated.class);
    final var slicedResult = new SliceImpl<>(List.of(getByCriteriaNominatedResult), pageable, false);

    final var query = new GetDistributionNominatedByCriteriaQuery(List.of(), List.of(), List.of(), List.of(), List.of(), List.of(),
        pageable);
    doReturn(slicedResult).when(this.queryBus).ask(query);

    this.mockMvc.perform(get("/v1/distributions-nominated").contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());

    verify(this.queryBus).ask(query);
  }

  @Test
  @WithUserDetails
  void distributionsNominatedGetAvailableCommitments_shouldReturnAvailableCommitments() throws Exception {
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final var pageable = PageRequest.of(0, 50);
    final var getAvailableCommitmentsResult = Instancio.create(AvailableCommitment.class);
    final var slicedResult = new SliceImpl<>(List.of(getAvailableCommitmentsResult), pageable, false);

    final var query = new GetAvailableCommitmentsQuery(referenceId, budgetCycle, useId, pageable.getPageNumber(),
        pageable.getPageSize());
    doReturn(slicedResult).when(this.queryBus).ask(query);

    this.mockMvc
        .perform(get("/v1/commitments-availability")
            .param("referenceId", String.valueOf(referenceId)).param("useId", String.valueOf(useId))
            .param("budgetCycle", budgetCycle).contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.data[0].commitmentOrderId")
            .value(getAvailableCommitmentsResult.commitmentOrderId().toString()))
        .andExpect(jsonPath("$.data[0].commitmentOrderLineId")
            .value(getAvailableCommitmentsResult.commitmentOrderLineId().toString()))
        .andExpect(jsonPath("$.data[0].expectedDate").exists())
        .andExpect(jsonPath("$.data[0].locationId").value(getAvailableCommitmentsResult.locationId()))
        .andExpect(jsonPath("$.data[0].availableQuantity").exists());

    verify(this.queryBus).ask(query);
  }

  private List<LineSelectionRequest> lineSelectionsRequestFromLineDTOs(final List<LineDTO> lineDTOs) {
    return lineDTOs.stream()
        .map(lineDTO -> new LineSelectionRequest(
            lineDTO.getCommitmentOrderId(),
            lineDTO.getCommitmentOrderLineId(),
            lineDTO.getRequestedQuantity()))
        .toList();
  }

  @Configuration
  static class Config {
    @Bean
    public UserDetailsService userDetailsService() {
      return username -> HeimdalUser.create(HeimdalUserDetails.minimum("app", username), List.of());
    }
  }
}
