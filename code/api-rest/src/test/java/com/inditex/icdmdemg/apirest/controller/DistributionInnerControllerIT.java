package com.inditex.icdmdemg.apirest.controller;

import static org.instancio.Select.field;
import static org.instancio.Select.fields;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

import com.inditex.amigafwk.service.aaa.userdetails.heimdal.HeimdalUser;
import com.inditex.amigafwk.service.aaa.userdetails.heimdal.model.HeimdalUserDetails;
import com.inditex.amigafwk.service.rest.core.properties.WebApiConfigProperties;
import com.inditex.icdmdemg.apirest.config.HeimdalUserContext;
import com.inditex.icdmdemg.apirest.config.JacksonOffsetDateTimeMapperConfiguration;
import com.inditex.icdmdemg.apirest.config.UserContext;
import com.inditex.icdmdemg.apirest.mapper.DistributionInnerDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.DistributionInnerRequestDtoMapper;
import com.inditex.icdmdemg.apirest.pagination.PageMapper;
import com.inditex.icdmdemg.application.distributioninner.command.CloseDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributioninner.command.CreateDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributioninner.command.CreateDistributionInnerCommand.InnerRequest;
import com.inditex.icdmdemg.application.distributioninner.command.CreateDistributionInnerResult;
import com.inditex.icdmdemg.application.distributioninner.command.DeleteDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributioninner.command.DeleteDistributionInnerCommand.DeleteInnerRequest;
import com.inditex.icdmdemg.application.distributioninner.command.PatchDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributioninner.command.RevertClosedDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributioninner.command.SendToDistributionDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributioninner.query.GetDistributionInnerByCriteriaQuery;
import com.inditex.icdmdemg.application.distributioninner.query.GetDistributionInnerByIdQuery;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.dto.DistributionInnerRequestDTO;
import com.inditex.icdmdemg.dto.DistributionInnerRequestPatchDTO;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;
import com.inditex.iopcmmnt.cqrs.core.QueryBus;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.SliceImpl;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(SpringExtension.class)
@WebMvcTest(controllers = DistributionInnerController.class)
@ContextConfiguration(
    classes = {DistributionInnerController.class, PageMapper.class, DistributionInnerRequestDtoMapper.class,
        DistributionInnerDTOMapper.class, ErrorControllerAdvice.class,
        WebApiConfigProperties.class, JacksonOffsetDateTimeMapperConfiguration.class, HeimdalUserContext.class,
        DistributionInnerControllerIT.Config.class})
class DistributionInnerControllerIT {

  @Autowired
  private MockMvc mockMvc;

  @Autowired
  private DistributionInnerController controller;

  @Autowired
  private ErrorControllerAdvice controllerAdvice;

  @MockitoBean
  private CommandBus commandBus;

  @MockitoBean
  private QueryBus queryBus;

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private UserContext userContext;

  @BeforeEach
  void setUp() {
    this.mockMvc = MockMvcBuilders.standaloneSetup(this.controller).setControllerAdvice(this.controllerAdvice)
        .setMessageConverters(new MappingJackson2HttpMessageConverter(this.objectMapper))
        .build();
  }

  @Test
  @WithUserDetails
  void distributionsInnerGetById_shouldReturnDistributionInner() throws Exception {
    final var distributionInnerId = UUID.randomUUID();
    final var getByIdInnerResult = Instancio.create(DistributionInner.class);

    doReturn(Response.ofResponse(getByIdInnerResult)).when(this.queryBus)
        .ask(new GetDistributionInnerByIdQuery(distributionInnerId));

    this.mockMvc
        .perform(get("/v1/distributions-inner/{distributionInnerId}", distributionInnerId)
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("id").value(getByIdInnerResult.getId().value().toString()))
        .andExpect(jsonPath("referenceId").value(getByIdInnerResult.referenceId().value().toString()))
        .andExpect(jsonPath("useId").value(getByIdInnerResult.useId().value().toString()))
        .andExpect(jsonPath("budgetCycle").value(getByIdInnerResult.budgetCycle().value()))
        .andExpect(jsonPath("productOrderId").value(getByIdInnerResult.productOrderId().value().toString()))
        .andExpect(jsonPath("productVariantGroupId").value(getByIdInnerResult.productVariantGroupId().value().toString()))
        .andExpect(jsonPath("requestedQuantity").value(getByIdInnerResult.requestedQuantity().value().doubleValue()))
        .andExpect(jsonPath("distributedQuantity").value(getByIdInnerResult.distributedQuantity().value().doubleValue()))
        .andExpect(jsonPath("status").value(getByIdInnerResult.status().name()))
        .andExpect(jsonPath("theoreticalQuantity").value(getByIdInnerResult.theoreticalQuantity().value().doubleValue()))
        .andExpect(jsonPath("consumptionFactor").value(getByIdInnerResult.consumptionFactor().value().doubleValue()))
        // jsonPath list DistributionInnerLineDTO
        .andExpect(jsonPath("lines[0].id").value(getByIdInnerResult.lines().value().getFirst().id().value().toString()))
        .andExpect(
            jsonPath("lines[0].trackingCode")
                .value(getByIdInnerResult.lines().value().getFirst().trackingCode().value()))
        .andExpect(
            jsonPath("lines[0].requestedQuantity")
                .value(getByIdInnerResult.lines().value().getFirst().requestedQuantity().value().doubleValue()))
        .andExpect(
            jsonPath("lines[0].distributedQuantity")
                .value(getByIdInnerResult.lines().value().getFirst().distributedQuantity().value().doubleValue()))
        .andExpect(jsonPath("lines[0].distributionStartDate")
            .value(getByIdInnerResult.lines().value().getFirst().distributionStartDate().value().format(
                DateTimeFormatter.ISO_DATE_TIME)))
        .andExpect(jsonPath("lines[0].distributionEndDate")
            .value(getByIdInnerResult.lines().value().getFirst().distributionEndDate().value().format(
                DateTimeFormatter.ISO_DATE_TIME)))
        .andExpect(jsonPath("lines[0].createdAt").value(getByIdInnerResult.lines().value().getFirst().audit().createdAt().format(
            DateTimeFormatter.ISO_DATE_TIME)))
        .andExpect(jsonPath("lines[0].updatedAt")
            .value(getByIdInnerResult.lines().value().getFirst().audit().updatedAt().format(DateTimeFormatter.ISO_DATE_TIME)))
        .andExpect(jsonPath("pendingAssigned").value(getByIdInnerResult.pendingAssigned().value().doubleValue()));

    verify(this.queryBus).ask(new GetDistributionInnerByIdQuery(distributionInnerId));
  }

  @Test
  @WithUserDetails
  void distributionsInnerGetByCriteria_shouldReturnDistributionInner() throws Exception {
    final var pageable = PageRequest.of(0, 50);
    final var innerResult = Instancio.of(DistributionInner.class)
        .set(field("budgetCycle"), new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())))
        .create();
    final var slicedResult = new SliceImpl<>(List.of(innerResult), pageable, false);

    final var query = new GetDistributionInnerByCriteriaQuery(
        List.of(innerResult.referenceId().value()),
        List.of(innerResult.productOrderId().value()),
        List.of(innerResult.budgetCycle().value()),
        List.of(innerResult.status().value()),
        pageable);
    doReturn(slicedResult).when(this.queryBus).ask(query);

    this.mockMvc
        .perform(get("/v1/distributions-inner")
            .param("productOrderIds", innerResult.productOrderId().value().toString())
            .param("referenceIds", innerResult.referenceId().value().toString())
            .param("budgetCycles", innerResult.budgetCycle().value())
            .param("status", innerResult.status().value())
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());

    verify(this.queryBus).ask(query);
  }

  @Test
  @WithUserDetails
  void distributionsInnerPost_shouldReturnCreated() throws Exception {
    final var distributionInnerId = UUID.randomUUID();
    final var distributionInnerRequestDTO = Instancio.of(DistributionInnerRequestDTO.class)
        .set(fields(field -> field.getName().equalsIgnoreCase("budgetCycle")),
            UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID()))
        .create();
    final InnerRequest innerRequest = new InnerRequest(
        distributionInnerRequestDTO.getReferenceId(),
        distributionInnerRequestDTO.getUseId(),
        distributionInnerRequestDTO.getBudgetCycle(),
        distributionInnerRequestDTO.getProductOrderId(),
        distributionInnerRequestDTO.getProductVariantGroupId(),
        distributionInnerRequestDTO.getTheoreticalQuantity(),
        distributionInnerRequestDTO.getRequestedQuantity(),
        distributionInnerRequestDTO.getConsumptionFactor(),
        distributionInnerRequestDTO.getSendToDistribution(),
        this.userContext.getCurrentUser());
    final var innerResult = new CreateDistributionInnerResult(new DistributionInner.Id(distributionInnerId));

    doReturn(innerResult).when(this.commandBus).execute(new CreateDistributionInnerCommand(innerRequest));

    this.mockMvc
        .perform(post("/v1/distributions-inner", distributionInnerRequestDTO)
            .content(this.objectMapper.writeValueAsString(distributionInnerRequestDTO))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated())
        .andExpect(jsonPath("distributionInnerId").value(String.valueOf(distributionInnerId)));

    verify(this.commandBus).execute(new CreateDistributionInnerCommand(innerRequest));
  }

  @Test
  @WithUserDetails
  void distributionInnerDelete_shouldReturnNoContent() throws Exception {
    final var distributionInnerId = UUID.randomUUID();
    final var deleteInnerResult = Instancio.create(DistributionInner.class);

    final DeleteInnerRequest deleteInnerRequest = new DeleteInnerRequest(distributionInnerId, this.userContext.getCurrentUser());
    doReturn(deleteInnerResult).when(this.commandBus).execute(new DeleteDistributionInnerCommand(deleteInnerRequest));

    this.mockMvc.perform(delete("/v1/distributions-inner/{distributionInnerId}", distributionInnerId)
        .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());

    verify(this.commandBus).execute(new DeleteDistributionInnerCommand(deleteInnerRequest));
  }

  @Test
  @WithUserDetails
  void distributionInnerDelete_shouldReturnBadRequest_whenIsCanceled() throws Exception {
    final var distributionInnerId = UUID.randomUUID();

    doThrow(new ErrorException(new BadRequest("Distribution inner cannot be deleted since its status is canceled")))
        .when(this.commandBus).execute(any());

    this.mockMvc
        .perform(delete("/v1/distributions-inner/{distributionInnerId}", distributionInnerId)
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());
  }

  @Test
  @WithUserDetails
  void distributionsInnerPatch_shouldReturnNoContent() throws Exception {
    final var distributionInnerId = UUID.randomUUID();
    final var distributionInnerRequestPatchDTO = Instancio.create(DistributionInnerRequestPatchDTO.class);
    final var patchInnerResult = Instancio.create(DistributionInner.class);

    doReturn(patchInnerResult).when(this.commandBus).execute(new PatchDistributionInnerCommand(distributionInnerId,
        distributionInnerRequestPatchDTO.getTheoreticalQuantity(),
        distributionInnerRequestPatchDTO.getRequestedQuantity(),
        distributionInnerRequestPatchDTO.getConsumptionFactor(),
        this.userContext.getCurrentUser()));

    this.mockMvc
        .perform(patch("/v1/distributions-inner/{distributionInnerId}", distributionInnerId)
            .contentType(MediaType.APPLICATION_JSON)
            .content(this.objectMapper.writeValueAsString(distributionInnerRequestPatchDTO)))
        .andExpect(status().isNoContent());

    verify(this.commandBus).execute(new PatchDistributionInnerCommand(distributionInnerId,
        distributionInnerRequestPatchDTO.getTheoreticalQuantity(),
        distributionInnerRequestPatchDTO.getRequestedQuantity(),
        distributionInnerRequestPatchDTO.getConsumptionFactor(),
        this.userContext.getCurrentUser()));
  }

  @Test
  @WithUserDetails
  void distributionsInnerSendToDistribution_shouldReturnNoContent() throws Exception {
    final var distributionInnerId = UUID.randomUUID();

    this.mockMvc
        .perform(patch("/v1/distributions-inner/{distributionInnerId}/send-to-distribution", distributionInnerId)
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());

    verify(this.commandBus).execute(new SendToDistributionDistributionInnerCommand(distributionInnerId,
        this.userContext.getCurrentUser()));
  }

  @Test
  @WithUserDetails
  void closeDistributionInner_shouldReturnNoContent() throws Exception {
    final var distributionInnerId = UUID.randomUUID();

    this.mockMvc
        .perform(patch("/v1/distributions-inner/{distributionInnerId}/close", distributionInnerId)
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());

    verify(this.commandBus).execute(new CloseDistributionInnerCommand(distributionInnerId,
        this.userContext.getCurrentUser()));
  }

  @Test
  @WithUserDetails
  void revertClosedDistributionInner() throws Exception {
    final var distributionInnerId = UUID.randomUUID();

    doReturn(null).when(this.commandBus).execute(new RevertClosedDistributionInnerCommand(distributionInnerId, any()));

    this.mockMvc.perform(patch("/v1/distributions-inner/{distributionInnerId}/revert-close", distributionInnerId)
        .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());

    verify(this.commandBus).execute(new RevertClosedDistributionInnerCommand(distributionInnerId, any()));
  }

  @Configuration
  static class Config {
    @Bean
    public UserDetailsService userDetailsService() {
      return username -> HeimdalUser.create(HeimdalUserDetails.minimum("app", username), List.of());
    }
  }
}
