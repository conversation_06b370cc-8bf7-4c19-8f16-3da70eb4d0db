package com.inditex.icdmdemg.apirest.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery.DistributionConsumptionFactor;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery.DistributionConsumptionFactorResponse;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class DistributionConsumptionFactorDTOMapperTest {
  private DistributionConsumptionFactorDTOMapper sut;

  @BeforeEach
  void setUp() {
    this.sut = new DistributionConsumptionFactorDTOMapper();
  }

  @Test
  void should_map_ok() {
    final var referenceId = UUID.randomUUID();
    final var variantGroupId = UUID.randomUUID();
    final var consumptionFactor = BigDecimal.ONE;
    final var distributionType = "NOMINATED";
    final var distributionsConsumptionFactor1 =
        new DistributionConsumptionFactor(referenceId, variantGroupId, consumptionFactor, distributionType);
    final var referenceId2 = UUID.randomUUID();
    final var variantGroupId2 = UUID.randomUUID();
    final var consumptionFactor2 = BigDecimal.ONE;
    final var distributionType2 = "INNER";
    final var distributionsConsumptionFactor2 =
        new DistributionConsumptionFactor(referenceId2, variantGroupId2, consumptionFactor2, distributionType2);
    final var distributionConsumptionFactorResponse =
        new DistributionConsumptionFactorResponse(List.of(distributionsConsumptionFactor1, distributionsConsumptionFactor2));

    final var result = this.sut.toDto(distributionConsumptionFactorResponse);

    assertThat(result).isNotNull();
    assertThat(result.getDistributionsConsumptionFactor().getFirst().getReferenceId()).isEqualTo(referenceId);
    assertThat(result.getDistributionsConsumptionFactor().getFirst().getVariantGroupId()).isEqualTo(variantGroupId);
    assertThat(result.getDistributionsConsumptionFactor().getFirst().getConsumptionFactor()).isEqualTo(consumptionFactor);
    assertThat(result.getDistributionsConsumptionFactor().getFirst().getDistributionType().getValue()).isEqualTo(distributionType);

    assertThat(result.getDistributionsConsumptionFactor().getLast().getReferenceId()).isEqualTo(referenceId2);
    assertThat(result.getDistributionsConsumptionFactor().getLast().getVariantGroupId()).isEqualTo(variantGroupId2);
    assertThat(result.getDistributionsConsumptionFactor().getLast().getConsumptionFactor()).isEqualTo(consumptionFactor2);
    assertThat(result.getDistributionsConsumptionFactor().getLast().getDistributionType().getValue()).isEqualTo(distributionType2);

  }
}
