package com.inditex.icdmdemg.apirest.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.UUID;

import com.inditex.icdmdemg.application.distributioninner.command.SendAdminEventDistributionInnerCommand.SendEventRequest;
import com.inditex.icdmdemg.dto.DistributionInnerAdminPostRequestDTO;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DistributionInnerAdminRequestDTOMapperTest {

  private DistributionInnerAdminRequestDTOMapper sut;

  @BeforeEach
  void setUp() {
    this.sut = new DistributionInnerAdminRequestDTOMapper();
  }

  @Test
  void should_map() {
    final var id = UUID.randomUUID();
    final var request = Instancio.create(DistributionInnerAdminPostRequestDTO.class);

    final var expected = new SendEventRequest(id, request.getEventTypeToSend());

    final var result = this.sut.toDomain(id, request);

    assertThat(result).isNotNull().isEqualTo(expected);
  }
}
