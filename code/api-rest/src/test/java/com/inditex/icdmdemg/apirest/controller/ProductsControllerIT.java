package com.inditex.icdmdemg.apirest.controller;

import static org.mockito.Mockito.doReturn;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.UUID;

import com.inditex.amigafwk.service.rest.core.properties.WebApiConfigProperties;
import com.inditex.icdmdemg.application.product.query.GetAlternativeProductReferenceQuery;
import com.inditex.icdmdemg.application.product.query.GetAlternativeProductReferenceQuery.GetAlternativeProductReferenceResponse;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.iopcmmnt.cqrs.core.QueryBus;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(SpringExtension.class)
@WebMvcTest(controllers = ProductsController.class)
@ContextConfiguration(classes = {ProductsController.class, ErrorControllerAdvice.class, WebApiConfigProperties.class})
class ProductsControllerIT {
  @Autowired
  private MockMvc mockMvc;

  @Autowired
  private ProductsController controller;

  @Autowired
  private ErrorControllerAdvice controllerAdvice;

  @MockitoBean
  private QueryBus queryBus;

  @BeforeEach
  void setUp() {
    this.mockMvc = MockMvcBuilders.standaloneSetup(this.controller).setControllerAdvice(this.controllerAdvice)
        .build();
  }

  @Test
  void alternativeProductReference_should_return_alternative_reference_id() throws Exception {
    final var referenceId = UUID.randomUUID();
    final var alternativeReferenceId = UUID.randomUUID();

    doReturn(Response.ofResponse(new GetAlternativeProductReferenceResponse(alternativeReferenceId)))
        .when(this.queryBus).ask(new GetAlternativeProductReferenceQuery(referenceId));

    this.mockMvc.perform(get("/v1/alternative-product-reference/{referenceId}", referenceId)
        .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

  @Test
  void alternativeProductReference_should_return_error() throws Exception {
    final var referenceId = UUID.randomUUID();

    doReturn(Response.ofResponse(new GetAlternativeProductReferenceResponse(null))).when(this.queryBus)
        .ask(new GetAlternativeProductReferenceQuery(referenceId));

    this.mockMvc.perform(get("/v1/alternative-product-reference/{referenceId}", referenceId)
        .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk());
  }

}
