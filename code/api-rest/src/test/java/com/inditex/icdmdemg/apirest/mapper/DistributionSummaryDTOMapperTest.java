package com.inditex.icdmdemg.apirest.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery.DistributionAllocated;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery.DistributionSummaryResponse;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.dto.DistributionsInnerSummaryDTO;
import com.inditex.icdmdemg.dto.DistributionsNominatedSummaryDTO;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class DistributionSummaryDTOMapperTest {
  private DistributionSummaryDTOMapper sut;

  @BeforeEach
  void setUp() {
    this.sut = new DistributionSummaryDTOMapper();
  }

  @Test
  void should_map_ok() {
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final var distributionNominated = DistributionNominatedMother.pending().build();
    final var distributionNominatedAllocated = new DistributionAllocated(distributionNominated.productOrderId().value(),
        distributionNominated.productVariantGroupId().value(), distributionNominated.requestedQuantity().value(),
        distributionNominated.distributedQuantity().value());
    final var distributionInner = DistributionInnerMother.pending().build();
    final var distributionInnerAllocated = new DistributionAllocated(distributionInner.productOrderId().value(),
        distributionInner.productVariantGroupId().value(), distributionInner.requestedQuantity().value(),
        distributionInner.distributedQuantity().value());
    final var distributionSummaryResponse = new DistributionSummaryResponse(
        referenceId,
        useId,
        budgetCycle,
        List.of(distributionNominatedAllocated),
        List.of(distributionInnerAllocated));

    final var result = this.sut.toDto(distributionSummaryResponse);

    assertThat(result).isNotNull();
    assertThat(result.getReferenceId()).isEqualTo(referenceId);
    assertThat(result.getBudgetCycle()).isEqualTo(budgetCycle);
    assertThat(result.getUseId()).isEqualTo(useId);

    assertThat(result.getDistributionsNominated()).hasSize(1);
    assertThat(result.getDistributionsNominated()).first().isEqualTo(new DistributionsNominatedSummaryDTO(
        distributionNominated.productOrderId().value(),
        distributionNominated.productVariantGroupId().value(),
        distributionNominated.requestedQuantity().value(),
        distributionNominated.distributedQuantity().value()));

    assertThat(result.getDistributionsInner()).first().isEqualTo(new DistributionsInnerSummaryDTO(
        distributionInner.productOrderId().value(),
        distributionInner.productVariantGroupId().value(),
        distributionInner.requestedQuantity().value(),
        distributionInner.distributedQuantity().value()));
  }
}
