package com.inditex.icdmdemg.apirest.mapper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;

import java.util.List;

import com.inditex.icdmdemg.application.use.command.CreateUseCommand.UseRequest;
import com.inditex.icdmdemg.application.use.command.UseRequestNames;
import com.inditex.icdmdemg.application.use.command.UseRequestNames.UseRequestName;
import com.inditex.icdmdemg.dto.AssignablePatchDTO;
import com.inditex.icdmdemg.dto.NamePairsDTO;
import com.inditex.icdmdemg.dto.UseRequestDTO;
import com.inditex.icdmdemg.shared.utils.LocaleUtils;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class UseRequestDTOMapperTest {

  private UseRequestDTOMapper sut;

  @BeforeEach
  void setUp() {
    this.sut = new UseRequestDTOMapper();
  }

  @Test
  void should_map_ok_when_request_is_not_null() {

    final var triggeredBy = "admin";
    final var useRequest = Instancio.create(UseRequestDTO.class);

    final var result = this.sut.toDomain(useRequest, triggeredBy);

    assertThat(result).isNotNull();
    assertThat(result.assignable()).isEqualTo(List.copyOf(useRequest.getAssignable()));
    assertThat(result.customer()).isEqualTo(useRequest.getPurchasePurpose().getCustomer());
    assertThat(result.taxonomy()).isEqualTo(useRequest.getPurchasePurpose().getTaxonomy());
    assertThat(result.purchaseType()).isEqualTo(List.copyOf(useRequest.getPurchasePurpose().getPurchaseTypes()));
    assertThat(result.conditions())
        .isEqualTo(useRequest.getPurchasePurpose().getConditionParameters().stream()
            .map(condition -> new UseRequest.Condition(condition.getCondition(), condition.getParameterConditionName(),
                condition.getParameterConditionValues()))
            .toList());
    assertThat(result.names()).isEqualTo(new UseRequestNames(
        useRequest.getNames().stream()
            .map(name -> new UseRequestName(LocaleUtils.createLocale(name.getLocale()), name.getTranslation()))
            .toList()));
    assertThat(result.triggeredBy()).isEqualTo(triggeredBy);

  }

  @Test
  void should_map_names_to_patch_request_name() {
    final NamePairsDTO namePairEs = new NamePairsDTO("es", "descripcion uso");
    final NamePairsDTO namePairEn = new NamePairsDTO("en", "use description");
    final List<NamePairsDTO> namePairs = List.of(namePairEs, namePairEn);

    final var result = this.sut.toUseRequestNames(namePairs);

    assertThat(result).isNotNull();
    assertThat(result.values()).hasSize(2);
    assertThat(result.values())
        .extracting(UseRequestName::locale, UseRequestName::translation)
        .contains(
            tuple(LocaleUtils.createLocale("es"), "descripcion uso"),
            tuple(LocaleUtils.createLocale("en"), "use description"));
  }

  @Test
  void should_map_assignable_to_patch_request_assignable() {

    final AssignablePatchDTO assignable = AssignablePatchDTO.NOMINATED_INNER;

    final var result = this.sut.toPatchRequestAssignable(assignable);

    assertThat(result).isNotNull();
    assertThat(String.join("_", result)).isEqualTo(assignable.toString());
  }

  @Test
  void should_map_null_assignable_to_patch_request_assignable() {

    final var result = this.sut.toPatchRequestAssignable(null);

    assertThat(result).isNull();
  }
}
