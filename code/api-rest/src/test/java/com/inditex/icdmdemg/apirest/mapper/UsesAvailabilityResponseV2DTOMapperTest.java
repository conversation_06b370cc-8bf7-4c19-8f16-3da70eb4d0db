package com.inditex.icdmdemg.apirest.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityTotalInnerQuantityV2;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityTotalNominatedQuantityV2;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityV2;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityV2Response;
import com.inditex.icdmdemg.dto.UseAvailabilityV2DTO;

import jakarta.validation.Valid;
import org.junit.jupiter.api.Test;

class UsesAvailabilityResponseV2DTOMapperTest {

  final UsesAvailabilityResponseV2DTOMapper sut = new UsesAvailabilityResponseV2DTOMapper();

  @Test
  void should_map_usesAvailabilityResponse_to_usesAvailabilityResponseV2DTO() {

    final var data1 = new UsesAvailabilityV2(
        UUID.randomUUID(),
        "useName",
        List.of("purchaseType1", "purchaseType2"),
        List.of("purchaseType1", "purchaseType2"),
        true,
        new UsesAvailabilityTotalNominatedQuantityV2(BigDecimal.ONE),
        new UsesAvailabilityTotalInnerQuantityV2(BigDecimal.ONE));

    final var data2 = new UsesAvailabilityV2(
        UUID.randomUUID(),
        "useName2",
        List.of("purchaseType1", "purchaseType2"),
        List.of("purchaseType1", "purchaseType2"),
        true,
        new UsesAvailabilityTotalNominatedQuantityV2(BigDecimal.ONE),
        new UsesAvailabilityTotalInnerQuantityV2(BigDecimal.TEN));

    final var usesAvailabilityResponse = new UsesAvailabilityV2Response(List.of(data1, data2));

    final var result = this.sut.toDto(usesAvailabilityResponse);

    assertThat(result.getData()).hasSize(2);
    this.assertUseAvailability(result.getData().getFirst(), data1);
    this.assertUseAvailability(result.getData().getLast(), data2);
  }

  private void assertUseAvailability(final @Valid UseAvailabilityV2DTO result, final UsesAvailabilityV2 expected) {

    assertThat(result.getUseId()).isEqualTo(expected.useId());
    assertThat(result.getUseName()).isEqualTo(expected.useName());
    assertThat(result.getPurchaseTypes()).isEqualTo(expected.purchaseTypes());
    assertThat(result.getAssignable()).isEqualTo(expected.assignable());
    assertThat(result.getMatchPurchasePurpose()).isEqualTo(expected.matchPurchasePurpose());
    assertThat(result.getNominated().getTotalQuantity()).isEqualTo(expected.nominated().totalQuantity());
    assertThat(result.getInner().getTotalQuantity()).isEqualTo(expected.inner().totalQuantity());
  }

}
