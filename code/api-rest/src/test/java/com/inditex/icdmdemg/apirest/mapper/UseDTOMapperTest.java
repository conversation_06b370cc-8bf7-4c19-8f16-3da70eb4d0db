package com.inditex.icdmdemg.apirest.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Locale;
import java.util.Set;

import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.entity.AssignableType;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues.ConditionValue;
import com.inditex.icdmdemg.domain.use.mother.UseMother;
import com.inditex.icdmdemg.dto.UseDTO;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class UseDTOMapperTest {

  private final Locale locale = Locale.of("es");

  private UseDTOMapper sut;

  @BeforeEach
  void setUp() {
    this.sut = new UseDTOMapper();
  }

  @Test
  void should_map_ok_when_assignable_is_not_null() {
    final var use = UseMother
        .generateBuild()
        .assignable(AssignableType.NOMINATED_INNER)
        .build();

    final var result = this.sut.toDto(use, this.locale);

    assertThat(result).isNotNull();
    assertThat(result.getAssignable()).isEqualTo(Set.copyOf(AssignableType.NOMINATED_INNER.getStringList()));
    this.validateResult(result, use);
  }

  @Test
  void should_map_ok_when_assignable_is_null() {

    final var use = UseMother
        .generateBuild()
        .assignable(null)
        .build();

    final var result = this.sut.toDto(use, this.locale);

    assertThat(result).isNotNull();
    assertThat(result.getAssignable()).isEmpty();
    this.validateResult(result, use);
  }

  private void validateResult(final UseDTO result, final Use use) {
    assertThat(result.getName()).isEqualTo("Nombre_1");
    final var purchasePurpose = result.getPurchasePurpose();
    assertThat(purchasePurpose.getTaxonomy()).isEqualTo(use.taxonomy().value());
    assertThat(purchasePurpose.getCustomer()).isEqualTo(use.customer().value());
    assertThat(purchasePurpose.getPurchaseTypes()).isEqualTo(Set.copyOf(use.purchaseType().getStringList()));
    assertThat(purchasePurpose.getConditionParameters()).isNotEmpty();
    final var firstConditionDto = purchasePurpose.getConditionParameters().getFirst();
    final var firstConditionUse = use.conditions().value().getFirst();
    assertThat(firstConditionDto.getCondition()).isEqualTo(firstConditionUse.condition().name());
    assertThat(firstConditionDto.getParameterConditionName()).isEqualTo(firstConditionUse.name().name());
    assertThat(firstConditionDto.getParameterConditionValues())
        .isEqualTo(firstConditionUse.values().value().stream().map(ConditionValue::value).toList());
    assertThat(result.getCreatedAt()).isEqualTo(use.audit().createdAt());
    assertThat(result.getUpdatedAt()).isEqualTo(use.audit().updatedAt());
  }
}
