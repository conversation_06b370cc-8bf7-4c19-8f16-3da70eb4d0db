package com.inditex.icdmdemg.apirest.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import com.inditex.icdmdemg.apirest.config.UserContext;
import com.inditex.icdmdemg.application.distributioninner.command.CreateDistributionInnerCommand.InnerRequest;
import com.inditex.icdmdemg.dto.DistributionInnerRequestDTO;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DistributionInnerRequestDtoMapperTest {

  private DistributionInnerRequestDtoMapper sut;

  @BeforeEach
  void setup() {
    this.sut = new DistributionInnerRequestDtoMapper();
  }

  @Test
  void toDomain() {
    final var request = Instancio.create(DistributionInnerRequestDTO.class);

    final var expected = this.buildExpectedDistributionInnerRequest(request);

    final var result = this.sut.toDomain(request, UserContext.ANONYMOUS_USER);

    assertThat(result).isNotNull().isEqualTo(expected);
  }

  private InnerRequest buildExpectedDistributionInnerRequest(final DistributionInnerRequestDTO request) {
    return new InnerRequest(
        request.getReferenceId(),
        request.getUseId(),
        request.getBudgetCycle(),
        request.getProductOrderId(),
        request.getProductVariantGroupId(),
        request.getTheoreticalQuantity(),
        request.getRequestedQuantity(),
        request.getConsumptionFactor(),
        request.getSendToDistribution(),
        UserContext.ANONYMOUS_USER);
  }
}
