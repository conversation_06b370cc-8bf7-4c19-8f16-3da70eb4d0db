package com.inditex.icdmdemg.apirest.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.DistributionQuantity;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.OrderQuantity;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.Product;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.ProductProvisioningSummaryResponse;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.Provision;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.Reference;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.StockLocationQuantities;
import com.inditex.icdmdemg.dto.DistributionQuantityDTO;
import com.inditex.icdmdemg.dto.OrderQuantityDTO;
import com.inditex.icdmdemg.dto.StockLocationQuantityDTO;
import com.inditex.icdmdemg.shared.utils.NumericUtils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class ProductProvisioningSummaryDTOMapperTest {
  private ProductProvisioningSummaryDTOMapper sut;

  @BeforeEach
  void setUp() {
    this.sut = new ProductProvisioningSummaryDTOMapper();
  }

  @Test
  void should_map_ok() {
    final var productId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var budgetCycle = "2022";
    final var useId = UUID.randomUUID();

    final var stockLocationIdInner = UUID.randomUUID().toString();
    final var stockLocationTypeInner = "Warehouse";
    final var stockInner = NumericUtils.roundUpScale2(new BigDecimal(100));
    final var orderedInner = NumericUtils.roundUpScale2(new BigDecimal(50));
    final var pendingInner = NumericUtils.roundUpScale2(new BigDecimal(20));
    final var enteredInner = NumericUtils.roundUpScale2(new BigDecimal(30));
    final var requestedInner = NumericUtils.roundUpScale2(new BigDecimal(30));
    final var distributedInner = NumericUtils.roundUpScale2(new BigDecimal(25));
    final var pendingAssignedInner = NumericUtils.roundUpScale2(new BigDecimal(5));

    final var stockLocationIdNominated = UUID.randomUUID().toString();
    final var stockLocationTypeNominated = "Warehouse";
    final var stockNominated = NumericUtils.roundUpScale2(new BigDecimal(100));
    final var orderedNominated = NumericUtils.roundUpScale2(new BigDecimal(50));
    final var pendingNominated = NumericUtils.roundUpScale2(new BigDecimal(20));
    final var enteredNominated = NumericUtils.roundUpScale2(new BigDecimal(30));
    final var requestedNominated = NumericUtils.roundUpScale2(new BigDecimal(30));
    final var distributedNominated = NumericUtils.roundUpScale2(new BigDecimal(25));
    final var pendingAssignedNominated = NumericUtils.roundUpScale2(new BigDecimal(5));

    final var stockLocationQuantitiesInner = new StockLocationQuantities(stockLocationIdInner, stockLocationTypeInner, stockInner);
    final var orderQuantityInner = new OrderQuantity(orderedInner, pendingInner, enteredInner);
    final var distributionQuantityInner = new DistributionQuantity(requestedInner, distributedInner);
    final var stockLocationQuantitiesNominated =
        new StockLocationQuantities(stockLocationIdNominated, stockLocationTypeNominated, stockNominated);
    final var orderQuantityNominated = new OrderQuantity(orderedNominated, pendingNominated, enteredNominated);
    final var distributionQuantityNominated = new DistributionQuantity(requestedNominated, distributedNominated);

    final var innerProvision = new Provision(List.of(stockLocationQuantitiesInner), orderQuantityInner, distributionQuantityInner);
    final var nominatedProvision =
        new Provision(List.of(stockLocationQuantitiesNominated), orderQuantityNominated, distributionQuantityNominated);

    final var reference = new Reference(referenceId, useId, budgetCycle, innerProvision, nominatedProvision);
    final var product = new Product(productId, List.of(reference));
    final var productProvisioningSummaryResponse = new ProductProvisioningSummaryResponse(List.of(product));

    final var result = this.sut.toDto(productProvisioningSummaryResponse);

    assertThat(result).isNotNull();
    assertThat(result.getProducts()).hasSize(1);

    final var productProvisionDTO = result.getProducts().getFirst();
    assertThat(productProvisionDTO.getProductId()).isEqualTo(productId);
    assertThat(productProvisionDTO.getReferences()).hasSize(1);

    final var referenceProvisionDTO = productProvisionDTO.getReferences().getFirst();
    assertThat(referenceProvisionDTO.getReferenceId()).isEqualTo(referenceId);
    assertThat(referenceProvisionDTO.getBudgetCycle()).isEqualTo(budgetCycle);
    assertThat(referenceProvisionDTO.getUseId()).isEqualTo(useId);

    final var provisionDetailDTO = referenceProvisionDTO.getInnerProvision();
    assertThat(provisionDetailDTO.getStockLocationQuantities()).hasSize(1);
    assertThat(provisionDetailDTO.getStockLocationQuantities().getFirst())
        .isEqualTo(new StockLocationQuantityDTO(stockLocationIdInner, stockLocationTypeInner, stockInner));
    assertThat(provisionDetailDTO.getOrderQuantity()).isEqualTo(new OrderQuantityDTO(orderedInner, pendingInner, enteredInner));
    assertThat(provisionDetailDTO.getDistributionQuantity())
        .isEqualTo(new DistributionQuantityDTO(requestedInner, distributedInner, pendingAssignedInner));

    final var nominatedProvisionDetailDTO = referenceProvisionDTO.getNominatedProvision();
    assertThat(nominatedProvisionDetailDTO.getStockLocationQuantities()).hasSize(1);
    assertThat(nominatedProvisionDetailDTO.getStockLocationQuantities().getFirst())
        .isEqualTo(new StockLocationQuantityDTO(stockLocationIdNominated, stockLocationTypeNominated, stockNominated));
    assertThat(nominatedProvisionDetailDTO.getOrderQuantity())
        .isEqualTo(new OrderQuantityDTO(orderedNominated, pendingNominated, enteredNominated));
    assertThat(nominatedProvisionDetailDTO.getDistributionQuantity())
        .isEqualTo(new DistributionQuantityDTO(requestedNominated, distributedNominated, pendingAssignedNominated));
  }
}
