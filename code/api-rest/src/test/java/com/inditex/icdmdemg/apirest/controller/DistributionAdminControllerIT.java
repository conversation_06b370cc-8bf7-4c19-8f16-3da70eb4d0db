package com.inditex.icdmdemg.apirest.controller;

import static org.instancio.Select.fields;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.List;
import java.util.UUID;

import com.inditex.amigafwk.service.aaa.userdetails.heimdal.HeimdalUser;
import com.inditex.amigafwk.service.aaa.userdetails.heimdal.model.HeimdalUserDetails;
import com.inditex.amigafwk.service.rest.core.properties.WebApiConfigProperties;
import com.inditex.icdmdemg.apirest.config.HeimdalUserContext;
import com.inditex.icdmdemg.apirest.config.JacksonOffsetDateTimeMapperConfiguration;
import com.inditex.icdmdemg.apirest.config.UserContext;
import com.inditex.icdmdemg.apirest.mapper.DistributionInnerAdminRequestDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.DistributionNominatedAdminRequestDTOMapper;
import com.inditex.icdmdemg.application.commitmentuse.command.CalculateNominatedProvisionCommand;
import com.inditex.icdmdemg.application.distributioninner.command.SendAdminEventDistributionInnerCommand;
import com.inditex.icdmdemg.application.distributioninner.command.SendAdminEventInnerResult;
import com.inditex.icdmdemg.application.distributionnominated.command.RegularizeDistributionNominatedBudgetCycleChangeCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.SendAdminEventDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.SendAdminEventDistributionNominatedCommand.SendEventRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.SendAdminEventNominatedResult;
import com.inditex.icdmdemg.application.process.ShipmentWarehouseProcessManager;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCreatedCommand.ShipmentWarehouseCreated;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseStartedCommand.ShipmentWarehouseStarted;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseUpdatedCommand.ShipmentWarehouseUpdated;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.dto.CalculateNominatedProvisionRequestDTO;
import com.inditex.icdmdemg.dto.DistributionInnerAdminPostRequestDTO;
import com.inditex.icdmdemg.dto.DistributionNominatedAdminPostRequestDTO;
import com.inditex.icdmdemg.dto.RegularizeBudgetCycleChangeRequestDTO;
import com.inditex.icdmdemg.dto.ShipmentWarehouseDeliveryPostRequestDTO;
import com.inditex.icdmdemg.dto.ShipmentWarehousePatchRequestDTO;
import com.inditex.icdmdemg.dto.ShipmentWarehousePostRequestDTO;
import com.inditex.icdmdemg.shared.utils.RandomValue;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(SpringExtension.class)
@WebMvcTest(controllers = DistributionAdminController.class)
@ContextConfiguration(classes = {DistributionAdminController.class, DistributionNominatedAdminRequestDTOMapper.class,
    DistributionInnerAdminRequestDTOMapper.class, ErrorControllerAdvice.class, WebApiConfigProperties.class, HeimdalUserContext.class,
    JacksonOffsetDateTimeMapperConfiguration.class, DistributionAdminControllerIT.Config.class})
class DistributionAdminControllerIT {

  @Autowired
  private MockMvc mockMvc;

  @Autowired
  private DistributionAdminController controller;

  @Autowired
  private ErrorControllerAdvice controllerAdvice;

  @MockitoBean
  private CommandBus commandBus;

  @MockitoBean
  private ShipmentWarehouseProcessManager shipmentWarehouseProcessManager;

  @Autowired
  private DistributionNominatedAdminRequestDTOMapper distributionNominatedAdminRequestDTOMapper;

  @Autowired
  private DistributionInnerAdminRequestDTOMapper distributionInnerAdminRequestDTOMapper;

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private UserContext userContext;

  @BeforeEach
  void setUp() {
    this.mockMvc = MockMvcBuilders.standaloneSetup(this.controller).setControllerAdvice(this.controllerAdvice)
        .setMessageConverters(new MappingJackson2HttpMessageConverter(this.objectMapper))
        .build();
  }

  @Test
  void distributionsNominatedAdminPost_shouldReturnCreated() throws Exception {

    final var distributionNominatedId = UUID.randomUUID();
    final var distributionNominatedRequestDTO = Instancio.create(DistributionNominatedAdminPostRequestDTO.class);

    final SendEventRequest nominatedRequest = new SendEventRequest(
        distributionNominatedId,
        distributionNominatedRequestDTO.getEventTypeToSend());
    final var sendAdminEventNominatedResult = new SendAdminEventNominatedResult(new DistributionNominated.Id(distributionNominatedId));

    doReturn(sendAdminEventNominatedResult).when(this.commandBus).execute(new SendAdminEventDistributionNominatedCommand(nominatedRequest));

    this.mockMvc
        .perform(post("/v1/distributions-nominated/{distributionNominatedId}", distributionNominatedId, distributionNominatedRequestDTO)
            .content(this.objectMapper.writeValueAsString(distributionNominatedRequestDTO))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated())
        .andExpect(jsonPath("distributionNominatedId").value(String.valueOf(distributionNominatedId)));

    verify(this.commandBus).execute(new SendAdminEventDistributionNominatedCommand(nominatedRequest));
  }

  @Test
  void distributionsNominatedAdminPost_shouldReturnBadRequest_whenErrorInHandler() throws Exception {

    final var distributionNominatedId = UUID.randomUUID();
    final var distributionNominatedRequestDTO = Instancio.create(DistributionNominatedAdminPostRequestDTO.class);

    final SendEventRequest nominatedRequest = new SendEventRequest(
        distributionNominatedId,
        distributionNominatedRequestDTO.getEventTypeToSend());

    doThrow(new ErrorException(new BadRequest("error")))
        .when(this.commandBus).execute(new SendAdminEventDistributionNominatedCommand(nominatedRequest));

    this.mockMvc
        .perform(post("/v1/distributions-nominated/{distributionNominatedId}", distributionNominatedId, distributionNominatedRequestDTO)
            .content(this.objectMapper.writeValueAsString(distributionNominatedRequestDTO))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());

    verify(this.commandBus).execute(new SendAdminEventDistributionNominatedCommand(nominatedRequest));
  }

  @Test
  void distributionsInnerAdminPost_shouldReturnCreated() throws Exception {

    final var distributionInnerId = UUID.randomUUID();
    final var distributionInnerRequestDTO = Instancio.create(DistributionInnerAdminPostRequestDTO.class);

    final SendAdminEventDistributionInnerCommand.SendEventRequest innerRequest =
        new SendAdminEventDistributionInnerCommand.SendEventRequest(
            distributionInnerId,
            distributionInnerRequestDTO.getEventTypeToSend());
    final var sendAdminEventInnerResult = new SendAdminEventInnerResult(new DistributionInner.Id(distributionInnerId));

    doReturn(sendAdminEventInnerResult).when(this.commandBus).execute(new SendAdminEventDistributionInnerCommand(innerRequest));

    this.mockMvc
        .perform(post("/v1/distributions-inner/{distributionInnerId}", distributionInnerId, distributionInnerRequestDTO)
            .content(this.objectMapper.writeValueAsString(distributionInnerRequestDTO))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated())
        .andExpect(jsonPath("distributionInnerId").value(String.valueOf(distributionInnerId)));

    verify(this.commandBus).execute(new SendAdminEventDistributionInnerCommand(innerRequest));
  }

  @Test
  @WithUserDetails
  void calculateNominatedProvisionPost_shouldReturnNoContent() throws Exception {

    final var request = Instancio.of(CalculateNominatedProvisionRequestDTO.class)
        .set(fields(field -> field.getName().equalsIgnoreCase("budgetCycle")),
            UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID()))
        .create();
    final var innerRequest = request.getSharedRawMaterials().stream()
        .map(dto -> new SharedRawMaterialNominated(dto.getReferenceId(), dto.getUseId(), dto.getBudgetCycle()))
        .toList();

    this.mockMvc
        .perform(post("/v1/calculate-nominated-provision", request)
            .content(this.objectMapper.writeValueAsString(request))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());

    verify(this.commandBus).execute(new CalculateNominatedProvisionCommand(innerRequest, this.userContext.getCurrentUser()));
  }

  @Test
  @WithUserDetails
  void calculateNominatedProvisionPost_shouldReturnBadRequest_withoutSharedRawMaterials() throws Exception {

    final var request = new CalculateNominatedProvisionRequestDTO(List.of());

    this.mockMvc
        .perform(post("/v1/calculate-nominated-provision", request)
            .content(this.objectMapper.writeValueAsString(request))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());

    verifyNoInteractions(this.commandBus);
  }

  @Test
  void distributionsInnerAdminPost_shouldReturnBadRequest_whenErrorInHandler() throws Exception {

    final var distributionInnerId = UUID.randomUUID();
    final var distributionInnerRequestDTO = Instancio.create(DistributionInnerAdminPostRequestDTO.class);

    final SendAdminEventDistributionInnerCommand.SendEventRequest innerRequest =
        new SendAdminEventDistributionInnerCommand.SendEventRequest(
            distributionInnerId,
            distributionInnerRequestDTO.getEventTypeToSend());

    doThrow(new ErrorException(new BadRequest("error")))
        .when(this.commandBus).execute(new SendAdminEventDistributionInnerCommand(innerRequest));

    this.mockMvc
        .perform(post("/v1/distributions-inner/{distributionInnerId}", distributionInnerId, distributionInnerRequestDTO)
            .content(this.objectMapper.writeValueAsString(distributionInnerRequestDTO))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isBadRequest());

    verify(this.commandBus).execute(new SendAdminEventDistributionInnerCommand(innerRequest));
  }

  @Test
  void shipmentWarehouseDeliveryPost_shouldReturnNoContent() throws Exception {

    final var request = Instancio.of(ShipmentWarehouseDeliveryPostRequestDTO.class)
        .generate(fields(field -> field.getName().contains("Date")),
            generators -> generators.temporal().offsetDateTime())
        .create();
    final var distributionTrackingCode = RandomValue.randomString();

    final var innerRequest = new ShipmentWarehouseUpdated(distributionTrackingCode, request.getQuantity(),
        request.getDistributionLastUpdateDate());

    this.mockMvc
        .perform(post("/v1/shipment-warehouse/{distributionTrackingCode}/delivery", distributionTrackingCode, request)
            .content(this.objectMapper.writeValueAsString(request))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());

    verify(this.shipmentWarehouseProcessManager).execute(innerRequest);
  }

  @Test
  void shipmentWarehousePost_shouldReturnNoContent() throws Exception {

    final var request = Instancio.create(ShipmentWarehousePostRequestDTO.class);

    final var innerRequest = new ShipmentWarehouseCreated(request.getDistributionInnerId(),
        request.getDistributionInnerLineId(), request.getDistributionTrackingCode());

    this.mockMvc
        .perform(post("/v1/shipment-warehouse", request)
            .content(this.objectMapper.writeValueAsString(request))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());

    verify(this.shipmentWarehouseProcessManager).execute(innerRequest);
  }

  @Test
  void shipmentWarehousePatch_shouldReturnNoContent() throws Exception {

    final var request = Instancio.of(ShipmentWarehousePatchRequestDTO.class)
        .generate(fields(field -> field.getName().contains("Date")),
            generators -> generators.temporal().offsetDateTime())
        .create();
    final var distributionTrackingCode = RandomValue.randomString();

    final var innerRequest = new ShipmentWarehouseStarted(distributionTrackingCode, request.getDistributionStartDate());

    this.mockMvc
        .perform(patch("/v1/shipment-warehouse/{distributionTrackingCode}", distributionTrackingCode, request)
            .content(this.objectMapper.writeValueAsString(request))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());

    verify(this.shipmentWarehouseProcessManager).execute(innerRequest);
  }

  @Test
  void regularizeBudgetCycleChangePost_shouldReturnNoContent() throws Exception {

    final var request = Instancio.of(RegularizeBudgetCycleChangeRequestDTO.class)
        .set(fields(field -> field.getName().equalsIgnoreCase("budgetCycle")),
            UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID()))
        .create();
    final var rawMaterialNominated = request.getSharedRawMaterials().stream()
        .map(dto -> new SharedRawMaterialNominated(dto.getReferenceId(), dto.getUseId(), dto.getBudgetCycle()))
        .toList();

    this.mockMvc
        .perform(post("/v1/distributions-nominated/regularize-budget-cycle-change", request)
            .content(this.objectMapper.writeValueAsString(request))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());

    verify(this.commandBus)
        .execute(new RegularizeDistributionNominatedBudgetCycleChangeCommand(rawMaterialNominated, this.userContext.getCurrentUser()));
  }

  @Configuration
  static class Config {
    @Bean
    public UserDetailsService userDetailsService() {
      return username -> HeimdalUser.create(HeimdalUserDetails.minimum("app", username), List.of());
    }
  }
}
