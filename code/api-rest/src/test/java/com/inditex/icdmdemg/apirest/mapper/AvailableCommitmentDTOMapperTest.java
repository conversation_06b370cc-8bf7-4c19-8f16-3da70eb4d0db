package com.inditex.icdmdemg.apirest.mapper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.inditex.icdmdemg.application.distributionnominated.query.GetAvailableCommitmentsQuery.AvailableCommitment;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)

class AvailableCommitmentDTOMapperTest {

  private AvailableCommitmentDTOMapper sut;

  @BeforeEach
  void setUp() {
    this.sut = new AvailableCommitmentDTOMapper();
  }

  @Test
  void should_not_map_from_null() {
    assertThatThrownBy(() -> this.sut.toDto(null)).isInstanceOf(NullPointerException.class);
  }

  @Test
  void should_map_ok() {
    final var availableCommitment = Instancio.create(AvailableCommitment.class);

    final var result = this.sut.toDto(availableCommitment);

    assertThat(result).isNotNull();
    assertThat(result.getCommitmentOrderId()).isEqualTo(availableCommitment.commitmentOrderId());
    assertThat(result.getCommitmentOrderLineId()).isEqualTo(availableCommitment.commitmentOrderLineId());
    assertThat(result.getExpectedDate()).isEqualTo(availableCommitment.expectedDate());
    assertThat(result.getLocationId()).isEqualTo(availableCommitment.locationId());
    assertThat(result.getAvailableQuantity()).isEqualTo(availableCommitment.availableQuantity());
  }
}
