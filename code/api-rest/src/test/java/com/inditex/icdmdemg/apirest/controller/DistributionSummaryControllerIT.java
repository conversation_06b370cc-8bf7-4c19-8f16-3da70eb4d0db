package com.inditex.icdmdemg.apirest.controller;

import static org.instancio.Select.field;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import com.inditex.amigafwk.service.rest.core.properties.WebApiConfigProperties;
import com.inditex.icdmdemg.apirest.mapper.DistributionConsumptionFactorDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.DistributionSummaryDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.ProductProvisioningSummaryDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.UsesAvailabilityResponseDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.UsesAvailabilityResponseV2DTOMapper;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery.DistributionConsumptionFactor;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery.DistributionConsumptionFactorRequest;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery.DistributionConsumptionFactorResponse;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery.DistributionSummaryRequest;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery.DistributionSummaryResponse;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.ProductProvisioningSummaryRequest;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.ProductProvisioningSummaryResponse;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.GetUsesAvailabilityRequest;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailability;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityResponse;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityTotalInnerQuantity;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityTotalNominatedQuantity;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.GetUsesAvailabilityV2Request;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityTotalInnerQuantityV2;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityTotalNominatedQuantityV2;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityV2;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityV2Response;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.icdmdemg.domain.use.entity.PurchaseType;
import com.inditex.icdmdemg.provis.domain.use.UseDTO.UsePurchaseType;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.iopcmmnt.cqrs.core.QueryBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

@ExtendWith(SpringExtension.class)
@WebMvcTest(controllers = DistributionSummaryController.class)
@ContextConfiguration(classes = {DistributionSummaryController.class, DistributionSummaryDTOMapper.class,
    ErrorControllerAdvice.class, WebApiConfigProperties.class, DistributionSummaryDTOMapper.class,
    ProductProvisioningSummaryDTOMapper.class, DistributionConsumptionFactorDTOMapper.class, UsesAvailabilityResponseDTOMapper.class,
    UsesAvailabilityResponseV2DTOMapper.class})
class DistributionSummaryControllerIT {

  @Autowired
  private MockMvc mockMvc;

  @Autowired
  private DistributionSummaryController controller;

  @Autowired
  private ErrorControllerAdvice controllerAdvice;

  @MockitoBean
  private QueryBus queryBus;

  @Autowired
  private DistributionSummaryDTOMapper distributionSummaryDTOMapper;

  @Autowired
  private ProductProvisioningSummaryDTOMapper productProvisioningSummaryDTOMapper;

  @Autowired
  private DistributionConsumptionFactorDTOMapper distributionConsumptionFactorDTOMapper;

  @Autowired
  private UsesAvailabilityResponseDTOMapper usesAvailabilityResponseDTOMapper;

  @Autowired
  private UsesAvailabilityResponseV2DTOMapper usesAvailabilityResponseV2DTOMapper;

  @BeforeEach
  void setUp() {
    this.mockMvc = MockMvcBuilders.standaloneSetup(this.controller).setControllerAdvice(this.controllerAdvice)
        .build();
  }

  @Test
  void distributionsSummaryGet_shouldReturnDistributionSummary() throws Exception {
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final var getSummaryResult = Instancio.create(DistributionSummaryResponse.class);
    final var request = new DistributionSummaryRequest(referenceId, useId, budgetCycle);

    doReturn(Response.ofResponse(getSummaryResult)).when(this.queryBus).ask(new GetDistributionSummaryQuery(request));

    this.mockMvc
        .perform(get("/v1/distributions-summary")
            .param("referenceId", String.valueOf(referenceId))
            .param("useId", String.valueOf(useId))
            .param("budgetCycle", budgetCycle)
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.referenceId").value(getSummaryResult.referenceId().toString()))
        .andExpect(jsonPath("$.useId").value(getSummaryResult.useId().toString()))
        .andExpect(jsonPath("$.budgetCycle").value(getSummaryResult.budgetCycle()))
        .andExpect(jsonPath("$.distributionsNominated[0].productOrderId")
            .value(getSummaryResult.distributionNominatesAllocated().get(0).productOrderId().toString()))
        .andExpect(jsonPath("$.distributionsNominated[0].productVariantGroupId")
            .value(getSummaryResult.distributionNominatesAllocated().get(0).productVariantGroupId().toString()))
        .andExpect(jsonPath("$.distributionsNominated[0].requestedQuantity")
            .value(getSummaryResult.distributionNominatesAllocated().get(0).requestedQuantity().doubleValue()))
        .andExpect(jsonPath("$.distributionsNominated[0].distributedQuantity")
            .value(getSummaryResult.distributionNominatesAllocated().get(0).distributedQuantity().doubleValue()))
        .andExpect(jsonPath("$.distributionsInner").isNotEmpty())
        .andExpect(jsonPath("$.distributionsInner[0].productOrderId")
            .value(getSummaryResult.distributionInners().get(0).productOrderId().toString()))
        .andExpect(jsonPath("$.distributionsInner[0].productVariantGroupId")
            .value(getSummaryResult.distributionInners().get(0).productVariantGroupId().toString()))
        .andExpect(jsonPath("$.distributionsInner[0].requestedQuantity")
            .value(getSummaryResult.distributionInners().get(0).requestedQuantity().doubleValue()))
        .andExpect(jsonPath("$.distributionsInner[0].distributedQuantity")
            .value(getSummaryResult.distributionInners().get(0).distributedQuantity().doubleValue()));

    verify(this.queryBus).ask(new GetDistributionSummaryQuery(request));
  }

  @Test
  void productProvisioningSummaryGet_shouldReturnProductProvisioningSummary() throws Exception {
    final var productIds = List.of(UUID.randomUUID());
    final var referenceIds = List.of(UUID.randomUUID());
    final var budgetCycles = List.of(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID()));
    final var getSummaryResult = Instancio.create(ProductProvisioningSummaryResponse.class);
    final var request = new ProductProvisioningSummaryRequest(productIds, referenceIds, budgetCycles);

    doReturn(Response.ofResponse(getSummaryResult)).when(this.queryBus).ask(new GetProductProvisioningSummaryQuery(request));

    this.mockMvc
        .perform(get("/v1/product-provisioning")
            .param("productIds", String.valueOf(productIds.get(0)))
            .param("referenceIds", String.valueOf(referenceIds.get(0)))
            .param("budgetCycles", budgetCycles.get(0))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.products").isNotEmpty())
        .andExpect(jsonPath("$.products[0].productId").value(getSummaryResult.products().get(0).productId().toString()))
        .andExpect(jsonPath("$.products[0].references").isNotEmpty())
        .andExpect(jsonPath("$.products[0].references[0].referenceId")
            .value(getSummaryResult.products().get(0).references().get(0).referenceId().toString()))
        .andExpect(
            jsonPath("$.products[0].references[0].budgetCycle").value(getSummaryResult.products().get(0).references().get(0).budgetCycle()))
        .andExpect(
            jsonPath("$.products[0].references[0].useId").value(getSummaryResult.products().get(0).references().get(0).useId().toString()))
        .andExpect(jsonPath("$.products[0].references[0].innerProvision.stockLocationQuantities[0].locationId").value(
            getSummaryResult.products().get(0).references().get(0).inner().stockLocationQuantities().get(0).locationId()))
        .andExpect(jsonPath("$.products[0].references[0].innerProvision.stockLocationQuantities[0].locationType")
            .value(getSummaryResult.products().get(0).references().get(0).inner().stockLocationQuantities().get(0).locationType()))
        .andExpect(jsonPath("$.products[0].references[0].innerProvision.stockLocationQuantities[0].stock")
            .value(getSummaryResult.products().get(0).references().get(0).inner().stockLocationQuantities().get(0).stock().doubleValue()))
        .andExpect(jsonPath("$.products[0].references[0].innerProvision.orderQuantity.ordered")
            .value(getSummaryResult.products().get(0).references().get(0).inner().orderQuantity().ordered().doubleValue()))
        .andExpect(jsonPath("$.products[0].references[0].innerProvision.orderQuantity.pending")
            .value(getSummaryResult.products().get(0).references().get(0).inner().orderQuantity().pending().doubleValue()))
        .andExpect(jsonPath("$.products[0].references[0].innerProvision.distributionQuantity.requested")
            .value(getSummaryResult.products().get(0).references().get(0).inner().distributionQuantity().requested().doubleValue()))
        .andExpect(jsonPath("$.products[0].references[0].innerProvision.distributionQuantity.distributed")
            .value(getSummaryResult.products().get(0).references().get(0).inner().distributionQuantity().distributed().doubleValue()))
        .andExpect(jsonPath("$.products[0].references[0].innerProvision.distributionQuantity.pendingAssigned")
            .value(getSummaryResult.products().get(0).references().get(0).inner().distributionQuantity().pendingAssigned().doubleValue()))
        .andExpect(jsonPath("$.products[0].references[0].nominatedProvision.stockLocationQuantities[0].locationId").value(
            getSummaryResult.products().get(0).references().get(0).nominated().stockLocationQuantities().get(0).locationId()))
        .andExpect(jsonPath("$.products[0].references[0].nominatedProvision.stockLocationQuantities[0].locationType").value(
            getSummaryResult.products().get(0).references().get(0).nominated().stockLocationQuantities().get(0).locationType()))
        .andExpect(jsonPath("$.products[0].references[0].nominatedProvision.stockLocationQuantities[0].stock").value(
            getSummaryResult.products().get(0).references().get(0).nominated().stockLocationQuantities().get(0).stock().doubleValue()))
        .andExpect(jsonPath("$.products[0].references[0].nominatedProvision.orderQuantity.ordered")
            .value(getSummaryResult.products().get(0).references().get(0).nominated().orderQuantity().ordered().doubleValue()))
        .andExpect(jsonPath("$.products[0].references[0].nominatedProvision.orderQuantity.pending")
            .value(getSummaryResult.products().get(0).references().get(0).nominated().orderQuantity().pending().doubleValue()))
        .andExpect(jsonPath("$.products[0].references[0].nominatedProvision.distributionQuantity.requested")
            .value(getSummaryResult.products().get(0).references().get(0).nominated().distributionQuantity().requested().doubleValue()))
        .andExpect(jsonPath("$.products[0].references[0].nominatedProvision.distributionQuantity.distributed")
            .value(getSummaryResult.products().get(0).references().get(0).nominated().distributionQuantity().distributed().doubleValue()))
        .andExpect(jsonPath("$.products[0].references[0].nominatedProvision.distributionQuantity.pendingAssigned")
            .value(
                getSummaryResult.products().get(0).references().get(0).nominated().distributionQuantity().pendingAssigned().doubleValue()));

    verify(this.queryBus).ask(new GetProductProvisioningSummaryQuery(request));
  }

  @Test
  void consumptionFactorDistributionGet_shouldReturnDistributionDistributionsConsumptionFactorSummary() throws Exception {
    final var productId1 = UUID.randomUUID();
    final var listProductIds = List.of(productId1);
    final var distributionConsumptionFactor = Instancio.of(DistributionConsumptionFactor.class)
        .set(field("distributionType"), "NOMINATED")
        .create();
    final var getConsumptionFactorSummaryResult = Instancio.of(DistributionConsumptionFactorResponse.class)
        .set(field("distributionsConsumptionFactor"), List.of(distributionConsumptionFactor))
        .create();
    final var request = new DistributionConsumptionFactorRequest(listProductIds);

    doReturn(Response.ofResponse(getConsumptionFactorSummaryResult)).when(this.queryBus)
        .ask(new GetDistributionConsumptionFactorQuery(request));

    this.mockMvc
        .perform(get("/v1/distributions-consumption-factor")
            .param("productOrderIds", String.valueOf(listProductIds.getFirst()))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.distributionsConsumptionFactor[0].referenceId").isNotEmpty())
        .andExpect(jsonPath("$.distributionsConsumptionFactor[0].variantGroupId").isNotEmpty())
        .andExpect(jsonPath("$.distributionsConsumptionFactor[0].consumptionFactor").isNotEmpty())
        .andExpect(jsonPath("$.distributionsConsumptionFactor[0].distributionType").isNotEmpty());

    verify(this.queryBus).ask(new GetDistributionConsumptionFactorQuery(request));
  }

  @Test
  void should_return_uses_availability_response_dto() throws Exception {
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());

    final var usesAvailabilityResponse = new UsesAvailabilityResponse(
        List.of(
            new UsesAvailability(
                useId,
                "useName",
                List.of("NOMINATED", "INNER"),
                true,
                new UsesAvailabilityTotalNominatedQuantity(BigDecimal.ONE),
                new UsesAvailabilityTotalInnerQuantity(BigDecimal.ONE))));

    final GetUsesAvailabilityRequest request = new GetUsesAvailabilityRequest(
        referenceId,
        useId,
        budgetCycle);
    doReturn(Response.ofResponse(usesAvailabilityResponse)).when(this.queryBus)
        .ask(new GetUsesAvailabilityQuery(request));

    final var firstData = usesAvailabilityResponse.data().getFirst();

    this.mockMvc
        .perform(get("/v1/uses-availability")
            .param("referenceId", String.valueOf(referenceId))
            .param("useId", String.valueOf(useId))
            .param("budgetCycle", budgetCycle)
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.data[0].useId").value(useId.toString()))
        .andExpect(jsonPath("$.data[0].useName").value(firstData.useName()))
        .andExpect(jsonPath("$.data[0].purchaseTypes[0]").value(
            firstData.purchaseTypes().getFirst()))
        .andExpect(jsonPath("$.data[0].purchaseTypes[1]").value(
            firstData.purchaseTypes().getLast()))
        .andExpect(jsonPath("$.data[0].assignable").value(firstData.assignable()))
        .andExpect(jsonPath("$.data[0].nominated.totalQuantity").value(firstData.nominated().totalQuantity()))
        .andExpect(jsonPath("$.data[0].inner.totalQuantity").value(firstData.inner().totalQuantity()));

    verify(this.queryBus).ask(new GetUsesAvailabilityQuery(request));
  }

  @Test
  void should_return_uses_availability_responseV2_dto() throws Exception {
    final var referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final UUID productOrderId = UUID.randomUUID();
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var name = "example";
    final var language = "en";
    final var useId = UUID.randomUUID();

    final var request = new GetUsesAvailabilityV2Request(
        referenceId,
        productReferenceId,
        budgetCycle,
        productOrderId,
        false,
        purchaseType,
        language);

    final var usesAvailabilityV2Response = new UsesAvailabilityV2Response(
        List.of(
            new UsesAvailabilityV2(
                useId,
                name,
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                purchaseType,
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(BigDecimal.ONE),
                new UsesAvailabilityTotalInnerQuantityV2(BigDecimal.ONE))));

    doReturn(Response.ofResponse(usesAvailabilityV2Response)).when(this.queryBus)
        .ask(new GetUsesAvailabilityV2Query(request));

    final var firstData = usesAvailabilityV2Response.data().getFirst();

    this.mockMvc
        .perform(get("/v2/uses-availability")
            .param("referenceId", String.valueOf(referenceId))
            .param("productReferenceId", String.valueOf(productReferenceId))
            .param("budgetCycle", budgetCycle)
            .param("productOrderId", String.valueOf(productOrderId))
            .header("Accept-Language", language)
            .param("filterNoAvailability", "false")
            .param("purchaseType", purchaseType.get(0), purchaseType.get(1))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.data[0].useId").value(useId.toString()))
        .andExpect(jsonPath("$.data[0].useName").value(firstData.useName()))
        .andExpect(jsonPath("$.data[0].purchaseTypes[0]").value(
            firstData.purchaseTypes().getFirst()))
        .andExpect(jsonPath("$.data[0].purchaseTypes[1]").value(
            firstData.purchaseTypes().getLast()))
        .andExpect(jsonPath("$.data[0].assignable[0]").value(firstData.assignable().getFirst()))
        .andExpect(jsonPath("$.data[0].assignable[1]").value(firstData.assignable().getLast()))
        .andExpect(jsonPath("$.data[0].matchPurchasePurpose").value(firstData.matchPurchasePurpose()))
        .andExpect(jsonPath("$.data[0].nominated.totalQuantity").value(firstData.nominated().totalQuantity()))
        .andExpect(jsonPath("$.data[0].inner.totalQuantity").value(firstData.inner().totalQuantity()));

    verify(this.queryBus).ask(new GetUsesAvailabilityV2Query(request));
  }
}
