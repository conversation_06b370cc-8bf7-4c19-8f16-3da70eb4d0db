package com.inditex.icdmdemg.apirest.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.UUID;

import com.inditex.icdmdemg.application.distributionnominated.command.SendAdminEventDistributionNominatedCommand.SendEventRequest;
import com.inditex.icdmdemg.dto.DistributionNominatedAdminPostRequestDTO;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DistributionNominatedAdminRequestDTOMapperTest {
  private DistributionNominatedAdminRequestDTOMapper sut;

  @BeforeEach
  void setUp() {
    this.sut = new DistributionNominatedAdminRequestDTOMapper();
  }

  @Test
  void should_map() {
    final var id = UUID.randomUUID();
    final var request = Instancio.create(DistributionNominatedAdminPostRequestDTO.class);

    final var expected = new SendEventRequest(id, request.getEventTypeToSend());

    final var result = this.sut.toDomain(id, request);

    assertThat(result).isNotNull().isEqualTo(expected);
  }
}
