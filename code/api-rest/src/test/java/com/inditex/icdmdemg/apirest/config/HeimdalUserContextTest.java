package com.inditex.icdmdemg.apirest.config;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;

@ExtendWith({MockitoExtension.class})
class HeimdalUserContextTest {

  private final HeimdalUserContext heimdalUserContext = new HeimdalUserContext();

  @Test
  void getCurrentUser_authenticated() {
    final var user = "authenticated user";

    final Authentication authentication = mock(Authentication.class);
    when(authentication.isAuthenticated()).thenReturn(true);
    when(authentication.getName()).thenReturn(user);
    final SecurityContext securityContext = mock(SecurityContext.class);
    when(securityContext.getAuthentication()).thenReturn(authentication);
    SecurityContextHolder.setContext(securityContext);

    final var username = this.heimdalUserContext.getCurrentUser();

    assertThat(username).isNotNull().isEqualTo(user);
  }

  @Test
  void getCurrentUser_null_authentication() {
    final SecurityContext securityContext = mock(SecurityContext.class);
    when(securityContext.getAuthentication()).thenReturn(null);
    SecurityContextHolder.setContext(securityContext);

    final var username = this.heimdalUserContext.getCurrentUser();

    assertNotNull(username);
    assertThat(username).isEqualTo(UserContext.ANONYMOUS_USER);
  }

  @Test
  void getCurrentUser_not_authenticated() {
    final Authentication authentication = mock(Authentication.class);
    when(authentication.isAuthenticated()).thenReturn(false);
    final SecurityContext securityContext = mock(SecurityContext.class);
    when(securityContext.getAuthentication()).thenReturn(authentication);
    SecurityContextHolder.setContext(securityContext);

    final var username = this.heimdalUserContext.getCurrentUser();

    assertNotNull(username);
    assertThat(username).isEqualTo(UserContext.ANONYMOUS_USER);
  }

}
