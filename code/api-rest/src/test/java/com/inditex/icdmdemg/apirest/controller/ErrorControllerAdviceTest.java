package com.inditex.icdmdemg.apirest.controller;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Set;
import java.util.UUID;

import com.inditex.amigafwk.service.rest.core.properties.WebApiConfigProperties;
import com.inditex.icdmdemg.domain.shared.Error.MessageError;
import com.inditex.icdmdemg.domain.shared.Error.NotFound;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.constraints.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ProblemDetail;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.validation.FieldError;
import org.springframework.web.context.request.ServletWebRequest;

@ExtendWith(MockitoExtension.class)
class ErrorControllerAdviceTest {

  ServletWebRequest webRequest;

  @Mock
  WebApiConfigProperties webApiConfigProperties;

  @InjectMocks
  ErrorControllerAdvice controllerAdvice;

  @BeforeEach
  void setUp() {
    final MockHttpServletRequest mockRequest = new MockHttpServletRequest();

    this.webRequest = new ServletWebRequest(mockRequest);
  }

  @Test
  void should_parse_constraint_violation() {
    final Validator validator = Validation.buildDefaultValidatorFactory().getValidator();
    final Set<ConstraintViolation<TestBean>> violations = validator.validate(new TestBean());
    final ConstraintViolationException exception = new ConstraintViolationException(violations);
    final var expectedProblemDetail = ProblemDetail.forStatusAndDetail(HttpStatus.BAD_REQUEST, String.format("Bad request: %s",
        exception.getConstraintViolations().stream().map(constraintViolation -> new FieldError(
            constraintViolation.getRootBeanClass().getName(),
            constraintViolation.getPropertyPath().toString(),
            constraintViolation.getMessage())).toList()));

    final var actual = this.controllerAdvice.handleConstraintViolationException(exception, this.webRequest);

    assertThat(actual.getBody()).isEqualTo(expectedProblemDetail);
  }

  @Test
  void should_parse_domain_error_to_http_error_when_message_error() {
    final var reason = "example";
    final var exception = new ErrorException(new MessageError(reason));
    final var expectedProblemDetail = ProblemDetail.forStatusAndDetail(HttpStatus.BAD_REQUEST, reason);

    final var actual = this.controllerAdvice.handleDomainErrorException(exception, this.webRequest);

    assertThat(actual.getBody()).isEqualTo(expectedProblemDetail);
  }

  @Test
  void should_parse_domain_error_to_http_error_when_not_found() {
    final var id = UUID.randomUUID().toString();
    final var exception = new ErrorException(new NotFound(id));
    final var expectedProblemDetail = ProblemDetail.forStatusAndDetail(HttpStatus.NOT_FOUND, "Resource not found with id " + id);

    final var actual = this.controllerAdvice.handleDomainErrorException(exception, this.webRequest);

    assertThat(actual.getBody()).isEqualTo(expectedProblemDetail);
  }

  @Test
  void should_parse_domain_error_to_http_error_when_unknown_error() {
    final var exception = new ErrorException(null);
    final var expectedProblemDetail = ProblemDetail.forStatus(HttpStatus.INTERNAL_SERVER_ERROR);

    final var actual = this.controllerAdvice.handleDomainErrorException(exception, this.webRequest);

    assertThat(actual.getBody()).isEqualTo(expectedProblemDetail);
  }

  public static class TestBean {
    @NotNull
    private final String testProductReferenceId = null;
  }

}
