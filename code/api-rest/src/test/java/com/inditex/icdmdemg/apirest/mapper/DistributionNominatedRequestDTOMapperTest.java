package com.inditex.icdmdemg.apirest.mapper;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.apirest.config.UserContext;
import com.inditex.icdmdemg.application.distributionnominated.command.CreateDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.CreateDistributionNominatedCommand.NominatedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PatchDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.PatchDistributionNominatedCommand.PatchNominatedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.AutoRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.PreselectedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.PreselectedRequest.LineSelectionRequest;
import com.inditex.icdmdemg.dto.DistributionNominatedRequestDTO;
import com.inditex.icdmdemg.dto.DistributionNominatedRequestPatchDTO;
import com.inditex.icdmdemg.dto.LineDTO;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class DistributionNominatedRequestDTOMapperTest {

  private DistributionNominatedRequestDTOMapper sut;

  @BeforeEach
  void setUp() {
    this.sut = new DistributionNominatedRequestDTOMapper();
  }

  @Test
  void should_throw_from_null_create_request() {
    assertThatThrownBy(() -> this.sut.toCreateCommand(null, UserContext.ANONYMOUS_USER)).isInstanceOf(NullPointerException.class);
  }

  @Test
  void should_map_create_command_planned() {
    final var request = Instancio.create(DistributionNominatedRequestDTO.class);

    final var expected = this.buildExpectedPreselectedCreateDistributionNominatedCommand(request);

    final var result = this.sut.toCreateCommand(request, UserContext.ANONYMOUS_USER);

    assertThat(result).isNotNull().isEqualTo(expected);
  }

  @Test
  void should_map_create_command_without_plan() {
    final var request = Instancio.of(DistributionNominatedRequestDTO.class)
        .set(field("plan"), null).create();

    final var expected = this.buildExpectedAutomaticCreateDistributionNominatedCommand(request);

    final var result = this.sut.toCreateCommand(request, UserContext.ANONYMOUS_USER);

    assertThat(result).isNotNull().isEqualTo(expected);
  }

  @Test
  void should_throw_from_null_modify_request() {
    assertThatThrownBy(() -> this.sut.toPatchCommand(null, null, UserContext.ANONYMOUS_USER))
        .isInstanceOf(NullPointerException.class);
    assertThatThrownBy(() -> this.sut.toPatchCommand(UUID.randomUUID(), null, UserContext.ANONYMOUS_USER))
        .isInstanceOf(NullPointerException.class);
  }

  @Test
  void should_map_modify_command_planned() {
    final var distributionNominatedId = UUID.randomUUID();
    final var request = Instancio.create(DistributionNominatedRequestPatchDTO.class);

    final var expected = this.buildExpectedPreselectedModifyDistributionNominatedCommand(distributionNominatedId, request);

    final var result = this.sut.toPatchCommand(distributionNominatedId, request, UserContext.ANONYMOUS_USER);

    assertThat(result).isNotNull().isEqualTo(expected);
  }

  @Test
  void should_map_modify_command_without_plan() {
    final var distributionNominatedId = UUID.randomUUID();
    final var request = Instancio.of(DistributionNominatedRequestPatchDTO.class)
        .set(field("plan"), null).create();

    final var expected = this.buildExpectedAutomaticModifyDistributionNominatedCommand(distributionNominatedId, request);

    final var result = this.sut.toPatchCommand(distributionNominatedId, request, UserContext.ANONYMOUS_USER);

    assertThat(result).isNotNull().isEqualTo(expected);
  }

  private CreateDistributionNominatedCommand buildExpectedAutomaticCreateDistributionNominatedCommand(
      final DistributionNominatedRequestDTO request) {
    return new CreateDistributionNominatedCommand(
        this.nominatedRequestFromRequest(request),
        new AutoRequest());
  }

  private CreateDistributionNominatedCommand buildExpectedPreselectedCreateDistributionNominatedCommand(
      final DistributionNominatedRequestDTO request) {
    return new CreateDistributionNominatedCommand(
        this.nominatedRequestFromRequest(request),
        new PreselectedRequest(this.lineSelectionsRequestFromLineDTOs(request.getPlan().getLines())));
  }

  private PatchDistributionNominatedCommand buildExpectedPreselectedModifyDistributionNominatedCommand(
      UUID distributionNominatedId,
      DistributionNominatedRequestPatchDTO request) {
    return new PatchDistributionNominatedCommand(
        this.modifyNominatedRequestFromRequest(distributionNominatedId, request),
        new PreselectedRequest(this.lineSelectionsRequestFromLineDTOs(request.getPlan().getLines())));
  }

  private PatchDistributionNominatedCommand buildExpectedAutomaticModifyDistributionNominatedCommand(
      UUID distributionNominatedId,
      DistributionNominatedRequestPatchDTO request) {
    return new PatchDistributionNominatedCommand(
        this.modifyNominatedRequestFromRequest(distributionNominatedId, request),
        new AutoRequest());
  }

  private NominatedRequest nominatedRequestFromRequest(DistributionNominatedRequestDTO request) {
    return new NominatedRequest(
        request.getReferenceId(),
        request.getUseId(),
        request.getBudgetCycle(),
        request.getProductOrderId(),
        request.getProductVariantGroupId(),
        request.getTheoreticalQuantity(),
        request.getRequestedQuantity(),
        request.getConsumptionFactor(),
        UserContext.ANONYMOUS_USER);
  }

  private PatchNominatedRequest modifyNominatedRequestFromRequest(UUID distributionNominatedId,
      DistributionNominatedRequestPatchDTO request) {
    return new PatchNominatedRequest(distributionNominatedId,
        request.getTheoreticalQuantity(),
        request.getRequestedQuantity(),
        request.getConsumptionFactor(),
        UserContext.ANONYMOUS_USER);
  }

  private List<LineSelectionRequest> lineSelectionsRequestFromLineDTOs(final List<LineDTO> lineDTOs) {
    return lineDTOs.stream()
        .map(lineDTO -> new LineSelectionRequest(
            lineDTO.getCommitmentOrderId(),
            lineDTO.getCommitmentOrderLineId(),
            lineDTO.getRequestedQuantity()))
        .toList();
  }

}
