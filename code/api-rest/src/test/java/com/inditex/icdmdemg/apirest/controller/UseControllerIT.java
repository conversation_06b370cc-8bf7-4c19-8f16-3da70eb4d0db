package com.inditex.icdmdemg.apirest.controller;

import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

import com.inditex.amigafwk.service.aaa.userdetails.heimdal.HeimdalUser;
import com.inditex.amigafwk.service.aaa.userdetails.heimdal.model.HeimdalUserDetails;
import com.inditex.amigafwk.service.rest.core.properties.WebApiConfigProperties;
import com.inditex.icdmdemg.apirest.config.HeimdalUserContext;
import com.inditex.icdmdemg.apirest.config.JacksonOffsetDateTimeMapperConfiguration;
import com.inditex.icdmdemg.apirest.config.UserContext;
import com.inditex.icdmdemg.apirest.mapper.UseDTOMapper;
import com.inditex.icdmdemg.apirest.mapper.UseRequestDTOMapper;
import com.inditex.icdmdemg.apirest.pagination.PageMapper;
import com.inditex.icdmdemg.application.use.command.CreateUseCommand;
import com.inditex.icdmdemg.application.use.command.CreateUseCommand.UseRequest;
import com.inditex.icdmdemg.application.use.command.CreateUseCommand.UseRequest.Condition;
import com.inditex.icdmdemg.application.use.command.CreateUseResult;
import com.inditex.icdmdemg.application.use.command.PatchUseCommand;
import com.inditex.icdmdemg.application.use.command.UseRequestNames;
import com.inditex.icdmdemg.application.use.command.UseRequestNames.UseRequestName;
import com.inditex.icdmdemg.application.use.query.GetUseByIdsQuery;
import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.mother.UseMother;
import com.inditex.icdmdemg.dto.AssignablePatchDTO;
import com.inditex.icdmdemg.dto.ConditionParametersDTO;
import com.inditex.icdmdemg.dto.NamePairsDTO;
import com.inditex.icdmdemg.dto.PurchasePurposeDTO;
import com.inditex.icdmdemg.dto.UsePatchRequestDTO;
import com.inditex.icdmdemg.dto.UseRequestDTO;
import com.inditex.icdmdemg.shared.utils.LocaleUtils;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;
import com.inditex.iopcmmnt.cqrs.core.QueryBus;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.SliceImpl;
import org.springframework.http.MediaType;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.test.context.support.WithUserDetails;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;

@ExtendWith(SpringExtension.class)
@WebMvcTest(controllers = UseController.class)
@ContextConfiguration(
    classes = {UseController.class, UseRequestDTOMapper.class, UseDTOMapper.class,
        ErrorControllerAdvice.class, WebApiConfigProperties.class, PageMapper.class,
        JacksonOffsetDateTimeMapperConfiguration.class, HeimdalUserContext.class,
        UseControllerIT.Config.class})
class UseControllerIT {

  @Autowired
  private MockMvc mockMvc;

  @MockitoBean
  private CommandBus commandBus;

  @MockitoBean
  private QueryBus queryBus;

  @Autowired
  private ObjectMapper objectMapper;

  @Autowired
  private UserContext userContext;

  @Test
  @WithUserDetails
  void useGet_should_return_values() throws Exception {
    final var useId1 = UUID.randomUUID();
    final var useId2 = UUID.randomUUID();
    final var language = "es";
    final var pageable = PageRequest.of(0, 50);
    final var innerResult = UseMother.generateBuild().build();

    final var slicedResult = new SliceImpl<>(List.of(innerResult), pageable, false);

    final var query = new GetUseByIdsQuery(List.of(useId1, useId2), pageable);
    doReturn(slicedResult).when(this.queryBus).ask(query);

    this.mockMvc
        .perform(get("/v1/uses?useIds={uuid1}&useIds={uuid2}", useId1, useId2)
            .header("Accept-language", language)
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.data").isNotEmpty());

    verify(this.queryBus).ask(query);
  }

  @Test
  @WithUserDetails
  void useGet_return_empty_result_if_uuids_not_exist() throws Exception {
    final var useId1 = UUID.randomUUID();
    final var useId2 = UUID.randomUUID();
    final var language = "es";
    final var pageable = PageRequest.of(0, 50);

    final var slicedResult = new SliceImpl<>(List.of(), pageable, false);

    final var query = new GetUseByIdsQuery(List.of(useId1, useId2), pageable);
    doReturn(slicedResult).when(this.queryBus).ask(query);

    this.mockMvc
        .perform(get("/v1/uses?useIds={uuid1}&useIds={uuid2}", useId1, useId2)
            .header("Accept-language", language)
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.data").isEmpty());

    verify(this.queryBus).ask(query);
  }

  @Test
  @WithUserDetails
  void useGet_return_results_when_no_ids_sent() throws Exception {
    final var language = "es";
    final var pageable = PageRequest.of(0, 50);
    final var innerResult = UseMother.generateBuild().build();

    final var slicedResult = new SliceImpl<>(List.of(innerResult), pageable, false);

    final var query = new GetUseByIdsQuery(List.of(), pageable);
    doReturn(slicedResult).when(this.queryBus).ask(query);

    this.mockMvc
        .perform(get("/v1/uses")
            .header("Accept-language", language)
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(jsonPath("$.data").isNotEmpty());

    verify(this.queryBus).ask(query);
  }

  @Test
  @WithUserDetails
  void usePost_shouldReturnCreated() throws Exception {
    final var useId = UUID.randomUUID();

    final var useRequestDTO = this.generateUseRequestDTO();

    final UseRequest useRequest = new UseRequest(this.getUseRequestNames(useRequestDTO.getNames()),
        useRequestDTO.getAssignable().stream().toList(),
        useRequestDTO.getPurchasePurpose().getTaxonomy(),
        useRequestDTO.getPurchasePurpose().getCustomer(),
        useRequestDTO.getPurchasePurpose().getPurchaseTypes().stream().toList(),
        useRequestDTO.getPurchasePurpose().getConditionParameters().stream()
            .map(condition -> new Condition(condition.getCondition(), condition.getParameterConditionName(),
                condition.getParameterConditionValues()))
            .toList(),
        this.userContext.getCurrentUser());

    final var useResult = new CreateUseResult(new Use.Id(useId));

    doReturn(useResult).when(this.commandBus).execute(new CreateUseCommand(useRequest));

    this.mockMvc
        .perform(post("/v1/uses", useRequestDTO)
            .content(this.objectMapper.writeValueAsString(useRequestDTO))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isCreated())
        .andExpect(jsonPath("useId").value(String.valueOf(useResult.useId().value())));

    verify(this.commandBus).execute(new CreateUseCommand(useRequest));
  }

  @Test
  @WithUserDetails
  void usePatch_shouldReturnUpdated() throws Exception {
    final var useId = UUID.randomUUID();

    final var usePatchRequestDTO = this.generateUsePatchRequestDTO();
    final var names = this.getUseRequestNames(usePatchRequestDTO.getNames());

    this.mockMvc
        .perform(patch("/v1/uses/{useId}", useId)
            .content(this.objectMapper.writeValueAsString(usePatchRequestDTO))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());

    verify(this.commandBus).execute(
        new PatchUseCommand(useId,
            names,
            List.of(usePatchRequestDTO.getAssignable().getValue()), this.userContext.getCurrentUser()));
  }

  @Test
  @WithUserDetails
  void usePatch_shouldReturnUpdatedWhenNullAssignable() throws Exception {
    final var useId = UUID.randomUUID();

    final var usePatchRequestDTO = this.generateUsePatchRequestDTONullAssignable();
    final var names = this.getUseRequestNames(usePatchRequestDTO.getNames());

    this.mockMvc
        .perform(patch("/v1/uses/{useId}", useId)
            .content(this.objectMapper.writeValueAsString(usePatchRequestDTO))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());

    verify(this.commandBus).execute(
        new PatchUseCommand(useId,
            names,
            null, this.userContext.getCurrentUser()));
  }

  @Test
  @WithUserDetails
  void usePatch_shouldReturnUpdatedWhenNullNames() throws Exception {
    final var useId = UUID.randomUUID();

    final var usePatchRequestDTO = this.generateUsePatchRequestDTONullNames();
    final var names = this.getUseRequestNames(usePatchRequestDTO.getNames());

    this.mockMvc
        .perform(patch("/v1/uses/{useId}", useId)
            .content(this.objectMapper.writeValueAsString(usePatchRequestDTO))
            .contentType(MediaType.APPLICATION_JSON))
        .andExpect(status().isNoContent());

    verify(this.commandBus).execute(
        new PatchUseCommand(useId,
            names,
            List.of(usePatchRequestDTO.getAssignable().getValue()), this.userContext.getCurrentUser()));
  }

  private UseRequestNames getUseRequestNames(final List<NamePairsDTO> usePatchRequestDTO) {
    return new UseRequestNames(usePatchRequestDTO.stream()
        .map(name -> new UseRequestName(LocaleUtils.createLocale(name.getLocale()), name.getTranslation()))
        .toList());
  }

  private UsePatchRequestDTO generateUsePatchRequestDTONullNames() {
    final List<NamePairsDTO> names = List.of();
    final AssignablePatchDTO assignable = this.generateAssignablePatch();
    final UsePatchRequestDTO usePatchRequestDTO = new UsePatchRequestDTO();

    usePatchRequestDTO.setNames(names);
    usePatchRequestDTO.setAssignable(assignable);

    return usePatchRequestDTO;
  }

  private UsePatchRequestDTO generateUsePatchRequestDTONullAssignable() {
    final List<NamePairsDTO> names = new ArrayList<>(this.generateNames());
    names.add(new NamePairsDTO("pt", "pt descripsao"));
    final UsePatchRequestDTO usePatchRequestDTO = new UsePatchRequestDTO();

    usePatchRequestDTO.setNames(names);
    usePatchRequestDTO.setAssignable(null);

    return usePatchRequestDTO;
  }

  private UsePatchRequestDTO generateUsePatchRequestDTO() {
    final List<NamePairsDTO> names = new ArrayList<>(this.generateNames());
    names.add(new NamePairsDTO("pt", "pt descripsao"));
    final AssignablePatchDTO assignable = this.generateAssignablePatch();
    final UsePatchRequestDTO usePatchRequestDTO = new UsePatchRequestDTO();

    usePatchRequestDTO.setNames(names);
    usePatchRequestDTO.setAssignable(assignable);

    return usePatchRequestDTO;
  }

  private AssignablePatchDTO generateAssignablePatch() {
    return AssignablePatchDTO.NOMINATED;
  }

  private UseRequestDTO generateUseRequestDTO() {
    final List<NamePairsDTO> names = this.generateNames();
    final Set<String> assignable = this.generateAssignable();
    final PurchasePurposeDTO purchasePurpose = this.generatePurchasePurposeDTO();

    return new UseRequestDTO(names, assignable, purchasePurpose);
  }

  private List<NamePairsDTO> generateNames() {
    final NamePairsDTO namePairES = new NamePairsDTO("es", "descripcion");
    final NamePairsDTO namePairEN = new NamePairsDTO("en", "description");
    return List.of(namePairES, namePairEN);
  }

  private Set<String> generateAssignable() {
    return Set.of("NOMINATED");
  }

  @NotNull
  private PurchasePurposeDTO generatePurchasePurposeDTO() {
    final Set<String> purchaseTypes = new HashSet<>(List.of("NOMINATED"));
    final ConditionParametersDTO conditionParametersDTO =
        new ConditionParametersDTO("EQUALS", "BUYERGROUP", List.of("urn:BUYERGROUP:123e4567-e89b-12d3-a456-526655440002"));
    final List<ConditionParametersDTO> conditionParametersDTOList = List.of(conditionParametersDTO);
    return new PurchasePurposeDTO("FABRIC", "urn:BUYER:123e4567-e89b-12d3-a456-526655440002", purchaseTypes, conditionParametersDTOList);
  }

  @Configuration
  static class Config {
    @Bean
    public UserDetailsService userDetailsService() {
      return username -> HeimdalUser.create(HeimdalUserDetails.minimum("app", username), List.of());
    }
  }
}
