package com.inditex.icdmdemg.apirest.mapper;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailability;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityResponse;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityTotalInnerQuantity;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityTotalNominatedQuantity;

import org.junit.jupiter.api.Test;

class UsesAvailabilityResponseDTOMapperTest {

  final UsesAvailabilityResponseDTOMapper sut = new UsesAvailabilityResponseDTOMapper();

  @Test
  void should_map_usesAvailabilityResponse_to_usesAvailabilityResponseDTO() {

    final var data1 = new UsesAvailability(
        UUID.randomUUID(),
        "useName",
        List.of("purchaseType1", "purchaseType2"),
        true,
        new UsesAvailabilityTotalNominatedQuantity(BigDecimal.ONE),
        new UsesAvailabilityTotalInnerQuantity(BigDecimal.TEN));

    final var data2 = new UsesAvailability(
        UUID.randomUUID(),
        "useName",
        List.of("purchaseType1", "purchaseType2"),
        true,
        new UsesAvailabilityTotalNominatedQuantity(BigDecimal.ONE),
        new UsesAvailabilityTotalInnerQuantity(BigDecimal.TEN));

    final var usesAvailabilityResponse = new UsesAvailabilityResponse(List.of(data1, data2));

    final var result = this.sut.toDto(usesAvailabilityResponse);

    assertThat(result.getData()).hasSize(2);
    assertThat(result.getData().getFirst().getUseId()).isEqualTo(data1.useId());
    assertThat(result.getData().getFirst().getUseName()).isEqualTo(data1.useName());
    assertThat(result.getData().getFirst().getPurchaseTypes()).isEqualTo(data1.purchaseTypes());
    assertThat(result.getData().getFirst().getAssignable()).isEqualTo(data1.assignable());
    assertThat(result.getData().getFirst().getNominated().getTotalQuantity()).isEqualTo(data1.nominated().totalQuantity());
    assertThat(result.getData().getFirst().getInner().getTotalQuantity()).isEqualTo(data1.inner().totalQuantity());

    assertThat(result.getData().getLast().getUseId()).isEqualTo(data2.useId());
    assertThat(result.getData().getLast().getUseName()).isEqualTo(data2.useName());
    assertThat(result.getData().getLast().getPurchaseTypes()).isEqualTo(data2.purchaseTypes());
    assertThat(result.getData().getLast().getAssignable()).isEqualTo(data2.assignable());
    assertThat(result.getData().getLast().getNominated().getTotalQuantity()).isEqualTo(data2.nominated().totalQuantity());
    assertThat(result.getData().getLast().getInner().getTotalQuantity()).isEqualTo(data2.inner().totalQuantity());
  }
}
