package com.inditex.icdmdemg.apirest.mapper;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import static org.assertj.core.api.Assertions.assertThat;

import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.DistributionEndDate;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.DistributionStartDate;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.TrackingCode;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class DistributionInnerDTOMapperTest {

  private DistributionInnerDTOMapper sut;

  @BeforeEach
  void setUp() {
    this.sut = new DistributionInnerDTOMapper();
  }

  @Test
  void should_map_ok() {
    final var distributionInner =
        DistributionInnerMother.pending().build();

    final var result = this.sut.toDto(distributionInner);

    assertThat(result).isNotNull();
    assertThat(result.getId()).isEqualTo(distributionInner.getId().value());
    assertThat(result.getReferenceId()).isEqualTo(distributionInner.referenceId().value());
    assertThat(result.getUseId()).isEqualTo(distributionInner.useId().value());
    assertThat(result.getBudgetCycle()).isEqualTo(distributionInner.budgetCycle().value());
    assertThat(result.getProductOrderId()).isEqualTo(distributionInner.productOrderId().value());
    assertThat(result.getProductVariantGroupId()).isEqualTo(distributionInner.productVariantGroupId().value());
    assertThat(result.getRequestedQuantity()).isEqualTo(distributionInner.requestedQuantity().value());
    assertThat(result.getDistributedQuantity()).isEqualTo(distributionInner.distributedQuantity().value());
    assertThat(result.getStatus()).isEqualTo(distributionInner.status().value());
    assertThat(result.getTheoreticalQuantity()).isEqualTo(distributionInner.theoreticalQuantity().value());
    assertThat(result.getConsumptionFactor()).isEqualTo(distributionInner.consumptionFactor().value());
    assertThat(result.getCreatedAt()).isEqualTo(distributionInner.audit().createdAt());
    assertThat(result.getUpdatedAt()).isEqualTo(distributionInner.audit().updatedAt());
    assertThat(result.getLines()).isNotNull().hasSize(distributionInner.lines().value().size());
    assertThat(result.getLines().get(0).getId()).isEqualTo(distributionInner.lines().value().get(0).id().value());
    assertThat(result.getLines().get(0).getRequestedQuantity()).isEqualTo(
        distributionInner.lines().value().get(0).requestedQuantity().value());
    assertThat(result.getLines().get(0).getDistributedQuantity())
        .isEqualTo(distributionInner.lines().value().get(0).distributedQuantity().value());
    assertThat(result.getLines().get(0).getDistributionStartDate())
        .isEqualTo(
            acceptNullElseMap(distributionInner.lines().value().get(0).distributionStartDate(), DistributionStartDate::value));
    assertThat(result.getLines().get(0).getDistributionEndDate())
        .isEqualTo(
            acceptNullElseMap(distributionInner.lines().value().get(0).distributionEndDate(), DistributionEndDate::value));
    assertThat(result.getLines().get(0).getTrackingCode())
        .isEqualTo(
            acceptNullElseMap(distributionInner.lines().value().get(0).trackingCode(), TrackingCode::value));
    assertThat(result.getLines().get(0).getCreatedAt()).isEqualTo(distributionInner.lines().value().get(0).audit().createdAt());
    assertThat(result.getLines().get(0).getUpdatedAt()).isEqualTo(distributionInner.lines().value().get(0).audit().updatedAt());
    assertThat(result.getPendingAssigned()).isEqualTo(distributionInner.pendingAssigned().value());

  }
}
