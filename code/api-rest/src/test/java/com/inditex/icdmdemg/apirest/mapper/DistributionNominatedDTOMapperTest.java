package com.inditex.icdmdemg.apirest.mapper;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import java.math.BigDecimal;

import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributionStartDate;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class DistributionNominatedDTOMapperTest {
  private DistributionNominatedDTOMapper sut;

  @BeforeEach
  void setUp() {
    this.sut = new DistributionNominatedDTOMapper();
  }

  @Test
  void should_map_from_domain_list_null() {
    assertThatThrownBy(() -> this.sut.toDto(null)).isInstanceOf(NullPointerException.class);
  }

  @Test
  void should_map_ok() {
    final var distributionNominated = DistributionNominatedMother.pending().build();

    final var result = this.sut.toDto(distributionNominated);

    assertThat(result).isNotNull();
    assertThat(result.getId()).isEqualTo(distributionNominated.getId().value());
    assertThat(result.getReferenceId()).isEqualTo(distributionNominated.referenceId().value());
    assertThat(result.getUseId()).isEqualTo(distributionNominated.useId().value());
    assertThat(result.getBudgetCycle()).isEqualTo(distributionNominated.budgetCycle().value());
    assertThat(result.getProductOrderId()).isEqualTo(distributionNominated.productOrderId().value());
    assertThat(result.getProductVariantGroupId()).isEqualTo(distributionNominated.productVariantGroupId().value());
    assertThat(result.getRequestedQuantity()).isEqualTo(distributionNominated.requestedQuantity().value());
    assertThat(result.getDistributedQuantity()).isEqualTo(distributionNominated.requestedQuantity().value());
    assertThat(result.getStatus()).isEqualTo(distributionNominated.status().value());
    assertThat(result.getTheoreticalQuantity()).isEqualTo(distributionNominated.theoreticalQuantity().value());
    assertThat(result.getConsumptionFactor()).isEqualTo(distributionNominated.consumptionFactor().value());
    assertThat(result.getCreatedAt()).isEqualTo(distributionNominated.audit().createdAt());
    assertThat(result.getUpdatedAt()).isEqualTo(distributionNominated.audit().updatedAt());
    assertThat(result.getLines()).isNotNull().hasSize(distributionNominated.lines().value().size());
    assertThat(result.getLines().getFirst().getId())
        .isEqualTo(distributionNominated.lines().value().getFirst().id().value());
    assertThat(result.getLines().getFirst().getCommitmentOrderId())
        .isEqualTo(distributionNominated.lines().value().getFirst().commitmentOrder().id().value());
    assertThat(result.getLines().getFirst().getCommitmentOrderLineId())
        .isEqualTo(distributionNominated.lines().value().getFirst().commitmentOrder().lineId().value());
    assertThat(result.getLines().getFirst().getRequestedQuantity())
        .isEqualTo(distributionNominated.lines().value().getFirst().requestedQuantity().value());
    assertThat(result.getLines().getFirst().getDistributedQuantity())
        .isEqualTo(distributionNominated.lines().value().getFirst().requestedQuantity().value());
    assertThat(result.getLines().getFirst().getDistributionStartDate()).isEqualTo(acceptNullElseMap(
        distributionNominated.lines().value().getFirst().distributionStartDate(), DistributionStartDate::value));
    assertThat(result.getLines().getFirst().getCreatedAt())
        .isEqualTo(distributionNominated.lines().value().getFirst().audit().createdAt());
    assertThat(result.getPlan().getValue()).isEqualTo(distributionNominated.plan().value());
    assertThat(result.getPendingAssigned()).isEqualTo(new BigDecimal("0.00"));
    assertThat(result.getLines().getFirst().getUpdatedAt())
        .isEqualTo(distributionNominated.lines().value().getFirst().audit().updatedAt());
  }
}
