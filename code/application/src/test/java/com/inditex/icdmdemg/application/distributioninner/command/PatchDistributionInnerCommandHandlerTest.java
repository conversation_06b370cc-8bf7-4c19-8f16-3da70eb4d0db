package com.inditex.icdmdemg.application.distributioninner.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.params.ParameterizedTest.INDEX_PLACEHOLDER;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerLineMother;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAuditMother;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.CurrentMethodName;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;
import com.inditex.icdmdemg.shared.utils.mother.BigDecimalMother;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PatchDistributionInnerCommandHandlerTest {

  @Mock
  private DistributionInnerRepository distributionInnerRepository;

  @Mock
  private ClockUtils clockUtils;

  @Mock
  private UuidGenerator uuidGenerator;

  @Mock
  private EventBus eventBus;

  @InjectMocks
  private PatchDistributionInnerCommandHandler sut;

  @Test
  void should_not_register_event_when_no_modification() {
    final var distributionInnerBuilder = DistributionInnerMother.pending();
    final var distributionInner = distributionInnerBuilder.build();
    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(distributionInner.audit().updatedAt());
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.of(distributionInner));

    this.sut.doHandle(commandFromDistributionInner(distributionInner));

    verifyNoInteractions(this.uuidGenerator);
    verify(this.eventBus).send(List.of());
    verify(this.distributionInnerRepository).save(distributionInner);
    verify(this.distributionInnerRepository).findById(distributionInner.getId());
  }

  @Test
  void should_update_theoretical_and_consumption_factor() {
    final var now = OffsetDateTime.now();
    final var triggeredBy = "triggeredBy";
    final var distributionInnerBuilder = DistributionInnerMother.pending();
    final var distributionInner = distributionInnerBuilder.build();
    final var distributionInnerUpdated = distributionInnerBuilder
        .theoreticalQuantity(DistributionInnerMother.randomTheoreticalQuantity())
        .consumptionFactor(DistributionInnerMother.randomConsumptionFactor())
        .audit(CompleteAuditMother.update(distributionInner.audit(), triggeredBy, now))
        .build();
    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(distributionInnerUpdated.audit().updatedAt());
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.of(distributionInner));

    this.sut.doHandle(commandFromDistributionInner(distributionInnerUpdated));

    verifyNoInteractions(this.uuidGenerator);
    verify(this.eventBus).send(List.of(new DistributionInnerUnifiedEvent(distributionInnerUpdated, EventType.CORRECTED)));
    verify(this.distributionInnerRepository).save(distributionInnerUpdated);
    verify(this.distributionInnerRepository).findById(distributionInnerUpdated.getId());
  }

  @Test
  void should_update_theoretical_and_consumption_factor_in_non_distributable() {
    final var now = OffsetDateTime.now();
    final var triggeredBy = "triggeredBy";
    final var distributionInnerBuilder = DistributionInnerMother.nonDistributable();
    final var distributionInner = distributionInnerBuilder.build();
    final var distributionInnerUpdated = distributionInnerBuilder
        .theoreticalQuantity(DistributionInnerMother.randomTheoreticalQuantity())
        .consumptionFactor(DistributionInnerMother.randomConsumptionFactor())
        .audit(CompleteAuditMother.update(distributionInner.audit(), triggeredBy, now))
        .build();
    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(distributionInnerUpdated.audit().updatedAt());
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.of(distributionInner));

    this.sut.doHandle(commandFromDistributionInner(distributionInnerUpdated));

    verifyNoInteractions(this.uuidGenerator);
    verify(this.eventBus).send(List.of(new DistributionInnerUnifiedEvent(distributionInnerUpdated, EventType.CORRECTED)));
    verify(this.distributionInnerRepository).save(distributionInnerUpdated);
    verify(this.distributionInnerRepository).findById(distributionInnerUpdated.getId());
  }

  @Test
  void should_increase_requested_quantity_when_di_is_non_distributable_and_register_updated_event() {
    final var now = OffsetDateTime.now();
    final var triggeredBy = "triggeredBy";
    final var initialRequestedQuantity = DistributionInnerMother.randomRequestedQuantity();
    final var increasedQuantity = DistributionInnerLineMother.randomRequestedQuantity();
    final var aliveLineBuilder = DistributionInnerLineMother.created()
        .theoreticalQuantity(new TheoreticalQuantity(BigDecimal.valueOf(100)))
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(initialRequestedQuantity.value()));
    final var distributionInnerLine = aliveLineBuilder.build();
    final var distributionInnerBuilder = DistributionInnerMother.nonDistributable()
        .requestedQuantity(initialRequestedQuantity)
        .lines(List.of(distributionInnerLine));
    final var distributionInner = distributionInnerBuilder.build();
    final var distributionInnerUpdated = distributionInnerBuilder
        .requestedQuantity(new RequestedQuantity(initialRequestedQuantity.value().add(increasedQuantity.value())))
        .audit(CompleteAuditMother.update(distributionInner.audit(), triggeredBy, now))
        .lines(List.of(aliveLineBuilder.requestedQuantity(new DistributionInnerLine.RequestedQuantity(
            distributionInner.requestedQuantity().value().add(increasedQuantity.value())))
            .audit(DistributionInnerLineMother.updateAudit(distributionInnerLine.audit(), now)).build()))
        .build();
    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(distributionInnerUpdated.audit().updatedAt());
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.of(distributionInner));

    this.sut.doHandle(commandFromDistributionInner(distributionInnerUpdated));

    verifyNoInteractions(this.uuidGenerator);
    verify(this.eventBus).send(List.of(new DistributionInnerUnifiedEvent(distributionInnerUpdated, EventType.UPDATED_NON_DISTRIBUTABLE)));
    verify(this.distributionInnerRepository).save(distributionInnerUpdated);
    verify(this.distributionInnerRepository).findById(distributionInnerUpdated.getId());
  }

  @Test
  void should_increase_requested_quantity_when_line_is_alive() {
    final var now = OffsetDateTime.now();
    final var triggeredBy = "triggeredBy";
    final var initialRequestedQuantity = DistributionInnerMother.randomRequestedQuantity();
    final var increasedQuantity = DistributionInnerLineMother.randomRequestedQuantity();
    final var aliveLineBuilder = DistributionInnerLineMother.created()
        .theoreticalQuantity(new TheoreticalQuantity(BigDecimal.valueOf(100)))
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(initialRequestedQuantity.value()));
    final var distributionInnerLine = aliveLineBuilder.build();
    final var distributionInnerBuilder = DistributionInnerMother.pending()
        .requestedQuantity(initialRequestedQuantity)
        .lines(List.of(distributionInnerLine));
    final var distributionInner = distributionInnerBuilder.build();
    final var distributionInnerUpdated = distributionInnerBuilder
        .requestedQuantity(new RequestedQuantity(initialRequestedQuantity.value().add(increasedQuantity.value())))
        .audit(CompleteAuditMother.update(distributionInner.audit(), triggeredBy, now))
        .lines(List.of(aliveLineBuilder.requestedQuantity(new DistributionInnerLine.RequestedQuantity(
            distributionInner.requestedQuantity().value().add(increasedQuantity.value())))
            .audit(DistributionInnerLineMother.updateAudit(distributionInnerLine.audit(), now)).build()))
        .build();
    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(distributionInnerUpdated.audit().updatedAt());
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.of(distributionInner));

    this.sut.doHandle(commandFromDistributionInner(distributionInnerUpdated));

    verifyNoInteractions(this.uuidGenerator);
    verify(this.eventBus).send(List.of(new DistributionInnerUnifiedEvent(distributionInnerUpdated, EventType.UPDATED_PENDING)));
    verify(this.distributionInnerRepository).save(distributionInnerUpdated);
    verify(this.distributionInnerRepository).findById(distributionInnerUpdated.getId());
  }

  @Test
  void should_create_new_line_when_increase_requested_quantity_and_start_date_not_null() {
    final var now = OffsetDateTime.now();
    final var triggeredBy = "triggeredBy";
    final var initialRequestedQuantity = new RequestedQuantity(BigDecimal.valueOf(100));
    final var increasedQuantity = new DistributionInnerLine.RequestedQuantity(BigDecimal.valueOf(100));
    final var inProgressLineBuilder = DistributionInnerLineMother.startedButNotDistributed()
        .theoreticalQuantity(new TheoreticalQuantity(BigDecimal.valueOf(100)))
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(initialRequestedQuantity.value()));
    final var inProgressLine = inProgressLineBuilder.build();
    final var distributionInnerBuilder = DistributionInnerMother.inProgress()
        .requestedQuantity(initialRequestedQuantity)
        .lines(List.of(inProgressLine));
    final var distributionInner = distributionInnerBuilder.build();
    final var newLine = DistributionInnerLineMother.created()
        .requestedQuantity(increasedQuantity)
        .theoreticalQuantity(new TheoreticalQuantity(BigDecimal.valueOf(50)))
        .audit(DistributionInnerLineMother.createAudit(now))
        .build();
    final var distributionInnerUpdated = distributionInnerBuilder
        .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(200)))
        .audit(CompleteAuditMother.update(distributionInner.audit(), triggeredBy, now))
        .lines(List.of(
            inProgressLineBuilder
                .requestedQuantity(new DistributionInnerLine.RequestedQuantity(BigDecimal.valueOf(100)))
                .theoreticalQuantity(new TheoreticalQuantity(BigDecimal.valueOf(50))).build(),
            newLine))
        .build();
    when(this.uuidGenerator.generate()).thenReturn(newLine.id().value());
    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(distributionInnerUpdated.audit().updatedAt());
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.of(distributionInner));

    this.sut.doHandle(commandFromDistributionInner(distributionInnerUpdated));

    verify(this.eventBus).send(List.of(new DistributionInnerUnifiedEvent(distributionInnerUpdated, EventType.UPDATED_PENDING)));
    verify(this.distributionInnerRepository).save(distributionInnerUpdated);
    verify(this.distributionInnerRepository).findById(distributionInnerUpdated.getId());
  }

  @Test
  void should_create_new_line_when_increase_requested_quantity_and_di_sent() {
    final var now = OffsetDateTime.now();
    final var triggeredBy = "triggeredBy";
    final var initialRequestedQuantity = new RequestedQuantity(BigDecimal.valueOf(100));
    final var increasedQuantity = new DistributionInnerLine.RequestedQuantity(BigDecimal.valueOf(100));
    final var sentLineBuilder = DistributionInnerLineMother.sent()
        .theoreticalQuantity(new TheoreticalQuantity(BigDecimal.valueOf(100)))
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(initialRequestedQuantity.value()));
    final var sentLine = sentLineBuilder.build();
    final var distributionInnerBuilder = DistributionInnerMother.sent()
        .requestedQuantity(initialRequestedQuantity)
        .lines(List.of(sentLine));
    final var distributionInner = distributionInnerBuilder.build();
    final var newLine = DistributionInnerLineMother.created()
        .requestedQuantity(increasedQuantity)
        .theoreticalQuantity(new TheoreticalQuantity(BigDecimal.valueOf(50)))
        .audit(DistributionInnerLineMother.createAudit(now))
        .build();
    final var distributionInnerUpdated = distributionInnerBuilder
        .status(DistributionInnerStatus.IN_PROGRESS)
        .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(200)))
        .audit(CompleteAuditMother.update(distributionInner.audit(), triggeredBy, now))
        .lines(List.of(
            sentLineBuilder
                .requestedQuantity(new DistributionInnerLine.RequestedQuantity(BigDecimal.valueOf(100)))
                .theoreticalQuantity(new TheoreticalQuantity(BigDecimal.valueOf(50))).build(),
            newLine))
        .build();
    when(this.uuidGenerator.generate()).thenReturn(newLine.id().value());
    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(distributionInnerUpdated.audit().updatedAt());
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.of(distributionInner));

    this.sut.doHandle(commandFromDistributionInner(distributionInnerUpdated));

    verify(this.eventBus).send(List.of(new DistributionInnerUnifiedEvent(distributionInnerUpdated, EventType.UPDATED_PENDING)));
    verify(this.distributionInnerRepository).save(distributionInnerUpdated);
    verify(this.distributionInnerRepository).findById(distributionInnerUpdated.getId());
  }

  @Test
  void should_fail_when_distribution_inner_does_not_exists() {
    final var distributionInnerId = "75e1ecfe-77cc-442c-9b93-41b5de71be56";
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.empty());

    final var command = new PatchDistributionInnerCommand(
        UUID.fromString(distributionInnerId),
        BigDecimalMother.random(), BigDecimalMother.random(), BigDecimalMother.random(),
        "triggeredBy");
    assertThatThrownBy(() -> this.sut.doHandle(command))
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("Distribution Inner 75e1ecfe-77cc-442c-9b93-41b5de71be56 not found");

    verifyNoInteractions(this.uuidGenerator);
    verifyNoInteractions(this.eventBus);
    verify(this.distributionInnerRepository, never()).save(any());
  }

  @ParameterizedTest
  @EnumSource(value = DistributionInnerStatus.class, names = {"CLOSED", "CANCELED"})
  void should_fail_when_distribution_inner_can_not_be_updated(final DistributionInnerStatus status) {
    final var distributionInner = DistributionInnerMother.pending()
        .status(status)
        .build();
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.of(distributionInner));

    final var command = commandFromDistributionInner(distributionInner);
    assertThatThrownBy(() -> this.sut.doHandle(command))
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining(String.format("Distribution inner cannot be modified since its status is %s", status));

    verifyNoInteractions(this.uuidGenerator);
    verifyNoInteractions(this.eventBus);
    verify(this.distributionInnerRepository, never()).save(any());
  }

  @Test
  void decreaseRequestedQuantityWhenTheNewRequestedQuantityIsLowerThanDistributedQuantityShouldThrowException() {

    final var initialRequestedQuantity = BigDecimal.valueOf(1000);
    final var initialDistributedQuantity = BigDecimal.valueOf(300);
    final var newRequestedQuantity = BigDecimal.valueOf(299);

    final var distributionInnerLine = DistributionInnerLineMother.created()
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(initialRequestedQuantity))
        .distributedQuantity(new DistributionInnerLine.DistributedQuantity(initialDistributedQuantity))
        .build();

    final var distributionInner = DistributionInnerMother.pending()
        .requestedQuantity(new DistributionInner.RequestedQuantity(initialRequestedQuantity))
        .distributedQuantity(new DistributionInner.DistributedQuantity(initialDistributedQuantity))
        .lines(List.of(distributionInnerLine))
        .build();

    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.of(distributionInner));

    final var command = new PatchDistributionInnerCommand(
        distributionInner.getId().value(),
        distributionInner.theoreticalQuantity().value(),
        newRequestedQuantity,
        distributionInner.consumptionFactor().value(),
        distributionInner.audit().updatedBy());

    assertThatThrownBy(() -> this.sut.doHandle(command))
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("Not enough requested quantity to decrease");

    verifyNoInteractions(this.uuidGenerator);
    verifyNoInteractions(this.eventBus);
    verify(this.distributionInnerRepository, never()).save(any());
  }

  @ParameterizedTest(name = "[" + INDEX_PLACEHOLDER + "] [{0}]")
  @MethodSource("getInputCasesAndExpectedReturnForDecreaseRequestedQuantity")
  void should_decrease_requested_quantity_ok(
      final String ignoredTestName,
      final DistributionInner distributionInner,
      final BigDecimal newRequestedQuantity,
      final EventType eventType,
      final List<RequestedAndDistributedValues> output) {

    final var command = new PatchDistributionInnerCommand(
        distributionInner.getId().value(),
        distributionInner.theoreticalQuantity().value(),
        newRequestedQuantity,
        distributionInner.consumptionFactor().value(),
        distributionInner.audit().updatedBy());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(OffsetDateTime.now());
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.of(distributionInner));

    this.sut.doHandle(command);

    verifyNoInteractions(this.uuidGenerator);
    verify(this.eventBus).send(List.of(new DistributionInnerUnifiedEvent(distributionInner, eventType)));
    verify(this.distributionInnerRepository).findById(distributionInner.getId());

    final ArgumentCaptor<DistributionInner> captor = ArgumentCaptor.forClass(DistributionInner.class);
    verify(this.distributionInnerRepository).save(captor.capture());
    final var distributionInnerSaved = captor.getValue();

    assertThat(distributionInnerSaved).isNotNull();
    assertThat(distributionInnerSaved.requestedQuantity()).isEqualTo(new DistributionInner.RequestedQuantity(newRequestedQuantity));
    assertThat(distributionInnerSaved.distributedQuantity())
        .isEqualTo(new DistributionInner.DistributedQuantity(distributionInner.distributedQuantity().value()));

    assertThat(distributionInnerSaved.lines().value()).hasSize(output.size());
    for (int i = 0; i < output.size(); i++) {
      final var lineSaved = distributionInnerSaved.lines().value().get(i);
      final var currentOutput = output.get(i);
      assertThat(lineSaved.requestedQuantity()).isEqualTo(new DistributionInnerLine.RequestedQuantity(currentOutput.requested()));
      assertThat(lineSaved.distributedQuantity()).isEqualTo(new DistributionInnerLine.DistributedQuantity(currentOutput.distributed()));
    }
  }

  public static Stream<Arguments> getInputCasesAndExpectedReturnForDecreaseRequestedQuantity() {
    return Stream.of(
        decreaseRequestedQuantityWhenThereIsNoDistributedQuantityAndDiIsPending(),
        decreaseRequestedQuantityWhenThereIsNoDistributedQuantityAndDiIsNonDistributable(),
        decreaseRequestedQuantityWhenThereIsDistributedQuantityButLowerThanNewRequestedQuantity(),
        decreaseRequestedQuantityWhenThereIsDistributedQuantityButLowerThanNewRequestedQuantityHavingTwoLines(),
        decreaseRequestedQuantityWhenThereIsDistributedQuantityAndNewRequestedQuantityIsEqualsDistributedHavingTwoLines(),
        decreaseRequestedQuantityWhenThereIsDistributedQuantityButLowerThanNewRequestedQuantityHavingThreeLinesRequest(),
        decreaseRequestedQuantityWhenThereIsDistributedQuantityButLowerThanNewRequestedQuantityHavingTwoLinesAndOneLineGoesToZero());
  }

  private static Arguments decreaseRequestedQuantityWhenThereIsNoDistributedQuantityAndDiIsPending() {

    final var initialRequestedQuantity = BigDecimal.valueOf(1000);
    final var initialDistributedQuantity = BigDecimal.valueOf(0);
    final var newRequestedQuantity = BigDecimal.valueOf(300);

    final var distributionInnerLine = DistributionInnerLineMother.created()
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(initialRequestedQuantity))
        .distributedQuantity(new DistributionInnerLine.DistributedQuantity(initialDistributedQuantity))
        .build();

    final var distributionInner = DistributionInnerMother.pending()
        .requestedQuantity(new DistributionInner.RequestedQuantity(initialRequestedQuantity))
        .distributedQuantity(new DistributionInner.DistributedQuantity(initialDistributedQuantity))
        .lines(List.of(distributionInnerLine))
        .build();

    return Arguments.of(
        CurrentMethodName.get(),
        distributionInner,
        newRequestedQuantity,
        EventType.UPDATED_PENDING,
        List.of(
            new RequestedAndDistributedValues(newRequestedQuantity, initialDistributedQuantity)));
  }

  private static Arguments decreaseRequestedQuantityWhenThereIsNoDistributedQuantityAndDiIsNonDistributable() {
    final var initialRequestedQuantity = BigDecimal.valueOf(1000);
    final var initialDistributedQuantity = BigDecimal.valueOf(0);
    final var newRequestedQuantity = BigDecimal.valueOf(300);

    final var distributionInnerLine = DistributionInnerLineMother.created()
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(initialRequestedQuantity))
        .distributedQuantity(new DistributionInnerLine.DistributedQuantity(initialDistributedQuantity))
        .build();

    final var distributionInner = DistributionInnerMother.nonDistributable()
        .requestedQuantity(new DistributionInner.RequestedQuantity(initialRequestedQuantity))
        .distributedQuantity(new DistributionInner.DistributedQuantity(initialDistributedQuantity))
        .lines(List.of(distributionInnerLine))
        .build();

    return Arguments.of(
        CurrentMethodName.get(),
        distributionInner,
        newRequestedQuantity,
        EventType.UPDATED_NON_DISTRIBUTABLE,
        List.of(
            new RequestedAndDistributedValues(newRequestedQuantity, initialDistributedQuantity)));
  }

  private static Arguments decreaseRequestedQuantityWhenThereIsDistributedQuantityButLowerThanNewRequestedQuantity() {
    final var initialRequestedQuantity = BigDecimal.valueOf(1000);
    final var initialDistributedQuantity = BigDecimal.valueOf(300);
    final var newRequestedQuantity = BigDecimal.valueOf(301);

    final var distributionInnerLine = DistributionInnerLineMother.created()
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(initialRequestedQuantity))
        .distributedQuantity(new DistributionInnerLine.DistributedQuantity(initialDistributedQuantity))
        .build();

    final var distributionInner = DistributionInnerMother.pending()
        .requestedQuantity(new DistributionInner.RequestedQuantity(initialRequestedQuantity))
        .distributedQuantity(new DistributionInner.DistributedQuantity(initialDistributedQuantity))
        .lines(List.of(distributionInnerLine))
        .build();

    return Arguments.of(
        CurrentMethodName.get(),
        distributionInner,
        newRequestedQuantity,
        EventType.UPDATED_PENDING,
        List.of(
            new RequestedAndDistributedValues(newRequestedQuantity, initialDistributedQuantity)));
  }

  private static Arguments decreaseRequestedQuantityWhenThereIsDistributedQuantityButLowerThanNewRequestedQuantityHavingTwoLines() {

    final var initialRequestedQuantity = BigDecimal.valueOf(1000);
    final var initialDistributedQuantity = BigDecimal.valueOf(300);
    final var newRequestedQuantity = BigDecimal.valueOf(301);

    final var distributionInnerLine1 = DistributionInnerLineMother.sent()
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(BigDecimal.valueOf(700)))
        .distributedQuantity(new DistributionInnerLine.DistributedQuantity(BigDecimal.valueOf(200)))
        .build();
    final var distributionInnerLine2 = DistributionInnerLineMother.inProgress()
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(BigDecimal.valueOf(300)))
        .distributedQuantity(new DistributionInnerLine.DistributedQuantity(BigDecimal.valueOf(100)))
        .build();

    final var distributionInner = DistributionInnerMother.pending()
        .requestedQuantity(new DistributionInner.RequestedQuantity(initialRequestedQuantity))
        .distributedQuantity(new DistributionInner.DistributedQuantity(initialDistributedQuantity))
        .lines(List.of(distributionInnerLine1, distributionInnerLine2))
        .build();

    return Arguments.of(
        CurrentMethodName.get(),
        distributionInner,
        newRequestedQuantity,
        EventType.UPDATED_PENDING,
        List.of(
            new RequestedAndDistributedValues(BigDecimal.valueOf(200), BigDecimal.valueOf(200)),
            new RequestedAndDistributedValues(BigDecimal.valueOf(101), BigDecimal.valueOf(100))));
  }

  private static Arguments decreaseRequestedQuantityWhenThereIsDistributedQuantityAndNewRequestedQuantityIsEqualsDistributedHavingTwoLines() {

    final var initialRequestedQuantity = BigDecimal.valueOf(1000);
    final var initialDistributedQuantity = BigDecimal.valueOf(900);
    final var newRequestedQuantity = BigDecimal.valueOf(900);

    final var distributionInnerLine1 = DistributionInnerLineMother.sent()
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(BigDecimal.valueOf(700)))
        .distributedQuantity(new DistributionInnerLine.DistributedQuantity(BigDecimal.valueOf(700)))
        .build();
    final var distributionInnerLine2 = DistributionInnerLineMother.inProgress()
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(BigDecimal.valueOf(300)))
        .distributedQuantity(new DistributionInnerLine.DistributedQuantity(BigDecimal.valueOf(200)))
        .build();

    final var distributionInner = DistributionInnerMother.pending()
        .requestedQuantity(new DistributionInner.RequestedQuantity(initialRequestedQuantity))
        .distributedQuantity(new DistributionInner.DistributedQuantity(initialDistributedQuantity))
        .lines(List.of(distributionInnerLine1, distributionInnerLine2))
        .build();

    return Arguments.of(
        CurrentMethodName.get(),
        distributionInner,
        newRequestedQuantity,
        EventType.UPDATED_PENDING,
        List.of(
            new RequestedAndDistributedValues(BigDecimal.valueOf(700), BigDecimal.valueOf(700)),
            new RequestedAndDistributedValues(BigDecimal.valueOf(200), BigDecimal.valueOf(200))));
  }

  private static Arguments decreaseRequestedQuantityWhenThereIsDistributedQuantityButLowerThanNewRequestedQuantityHavingTwoLinesAndOneLineGoesToZero() {

    final var initialRequestedQuantity = BigDecimal.valueOf(900);
    final var initialDistributedQuantity = BigDecimal.valueOf(600);
    final var newRequestedQuantity = BigDecimal.valueOf(600);

    final var distributionInnerLine1 = DistributionInnerLineMother.sent()
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(BigDecimal.valueOf(700)))
        .distributedQuantity(new DistributionInnerLine.DistributedQuantity(BigDecimal.valueOf(600)))
        .build();
    final var distributionInnerLine2 = DistributionInnerLineMother.inProgress()
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(BigDecimal.valueOf(200)))
        .distributedQuantity(new DistributionInnerLine.DistributedQuantity(BigDecimal.valueOf(0)))
        .build();

    final var distributionInner = DistributionInnerMother.pending()
        .requestedQuantity(new DistributionInner.RequestedQuantity(initialRequestedQuantity))
        .distributedQuantity(new DistributionInner.DistributedQuantity(initialDistributedQuantity))
        .lines(List.of(distributionInnerLine1, distributionInnerLine2))
        .build();

    return Arguments.of(
        CurrentMethodName.get(),
        distributionInner,
        newRequestedQuantity,
        EventType.UPDATED_PENDING,
        List.of(
            new RequestedAndDistributedValues(BigDecimal.valueOf(600), BigDecimal.valueOf(600)),
            new RequestedAndDistributedValues(BigDecimal.valueOf(0), BigDecimal.valueOf(0))));
  }

  private static Arguments decreaseRequestedQuantityWhenThereIsDistributedQuantityButLowerThanNewRequestedQuantityHavingThreeLinesRequest() {
    final var initialRequestedQuantity = BigDecimal.valueOf(1000);
    final var initialDistributedQuantity = BigDecimal.valueOf(560);
    final var newRequestedQuantity = BigDecimal.valueOf(800);

    final var distributionInnerLine1 = DistributionInnerLineMother.inProgress()
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(BigDecimal.valueOf(250)))
        .distributedQuantity(new DistributionInnerLine.DistributedQuantity(BigDecimal.valueOf(100)))
        .build();
    final var distributionInnerLine2 = DistributionInnerLineMother.sent()
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(BigDecimal.valueOf(450)))
        .distributedQuantity(new DistributionInnerLine.DistributedQuantity(BigDecimal.valueOf(460)))
        .build();

    final var distributionInnerLine3 = DistributionInnerLineMother.created()
        .requestedQuantity(new DistributionInnerLine.RequestedQuantity(BigDecimal.valueOf(300)))
        .distributedQuantity(new DistributionInnerLine.DistributedQuantity(BigDecimal.valueOf(0)))
        .build();

    final var distributionInner = DistributionInnerMother.pending()
        .requestedQuantity(new DistributionInner.RequestedQuantity(initialRequestedQuantity))
        .distributedQuantity(new DistributionInner.DistributedQuantity(initialDistributedQuantity))
        .lines(List.of(distributionInnerLine1, distributionInnerLine2, distributionInnerLine3))
        .build();

    return Arguments.of(
        CurrentMethodName.get(),
        distributionInner,
        newRequestedQuantity,
        EventType.UPDATED_PENDING,
        List.of(
            new RequestedAndDistributedValues(BigDecimal.valueOf(250), BigDecimal.valueOf(100)),
            new RequestedAndDistributedValues(BigDecimal.valueOf(450), BigDecimal.valueOf(460)),
            new RequestedAndDistributedValues(BigDecimal.valueOf(100), BigDecimal.valueOf(0))));
  }

  private static PatchDistributionInnerCommand commandFromDistributionInner(final DistributionInner distributionInner) {
    return new PatchDistributionInnerCommand(
        distributionInner.getId().value(),
        distributionInner.theoreticalQuantity().value(),
        distributionInner.requestedQuantity().value(),
        distributionInner.consumptionFactor().value(),
        distributionInner.audit().updatedBy());
  }

  private record RequestedAndDistributedValues(BigDecimal requested, BigDecimal distributed) {
  }
}
