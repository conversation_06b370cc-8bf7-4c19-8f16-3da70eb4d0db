package com.inditex.icdmdemg.application.use.mother;

import java.time.OffsetDateTime;
import java.util.List;

import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeCondition;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues;
import com.inditex.icdmdemg.domain.use.entity.UsePurchasePurposeCondition;
import com.inditex.icdmdemg.shared.utils.RandomValue;

public interface UsePurchasePurposeConditionMother {

  static Builder generateBuild() {
    return random();
  }

  private static Builder random() {
    return new Builder()
        .purchasePurposeCondition(randomPurchasePurposeCondition())
        .purchasePurposeConditionName(randomPurchasePurposeConditionName())
        .purchasePurposeConditionValues(randomPurchasePurposeConditionValues())
        .audit(randomAudit());
  }

  static PurchasePurposeCondition randomPurchasePurposeCondition() {
    final var randomPosition = RandomValue.randomPositiveInteger(PurchasePurposeCondition.values().length);
    return PurchasePurposeCondition.values()[randomPosition];
  }

  static PurchasePurposeConditionName randomPurchasePurposeConditionName() {
    final var randomPosition = RandomValue.randomPositiveInteger(PurchasePurposeConditionName.values().length);
    return PurchasePurposeConditionName.values()[randomPosition];
  }

  static PurchasePurposeConditionValues randomPurchasePurposeConditionValues() {
    return new PurchasePurposeConditionValues(List.of(new PurchasePurposeConditionValues.ConditionValue("value")));
  }

  static BasicAudit randomAudit() {
    return new BasicAudit(OffsetDateTime.now(), OffsetDateTime.now());
  }

  class Builder {
    PurchasePurposeCondition purchasePurposeCondition;

    PurchasePurposeConditionName purchasePurposeConditionName;

    PurchasePurposeConditionValues purchasePurposeConditionValues;

    BasicAudit audit;

    public Builder purchasePurposeCondition(final PurchasePurposeCondition purchasePurposeCondition) {
      this.purchasePurposeCondition = purchasePurposeCondition;
      return this;
    }

    public Builder purchasePurposeConditionName(final PurchasePurposeConditionName purchasePurposeConditionName) {
      this.purchasePurposeConditionName = purchasePurposeConditionName;
      return this;
    }

    public Builder purchasePurposeConditionValues(final PurchasePurposeConditionValues purchasePurposeConditionValues) {
      this.purchasePurposeConditionValues = purchasePurposeConditionValues;
      return this;
    }

    public Builder audit(final BasicAudit audit) {
      this.audit = audit;
      return this;
    }

    public UsePurchasePurposeCondition build() {
      return new UsePurchasePurposeCondition(this.purchasePurposeCondition, this.purchasePurposeConditionName,
          this.purchasePurposeConditionValues, this.audit);
    }
  }
}
