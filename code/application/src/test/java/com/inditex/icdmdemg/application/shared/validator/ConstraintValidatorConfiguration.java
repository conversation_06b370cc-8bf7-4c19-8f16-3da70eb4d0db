package com.inditex.icdmdemg.application.shared.validator;

import jakarta.validation.Validation;
import jakarta.validation.Validator;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.beanvalidation.SpringConstraintValidatorFactory;

@Configuration
public class ConstraintValidatorConfiguration {
  @Bean
  public Validator validator(final AutowireCapableBeanFactory autowireCapableBeanFactory) {
    return Validation.byDefaultProvider()
        .configure()
        .constraintValidatorFactory(new SpringConstraintValidatorFactory(autowireCapableBeanFactory))
        .buildValidatorFactory()
        .getValidator();
  }

}
