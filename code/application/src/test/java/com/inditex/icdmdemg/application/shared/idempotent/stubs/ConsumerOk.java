package com.inditex.icdmdemg.application.shared.idempotent.stubs;

import java.util.function.Function;

import com.inditex.icdmdemg.application.shared.idempotent.EnableIdempotentConsuming;
import com.inditex.icdmdemg.application.shared.idempotent.IdempotentConsumer;
import com.inditex.icdmdemg.domain.consumermessage.ConsumerMessage;
import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageId;
import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageName;
import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageTimestamp;
import com.inditex.icdmdemg.shared.utils.ClockUtils;

import lombok.RequiredArgsConstructor;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@EnableIdempotentConsuming
public class ConsumerOk implements IdempotentConsumer<FakeEnvelope> {

  private final ClockUtils clockUtils;

  @Override
  public void accept(final Message<FakeEnvelope> message) {
    System.out.println("Received message");
  }

  @Override
  public Function<Message<FakeEnvelope>, ConsumerMessage> buildConsumerMessage() {
    return message -> new ConsumerMessage(
        ConsumerMessageId.from("fake-id"),
        ConsumerMessageName.from("fake-name"),
        ConsumerMessageTimestamp.of(this.clockUtils.getCurrentOffsetDateTime()));
  }
}
