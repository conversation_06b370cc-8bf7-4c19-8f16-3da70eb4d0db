package com.inditex.icdmdemg.application.product.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.domain.product.Product;
import com.inditex.icdmdemg.domain.product.ProductCollection;
import com.inditex.icdmdemg.domain.product.ProductRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductCampaign;
import com.inditex.icdmdemg.domain.product.entity.ProductColor;
import com.inditex.icdmdemg.domain.product.entity.ProductFamilies;
import com.inditex.icdmdemg.domain.product.entity.ProductId;
import com.inditex.icdmdemg.domain.product.entity.ProductOriginMarket;
import com.inditex.icdmdemg.domain.product.entity.ProductOwners;
import com.inditex.icdmdemg.domain.product.entity.ProductQuality;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.domain.product.entity.ProductSupplier;
import com.inditex.icdmdemg.domain.product.entity.ProductVersion;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ProductAlternativeProviderTest {

  private ProductRepository productRepository;

  private final String uncolourId = UuidMother.fromInteger(333).toString();

  private ProductAlternativeProvider sut;

  @BeforeEach
  void setUp() {
    this.productRepository = mock(ProductRepository.class);
    this.sut = new ProductAlternativeProvider(this.productRepository, this.uncolourId);
  }

  @Test
  void should_match_alternativeProduct() {
    final var product1 = product(101, 301, 401, 501, 601, 701, 201);
    final var equivalent11 = product(101, 333, 401, 501, 601, 701, 211);

    doReturn(new ProductCollection(List.of(equivalent11))).when(this.productRepository).findByAnyEquivalentReference(anyList());

    final var alternativeOpt = this.sut.alternativeProduct(product1);

    assertThat(alternativeOpt).hasValue(equivalent11);
  }

  @Test
  void should_match_alternativeProduct_by_reference_id() {
    final var alternativeReferenceId = UUID.randomUUID();
    final var productReferenceId = new ProductReferenceId(alternativeReferenceId.toString());
    final var product1 = product(101, 301, 401, 501, 601, 701, 201);
    final var equivalent11 = product(101, 333, 401, 501, 601, 701, 211);

    doReturn(Optional.of(product1)).when(this.productRepository).findByReferenceId(productReferenceId);
    doReturn(new ProductCollection(List.of(equivalent11))).when(this.productRepository).findByAnyEquivalentReference(anyList());

    final var alternativeOpt = this.sut.alternativeProductReference(productReferenceId);

    assertThat(alternativeOpt).hasValue(equivalent11);
  }

  @Test
  void should_not_found_alternativeProduct_by_reference_id() {
    final var alternativeReferenceId = UUID.randomUUID();
    final var productReferenceId = new ProductReferenceId(alternativeReferenceId.toString());

    doReturn(Optional.empty()).when(this.productRepository).findByReferenceId(productReferenceId);

    final var alternativeOpt = this.sut.alternativeProductReference(productReferenceId);

    assertThat(alternativeOpt).isEmpty();
  }

  @Test
  void should_match_alternativeProducts() {
    final var product1 = product(101, 301, 401, 501, 601, 701, 201);
    final var equivalent11 = product(101, 333, 401, 501, 601, 701, 211);
    final var equivalent12 = product(101, 333, 401, 501, 601, 701, 212);
    final var product2 = product(102, 302, 402, 502, 602, 702, 202);
    final var equivalent21 = product(102, 333, 402, 502, 602, 702, 221);
    final var equivalent22 = product(102, 333, 402, 502, 602, 702, 222);
    final var product3 = product(103, 303, 403, 503, 603, 703, 203);

    doReturn(new ProductCollection(List.of(equivalent11, equivalent12, equivalent21, equivalent22)))
        .when(this.productRepository).findByAnyEquivalentReference(anyList());

    final var equivalentMap = this.sut.alternativeProducts(List.of(product1, product2, product3));

    assertThat(equivalentMap).isEqualTo(Map.of(
        product1, Optional.of(equivalent11),
        product2, Optional.of(equivalent21),
        product3, Optional.empty()));
  }

  @Test
  void should_match_alternativeProductReferences() {
    final var ref1 = new ProductReferenceId(UuidMother.fromInteger(201).toString());
    final var product1 = product(101, 301, 401, 501, 601, 701, 201);
    final var equivalent11 = product(101, 333, 401, 501, 601, 701, 211);
    final var equivalent12 = product(101, 333, 401, 501, 601, 701, 212);
    final var ref2 = new ProductReferenceId(UuidMother.fromInteger(202).toString());
    final var product2 = product(102, 302, 402, 502, 602, 702, 202);
    final var equivalent21 = product(102, 333, 402, 502, 602, 702, 221);
    final var equivalent22 = product(102, 333, 402, 502, 602, 702, 222);
    final var ref3 = new ProductReferenceId(UuidMother.fromInteger(203).toString());
    final var product3 = product(103, 303, 403, 503, 603, 703, 203);
    final var ref4 = new ProductReferenceId(UuidMother.fromInteger(204).toString());

    doReturn(new ProductCollection(List.of(product1, product2, product3)))
        .when(this.productRepository).findByReferenceIds(List.of(ref1, ref2, ref3, ref4));
    doReturn(new ProductCollection(List.of(equivalent11, equivalent12, equivalent21, equivalent22)))
        .when(this.productRepository).findByAnyEquivalentReference(anyList());

    final var equivalentMap = this.sut.alternativeProductReferences(List.of(ref1, ref2, ref3, ref4));

    assertThat(equivalentMap).isEqualTo(Map.of(
        ref1, Optional.of(equivalent11),
        ref2, Optional.of(equivalent21),
        ref3, Optional.empty(),
        ref4, Optional.empty()));
  }

  private static Product product(final int id, final int color, final int supplier, final int campaign, final int originMarket,
      final int quality, final int referenceId) {
    return new Product(
        new ProductId(UuidMother.fromInteger(id).toString()),
        new ProductReferenceId(UuidMother.fromInteger(referenceId).toString()),
        new ProductColor(UuidMother.fromInteger(color)),
        new ProductSupplier(UuidMother.fromInteger(supplier).toString()),
        new ProductCampaign(UuidMother.fromInteger(campaign).toString()),
        new ProductOriginMarket(UuidMother.fromInteger(originMarket)),
        new ProductQuality(quality),
        new ProductFamilies(List.of()),
        new ProductOwners(List.of()),
        ProductVersion.firstVersion(),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(1)));
  }

}
