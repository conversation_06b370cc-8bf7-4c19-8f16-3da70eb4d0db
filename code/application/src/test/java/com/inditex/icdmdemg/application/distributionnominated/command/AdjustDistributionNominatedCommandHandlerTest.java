package com.inditex.icdmdemg.application.distributionnominated.command;

import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.created;
import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.kept;
import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.updated;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.inditex.icdmdemg.application.distributionnominated.service.AlternativeSharedRawMaterialProvider;
import com.inditex.icdmdemg.application.distributionnominated.service.DistributionNominatedLinesIdentifier;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjustedProvider;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.UseId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.Id;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedLineMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Line;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidGeneratorMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = AdjustDistributionNominatedCommandHandlerTest.Config.class)
class AdjustDistributionNominatedCommandHandlerTest {

  @MockitoBean
  private DistributionNominatedRepository distributionNominatedRepository;

  @MockitoBean
  private CommitmentAdjustedProvider commitmentAdjustedProvider;

  @MockitoBean
  private AlternativeSharedRawMaterialProvider alternativeSharedRawMaterialProvider;

  @MockitoBean
  private DistributionNominatedLinesIdentifier linesIdentifier;

  @MockitoSpyBean
  private ClockUtils clockUtils;

  @MockitoSpyBean
  private Transaction transaction;

  @MockitoBean
  private EventBus eventBus;

  @Captor
  ArgumentCaptor<Collection<DomainEvent<?>>> eventsCaptor;

  @Autowired
  private AdjustDistributionNominatedCommandHandler sut;

  @Test
  void should_not_adjustLines_when_commitment_no_decrease() {
    final var rootId = UuidMother.fromInteger(901);
    final var useId = UuidMother.fromInteger(101);
    final var referenceId = UuidMother.fromInteger(201);
    final var budgetCycle = UuidMother.fromInteger(301);
    final var commitmentOrderId = UuidMother.fromInteger(401);
    final var commitmentOrderLineId = UuidMother.fromInteger(501);
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601));
    final var creationDate = OffsetDateTimeMother.fromInteger(1);
    final var now = OffsetDateTimeMother.fromInteger(10);
    final var commitmentOrder = new CommitmentOrder(new Id(commitmentOrderId), new LineId(commitmentOrderLineId),
        new SupplierId(commitmentOrderSupplierId));
    final var line =
        DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(1000), creationDate)
            .commitmentOrder(commitmentOrder)
            .build();
    final var nominated = DistributionNominatedMother.inProgress()
        .id(new DistributionNominated.Id(rootId))
        .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(1000)))
        .theoreticalQuantity(new DistributionNominated.TheoreticalQuantity(BigDecimal.valueOf(1200)))
        .useId(new UseId(useId))
        .referenceId(new ReferenceId(referenceId))
        .budgetCycle(new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetCycle)))
        .lines(new ArrayList<>(List.of(line)))
        .audit(CompleteAudit.create("test", creationDate))
        .build();
    final var commitment = CommitmentAdjusted.of(
        MaterialCommitmentUseMother.withCompositeIdAndQuantity(commitmentOrderId, commitmentOrderLineId, budgetCycle, referenceId, useId,
            BigDecimal.valueOf(1500), commitmentOrderSupplierId),
        BigDecimal.valueOf(1000));
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(List.of(nominated))
        .when(this.distributionNominatedRepository)
        .findByAnySharedRawMaterial(List.of(nominated.sharedRawMaterial()));
    doReturn(List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())))
        .when(this.alternativeSharedRawMaterialProvider)
        .alternatives(List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())));
    doReturn(List.of(commitment)).when(this.commitmentAdjustedProvider).findByAnySharedRawMaterial(
        List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())));
    final var adjustedLine1 = kept(new Line(new DistributionNominated.Id(rootId), commitmentOrder, new ReferenceId(referenceId),
        new ReferenceId(referenceId), NumericUtils.roundUpScale2(BigDecimal.valueOf(1000)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.of(creationDate)));
    doReturn(Map.of(new DistributionNominated.Id(rootId), List.of(line)))
        .when(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine1), now);

    this.sut.doHandle(new AdjustDistributionNominatedCommand(List.of(nominated.sharedRawMaterial()), "test"));

    verify(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine1), now);
    verify(this.distributionNominatedRepository).save(nominated);
    verify(this.eventBus).send(this.eventsCaptor.capture());
    assertThat(this.eventsCaptor.getValue()).isEmpty();
  }

  @Test
  void should_adjustLines_when_commitment_decrease() {
    final var rootId = UuidMother.fromInteger(901);
    final var useId = UuidMother.fromInteger(101);
    final var referenceId = UuidMother.fromInteger(201);
    final var budgetCycle = UuidMother.fromInteger(301);
    final var commitmentOrderId = UuidMother.fromInteger(401);
    final var commitmentOrderLineId = UuidMother.fromInteger(501);
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601));
    final var creationDate = OffsetDateTimeMother.fromInteger(1);
    final var now = OffsetDateTimeMother.fromInteger(10);
    final var commitmentOrder =
        new CommitmentOrder(new Id(commitmentOrderId), new LineId(commitmentOrderLineId), new SupplierId(commitmentOrderSupplierId));
    final var line =
        DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(1000), creationDate)
            .commitmentOrder(commitmentOrder)
            .build();
    final var nominated = DistributionNominatedMother.inProgress()
        .id(new DistributionNominated.Id(rootId))
        .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(1000)))
        .theoreticalQuantity(new DistributionNominated.TheoreticalQuantity(BigDecimal.valueOf(1200)))
        .useId(new UseId(useId))
        .referenceId(new ReferenceId(referenceId))
        .budgetCycle(new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetCycle)))
        .lines(new ArrayList<>(List.of(line)))
        .audit(CompleteAudit.create("test", creationDate))
        .build();
    final var commitment = CommitmentAdjusted.of(
        MaterialCommitmentUseMother.withCompositeIdAndQuantity(commitmentOrderId, commitmentOrderLineId, budgetCycle, referenceId, useId,
            BigDecimal.valueOf(500), commitmentOrderSupplierId),
        BigDecimal.valueOf(400));
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(List.of(nominated))
        .when(this.distributionNominatedRepository)
        .findByAnySharedRawMaterial(List.of(nominated.sharedRawMaterial()));
    doReturn(List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())))
        .when(this.alternativeSharedRawMaterialProvider)
        .alternatives(List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())));
    doReturn(List.of(commitment)).when(this.commitmentAdjustedProvider).findByAnySharedRawMaterial(List.of(
        MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())));
    final var adjustedLine1 = updated(new Line(new DistributionNominated.Id(rootId), commitmentOrder, new ReferenceId(referenceId),
        new ReferenceId(referenceId), NumericUtils.roundUpScale2(BigDecimal.valueOf(400)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.of(creationDate)));
    final var newRequestedQuantity = new DistributionNominatedLine.RequestedQuantity(BigDecimal.valueOf(400));
    final var parentRequestedQuantity = new RequestedQuantity(BigDecimal.valueOf(1000));
    final var expectedLine = line.updateRequested(newRequestedQuantity, parentRequestedQuantity, now);
    doReturn(Map.of(new DistributionNominated.Id(rootId), List.of(expectedLine)))
        .when(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine1), now);

    final var expectedNominated = DistributionNominatedMother.of(nominated)
        .lines(List.of(expectedLine))
        .audit(nominated.audit().update("test", now)).build();

    this.sut.doHandle(new AdjustDistributionNominatedCommand(List.of(nominated.sharedRawMaterial()), "test"));

    verify(this.distributionNominatedRepository).findByAnySharedRawMaterial(List.of(nominated.sharedRawMaterial()));
    verify(this.alternativeSharedRawMaterialProvider, times(2))
        .alternatives(List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())));
    verify(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine1), now);
    verify(this.distributionNominatedRepository).save(expectedNominated);
    verify(this.eventBus).send(this.eventsCaptor.capture());
    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(DistributionNominatedUnifiedEvent.class::isInstance)
        .allMatch(domainEvent -> domainEvent.eventType().equals(EventType.UPDATED.value));
  }

  @Test
  void should_adjustLines_when_new_commitment() {
    final var rootId = UuidMother.fromInteger(901);
    final var useId = UuidMother.fromInteger(101);
    final var referenceId = UuidMother.fromInteger(201);
    final var budgetCycle = UuidMother.fromInteger(301);
    final var commitmentOrderId = UuidMother.fromInteger(401);
    final var commitmentOrderLineId = UuidMother.fromInteger(501);
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601));
    final var creationDate = OffsetDateTimeMother.fromInteger(1);
    final var now = OffsetDateTimeMother.fromInteger(10);
    final var nominated = DistributionNominatedMother.inProgress()
        .id(new DistributionNominated.Id(rootId))
        .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(1000)))
        .theoreticalQuantity(new DistributionNominated.TheoreticalQuantity(BigDecimal.valueOf(1200)))
        .useId(new UseId(useId))
        .referenceId(new ReferenceId(referenceId))
        .budgetCycle(new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetCycle)))
        .lines(new ArrayList<>())
        .audit(CompleteAudit.create("test", creationDate))
        .build();
    final var commitment = CommitmentAdjusted.of(
        MaterialCommitmentUseMother.withCompositeIdAndQuantity(commitmentOrderId, commitmentOrderLineId, budgetCycle, referenceId, useId,
            BigDecimal.valueOf(1500), commitmentOrderSupplierId),
        BigDecimal.valueOf(1000));
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(List.of(nominated))
        .when(this.distributionNominatedRepository)
        .findByAnySharedRawMaterial(List.of(nominated.sharedRawMaterial()));
    doReturn(List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())))
        .when(this.alternativeSharedRawMaterialProvider)
        .alternatives(List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())));
    doReturn(List.of(commitment)).when(this.commitmentAdjustedProvider).findByAnySharedRawMaterial(
        List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())));
    final var commitmentOrder = new CommitmentOrder(new Id(commitmentOrderId), new LineId(commitmentOrderLineId), new SupplierId(
        commitmentOrderSupplierId));
    final var adjustedLine1 = created(new Line(new DistributionNominated.Id(rootId), commitmentOrder, new ReferenceId(referenceId),
        new ReferenceId(referenceId), NumericUtils.roundUpScale2(BigDecimal.valueOf(1000)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.empty()));
    final var expectedLine = DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(1000), now)
        .id(new DistributionNominatedLine.Id(UuidMother.fromInteger(1001)))
        .theoreticalQuantity(new TheoreticalQuantity(BigDecimal.valueOf(1200)))
        .commitmentOrder(commitmentOrder)
        .build();
    doReturn(Map.of(new DistributionNominated.Id(rootId), List.of(expectedLine)))
        .when(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine1), now);

    final var expectedNominated = DistributionNominatedMother.of(nominated)
        .lines(List.of(expectedLine))
        .audit(nominated.audit().update("test", now)).build();

    this.sut.doHandle(new AdjustDistributionNominatedCommand(List.of(nominated.sharedRawMaterial()), "test"));

    verify(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine1), now);
    verify(this.distributionNominatedRepository).save(expectedNominated);
    verify(this.eventBus).send(this.eventsCaptor.capture());
    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(DistributionNominatedUnifiedEvent.class::isInstance)
        .allMatch(domainEvent -> domainEvent.eventType().equals(EventType.UPDATED.value));
  }

  @Test
  void should_do_nothing_when_shared_raw_material_is_empty() {
    final var emptySharedRawMaterialsCommand = new AdjustDistributionNominatedCommand(List.of(), "test");

    this.sut.doHandle(emptySharedRawMaterialsCommand);

    verifyNoInteractions(this.distributionNominatedRepository);
    verifyNoInteractions(this.commitmentAdjustedProvider);
    verifyNoInteractions(this.linesIdentifier);
    verifyNoInteractions(this.alternativeSharedRawMaterialProvider);
    verifyNoInteractions(this.clockUtils);
    verifyNoInteractions(this.eventBus);
  }

  @Configuration
  @Import({ClockUtils.class, Transaction.class, AdjustDistributionNominatedCommandHandler.class})
  public static class Config {

    @Bean
    UuidGenerator uuidGenerator() {
      return UuidGeneratorMother.fromStartingInteger(1001);
    }

  }
}
