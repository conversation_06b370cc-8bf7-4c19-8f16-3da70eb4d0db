package com.inditex.icdmdemg.application.distributionnominated.query;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionnominated.query.GetDistributionNominatedByRegularizationCriteriaQuery.LineRegularization;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.AlternativeReference.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.Id;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GetDistributionNominatedByRegularizationCriteriaQueryHandlerTest {

  public static final UUID NO_COLOR_MATERIAL_REFERENCE_UUID = UUID.fromString("a4a8ce29-ab6a-44b2-a3b6-30143e1ab546");

  private static final UUID COLOR_MATERIAL_REFERENCE_UUID = UUID.fromString("e1eaf5d4-085b-492f-bd28-75c6e333060f");

  public static final UUID NO_COLOR_COMMITMENT_ORDER_ID_UUID = UUID.fromString("ff282e1e-a384-4097-9164-9ee2df033817");

  public static final UUID NO_COLOR_COMMITMENT_LINE_ID_UUID = UUID.fromString("feb9096c-efd6-4345-8225-1128ab6d3fe5");

  @Mock
  private DistributionNominatedRepository distributionNominatedRepository;

  @InjectMocks
  private GetDistributionNominatedByRegularizationCriteriaQueryHandler sut;

  @Test
  void should_return_line_regularization() {
    final var expectedDistributionNominatedId = UUID.fromString("a4610d43-1505-44c9-8893-cc40bdd866c0");
    final var expectedDistributionNominatedLineId = UUID.fromString("0ebd5887-6abd-4acf-a29e-ab46e02ee0db");
    final var today = OffsetDateTime.now();
    final var yesterday = today.minusDays(1);
    final var supplierId = new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(901)));
    final List<DistributionNominated> distributionsInDb =
        generateDistributionsToReturnFromRepository(expectedDistributionNominatedId, expectedDistributionNominatedLineId, yesterday, today);
    final var criteria1 = new GetDistributionNominatedByRegularizationCriteriaQuery.RegularizationCriteria(
        new CommitmentOrder(new Id(NO_COLOR_COMMITMENT_ORDER_ID_UUID), new LineId(NO_COLOR_COMMITMENT_LINE_ID_UUID),
            supplierId),
        new ReferenceId(COLOR_MATERIAL_REFERENCE_UUID), new RequestedQuantity(BigDecimal.valueOf(5000)));
    final var criteria2 = new GetDistributionNominatedByRegularizationCriteriaQuery.RegularizationCriteria(
        new CommitmentOrder(new Id(NO_COLOR_COMMITMENT_ORDER_ID_UUID), new LineId(NO_COLOR_COMMITMENT_LINE_ID_UUID),
            supplierId),
        new ReferenceId(NO_COLOR_MATERIAL_REFERENCE_UUID), new RequestedQuantity(BigDecimal.valueOf(6000)));

    doReturn(distributionsInDb).when(this.distributionNominatedRepository).findByCommitmentOrderAndReferenceId(criteria1.commitmentOrder(),
        criteria1.referenceId());
    doReturn(List.of()).when(this.distributionNominatedRepository).findByCommitmentOrderAndReferenceId(criteria2.commitmentOrder(),
        criteria2.referenceId());

    final Optional<LineRegularization> result =
        this.sut.ask(new GetDistributionNominatedByRegularizationCriteriaQuery(List.of(criteria2, criteria1)));

    verify(this.distributionNominatedRepository, times(2)).findByCommitmentOrderAndReferenceId(any(), any());
    assertThat(result).isPresent()
        .hasValue(
            new LineRegularization(new DistributionNominated.Id(expectedDistributionNominatedId),
                new DistributionNominatedLine.Id(expectedDistributionNominatedLineId)));

  }

  @Test
  void should_return_line_second_regularization() {
    final var lineAlreadyRegularizedQuantity = BigDecimal.valueOf(1000);
    final var lineToRegularizeQuantity = BigDecimal.valueOf(6000);
    final var dn = DistributionNominatedMother.withOneLinewithCommitmentOrderAndReferenceAndAlternativeReference(
        NO_COLOR_COMMITMENT_ORDER_ID_UUID,
        NO_COLOR_COMMITMENT_LINE_ID_UUID,
        COLOR_MATERIAL_REFERENCE_UUID,
        lineAlreadyRegularizedQuantity,
        lineToRegularizeQuantity).build();
    final var expectedDistributionNominatedId = dn.getId().value();
    final var onlyLineWithAlternativeRef = dn.lines().value().get(0);
    final var expectedDistributionNominatedLineId = onlyLineWithAlternativeRef.id().value();
    final var supplierId = new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(901)));

    final var criteria1 = new GetDistributionNominatedByRegularizationCriteriaQuery.RegularizationCriteria(
        new CommitmentOrder(new Id(NO_COLOR_COMMITMENT_ORDER_ID_UUID), new LineId(NO_COLOR_COMMITMENT_LINE_ID_UUID),
            supplierId),
        new ReferenceId(COLOR_MATERIAL_REFERENCE_UUID),
        new RequestedQuantity(BigDecimal.valueOf(7000)));
    final var criteria2 = new GetDistributionNominatedByRegularizationCriteriaQuery.RegularizationCriteria(
        new CommitmentOrder(new Id(NO_COLOR_COMMITMENT_ORDER_ID_UUID), new LineId(NO_COLOR_COMMITMENT_LINE_ID_UUID),
            supplierId),
        new ReferenceId(NO_COLOR_MATERIAL_REFERENCE_UUID), new RequestedQuantity(BigDecimal.valueOf(6000)));

    doReturn(List.of(dn)).when(this.distributionNominatedRepository).findByCommitmentOrderAndReferenceId(criteria1.commitmentOrder(),
        criteria1.referenceId());
    doReturn(List.of()).when(this.distributionNominatedRepository).findByCommitmentOrderAndReferenceId(criteria2.commitmentOrder(),
        criteria2.referenceId());

    final Optional<LineRegularization> result =
        this.sut.ask(new GetDistributionNominatedByRegularizationCriteriaQuery(List.of(criteria2, criteria1)));

    verify(this.distributionNominatedRepository, times(2)).findByCommitmentOrderAndReferenceId(any(), any());
    assertThat(result).isPresent()
        .hasValue(
            new LineRegularization(new DistributionNominated.Id(expectedDistributionNominatedId),
                new DistributionNominatedLine.Id(expectedDistributionNominatedLineId)));

  }

  private static List<DistributionNominated> generateDistributionsToReturnFromRepository(
      final UUID expectedDistributionNominatedId,
      final UUID expectedDistributionNominatedLineId, final OffsetDateTime yesterday, final OffsetDateTime today) {
    return List.of(
        DistributionNominatedMother.withTwoLinesAndAlternativeReferenceInOneOfThem(
            expectedDistributionNominatedId,
            expectedDistributionNominatedLineId,
            NO_COLOR_COMMITMENT_ORDER_ID_UUID,
            NO_COLOR_COMMITMENT_LINE_ID_UUID,
            BigDecimal.valueOf(4000),
            BigDecimal.valueOf(6000),
            COLOR_MATERIAL_REFERENCE_UUID,
            NO_COLOR_MATERIAL_REFERENCE_UUID,
            yesterday).build(),
        DistributionNominatedMother.withTwoLinesAndAlternativeReferenceInOneOfThem(
            UUID.randomUUID(),
            UUID.randomUUID(),
            NO_COLOR_COMMITMENT_ORDER_ID_UUID,
            NO_COLOR_COMMITMENT_LINE_ID_UUID,
            BigDecimal.valueOf(4000),
            BigDecimal.valueOf(6000),
            COLOR_MATERIAL_REFERENCE_UUID,
            NO_COLOR_MATERIAL_REFERENCE_UUID,
            today).build(),
        DistributionNominatedMother.withCommitmentOrderAndReferenceAndRequestedQuantity(
            NO_COLOR_COMMITMENT_ORDER_ID_UUID,
            NO_COLOR_COMMITMENT_LINE_ID_UUID,
            COLOR_MATERIAL_REFERENCE_UUID,
            BigDecimal.valueOf(1000)).build());
  }

}
