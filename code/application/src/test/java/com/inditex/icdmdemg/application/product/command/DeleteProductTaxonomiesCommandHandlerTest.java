package com.inditex.icdmdemg.application.product.command;

import static org.mockito.Mockito.verify;

import java.util.UUID;

import com.inditex.icdmdemg.application.product.command.DeleteProductTaxonomiesCommand.DeletedProduct;
import com.inditex.icdmdemg.domain.product.ProductTaxonomyRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductTaxonomy.ProductId;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class})
class DeleteProductTaxonomiesCommandHandlerTest {
  @Mock
  ProductTaxonomyRepository productTaxonomyRepository;

  @InjectMocks
  DeleteProductTaxonomiesCommandHandler sut;

  @Test
  void should_call_delete() {
    final var deletedProductId = UUID.randomUUID();
    final var command = new DeleteProductTaxonomiesCommand(new DeletedProduct(deletedProductId));

    this.sut.doHandle(command);

    verify(this.productTaxonomyRepository).deleteAllByProductId(new ProductId(deletedProductId));
  }
}
