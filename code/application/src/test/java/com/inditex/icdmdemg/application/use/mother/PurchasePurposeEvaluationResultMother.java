package com.inditex.icdmdemg.application.use.mother;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.use.evaluation.PurchasePurposeEvaluationResult;
import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;
import com.inditex.icdmdemg.shared.utils.RandomValue;

public interface PurchasePurposeEvaluationResultMother {

  static Builder generateBuild() {
    return random();
  }

  private static Builder random() {
    return new Builder()
        .id(randomId())
        .oks(randomOks())
        .kos(randomKos())
        .paramsWithOk(randomParamsWithOk())
        .coincidences(randomCoincidences());
  }

  static PurchasePurposeEvaluationResult withUseOkAndParam(Use.Id useId, PurchasePurposeConditionName conditionName) {
    return new Builder()
        .id(useId)
        .oks(randomOks())
        .kos(0)
        .paramsWithOk(List.of(conditionName))
        .coincidences(randomCoincidences())
        .build();
  }

  static Use.Id randomId() {
    return new Use.Id(UUID.randomUUID());
  }

  static int randomOks() {
    return RandomValue.randomPositiveInteger(10);
  }

  static int randomKos() {
    return RandomValue.randomPositiveInteger(10);
  }

  static int randomCoincidences() {
    return RandomValue.randomPositiveInteger(10);
  }

  static List<PurchasePurposeConditionName> randomParamsWithOk() {
    final var purchasePurposeConditionName = RandomValue.randomEnum(PurchasePurposeConditionName.class);
    return List.of(purchasePurposeConditionName);
  }

  class Builder {
    Use.Id id;

    int oks;

    int kos;

    List<PurchasePurposeConditionName> paramsWithOk;

    int coincidences;

    public Builder id(final Use.Id id) {
      this.id = id;
      return this;
    }

    public Builder oks(final int oks) {
      this.oks = oks;
      return this;
    }

    public Builder kos(final int kos) {
      this.kos = kos;
      return this;
    }

    public Builder paramsWithOk(final List<PurchasePurposeConditionName> paramsWithOk) {
      this.paramsWithOk = paramsWithOk;
      return this;
    }

    public Builder coincidences(final int coincidences) {
      this.coincidences = coincidences;
      return this;
    }

    public PurchasePurposeEvaluationResult build() {
      return new PurchasePurposeEvaluationResult(this.id, this.kos, this.paramsWithOk, this.coincidences);
    }

  }

}
