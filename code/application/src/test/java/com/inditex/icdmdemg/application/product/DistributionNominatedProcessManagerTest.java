package com.inditex.icdmdemg.application.product;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

import java.util.List;

import com.inditex.icdmdemg.application.commitmentuse.command.CalculateNominatedProvisionCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.AdjustDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.CreateDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.DeleteDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.DeleteDistributionNominatedCommand.DeleteNominatedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PatchDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.PatchDistributionNominatedCommand.PatchNominatedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.AutoRequest;
import com.inditex.icdmdemg.application.process.DistributionNominatedProcessManager;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DistributionNominatedProcessManagerTest {
  @Mock
  private CommandBus commandBus;

  @InjectMocks
  private DistributionNominatedProcessManager sut;

  @Test
  void distributionsNominatedPost_shouldReturnCreated() {
    final var nominatedRequest = Instancio.create(CreateDistributionNominatedCommand.class);
    final var postNominatedResult = Instancio.create(DistributionNominated.class);

    doReturn(postNominatedResult).when(this.commandBus).execute(nominatedRequest);

    final var result = this.sut.createDistributionNominated(nominatedRequest);

    verify(this.commandBus).execute(nominatedRequest);
    verify(this.commandBus)
        .execute(nominatedRequest);
    assertThat(result).isEqualTo(postNominatedResult.getId().value());
  }

  @Test
  void distributionsNominatedPatch_shouldReturnNoContent() {
    final var patchNominatedRequest = Instancio.create(PatchNominatedRequest.class);
    final var patchNominatedResult = Instancio.create(DistributionNominated.class);
    final var command = new PatchDistributionNominatedCommand(patchNominatedRequest, new AutoRequest());

    doReturn(patchNominatedResult).when(this.commandBus).execute(command);

    this.sut.patchDistributionNominated(command);

    verify(this.commandBus).execute(command);
    verify(this.commandBus).execute(new AdjustDistributionNominatedCommand(
        List.of(patchNominatedResult.sharedRawMaterial()), patchNominatedRequest.triggeredBy()));
    verify(this.commandBus).execute(
        new CalculateNominatedProvisionCommand(List.of(patchNominatedResult.sharedRawMaterial()), patchNominatedRequest.triggeredBy()));

  }

  @Test
  void distributionsNominatedDelete_shouldReturnNoContent() {
    final var deleteNominatedResult = Instancio.create(DistributionNominated.class);
    final DeleteNominatedRequest deleteNominatedRequest = Instancio.create(DeleteNominatedRequest.class);

    doReturn(deleteNominatedResult).when(this.commandBus).execute(new DeleteDistributionNominatedCommand(deleteNominatedRequest));

    this.sut.deleteDistributionNominated(deleteNominatedRequest);

    verify(this.commandBus).execute(new DeleteDistributionNominatedCommand(deleteNominatedRequest));
    verify(this.commandBus).execute(new AdjustDistributionNominatedCommand(
        List.of(deleteNominatedResult.sharedRawMaterial()), deleteNominatedRequest.triggeredBy()));
    verify(this.commandBus).execute(
        new CalculateNominatedProvisionCommand(List.of(deleteNominatedResult.sharedRawMaterial()), deleteNominatedRequest.triggeredBy()));

  }
}
