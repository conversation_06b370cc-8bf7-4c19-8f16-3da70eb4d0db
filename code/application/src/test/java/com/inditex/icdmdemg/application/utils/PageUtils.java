package com.inditex.icdmdemg.application.utils;

import java.security.SecureRandom;

import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

public class PageUtils {
  public static Pageable randomPageable() {
    final var pageSize = new SecureRandom().nextInt(1, 10);
    final var pageNumber = new SecureRandom().nextInt(0, 100) % pageSize;
    return PageRequest.of(pageNumber, pageSize);
  }
}
