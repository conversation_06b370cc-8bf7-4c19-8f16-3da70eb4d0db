package com.inditex.icdmdemg.application.distributionnominated.command;

import static java.util.Collections.emptyList;

import static com.inditex.icdmdemg.shared.utils.NumericUtils.roundUpScale2;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.tuple;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;
import static org.instancio.Select.field;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionnominated.command.CreateDistributionNominatedCommand.NominatedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.AutoRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.PreselectedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.PreselectedRequest.LineSelectionRequest;
import com.inditex.icdmdemg.application.distributionnominated.service.AlternativeSharedRawMaterialProvider;
import com.inditex.icdmdemg.application.distributionnominated.service.DistributionNominatedLinesIdentifier;
import com.inditex.icdmdemg.application.shared.validator.ConstraintValidatorConfiguration;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjustedProvider;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycleChangePendingQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ConsumptionFactor;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductSupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.UseId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedPlan;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedStatus;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedLineMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Line;
import com.inditex.icdmdemg.domain.lock.Lock;
import com.inditex.icdmdemg.domain.lock.LockRepository;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.OrderLines;
import com.inditex.icdmdemg.domain.order.entity.OrderStatusKey;
import com.inditex.icdmdemg.domain.order.entity.OrderSupplierId;
import com.inditex.icdmdemg.domain.order.entity.OrderVersion;
import com.inditex.icdmdemg.domain.product.Product;
import com.inditex.icdmdemg.domain.product.ProductRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductId;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.domain.shared.ActionType;
import com.inditex.icdmdemg.domain.shared.EntityAndActionResult;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAuditMother;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.mockito.ArgumentCaptor;
import org.springframework.boot.test.context.SpringBootTest;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = {ConstraintValidatorConfiguration.class})
class CreateDistributionNominatedCommandHandlerTest {

  private ClockUtils clockUtils;

  private UuidGenerator uuidGenerator;

  private DistributionNominatedRepository distributionNominatedRepository;

  private LockRepository lockRepository;

  private CommitmentAdjustedProvider commitmentAdjustedProvider;

  private AlternativeSharedRawMaterialProvider alternativeSharedRawMaterialProvider;

  private DistributionNominatedLinesIdentifier linesIdentifier;

  private OrderRepository orderRepository;

  private ProductRepository productRepository;

  private EventBus eventBus;

  private CreateDistributionNominatedCommandHandler sut;

  @BeforeEach
  void setup() {
    final var transaction = new Transaction();
    this.clockUtils = mock(ClockUtils.class);
    this.uuidGenerator = mock(UuidGenerator.class);
    this.distributionNominatedRepository = mock(DistributionNominatedRepository.class);
    this.lockRepository = mock(LockRepository.class);
    this.commitmentAdjustedProvider = mock(CommitmentAdjustedProvider.class);
    this.alternativeSharedRawMaterialProvider = mock(AlternativeSharedRawMaterialProvider.class);
    this.linesIdentifier = mock(DistributionNominatedLinesIdentifier.class);
    this.orderRepository = mock(OrderRepository.class);
    this.productRepository = mock(ProductRepository.class);
    this.eventBus = mock(EventBus.class);
    this.sut = new CreateDistributionNominatedCommandHandler(transaction, this.clockUtils, this.uuidGenerator,
        this.distributionNominatedRepository, this.lockRepository, this.commitmentAdjustedProvider,
        this.alternativeSharedRawMaterialProvider, this.linesIdentifier, this.orderRepository, this.productRepository, this.eventBus);
  }

  @Test
  void should_save_nominated_entity_with_automatic_plan_and_return_entity_with_unique_line() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var referenceProductId = UUID.randomUUID();
    final var theoreticalQuantity = BigDecimal.valueOf(3000);
    final var requestedQuantity = BigDecimal.valueOf(5000);
    final var productOrderId = new OrderId(orderId.toString());
    final var productVariantGroupId = new ProductVariantGroupId(UUID.randomUUID());
    final var supplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var productSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var triggeredBy = "TEST";

    final var nominatedRequest = new NominatedRequest(
        referenceId,
        useId,
        budgetCycle,
        UUID.fromString(productOrderId.value()),
        productVariantGroupId.value(),
        theoreticalQuantity,
        requestedQuantity,
        BigDecimal.ZERO,
        triggeredBy);

    final var materialCommitmentUseSharedRawMaterial =
        MaterialCommitmentUseSharedRawMaterial.of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);
    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        orderId, orderLineId, budgetId, referenceId, useId,
        BigDecimal.valueOf(10000), supplierId);

    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, BigDecimal.valueOf(9000)));

    final var order = new Order(productOrderId, OrderStatusKey.DRAFT, new OrderLines(new ArrayList<>()),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(1)),
        new OrderSupplierId(productSupplierId), false, OrderVersion.firstVersion());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.uuidGenerator.generate()).thenReturn(distributionNominatedId);
    final var sharedRawMaterial = new SharedRawMaterialNominated(referenceId, useId, budgetCycle);
    when(this.distributionNominatedRepository.findBySharedRawMaterial(
        sharedRawMaterial)).thenReturn(emptyList());
    when(this.alternativeSharedRawMaterialProvider.alternatives(materialCommitmentUseSharedRawMaterial))
        .thenReturn(List.of(materialCommitmentUseSharedRawMaterial));
    when(this.commitmentAdjustedProvider.findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
        .thenReturn(partialQuantities);
    when(this.orderRepository.find(productOrderId)).thenReturn(Optional.of(order));
    final var product = Instancio.of(Product.class).set(field("productId"), new ProductId(referenceProductId.toString())).create();
    when(this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.toString()))).thenReturn(Optional.of(product));
    final var line = DistributionNominatedLineMother.createdWithQuantityAndDate(requestedQuantity, now).build();
    doReturn(List.of(line)).when(this.linesIdentifier).identifyCreatedLines(any(), any(), any(), any(), any());

    final var result = this.sut.execute(new CreateDistributionNominatedCommand(nominatedRequest, new AutoRequest()));

    assertThat(result.getId().value()).isEqualTo(distributionNominatedId);

    final var expectedDistributionNominated = DistributionNominatedMother.pending()
        .id(new Id(distributionNominatedId))
        .referenceId(new ReferenceId(referenceId))
        .useId(new UseId(useId))
        .budgetCycle(new BudgetCycle(budgetCycle))
        .referenceProductId(new ReferenceProductId(referenceProductId))
        .productOrderId(new ProductOrderId(UUID.fromString(productOrderId.value())))
        .productSupplierId(new ProductSupplierId(productSupplierId))
        .productVariantGroupId(productVariantGroupId)
        .theoreticalQuantity(new DistributionNominated.TheoreticalQuantity(theoreticalQuantity))
        .requestedQuantity(new RequestedQuantity(requestedQuantity))
        .audit(CompleteAuditMother.createdWith(now, triggeredBy))
        .consumptionFactor(new ConsumptionFactor(roundUpScale2(BigDecimal.ZERO)))
        .distributedQuantity(new DistributedQuantity(roundUpScale2(BigDecimal.ZERO)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(roundUpScale2(BigDecimal.ZERO)))
        .status(DistributionNominatedStatus.NON_DISTRIBUTABLE)
        .lines(List.of(line))
        .plan(DistributionNominatedPlan.AUTO)
        .build();
    verify(this.distributionNominatedRepository).save(expectedDistributionNominated);
    verify(this.lockRepository).lock(Lock.from(sharedRawMaterial));
    verify(this.eventBus).send(expectedEvent(expectedDistributionNominated));
    verify(this.linesIdentifier).identifyCreatedLines(new Id(distributionNominatedId),
        new RequestedQuantity(requestedQuantity),
        new DistributionNominated.TheoreticalQuantity(theoreticalQuantity),
        List.of(new EntityAndActionResult<>(new Line(
            new Id(distributionNominatedId),
            new CommitmentOrder(new CommitmentOrder.Id(orderId), new LineId(orderLineId), new SupplierId(supplierId)),
            new ReferenceId(referenceId),
            new ReferenceId(referenceId),
            roundUpScale2(requestedQuantity),
            roundUpScale2(BigDecimal.ZERO)),
            ActionType.CREATE)),
        now);
  }

  @Test
  void should_save_nominated_entity_with_preselected_plan_and_return_entity_with_unique_line() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var referenceProductId = UUID.randomUUID();
    final var theoreticalQuantity = BigDecimal.valueOf(3000);
    final var requestedQuantity = BigDecimal.valueOf(5000);
    final var productOrderId = new OrderId(orderId.toString());
    final var productVariantGroupId = new ProductVariantGroupId(UUID.randomUUID());
    final var supplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var line = DistributionNominatedLineMother.createdWithQuantityAndDate(requestedQuantity, now).build();
    final var triggeredBy = "TEST";
    final var productSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var sharedRawMaterial = new SharedRawMaterialNominated(referenceId, useId, budgetCycle);
    final var nominatedRequest = new NominatedRequest(
        referenceId,
        useId,
        budgetCycle,
        UUID.fromString(productOrderId.value()),
        productVariantGroupId.value(),
        theoreticalQuantity,
        requestedQuantity,
        BigDecimal.ZERO,
        triggeredBy);

    final var materialCommitmentUseSharedRawMaterial =
        MaterialCommitmentUseSharedRawMaterial.of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);
    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        orderId, orderLineId, budgetId, referenceId, useId,
        BigDecimal.valueOf(10000), supplierId);

    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, BigDecimal.valueOf(9000)));

    final var order = new Order(productOrderId, OrderStatusKey.DRAFT, new OrderLines(new ArrayList<>()),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(1)),
        new OrderSupplierId(productSupplierId), false, OrderVersion.firstVersion());
    final var preselection = new PreselectedRequest(List.of(new LineSelectionRequest(orderId, orderLineId, requestedQuantity)));

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.uuidGenerator.generate()).thenReturn(distributionNominatedId);
    when(this.distributionNominatedRepository.findBySharedRawMaterial(
        new SharedRawMaterialNominated(referenceId, useId, budgetCycle))).thenReturn(emptyList());
    when(this.alternativeSharedRawMaterialProvider.alternatives(materialCommitmentUseSharedRawMaterial))
        .thenReturn(List.of(materialCommitmentUseSharedRawMaterial));
    when(this.commitmentAdjustedProvider.findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
        .thenReturn(partialQuantities);
    when(this.orderRepository.find(productOrderId)).thenReturn(Optional.of(order));
    final var product = Instancio.of(Product.class).set(field("productId"), new ProductId(referenceProductId.toString())).create();
    when(this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.toString()))).thenReturn(Optional.of(product));
    doReturn(List.of(line)).when(this.linesIdentifier).identifyCreatedLines(any(), any(), any(), any(), any());

    final var result = this.sut.execute(new CreateDistributionNominatedCommand(nominatedRequest, preselection));
    verify(this.lockRepository).lock(Lock.from(sharedRawMaterial));

    assertThat(result.getId().value()).isEqualTo(distributionNominatedId);

    final var expectedDistributionNominated = DistributionNominatedMother.pending()
        .id(new Id(distributionNominatedId))
        .referenceId(new ReferenceId(referenceId))
        .useId(new UseId(useId))
        .budgetCycle(new BudgetCycle(budgetCycle))
        .referenceProductId(new ReferenceProductId(referenceProductId))
        .productOrderId(new ProductOrderId(UUID.fromString(productOrderId.value())))
        .productSupplierId(new ProductSupplierId(productSupplierId))
        .productVariantGroupId(productVariantGroupId)
        .theoreticalQuantity(new DistributionNominated.TheoreticalQuantity(theoreticalQuantity))
        .requestedQuantity(new RequestedQuantity(requestedQuantity))
        .audit(CompleteAuditMother.createdWith(now, triggeredBy))
        .consumptionFactor(new ConsumptionFactor(roundUpScale2(BigDecimal.ZERO)))
        .distributedQuantity(new DistributedQuantity(roundUpScale2(BigDecimal.ZERO)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(roundUpScale2(BigDecimal.ZERO)))
        .status(DistributionNominatedStatus.NON_DISTRIBUTABLE)
        .lines(List.of(line))
        .plan(DistributionNominatedPlan.PRESELECTED)
        .build();
    verify(this.linesIdentifier).identifyCreatedLines(new Id(distributionNominatedId),
        new RequestedQuantity(requestedQuantity),
        new DistributionNominated.TheoreticalQuantity(theoreticalQuantity),
        List.of(new EntityAndActionResult<>(new Line(
            new Id(distributionNominatedId),
            new CommitmentOrder(new CommitmentOrder.Id(orderId), new LineId(orderLineId), new SupplierId(supplierId)),
            new ReferenceId(referenceId),
            new ReferenceId(referenceId),
            roundUpScale2(requestedQuantity),
            roundUpScale2(BigDecimal.ZERO)),
            ActionType.CREATE)),
        now);
    verify(this.distributionNominatedRepository).save(expectedDistributionNominated);
    verify(this.lockRepository).lock(Lock.from(sharedRawMaterial));
    verify(this.eventBus).send(expectedEvent(expectedDistributionNominated));
  }

  @Test
  void should_save_nominated_entity_and_return_entity_unique_line_existing_DN() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var referenceProductId = UUID.randomUUID();
    final var productOrderId = new OrderId(orderId.toString());
    final var productVariantGroupId = new ProductVariantGroupId(UUID.randomUUID());
    final var theoreticalQuantity = BigDecimal.valueOf(3000);
    final var requestedQuantity = BigDecimal.valueOf(5000);
    final var productSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var triggeredBy = "TEST";

    final var nominatedRequest = new NominatedRequest(
        referenceId,
        useId,
        budgetCycle,
        UUID.fromString(productOrderId.value()),
        productVariantGroupId.value(),
        theoreticalQuantity,
        requestedQuantity,
        BigDecimal.ZERO,
        triggeredBy);

    final var materialCommitmentUseSharedRawMaterial =
        MaterialCommitmentUseSharedRawMaterial.of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);
    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        orderId, orderLineId, budgetId, referenceId, useId,
        BigDecimal.valueOf(10000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, BigDecimal.valueOf(9000)));

    final var order = new Order(productOrderId, OrderStatusKey.DRAFT, new OrderLines(new ArrayList<>()),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(1)),
        new OrderSupplierId(productSupplierId), false, OrderVersion.firstVersion());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.uuidGenerator.generate()).thenReturn(distributionNominatedId);
    final var sharedRawMaterial = new SharedRawMaterialNominated(referenceId, useId, budgetCycle);
    when(this.distributionNominatedRepository.findBySharedRawMaterial(
        sharedRawMaterial)).thenReturn(
            List.of(DistributionNominatedMother.pendingWithOneLineAndRequestedQuantity(BigDecimal.valueOf(1000))
                .referenceId(new ReferenceId(referenceId)).useId(new UseId(useId)).budgetCycle(new BudgetCycle(budgetCycle)).build()));
    when(this.alternativeSharedRawMaterialProvider.alternatives(materialCommitmentUseSharedRawMaterial))
        .thenReturn(List.of(materialCommitmentUseSharedRawMaterial));
    when(this.commitmentAdjustedProvider.findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
        .thenReturn(partialQuantities);
    when(this.orderRepository.find(productOrderId)).thenReturn(Optional.of(order));
    final var product = Instancio.of(Product.class).set(field("productId"), new ProductId(referenceProductId.toString())).create();
    when(this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.toString()))).thenReturn(Optional.of(product));
    final var line = DistributionNominatedLineMother.createdWithQuantityAndDate(requestedQuantity, now).build();
    doReturn(List.of(line)).when(this.linesIdentifier).identifyCreatedLines(any(), any(), any(), any(), any());

    final var result = this.sut.execute(new CreateDistributionNominatedCommand(nominatedRequest, new AutoRequest()));

    final var expectedDistributionNominated = DistributionNominatedMother.pending()
        .id(new Id(distributionNominatedId))
        .referenceId(new ReferenceId(referenceId))
        .useId(new UseId(useId))
        .budgetCycle(new BudgetCycle(budgetCycle))
        .referenceProductId(new ReferenceProductId(referenceProductId))
        .productOrderId(new ProductOrderId(UUID.fromString(productOrderId.value())))
        .productSupplierId(new ProductSupplierId(productSupplierId))
        .productVariantGroupId(productVariantGroupId)
        .theoreticalQuantity(new DistributionNominated.TheoreticalQuantity(theoreticalQuantity))
        .requestedQuantity(new RequestedQuantity(requestedQuantity))
        .audit(CompleteAuditMother.createdWith(now, triggeredBy))
        .consumptionFactor(new ConsumptionFactor(roundUpScale2(BigDecimal.ZERO)))
        .distributedQuantity(new DistributedQuantity(roundUpScale2(BigDecimal.ZERO)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(roundUpScale2(BigDecimal.ZERO)))
        .status(DistributionNominatedStatus.NON_DISTRIBUTABLE)
        .lines(List.of(line))
        .plan(DistributionNominatedPlan.AUTO)
        .build();
    assertThat(result.getId().value()).isEqualTo(distributionNominatedId);
    verify(this.distributionNominatedRepository).save(expectedDistributionNominated);
    verify(this.eventBus).send(expectedEvent(expectedDistributionNominated));
    verify(this.lockRepository).lock(Lock.from(sharedRawMaterial));
  }

  @Test
  void should_throw_exception_when_preselection_commitments_does_not_exits() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var referenceProductId = UUID.randomUUID();
    final var theoreticalQuantity = BigDecimal.valueOf(3000);
    final var requestedQuantity = BigDecimal.valueOf(5000);
    final var productOrderId = new OrderId(orderId.toString());
    final var productVariantGroupId = new ProductVariantGroupId(UUID.randomUUID());
    final var line = DistributionNominatedLineMother.createdWithQuantityAndDate(requestedQuantity, now).build();
    final var triggeredBy = "TEST";
    final var productSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var sharedRawMaterial = new SharedRawMaterialNominated(referenceId, useId, budgetCycle);
    final var nominatedRequest = new NominatedRequest(
        referenceId,
        useId,
        budgetCycle,
        UUID.fromString(productOrderId.value()),
        productVariantGroupId.value(),
        theoreticalQuantity,
        requestedQuantity,
        BigDecimal.ZERO,
        triggeredBy);
    final var order = new Order(productOrderId, OrderStatusKey.DRAFT, new OrderLines(new ArrayList<>()),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(1)),
        new OrderSupplierId(productSupplierId), false, OrderVersion.firstVersion());
    final var preselection = new PreselectedRequest(List.of(new LineSelectionRequest(orderId, orderLineId, requestedQuantity)));

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.uuidGenerator.generate()).thenReturn(distributionNominatedId);
    when(this.distributionNominatedRepository.findBySharedRawMaterial(
        new SharedRawMaterialNominated(referenceId, useId, budgetCycle))).thenReturn(emptyList());
    when(this.orderRepository.find(productOrderId)).thenReturn(Optional.of(order));
    final var product = Instancio.of(Product.class).set(field("productId"), new ProductId(referenceProductId.toString())).create();
    when(this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.toString()))).thenReturn(Optional.of(product));
    doReturn(List.of(line)).when(this.linesIdentifier).identifyCreatedLines(any(), any(), any(), any(), any());

    final Throwable result = catchThrowable(() -> this.sut.execute(new CreateDistributionNominatedCommand(nominatedRequest, preselection)));

    assertThat(result).isInstanceOf(ErrorException.class)
        .hasMessageContaining(String.format("Commitment %s with line %s not found", orderId, orderLineId));
    verify(this.lockRepository).lock(Lock.from(sharedRawMaterial));
    verify(this.distributionNominatedRepository, never()).save(any());
    verify(this.lockRepository).lock(Lock.from(sharedRawMaterial));
    verifyNoInteractions(this.eventBus);
  }

  @Test
  void should_throw_exception_when_not_enough_assignable_quantity() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var referenceProductId = UUID.randomUUID();
    final var productOrderId = new OrderId(orderId.toString());
    final var productVariantGroupId = new ProductVariantGroupId(UUID.randomUUID());
    final var theoreticalQuantity = BigDecimal.valueOf(3000);
    final var requestedQuantity = BigDecimal.valueOf(5000);
    final var assignableQuantityTotal = BigDecimal.valueOf(6000);

    final var nominatedRequest = new NominatedRequest(
        referenceId,
        useId,
        budgetCycle,
        UUID.fromString(productOrderId.value()),
        productVariantGroupId.value(),
        theoreticalQuantity,
        requestedQuantity,
        BigDecimal.ZERO,
        "TEST");

    final var materialCommitmentUseSharedRawMaterial =
        MaterialCommitmentUseSharedRawMaterial.of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);
    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        orderId, orderLineId, budgetId, referenceId, useId,
        BigDecimal.valueOf(10000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));

    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, assignableQuantityTotal));

    final var order = new Order(productOrderId, OrderStatusKey.DRAFT, new OrderLines(new ArrayList<>()),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(1)),
        new OrderSupplierId(UUID.randomUUID().toString()), false, OrderVersion.firstVersion());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.uuidGenerator.generate()).thenReturn(distributionNominatedId);
    final var sharedRawMaterial = new SharedRawMaterialNominated(referenceId, useId, budgetCycle);
    when(this.distributionNominatedRepository.findBySharedRawMaterial(
        sharedRawMaterial)).thenReturn(emptyList());
    when(this.commitmentAdjustedProvider.findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
        .thenReturn(partialQuantities);
    when(this.orderRepository.find(productOrderId)).thenReturn(Optional.of(order));
    final var product = Instancio.of(Product.class).set(field("productId"), new ProductId(referenceProductId.toString())).create();
    when(this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.toString()))).thenReturn(Optional.of(product));

    final Throwable result =
        catchThrowable(() -> this.sut.execute(new CreateDistributionNominatedCommand(nominatedRequest, new AutoRequest())));

    verify(this.distributionNominatedRepository, never()).save(any());
    verify(this.lockRepository).lock(Lock.from(sharedRawMaterial));

    assertThat(result)
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("Insufficient commitment");
  }

  @Test
  void should_throw_exception_when_not_enough_assignable_quantity_when_more_DNs() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var referenceProductId = UUID.randomUUID();
    final var productOrderId = new OrderId(orderId.toString());
    final var productVariantGroupId = new ProductVariantGroupId(UUID.randomUUID());
    final var theoreticalQuantity = BigDecimal.valueOf(3000);

    final var requestedQuantity = BigDecimal.valueOf(7000);

    final var nominatedRequest = new NominatedRequest(
        referenceId,
        useId,
        budgetCycle,
        UUID.fromString(productOrderId.value()),
        productVariantGroupId.value(),
        theoreticalQuantity,
        requestedQuantity,
        BigDecimal.ZERO,
        "TEST");

    final var existingDNForSharedRawMaterial =
        DistributionNominatedMother.pendingWithOneLineAndRequestedQuantity(BigDecimal.valueOf(4000))
            .referenceId(new ReferenceId(referenceId))
            .build();

    final var materialCommitmentUseSharedRawMaterial =
        MaterialCommitmentUseSharedRawMaterial.of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);
    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        existingDNForSharedRawMaterial.lines().value().getFirst().commitmentOrder().id().value(),
        existingDNForSharedRawMaterial.lines().value().getFirst().commitmentOrder().lineId().value(), budgetId, referenceId, useId,
        BigDecimal.valueOf(10000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
    final var order = new Order(productOrderId, OrderStatusKey.DRAFT, new OrderLines(new ArrayList<>()),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(1)),
        new OrderSupplierId(UUID.randomUUID().toString()), false, OrderVersion.firstVersion());
    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, materialCommitmentUse.getQuantity().value()));

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.uuidGenerator.generate()).thenReturn(distributionNominatedId);
    final var sharedRawMaterial = new SharedRawMaterialNominated(referenceId, useId, budgetCycle);
    when(this.distributionNominatedRepository.findBySharedRawMaterial(
        sharedRawMaterial)).thenReturn(
            List.of(existingDNForSharedRawMaterial));
    when(this.commitmentAdjustedProvider.findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
        .thenReturn(partialQuantities);
    when(this.orderRepository.find(productOrderId)).thenReturn(Optional.of(order));
    final var product = Instancio.of(Product.class).set(field("productId"), new ProductId(referenceProductId.toString())).create();
    when(this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.toString()))).thenReturn(Optional.of(product));

    final Throwable result =
        catchThrowable(() -> this.sut.execute(new CreateDistributionNominatedCommand(nominatedRequest, new AutoRequest())));

    verify(this.distributionNominatedRepository, never()).save(any());
    verify(this.lockRepository).lock(Lock.from(sharedRawMaterial));

    assertThat(result)
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("Insufficient commitment");
  }

  @Test
  void should_throw_exception_when_reference_product_id_not_found() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var productOrderId = new OrderId(orderId.toString());
    final var productVariantGroupId = new ProductVariantGroupId(UUID.randomUUID());
    final var theoreticalQuantity = BigDecimal.valueOf(3000);

    final var requestedQuantity = BigDecimal.valueOf(7000);

    final var nominatedRequest = new NominatedRequest(
        referenceId,
        useId,
        budgetCycle,
        UUID.fromString(productOrderId.value()),
        productVariantGroupId.value(),
        theoreticalQuantity,
        requestedQuantity,
        BigDecimal.ZERO,
        "TEST");

    final var existingDNForSharedRawMaterial = DistributionNominatedMother.pendingWithOneLineAndRequestedQuantity(BigDecimal.valueOf(4000))
        .referenceId(new ReferenceId(referenceId))
        .build();

    final var materialCommitmentUseSharedRawMaterial =
        MaterialCommitmentUseSharedRawMaterial.of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);
    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        existingDNForSharedRawMaterial.lines().value().getFirst().commitmentOrder().id().value(),
        existingDNForSharedRawMaterial.lines().value().getFirst().commitmentOrder().lineId().value(), budgetId, referenceId, useId,
        BigDecimal.valueOf(10000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, materialCommitmentUse.getQuantity().value()));

    final var order = new Order(productOrderId, OrderStatusKey.DRAFT, new OrderLines(new ArrayList<>()),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(1)),
        new OrderSupplierId(UUID.randomUUID().toString()), false, OrderVersion.firstVersion());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.uuidGenerator.generate()).thenReturn(distributionNominatedId);
    final var sharedRawMaterial = new SharedRawMaterialNominated(referenceId, useId, budgetCycle);
    when(this.distributionNominatedRepository.findBySharedRawMaterial(
        sharedRawMaterial)).thenReturn(
            List.of(existingDNForSharedRawMaterial));
    when(this.commitmentAdjustedProvider.findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
        .thenReturn(partialQuantities);
    when(this.orderRepository.find(productOrderId)).thenReturn(Optional.of(order));
    when(this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.toString()))).thenReturn(Optional.empty());

    final Throwable result =
        catchThrowable(() -> this.sut.execute(new CreateDistributionNominatedCommand(nominatedRequest, new AutoRequest())));

    verify(this.distributionNominatedRepository, never()).save(any());
    verify(this.lockRepository).lock(Lock.from(sharedRawMaterial));

    assertThat(result)
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("Product of reference " + referenceId + " not found");
  }

  @Test
  void should_throw_exception_when_reference_order_not_found() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var referenceProductId = UUID.randomUUID();
    final var productOrderId = new OrderId(orderId.toString());
    final var productVariantGroupId = new ProductVariantGroupId(UUID.randomUUID());
    final var theoreticalQuantity = BigDecimal.valueOf(3000);
    final var requestedQuantity = BigDecimal.valueOf(7000);

    final var nominatedRequest = new NominatedRequest(
        referenceId,
        useId,
        budgetCycle,
        UUID.fromString(productOrderId.value()),
        productVariantGroupId.value(),
        theoreticalQuantity,
        requestedQuantity,
        BigDecimal.ZERO,
        "TEST");

    final var existingDNForSharedRawMaterial = DistributionNominatedMother.pendingWithOneLineAndRequestedQuantity(BigDecimal.valueOf(4000))
        .referenceId(new ReferenceId(referenceId))
        .build();

    final var materialCommitmentUseSharedRawMaterial =
        MaterialCommitmentUseSharedRawMaterial.of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);
    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        existingDNForSharedRawMaterial.lines().value().getFirst().commitmentOrder().id().value(),
        existingDNForSharedRawMaterial.lines().value().getFirst().commitmentOrder().lineId().value(), budgetId, referenceId, useId,
        BigDecimal.valueOf(10000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, materialCommitmentUse.getQuantity().value()));

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.uuidGenerator.generate()).thenReturn(distributionNominatedId);
    final var sharedRawMaterial = new SharedRawMaterialNominated(referenceId, useId, budgetCycle);
    when(this.distributionNominatedRepository.findBySharedRawMaterial(
        sharedRawMaterial)).thenReturn(
            List.of(existingDNForSharedRawMaterial));
    when(this.commitmentAdjustedProvider.findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
        .thenReturn(partialQuantities);
    when(this.orderRepository.find(new OrderId(orderId.toString()))).thenReturn(Optional.empty());
    final var product = Instancio.of(Product.class).set(field("productId"), new ProductId(referenceProductId.toString())).create();
    when(this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.toString()))).thenReturn(Optional.of(product));

    final Throwable result =
        catchThrowable(() -> this.sut.execute(new CreateDistributionNominatedCommand(nominatedRequest, new AutoRequest())));

    verify(this.distributionNominatedRepository, never()).save(any());
    verify(this.lockRepository).lock(Lock.from(sharedRawMaterial));

    assertThat(result)
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("Order " + orderId + " not found");
  }

  @Test
  void should_save_nominated_entity_and_return_id_multiple_line() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var orderId2 = UUID.randomUUID();
    final var orderLineId2 = UUID.randomUUID();
    final var referenceProductId = UUID.randomUUID();
    final var productOrderId = new OrderId(orderId.toString());
    final var productVariantGroupId = new ProductVariantGroupId(UUID.randomUUID());
    final var theoreticalQuantity = BigDecimal.valueOf(3000);
    final var requestedQuantity = BigDecimal.valueOf(4000);

    final var nominatedRequest = new NominatedRequest(
        referenceId,
        useId,
        budgetCycle,
        UUID.fromString(productOrderId.value()),
        productVariantGroupId.value(),
        theoreticalQuantity,
        requestedQuantity,
        BigDecimal.ZERO,
        "TEST");

    final var materialCommitmentUseSharedRawMaterial =
        MaterialCommitmentUseSharedRawMaterial.of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);

    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        orderId, orderLineId, budgetId, referenceId, useId,
        BigDecimal.valueOf(2000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
    final var materialCommitmentUse2 = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        orderId2, orderLineId2, budgetId, referenceId, useId,
        BigDecimal.valueOf(2000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));

    final var order = new Order(productOrderId, OrderStatusKey.DRAFT, new OrderLines(new ArrayList<>()),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(1)),
        new OrderSupplierId(UUID.randomUUID().toString()), false, OrderVersion.firstVersion());

    final var partialQuantities = List.of(
        CommitmentAdjusted.of(materialCommitmentUse, BigDecimal.valueOf(2000)),
        CommitmentAdjusted.of(materialCommitmentUse2, BigDecimal.valueOf(2000)));

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.uuidGenerator.generate()).thenReturn(distributionNominatedId);
    final var sharedRawMaterial = new SharedRawMaterialNominated(referenceId, useId, budgetCycle);
    when(this.distributionNominatedRepository.findBySharedRawMaterial(
        sharedRawMaterial)).thenReturn(emptyList());
    final var productReferenceId = new ProductReferenceId(UuidMother.fromInteger(999).toString());
    final var alternative = MaterialCommitmentUseSharedRawMaterial.of(
        materialCommitmentUseSharedRawMaterial.materialCommitmentUseId().value(),
        productReferenceId.value(), materialCommitmentUseSharedRawMaterial.materialCommitmentBudgetId().value());
    final var sharedRawMaterialList = List.of(materialCommitmentUseSharedRawMaterial, alternative);
    doReturn(sharedRawMaterialList).when(this.alternativeSharedRawMaterialProvider).alternatives(materialCommitmentUseSharedRawMaterial);
    when(this.commitmentAdjustedProvider.findByAnySharedRawMaterial(sharedRawMaterialList))
        .thenReturn(partialQuantities);
    when(this.orderRepository.find(productOrderId)).thenReturn(Optional.of(order));
    final var product = Instancio.of(Product.class).set(field("productId"), new ProductId(referenceProductId.toString())).create();
    when(this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.toString()))).thenReturn(Optional.of(product));
    final var line1 = DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(2000), now)
        .theoreticalQuantity(new TheoreticalQuantity(BigDecimal.valueOf(1500)))
        .build();
    final var line2 = DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(2000), now)
        .theoreticalQuantity(new TheoreticalQuantity(BigDecimal.valueOf(1500)))
        .build();
    doReturn(List.of(line1, line2)).when(this.linesIdentifier).identifyCreatedLines(any(), any(), any(), any(), any());

    final var result = this.sut.execute(new CreateDistributionNominatedCommand(nominatedRequest, new AutoRequest()));

    assertThat(result.getId().value()).isEqualTo(distributionNominatedId);

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor.forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    final DistributionNominated distributionNominated = nominatedCaptor.getValue();
    verify(this.eventBus).send(expectedEvent(distributionNominated));
    verify(this.lockRepository).lock(Lock.from(sharedRawMaterial));

    assertThat(distributionNominated.lines().value())
        .hasSize(2)
        .extracting(
            DistributionNominatedLine::requestedQuantity,
            DistributionNominatedLine::theoreticalQuantity)
        .containsExactlyInAnyOrder(
            tuple(
                new DistributionNominatedLine.RequestedQuantity(roundUpScale2(BigDecimal.valueOf(2000))),
                new TheoreticalQuantity(roundUpScale2(BigDecimal.valueOf(1500)))),
            tuple(
                new DistributionNominatedLine.RequestedQuantity(roundUpScale2(BigDecimal.valueOf(2000))),
                new TheoreticalQuantity(roundUpScale2(BigDecimal.valueOf(1500)))));
  }

  private static List<DomainEvent<?>> expectedEvent(final DistributionNominated nominatedCaptor) {
    return List.of(new DistributionNominatedUnifiedEvent(nominatedCaptor, DistributionNominatedUnifiedEvent.EventType.CREATED));
  }
}
