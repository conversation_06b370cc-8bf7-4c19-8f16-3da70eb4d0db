package com.inditex.icdmdemg.application.commitmentuse.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseUniqueKey;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseUniqueKeyMother;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class MaterialCommitmentUseUniqueKeyTest {

  @Test
  void should_map_key_from_MaterialCommitmentUse() {
    final var materialCommitment = Instancio.create(MaterialCommitmentUse.class);
    final var expectedKey = new MaterialCommitmentUseUniqueKey(materialCommitment.getOrderLine().orderId().value(),
        materialCommitment.getOrderLine().orderLineId().value(),
        materialCommitment.getMaterialReferenceId().value(), materialCommitment.getOrderLine().budgetId().value(),
        materialCommitment.getUseId().value());

    final var result = MaterialCommitmentUseUniqueKey.fromMaterialCommitmentUse(materialCommitment);

    assertThat(result).isEqualTo(expectedKey);
  }

  @Test
  void should_compareTo() {
    final var expectedKey = MaterialCommitmentUseUniqueKeyMother.with(601, 701, 501);
    final var expectedKey2 = MaterialCommitmentUseUniqueKeyMother.with(601, 701, 501);

    assertThat(expectedKey).isEqualByComparingTo(expectedKey2);
  }

  @Test
  void should_not_accept_null_parameters() {
    final var expectedKey = MaterialCommitmentUseUniqueKeyMother.with(601, 701, 501);
    assertThatThrownBy(() -> expectedKey.compareTo(null)).isInstanceOf(NullPointerException.class);
  }

}
