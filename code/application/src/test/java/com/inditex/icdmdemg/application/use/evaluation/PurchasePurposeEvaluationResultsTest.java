package com.inditex.icdmdemg.application.use.evaluation;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;

import org.junit.jupiter.api.Test;

class PurchasePurposeEvaluationResultsTest {

  @Test
  void should_compare_and_order_purchase_purpose_conditions() {
    final var result1 = this.createResult(0,
        List.of(PurchasePurposeConditionName.BUYERCODE, PurchasePurposeConditionName.FAMILY, PurchasePurposeConditionName.SUPPLIER_PT), 5);
    final var result2 =
        this.createResult(0, List.of(PurchasePurposeConditionName.BUYERGROUP, PurchasePurposeConditionName.BUYERSUBGROUP), 3);
    final var result3 = this.createResult(1, List.of(PurchasePurposeConditionName.BUYERSUBGROUP), 6);
    final var result4 = this.createResult(0,
        List.of(PurchasePurposeConditionName.BUYERGROUP, PurchasePurposeConditionName.FAMILY, PurchasePurposeConditionName.SUPPLIER_PT), 4);

    final var purchasePurposeEvaluationResults = new PurchasePurposeEvaluationResults(List.of(result1, result2, result3, result4));

    final var sorted = purchasePurposeEvaluationResults.results();

    assertThat(sorted).containsExactly(result4, result1, result2, result3);
  }

  @Test
  void should_compare_and_order_purchase_purpose_conditions_scenario_6_input_1() {
    // u1, u1a, u1a_s1, u1a_NOs1, u1b, u1b_F1, u1b_F23
    final var u1 = this.createResult(0, List.of(), 0);
    final var u1a = this.createResult(0, List.of(PurchasePurposeConditionName.BUYERGROUP), 1);
    final var u1as1 = this.createResult(1, List.of(PurchasePurposeConditionName.BUYERGROUP), 1);
    final var u1aNOs1 =
        this.createResult(0, List.of(PurchasePurposeConditionName.BUYERGROUP, PurchasePurposeConditionName.SUPPLIER_PT), 2);
    final var u1b = this.createResult(1, List.of(), 0);
    final var u1bF1 = this.createResult(1, List.of(PurchasePurposeConditionName.FAMILY), 1);
    final var u1bF23 = this.createResult(1, List.of(PurchasePurposeConditionName.FAMILY), 2);

    final var input = new PurchasePurposeEvaluationResults(List.of(u1, u1a, u1as1, u1aNOs1, u1b, u1bF1, u1bF23));

    final var sorted = input.results();

    assertThat(sorted).containsExactly(u1aNOs1, u1a, u1, u1as1, u1bF23, u1bF1, u1b);
  }

  @Test
  void should_compare_and_order_purchase_purpose_conditions_scenario_6_input_2() {
    // u1, u1a, u1a_s1, u1a_NOs1, u1b, u1b_F1, u1b_F23
    final var u1 = this.createResult(0, List.of(), 0);
    final var u1a = this.createResult(1, List.of(), 0);
    final var u1aS1 = this.createResult(1, List.of(PurchasePurposeConditionName.SUPPLIER_PT), 1);
    final var u1aNOs1 = this.createResult(2, List.of(), 0);
    final var u1b = this.createResult(0, List.of(PurchasePurposeConditionName.BUYERGROUP), 0);
    final var u1bF1 = this.createResult(1, List.of(PurchasePurposeConditionName.BUYERGROUP), 1);
    final var u1bF23 = this.createResult(0, List.of(PurchasePurposeConditionName.BUYERGROUP, PurchasePurposeConditionName.FAMILY), 3);

    final var input = new PurchasePurposeEvaluationResults(List.of(u1, u1a, u1aS1, u1aNOs1, u1b, u1bF1, u1bF23));

    final var sorted = input.results();

    assertThat(sorted).containsExactly(u1bF23, u1b, u1, u1bF1, u1aS1, u1a, u1aNOs1);
  }

  private PurchasePurposeEvaluationResult createResult(
      final int kos,
      final List<PurchasePurposeConditionName> paramsWithOk,
      final int coincidences) {
    return new PurchasePurposeEvaluationResult(
        new Use.Id(UUID.randomUUID()),
        kos,
        paramsWithOk,
        coincidences);
  }

}
