package com.inditex.icdmdemg.application.distributionsummary.query;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.ParameterizedTest.INDEX_PLACEHOLDER;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doReturn;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.DistributionQuantity;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.OrderQuantity;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.Product;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.ProductProvisioningSummaryRequest;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.Provision;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.Reference;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.StockLocationQuantities;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerSummaryDTO;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.DistributionDistributed;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.DistributionRequested;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvisionCollection;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvisionRepository;
import com.inditex.icdmdemg.domain.nominatedprovision.entity.NominatedProvisionAudit;
import com.inditex.icdmdemg.domain.nominatedprovision.entity.NominatedProvisionAudit.AuditBy;
import com.inditex.icdmdemg.domain.nominatedprovision.entity.NominatedProvisionAudit.AuditTimestamp;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseRepository;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.Entered;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.OrderUseType;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.Ordered;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.Pending;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseRepository;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO.LocalizationId;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO.LocalizationType;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO.ProductId;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO.Stock;
import com.inditex.icdmdemg.shared.utils.CurrentMethodName;
import com.inditex.icdmdemg.shared.utils.NumericUtils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GetProductProvisioningSummaryQueryHandlerTest {

  public static final String PARAMETERIZED_TEST_DESCRIPTION = "[" + INDEX_PLACEHOLDER + "] [{0}]";

  @Mock
  private NominatedProvisionRepository nominatedProvisionRepository;

  @Mock
  private DistributionInnerRepository distributionInnerRepository;

  @Mock
  private OrderUseRepository orderUseRepository;

  @Mock
  private ProductUseRepository productUseRepository;

  @InjectMocks
  private GetProductProvisioningSummaryQueryHandler sut;

  @ParameterizedTest(name = PARAMETERIZED_TEST_DESCRIPTION)
  @MethodSource("getInputCasesAndExpectedReturn")
  void should_work_correct_products(
      final String ignoredTestName,
      final ProductProvisioningSummaryRequest request,
      final List<NominatedProvision> nominatedProvisionDTOs,
      final List<DistributionInnerSummaryDTO> innerSummaryDTOS,
      final List<OrderUseSummaryDTO> orderUseSummaryDTOS,
      final List<ProductUseSummaryDTO> productUseSummaryDTOS,
      final List<Product> expected) {

    doReturn(new NominatedProvisionCollection(nominatedProvisionDTOs)).when(this.nominatedProvisionRepository)
        .findByProductsAndReferencesAndBudgets(anyList(),
            anyList(),
            anyList());
    doReturn(innerSummaryDTOS).when(this.distributionInnerRepository).findDistributionInnerSummaries(anyList(), anyList(), anyList(),
        anyList());
    doReturn(orderUseSummaryDTOS).when(this.orderUseRepository).findOrderUseSummaries(anyList(), anyList(), anyList(), anyList());
    doReturn(productUseSummaryDTOS).when(this.productUseRepository).findProductUseSummaries(anyList(), anyList(), anyList());

    final var result = this.sut.ask(new GetProductProvisioningSummaryQuery(request));

    assertThat(result.response().get().products().size()).isEqualTo(expected.size());

    result.response().get().products().forEach(productResult -> {
      final var productExpected =
          expected.stream().filter(product -> Objects.equals(product.productId(), productResult.productId())).findAny();
      assertThat(productExpected).isNotEmpty();
      assertThat(productResult.references()).containsExactlyInAnyOrderElementsOf(productExpected.get().references());
    });

  }

  @Test
  void should_not_work_correct_with_empty_references_and_emtpy_products_request() {
    final var request = new ProductProvisioningSummaryRequest(
        List.of(), List.of(),
        List.of("urn:BUDGETCYCLE:a02b554c-9802-4ad2-bf46-fe0bd3d116fc"));

    final var query = new GetProductProvisioningSummaryQuery(request);

    final var result = this.sut.ask(query);

    assertThat(result.error().isPresent()).isTrue();
  }

  @Test
  void should_not_work_correct_with_references_and_products_request() {
    final var request = new ProductProvisioningSummaryRequest(
        List.of(UUID.randomUUID(), UUID.randomUUID()), List.of(UUID.randomUUID(), UUID.randomUUID()),
        List.of("urn:BUDGETCYCLE:a02b554c-9802-4ad2-bf46-fe0bd3d116fc"));

    final var query = new GetProductProvisioningSummaryQuery(request);

    final var result = this.sut.ask(query);

    assertThat(result.error().isPresent()).isTrue();
  }

  private static Stream<Arguments> getInputCasesAndExpectedReturn() {
    return Stream.of(
        shouldBuildCompleteResult(),
        shouldBuildWithDefaultValues(),
        shouldBuildWithInnersWithoutOrderUse());
  }

  private static Arguments shouldBuildCompleteResult() {
    final var productId1 = UUID.randomUUID();
    final var productId2 = UUID.randomUUID();

    final UUID referenceId1 = UUID.randomUUID();
    final UUID useId1 = UUID.randomUUID();
    final String budget1 = UUID.randomUUID().toString();

    final UUID referenceId2 = UUID.randomUUID();
    final UUID useId2 = UUID.randomUUID();
    final String budget2 = UUID.randomUUID().toString();

    final List<NominatedProvision> nominatedProvisionDTOs =
        List.of(
            generateNominatedProvision(productId1, referenceId1, useId1, budget1, "Laracha", 200, 200, 100, 100, 100),
            generateNominatedProvision(productId1, referenceId2, useId2, budget2, "Almacen", 210, 210, 100, 110, 110),
            generateNominatedProvision(productId2, referenceId1, useId1, budget1, "Laracha", 220, 220, 100, 120, 120),
            generateNominatedProvision(productId2, referenceId2, useId2, budget2, "Almacen", 230, 230, 100, 130, 130));

    final List<DistributionInnerSummaryDTO> innerSummaryDTOS =
        List.of(
            generateInnerSummaryDTO(productId1, referenceId1, useId1, budget1, 200, 100),
            generateInnerSummaryDTO(productId1, referenceId2, useId2, budget2, 210, 110),
            generateInnerSummaryDTO(productId2, referenceId1, useId1, budget1, 220, 120),
            generateInnerSummaryDTO(productId2, referenceId2, useId2, budget2, 230, 130));

    final List<OrderUseSummaryDTO> orderUseSummaryDTOS =
        List.of(
            generateOrderUseSummaryDTO(productId1, referenceId1, useId1, budget1, "ORDINARY", 200, 100),
            generateOrderUseSummaryDTO(productId1, referenceId2, useId2, budget2, "ORDINARY", 210, 110),
            generateOrderUseSummaryDTO(productId2, referenceId1, useId1, budget1, "ORDINARY", 220, 120),
            generateOrderUseSummaryDTO(productId2, referenceId2, useId2, budget2, "ORDINARY", 230, 130),
            generateOrderUseSummaryDTO(productId1, referenceId1, useId1, budget1, "COMMITMENT", 200, 100),
            generateOrderUseSummaryDTO(productId1, referenceId2, useId2, budget2, "COMMITMENT", 210, 110),
            generateOrderUseSummaryDTO(productId2, referenceId1, useId1, budget1, "COMMITMENT", 220, 120),
            generateOrderUseSummaryDTO(productId2, referenceId2, useId2, budget2, "COMMITMENT", 230, 130));

    final List<ProductUseSummaryDTO> productUseSummaryDTOS =
        List.of(
            generateProductUseSummaryDTO(productId1, referenceId1, useId1, budget1, "WAREHOUSE_DC", "Laracha", 100),
            generateProductUseSummaryDTO(productId1, referenceId2, useId2, budget2, "WAREHOUSE_DC", "Almacen", 110),
            generateProductUseSummaryDTO(productId2, referenceId1, useId1, budget1, "WAREHOUSE_DC", "Laracha", 120),
            generateProductUseSummaryDTO(productId2, referenceId2, useId2, budget2, "WAREHOUSE_DC", "Almacen", 130),
            generateProductUseSummaryDTO(productId1, referenceId1, useId1, budget1, "COMMITMENT_SUPPLIER", "Laracha", 100),
            generateProductUseSummaryDTO(productId1, referenceId2, useId2, budget2, "COMMITMENT_SUPPLIER", "Almacen", 110),
            generateProductUseSummaryDTO(productId2, referenceId1, useId1, budget1, "COMMITMENT_SUPPLIER", "Laracha", 120),
            generateProductUseSummaryDTO(productId2, referenceId2, useId2, budget2, "COMMITMENT_SUPPLIER", "Almacen", 130));

    final var request = new ProductProvisioningSummaryRequest(List.of(productId1, productId2), List.of(), List.of());

    final var result = List.of(
        new Product(productId1, List.of(
            new Reference(
                referenceId1, useId1, budget1,
                new Provision(
                    List.of(
                        new StockLocationQuantities("Laracha", "WAREHOUSE_DC", BigDecimal.valueOf(100))),
                    new OrderQuantity(BigDecimal.valueOf(200), BigDecimal.valueOf(100), BigDecimal.valueOf(100)),
                    new DistributionQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(200)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(100)))),
                new Provision(
                    List.of(
                        new StockLocationQuantities("Laracha", "COMMITMENT_SUPPLIER", BigDecimal.valueOf(100))),
                    new OrderQuantity(BigDecimal.valueOf(200), BigDecimal.valueOf(100), BigDecimal.valueOf(100)),
                    new DistributionQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(200)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(200))))),
            new Reference(
                referenceId2, useId2, budget2,
                new Provision(
                    List.of(
                        new StockLocationQuantities("Almacen", "WAREHOUSE_DC", BigDecimal.valueOf(110))),
                    new OrderQuantity(BigDecimal.valueOf(210), BigDecimal.valueOf(110), BigDecimal.valueOf(100)),
                    new DistributionQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(210)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(110)))),
                new Provision(
                    List.of(
                        new StockLocationQuantities("Almacen", "COMMITMENT_SUPPLIER", BigDecimal.valueOf(110))),
                    new OrderQuantity(BigDecimal.valueOf(210), BigDecimal.valueOf(110), BigDecimal.valueOf(100)),
                    new DistributionQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(210)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(210))))))),
        new Product(productId2, List.of(
            new Reference(
                referenceId1, useId1, budget1,
                new Provision(
                    List.of(
                        new StockLocationQuantities("Laracha", "WAREHOUSE_DC", BigDecimal.valueOf(120))),
                    new OrderQuantity(BigDecimal.valueOf(220), BigDecimal.valueOf(120), BigDecimal.valueOf(100)),
                    new DistributionQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(220)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(120)))),
                new Provision(
                    List.of(
                        new StockLocationQuantities("Laracha", "COMMITMENT_SUPPLIER", BigDecimal.valueOf(120))),
                    new OrderQuantity(BigDecimal.valueOf(220), BigDecimal.valueOf(120), BigDecimal.valueOf(100)),
                    new DistributionQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(220)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(220))))),
            new Reference(
                referenceId2, useId2, budget2,
                new Provision(
                    List.of(
                        new StockLocationQuantities("Almacen", "WAREHOUSE_DC", BigDecimal.valueOf(130))),
                    new OrderQuantity(BigDecimal.valueOf(230), BigDecimal.valueOf(130), BigDecimal.valueOf(100)),
                    new DistributionQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(230)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(130)))),
                new Provision(
                    List.of(
                        new StockLocationQuantities("Almacen", "COMMITMENT_SUPPLIER", BigDecimal.valueOf(130))),
                    new OrderQuantity(BigDecimal.valueOf(230), BigDecimal.valueOf(130), BigDecimal.valueOf(100)),
                    new DistributionQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(230)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(230))))))));

    return Arguments.of(CurrentMethodName.get(), request, nominatedProvisionDTOs, innerSummaryDTOS,
        orderUseSummaryDTOS,
        productUseSummaryDTOS, result);
  }

  private static Arguments shouldBuildWithDefaultValues() {
    final var productId1 = UUID.randomUUID();
    final var productId2 = UUID.randomUUID();

    final UUID referenceId1 = UUID.randomUUID();
    final UUID useId1 = UUID.randomUUID();
    final String budget1 = UUID.randomUUID().toString();

    final var nominatedProvisionDTOs = List.of(
        generateNominatedProvision(productId2, referenceId1, useId1, budget1, UUID.randomUUID().toString(), 220, 0, 50, 120, 0));

    final List<DistributionInnerSummaryDTO> innerSummaryDTOS = List.of();

    final List<OrderUseSummaryDTO> orderUseSummaryDTOS =
        List.of(
            generateOrderUseSummaryDTO(productId1, referenceId1, useId1, budget1, "ORDINARY", 200, 100),
            generateOrderUseSummaryDTO(productId2, referenceId1, useId1, budget1, "COMMITMENT", 220, 120));

    final List<ProductUseSummaryDTO> productUseSummaryDTOS = List.of();

    final var request = new ProductProvisioningSummaryRequest(List.of(productId1, productId2), List.of(), List.of());

    final var result = List.of(
        new Product(productId1, List.of(
            new Reference(
                referenceId1, useId1, budget1,
                new Provision(
                    List.of(),
                    new OrderQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(200)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(100)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(100))),
                    new DistributionQuantity(
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(0)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(0)))),
                new Provision(
                    List.of(),
                    new OrderQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(0)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(0)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(0))),
                    new DistributionQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(0)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(0))))))),
        new Product(productId2, List.of(
            new Reference(
                referenceId1, useId1, budget1,
                new Provision(
                    List.of(),
                    new OrderQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(0)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(0)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(0))),
                    new DistributionQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(0)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(0)))),
                new Provision(
                    List.of(new StockLocationQuantities(nominatedProvisionDTOs.getFirst().localizationId()
                        .value(),
                        LocalizationType.COMMITMENT_SUPPLIER
                            .value(),
                        nominatedProvisionDTOs.getFirst().stock().value())),
                    new OrderQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(220)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(120)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(50))),
                    new DistributionQuantity(
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(0)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(0))))))));

    return Arguments.of(CurrentMethodName.get(), request, nominatedProvisionDTOs, innerSummaryDTOS, orderUseSummaryDTOS,
        productUseSummaryDTOS, result);
  }

  private static Arguments shouldBuildWithInnersWithoutOrderUse() {
    final var productId1 = UUID.randomUUID();

    final UUID referenceId1 = UUID.randomUUID();
    final UUID useId1 = UUID.randomUUID();
    final String budget1 = UUID.randomUUID().toString();

    final var nominatedProvisionDTOs = List.of();

    final List<DistributionInnerSummaryDTO> innerSummaryDTOS = List.of(
        generateInnerSummaryDTO(productId1, referenceId1, useId1, budget1, 200, 200));

    final List<OrderUseSummaryDTO> orderUseSummaryDTOS = List.of();

    final List<ProductUseSummaryDTO> productUseSummaryDTOS = List.of();

    final var request = new ProductProvisioningSummaryRequest(List.of(productId1), List.of(), List.of());

    final var result = List.of(
        new Product(productId1, List.of(
            new Reference(
                referenceId1, useId1, budget1,
                new Provision(
                    List.of(),
                    new OrderQuantity(BigDecimal.valueOf(0), BigDecimal.valueOf(0), BigDecimal.valueOf(0)),
                    new DistributionQuantity(
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(200)),
                        NumericUtils.roundUpScale2(BigDecimal.valueOf(200)))),
                new Provision(
                    List.of(),
                    new OrderQuantity(BigDecimal.valueOf(0), BigDecimal.valueOf(0), BigDecimal.valueOf(0)),
                    new DistributionQuantity(BigDecimal.valueOf(0),
                        BigDecimal.valueOf(0)))))));

    return Arguments.of(CurrentMethodName.get(), request, nominatedProvisionDTOs, innerSummaryDTOS, orderUseSummaryDTOS,
        productUseSummaryDTOS, result);
  }

  private static NominatedProvision generateNominatedProvision(final UUID productId, final UUID referenceId, final UUID useId,
      final String budget, final String localizationId, final int ordered, final int distributionRequested, final int entered,
      final int pending,
      final int stock) {
    return new NominatedProvision(
        new NominatedProvision.ProductId(productId.toString()),
        new NominatedProvision.ReferenceId(referenceId.toString()),
        new NominatedProvision.UseId(useId.toString()),
        new NominatedProvision.BudgetId(budget),
        new NominatedProvision.LocalizationId(localizationId),
        new NominatedProvision.Ordered(NumericUtils.roundUpScale2(BigDecimal.valueOf(ordered))),
        new NominatedProvision.Entered(NumericUtils.roundUpScale2(BigDecimal.valueOf(entered))),
        new NominatedProvision.Pending(NumericUtils.roundUpScale2(BigDecimal.valueOf(pending))),
        new NominatedProvision.Stock(NumericUtils.roundUpScale2(BigDecimal.valueOf(stock))),
        new DistributionDistributed(NumericUtils.roundUpScale2(BigDecimal.valueOf(distributionRequested))),
        new DistributionRequested(NumericUtils.roundUpScale2(BigDecimal.valueOf(distributionRequested))),
        new NominatedProvisionAudit(new AuditTimestamp(OffsetDateTime.now()), new AuditBy("test")));
  }

  private static DistributionInnerSummaryDTO generateInnerSummaryDTO(final UUID productId, final UUID referenceId, final UUID useId,
      final String budget, final int requested, final int distributed) {
    return new DistributionInnerSummaryDTO(
        new DistributionInner.ReferenceProductId(productId),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.UseId(useId),
        new DistributionInner.BudgetCycle(budget),
        new DistributionInner.RequestedQuantity(BigDecimal.valueOf(requested)),
        new DistributionInner.DistributedQuantity(BigDecimal.valueOf(distributed)));
  }

  private static OrderUseSummaryDTO generateOrderUseSummaryDTO(final UUID productId, final UUID referenceId, final UUID useId,
      final String budget, final String type, final int ordered, final int pending) {
    return new OrderUseSummaryDTO(
        new OrderUseSummaryDTO.ProductId(productId),
        new OrderUseSummaryDTO.ProductReferenceId(referenceId),
        new OrderUseSummaryDTO.UseId(useId),
        new OrderUseSummaryDTO.BudgetCycle(budget),
        new Ordered(BigDecimal.valueOf(ordered)),
        new Pending(BigDecimal.valueOf(pending)),
        new Entered(BigDecimal.valueOf(ordered).subtract(BigDecimal.valueOf(pending))),
        OrderUseType.valueOf(type));
  }

  private static ProductUseSummaryDTO generateProductUseSummaryDTO(final UUID productId, final UUID referenceId, final UUID useId,
      final String budget, final String type, final String localizationId, final int stock) {
    return new ProductUseSummaryDTO(
        new ProductId(productId),
        new ProductUseSummaryDTO.ReferenceId(referenceId),
        new ProductUseSummaryDTO.UseId(useId),
        new ProductUseSummaryDTO.BudgetCycle(budget),
        LocalizationType.valueOf(type),
        new LocalizationId(localizationId),
        new Stock(BigDecimal.valueOf(stock)));
  }

}
