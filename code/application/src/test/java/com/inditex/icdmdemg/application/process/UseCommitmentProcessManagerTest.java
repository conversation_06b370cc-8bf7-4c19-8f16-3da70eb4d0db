package com.inditex.icdmdemg.application.process;

import static org.instancio.Select.field;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.commitmentuse.command.CalculateNominatedProvisionCommand;
import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand;
import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.OrderUse;
import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.Use;
import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommandResult;
import com.inditex.icdmdemg.application.commitmentuse.command.RegularizeColorAndProjectCommitmentCommandResult;
import com.inditex.icdmdemg.application.distributionnominated.command.AdjustDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.RegularizeColorAndProjectCommitmentCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.RegularizeColorAndProjectCommitmentCommand.LineToRegularize;
import com.inditex.icdmdemg.application.distributionnominated.command.RegularizeDistributionNominatedBudgetCycleChangeCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedLineCommitmentOrderSupplierCommand;
import com.inditex.icdmdemg.application.distributionnominated.query.GetDistributionNominatedByRegularizationCriteriaQuery;
import com.inditex.icdmdemg.application.distributionnominated.query.GetDistributionNominatedByRegularizationCriteriaQuery.LineRegularization;
import com.inditex.icdmdemg.application.process.UseCommitmentProcessManager.UseCommitmentProcessManagerInput;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseMaterialReferenceId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLine;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus.MaterialCommitmentUseStatusEnum;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseUseId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;
import com.inditex.iopcmmnt.cqrs.core.QueryBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class})
class UseCommitmentProcessManagerTest {

  @Mock
  CommandBus commandBus;

  @Mock
  QueryBus queryBus;

  @InjectMocks
  UseCommitmentProcessManager sut;

  @Test
  void should_execute_commands_projector_and_adjuster_and_calculate_nominated_provision() {
    final var uses = List.of(
        new Use(String.valueOf(UUID.randomUUID()),
            String.valueOf(UUID.randomUUID()),
            null,
            MaterialCommitmentUseStatusEnum.OPEN.name(),
            BigDecimal.ONE));

    final var useCommitmentOrders = Instancio.of(OrderUse.class)
        .set(field("orderId"), String.valueOf(UUID.randomUUID()))
        .set(field("orderLineId"), String.valueOf(UUID.randomUUID()))
        .set(field("uses"), uses)
        .create();

    final var materialCommitmentUseOrderLine = Instancio.of(MaterialCommitmentUseOrderLine.class)
        .set(field("budgetId"), new MaterialCommitmentUseBudgetId(UUID.randomUUID().toString()))
        .create();
    final var materialCommitmentUse = Instancio.of(MaterialCommitmentUse.class)
        .set(field("useId"), new MaterialCommitmentUseUseId(UUID.randomUUID().toString()))
        .set(field("materialReferenceId"), new MaterialCommitmentUseMaterialReferenceId(UUID.randomUUID().toString()))
        .set(field("orderLine"), materialCommitmentUseOrderLine)
        .create();
    final List<MaterialCommitmentUse> materialCommitmentUses = List.of(materialCommitmentUse);
    final var projectMaterialCommitmentUseCommand = new ProjectMaterialCommitmentUseCommand(useCommitmentOrders);
    final var projectMaterialCommitmentUseCommandResult = new ProjectMaterialCommitmentUseCommandResult(materialCommitmentUses);

    final var input = new UseCommitmentProcessManagerInput(useCommitmentOrders);
    doReturn(Optional.empty()).when(this.queryBus).ask(any(GetDistributionNominatedByRegularizationCriteriaQuery.class));

    final var sharedRawMaterialNominated =
        projectMaterialCommitmentUseCommandResult.materialCommitmentUses().stream()
            .map(use -> new SharedRawMaterialNominated(UUID.fromString(use.getMaterialReferenceId().value()),
                UUID.fromString(use.getUseId().value()),
                use.getOrderLine().budgetId().value()))
            .toList();
    final var adjustDistributionNominatedCommand =
        new AdjustDistributionNominatedCommand(sharedRawMaterialNominated, UseCommitmentProcessManager.TRIGGERED_BY);
    final var regularizeCommand =
        new RegularizeDistributionNominatedBudgetCycleChangeCommand(
            sharedRawMaterialNominated,
            UseCommitmentProcessManager.TRIGGERED_BY);
    final var calculateNominatedProvisionCommand = new CalculateNominatedProvisionCommand(
        sharedRawMaterialNominated,
        UseCommitmentProcessManager.TRIGGERED_BY);

    doReturn(projectMaterialCommitmentUseCommandResult).when(this.commandBus).execute(projectMaterialCommitmentUseCommand);

    this.sut.execute(input);

    verify(this.commandBus).execute(projectMaterialCommitmentUseCommand);
    verify(this.commandBus).execute(adjustDistributionNominatedCommand);
    verify(this.commandBus).execute(regularizeCommand);
    verify(this.commandBus).execute(calculateNominatedProvisionCommand);
    verify(this.commandBus).execute(this.getUpdateDistributionNominatedLineCommitmentOrderSupplierCommand(input));
    verify(this.commandBus).execute(any(CalculateNominatedProvisionCommand.class));
    verify(this.commandBus, times(0)).execute(any(RegularizeColorAndProjectCommitmentCommand.class));
  }

  @Test
  void should_execute_commands_regularization_and_projector() {
    final var uses = List.of(
        new Use(String.valueOf(UUID.randomUUID()),
            String.valueOf(UUID.randomUUID()),
            null,
            MaterialCommitmentUseStatusEnum.OPEN.name(),
            BigDecimal.ONE));

    final var useCommitmentOrders = Instancio.of(OrderUse.class)
        .set(field("orderId"), String.valueOf(UUID.randomUUID()))
        .set(field("orderLineId"), String.valueOf(UUID.randomUUID()))
        .set(field("uses"), uses)
        .create();
    final var materialCommitmentUseOrderLine = Instancio.of(MaterialCommitmentUseOrderLine.class)
        .set(field("budgetId"), new MaterialCommitmentUseBudgetId(UUID.randomUUID().toString()))
        .create();
    final var materialCommitmentUse = Instancio.of(MaterialCommitmentUse.class)
        .set(field("useId"), new MaterialCommitmentUseUseId(UUID.randomUUID().toString()))
        .set(field("materialReferenceId"), new MaterialCommitmentUseMaterialReferenceId(UUID.randomUUID().toString()))
        .set(field("orderLine"), materialCommitmentUseOrderLine)
        .create();
    final List<MaterialCommitmentUse> materialCommitmentUses = List.of(materialCommitmentUse);
    final var input = new UseCommitmentProcessManagerInput(useCommitmentOrders);

    final var lineToRegularize = Instancio.create(LineRegularization.class);
    final var regularizeColorAndProjectCommitmentCommand =
        new RegularizeColorAndProjectCommitmentCommand(new LineToRegularize(lineToRegularize.distributionNominatedLineId()),
            useCommitmentOrders);

    doReturn(Optional.of(lineToRegularize)).when(this.queryBus)
        .ask(any(GetDistributionNominatedByRegularizationCriteriaQuery.class));
    doReturn(new RegularizeColorAndProjectCommitmentCommandResult(materialCommitmentUses)).when(this.commandBus)
        .execute(regularizeColorAndProjectCommitmentCommand);

    this.sut.execute(input);

    verify(this.commandBus).execute(regularizeColorAndProjectCommitmentCommand);
    verify(this.commandBus).execute(this.getUpdateDistributionNominatedLineCommitmentOrderSupplierCommand(input));
    verify(this.commandBus).execute(any(RegularizeDistributionNominatedBudgetCycleChangeCommand.class));
    verify(this.commandBus).execute(any(CalculateNominatedProvisionCommand.class));
    verify(this.commandBus, never()).execute(any(ProjectMaterialCommitmentUseCommand.class));
    verify(this.commandBus, never()).execute(any(AdjustDistributionNominatedCommand.class));
  }

  private UpdateDistributionNominatedLineCommitmentOrderSupplierCommand getUpdateDistributionNominatedLineCommitmentOrderSupplierCommand(
      final UseCommitmentProcessManagerInput input) {
    return new UpdateDistributionNominatedLineCommitmentOrderSupplierCommand(input.orderUse().orderId(),
        input.orderUse().orderLineId(), input.orderUse().serviceLocalizationId(), "UseAssignmentToOrder event");
  }
}
