package com.inditex.icdmdemg.application.product.command;

import static org.mockito.Mockito.verify;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.product.command.ProjectProductCommand.Attributes;
import com.inditex.icdmdemg.application.product.command.ProjectProductCommand.ProductReference;
import com.inditex.icdmdemg.application.product.service.ProductProjector;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class})
class ProjectProductCommandHandlerTest {

  @Mock
  ProductProjector productProjector;

  @InjectMocks
  ProjectProductCommandHandler sut;

  @Test
  void should_call_product_projector() {
    final var productReference =
        new ProductReference(String.valueOf(UUID.randomUUID()), String.valueOf(UUID.randomUUID()),
            "supplierId", "campaignId", UUID.randomUUID(), List.of(), new Attributes(UUID.randomUUID(), 1, List.of()));
    final var command = new ProjectProductCommand(productReference);

    this.sut.doHandle(command);

    verify(this.productProjector).fromProductCreatedOrUpdated(productReference);
  }

}
