package com.inditex.icdmdemg.application.distributioninner.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;
import static org.instancio.Select.field;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributioninner.command.CreateDistributionInnerCommand.InnerRequest;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ConsumptionFactor;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.UseId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributioninner.entity.CompositeKeyInner;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.OrderStatusKey;
import com.inditex.icdmdemg.domain.order.mother.OrderMother;
import com.inditex.icdmdemg.domain.product.Product;
import com.inditex.icdmdemg.domain.product.ProductRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductId;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CreateDistributionInnerCommandHandlerTest {

  @Mock
  private DistributionInnerRepository distributionInnerRepository;

  @Mock
  private ClockUtils clockUtils;

  @Mock
  private UuidGenerator uuidGenerator;

  @Mock
  private OrderRepository orderRepository;

  @Mock
  private ProductRepository productRepository;

  @Spy
  private Transaction transaction = new Transaction();

  @Mock
  private EventBus eventBus;

  @InjectMocks
  private CreateDistributionInnerCommandHandler sut;

  @Test
  void should_execute_success() {
    final var expectedRootId = UUID.randomUUID();
    final var expectedLineId = UUID.randomUUID();
    final var now = OffsetDateTime.MIN;
    final var referenceId = UUID.randomUUID();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    final var innerRequest = new InnerRequest(
        referenceId,
        java.util.UUID.randomUUID(),
        "budgetCycle",
        java.util.UUID.randomUUID(),
        java.util.UUID.randomUUID(),
        java.math.BigDecimal.valueOf(10),
        java.math.BigDecimal.valueOf(10),
        java.math.BigDecimal.valueOf(10),
        false,
        "triggeredBy");
    final Order order = OrderMother.randomWith(OrderStatusKey.FORMALIZED, UUID.randomUUID(), false);
    final var referenceProductId = UUID.randomUUID();

    final var expectedDistributionInner =
        this.createExpectedDistributionInner(innerRequest, expectedRootId, expectedLineId, now,
            referenceProductId, DistributionInnerStatus.NON_DISTRIBUTABLE);
    doReturn(Optional.of(order)).when(this.orderRepository)
        .find(new OrderId(innerRequest.productOrderId().toString()));
    doReturn(expectedRootId)
        .doReturn(expectedLineId)
        .when(this.uuidGenerator).generate();
    final var product = Instancio.of(Product.class).set(field("productId"), new ProductId(referenceProductId.toString())).create();
    when(this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.toString()))).thenReturn(Optional.of(product));
    final ArgumentCaptor<DistributionInner> innerCaptor = ArgumentCaptor.forClass(DistributionInner.class);

    final var result = this.sut.execute(new CreateDistributionInnerCommand(innerRequest));

    assertEquals(expectedRootId, result.distributionInnerId().value());
    verify(this.distributionInnerRepository).save(innerCaptor.capture());
    final var capturedDistributionInner = innerCaptor.getValue();
    assertEquals(expectedDistributionInner, capturedDistributionInner);
    verify(this.eventBus).send(List.of(new DistributionInnerUnifiedEvent(innerCaptor.getValue(), EventType.CREATED_NON_DISTRIBUTABLE)));
  }

  @Test
  void should_execute_success_pending() {
    final var expectedRootId = UUID.randomUUID();
    final var expectedLineId = UUID.randomUUID();
    final var now = OffsetDateTime.MIN;
    final var referenceId = UUID.randomUUID();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    final var innerRequest = new InnerRequest(
        referenceId,
        java.util.UUID.randomUUID(),
        "budgetCycle",
        java.util.UUID.randomUUID(),
        java.util.UUID.randomUUID(),
        java.math.BigDecimal.valueOf(10),
        java.math.BigDecimal.valueOf(10),
        java.math.BigDecimal.valueOf(10),
        true,
        "triggeredBy");
    final Order order = OrderMother.randomWith(OrderStatusKey.FORMALIZED, UUID.randomUUID(), false);
    final var referenceProductId = UUID.randomUUID();

    final var expectedDistributionInner =
        this.createExpectedDistributionInner(innerRequest, expectedRootId, expectedLineId, now,
            referenceProductId, DistributionInnerStatus.PENDING);
    doReturn(Optional.of(order)).when(this.orderRepository)
        .find(new OrderId(innerRequest.productOrderId().toString()));
    doReturn(expectedRootId)
        .doReturn(expectedLineId)
        .when(this.uuidGenerator).generate();
    final var product = Instancio.of(Product.class).set(field("productId"), new ProductId(referenceProductId.toString())).create();
    when(this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.toString()))).thenReturn(Optional.of(product));
    final ArgumentCaptor<DistributionInner> innerCaptor = ArgumentCaptor.forClass(DistributionInner.class);

    final var result = this.sut.execute(new CreateDistributionInnerCommand(innerRequest));

    assertEquals(expectedRootId, result.distributionInnerId().value());
    verify(this.distributionInnerRepository).save(innerCaptor.capture());
    final var capturedDistributionInner = innerCaptor.getValue();
    assertEquals(expectedDistributionInner, capturedDistributionInner);
    verify(this.eventBus).send(List.of(new DistributionInnerUnifiedEvent(innerCaptor.getValue(), EventType.CREATED_PENDING)));
  }

  @Test
  void should_throw_exception_when_order_not_exists() {
    final var innerRequest = new InnerRequest(
        java.util.UUID.randomUUID(),
        java.util.UUID.randomUUID(),
        "budgetCycle",
        java.util.UUID.randomUUID(),
        java.util.UUID.randomUUID(),
        java.math.BigDecimal.valueOf(10),
        java.math.BigDecimal.valueOf(10),
        java.math.BigDecimal.valueOf(10),
        false,
        "triggeredBy");
    final var orderId = innerRequest.productOrderId().toString();
    doReturn(Optional.empty()).when(this.orderRepository).find(new OrderId(orderId));

    final var command = new CreateDistributionInnerCommand(innerRequest);

    final Throwable result = catchThrowable(() -> this.sut.execute(command));

    assertThat(result)
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("Order " + orderId + " not found");
  }

  @Test
  void should_throw_exception_when_product_not_exists() {
    final var referenceId = UUID.randomUUID();
    final var innerRequest = new InnerRequest(
        referenceId,
        java.util.UUID.randomUUID(),
        "budgetCycle",
        java.util.UUID.randomUUID(),
        java.util.UUID.randomUUID(),
        java.math.BigDecimal.valueOf(10),
        java.math.BigDecimal.valueOf(10),
        java.math.BigDecimal.valueOf(10),
        false,
        "triggeredBy");
    final var orderId = UUID.randomUUID();
    final Order order = OrderMother.randomWith(OrderStatusKey.FORMALIZED, orderId, false);
    doReturn(Optional.of(order)).when(this.orderRepository)
        .find(new OrderId(innerRequest.productOrderId().toString()));

    final var command = new CreateDistributionInnerCommand(innerRequest);

    final Throwable result = catchThrowable(() -> this.sut.execute(command));

    assertThat(result)
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("Product of reference " + referenceId + " not found");
  }

  @Test
  void should_throw_exception_when_distribution_inner_already_exists() {
    final var referenceId = UUID.randomUUID();
    final var referenceProductId = UUID.randomUUID();
    final var innerRequest = new InnerRequest(
        referenceId,
        java.util.UUID.randomUUID(),
        "budgetCycle",
        java.util.UUID.randomUUID(),
        java.util.UUID.randomUUID(),
        java.math.BigDecimal.valueOf(10),
        java.math.BigDecimal.valueOf(10),
        java.math.BigDecimal.valueOf(10),
        false,
        "triggeredBy");
    doReturn(Optional.of(Instancio.create(Order.class))).when(this.orderRepository)
        .find(new OrderId(innerRequest.productOrderId().toString()));
    doReturn(Optional.of(Instancio.create(DistributionInner.class))).when(this.distributionInnerRepository).findByCompositeKey(
        new CompositeKeyInner(innerRequest.referenceId(),
            innerRequest.useId(),
            innerRequest.budgetCycle(),
            innerRequest.productOrderId(),
            innerRequest.productVariantGroupId()));
    final var product = Instancio.of(Product.class).set(field("productId"), new ProductId(referenceProductId.toString())).create();
    when(this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.toString()))).thenReturn(Optional.of(product));

    final var command = new CreateDistributionInnerCommand(innerRequest);

    assertThatThrownBy(() -> this.sut.execute(command))
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("DistributionInner already exists");
  }

  private DistributionInner createExpectedDistributionInner(final InnerRequest request,
      final UUID expectedRootId,
      final UUID expectedLineId,
      final OffsetDateTime expectedInstant,
      final UUID referenceProductId,
      final DistributionInnerStatus expectedStatus) {
    return DistributionInnerMother.created(
        new Id(expectedRootId),
        new DistributionInnerLine.Id(expectedLineId),
        new ReferenceId(request.referenceId()),
        new UseId(request.useId()),
        new BudgetCycle(request.budgetCycle()),
        new ReferenceProductId(referenceProductId),
        new ProductOrderId(request.productOrderId()),
        new ProductVariantGroupId(request.productVariantGroupId()),
        expectedStatus,
        new TheoreticalQuantity(request.theoreticalQuantity()),
        new ConsumptionFactor(request.consumptionFactor()),
        new RequestedQuantity(request.requestedQuantity()),
        expectedInstant,
        request.triggeredBy()).build();
  }

}
