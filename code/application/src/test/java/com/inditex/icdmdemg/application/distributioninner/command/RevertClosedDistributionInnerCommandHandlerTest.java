package com.inditex.icdmdemg.application.distributioninner.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerLineMother;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderStatusKey;
import com.inditex.icdmdemg.domain.order.mother.OrderMother;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAuditMother;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.EnumSource;
import org.junit.jupiter.params.provider.EnumSource.Mode;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;

class RevertClosedDistributionInnerCommandHandlerTest {

  private final ArgumentCaptor<Collection<DomainEvent<?>>> eventCaptor = ArgumentCaptor.forClass(Collection.class);

  private final DistributionInnerRepository distributionInnerRepository = mock();

  private final OrderRepository orderRepository = mock();

  private final ClockUtils clockUtils = mock();

  private final EventBus eventBus = mock();

  private final RevertClosedDistributionInnerCommandHandler sut = new RevertClosedDistributionInnerCommandHandler(
      this.distributionInnerRepository,
      this.orderRepository,
      this.clockUtils,
      this.eventBus);

  @Test
  void should_fail_when_distribution_inner_not_exists() {
    final var distributionInner = DistributionInnerMother.closed().build();
    final var command = commandFromDistribution(distributionInner);
    this.distributionInnerDoesNotExist();

    assertThatThrownBy(() -> this.sut.doHandle(command))
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining(String.format("Distribution Inner %s not found", distributionInner.getId().value()));
  }

  @Test
  void should_fail_when_product_order_not_exists() {
    final var distributionInner = DistributionInnerMother.closed().build();
    final var command = commandFromDistribution(distributionInner);
    this.distributionInnerExists(distributionInner);
    this.productOrderDoesNotExists();

    assertThatThrownBy(() -> this.sut.doHandle(command))
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining(String.format("Product Order %s not found", distributionInner.productOrderId().value()));
  }

  @ParameterizedTest
  @MethodSource("provideStatusCases")
  void should_fail_when_product_order_is_closed_or_is_cancelled(Order productOrder) {
    final var distributionInner =
        DistributionInnerMother.closed().productOrderId(new ProductOrderId(UUID.fromString(productOrder.getId().value()))).build();
    final var command = commandFromDistribution(distributionInner);
    this.distributionInnerExists(distributionInner);
    this.productOrderExists(productOrder);

    assertThatThrownBy(() -> this.sut.doHandle(command))
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining(
            String.format("No reopen allowed in DI with id %s because finish product order is %s", distributionInner.getId().value(),
                productOrder.orderStatusKey().value()));
  }

  @Test
  void should_modify_state_to_PENDING_when_CLOSED_and_not_any_line_started() {
    final var now = OffsetDateTime.now();
    final var distributionInnerLine1 = DistributionInnerLineMother.created();
    final var distributionInnerLine2 = DistributionInnerLineMother.created();
    final var distributionInnerBuilder = DistributionInnerMother.closed().lines(List.of(
        distributionInnerLine1.build(),
        distributionInnerLine2.build()));
    final var distributionInner = distributionInnerBuilder.build();
    final var productOrder = OrderMother.randomWith(OrderStatusKey.FORMALIZED, distributionInner.productOrderId().value(), true);
    final var expectedDistributionInner = distributionInnerBuilder.status(DistributionInnerStatus.PENDING)
        .audit(CompleteAuditMother.update(distributionInner.audit(), "PATCH", now)).build();
    this.distributionInnerExists(distributionInner);
    this.productOrderExists(productOrder);
    this.clockIsNow(now);
    final var command = commandFromDistribution(distributionInner);

    this.sut.doHandle(command);

    verify(this.distributionInnerRepository).save(expectedDistributionInner);
    verify(this.eventBus).send(this.eventCaptor.capture());

    final var event = this.eventCaptor.getValue().stream().findFirst().orElseThrow();
    assertThat(((DistributionInnerUnifiedEvent) event).type()).isEqualTo(EventType.UPDATED_PENDING);
  }

  @ParameterizedTest
  @EnumSource(value = DistributionInnerStatus.class, names = {"CLOSED"}, mode = Mode.EXCLUDE)
  void should_throw_exception_when_DI_not_CLOSED(DistributionInnerStatus actualStatus) {
    final var distributionInner = DistributionInnerMother.closed().status(actualStatus).build();
    final var productOrder = OrderMother.randomWith(OrderStatusKey.FORMALIZED, distributionInner.productOrderId().value(), true);
    final var command = commandFromDistribution(distributionInner);

    this.distributionInnerExists(distributionInner);
    this.productOrderExists(productOrder);

    assertThatThrownBy(() -> this.sut.doHandle(command))
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("No reversion allowed for current DI status %s", actualStatus);
  }

  @Test
  void should_modify_state_to_SENT_when_CLOSED_and_all_lines_sent() {
    final var now = OffsetDateTime.now();
    final var distributionInnerLine1 = DistributionInnerLineMother.sent();
    final var distributionInnerLine2 = DistributionInnerLineMother.sent();
    final var distributionInnerBuilder = DistributionInnerMother.closed().lines(List.of(
        distributionInnerLine1.build(),
        distributionInnerLine2.build()));
    final var distributionInner = distributionInnerBuilder.build();
    final var productOrder = OrderMother.randomWith(OrderStatusKey.FORMALIZED, distributionInner.productOrderId().value(), true);
    final var expectedDistributionInner = distributionInnerBuilder.status(DistributionInnerStatus.SENT)
        .audit(CompleteAuditMother.update(distributionInner.audit(), "PATCH", now)).build();
    this.distributionInnerExists(distributionInner);
    this.productOrderExists(productOrder);
    this.clockIsNow(now);
    final var command = commandFromDistribution(distributionInner);

    this.sut.doHandle(command);

    verify(this.distributionInnerRepository).save(expectedDistributionInner);
    verify(this.eventBus).send(this.eventCaptor.capture());

    final var event = this.eventCaptor.getValue().stream().findFirst().orElseThrow();
    assertThat(((DistributionInnerUnifiedEvent) event).type()).isEqualTo(EventType.DISTRIBUTED);
  }

  @Test
  void should_modify_state_to_IN_PROGRESS_when_CLOSED_and_not_all_lines_sent_and_any_line_has_distribution_start_date() {
    final var now = OffsetDateTime.now();
    final var distributionInnerLine1 = DistributionInnerLineMother.startedButNotDistributed();
    final var distributionInnerLine2 = DistributionInnerLineMother.sent();
    final var distributionInnerBuilder = DistributionInnerMother.closed().lines(List.of(
        distributionInnerLine1.build(),
        distributionInnerLine2.build()));
    final var distributionInner = distributionInnerBuilder.build();
    final var productOrder = OrderMother.randomWith(OrderStatusKey.FORMALIZED, distributionInner.productOrderId().value(), true);
    final var expectedDistributionInner = distributionInnerBuilder.status(DistributionInnerStatus.IN_PROGRESS)
        .audit(CompleteAuditMother.update(distributionInner.audit(), "PATCH", now)).build();
    this.distributionInnerExists(distributionInner);
    this.productOrderExists(productOrder);
    this.clockIsNow(now);
    final var command = commandFromDistribution(distributionInner);

    this.sut.doHandle(command);

    verify(this.distributionInnerRepository).save(expectedDistributionInner);
    verify(this.eventBus).send(this.eventCaptor.capture());

    final var event = this.eventCaptor.getValue().stream().findFirst().orElseThrow();
    assertThat(((DistributionInnerUnifiedEvent) event).type()).isEqualTo(EventType.DISTRIBUTED);
  }

  private void clockIsNow(final OffsetDateTime now) {
    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
  }

  private void productOrderExists(final Order order) {
    when(this.orderRepository.find(order.getId())).thenReturn(Optional.of(order));
  }

  private void productOrderDoesNotExists() {
    when(this.orderRepository.find(any())).thenReturn(Optional.empty());
  }

  private void distributionInnerDoesNotExist() {
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.empty());
  }

  private void distributionInnerExists(final DistributionInner distributionInner) {
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.of(distributionInner));
  }

  private static RevertClosedDistributionInnerCommand commandFromDistribution(DistributionInner distributionInner) {
    return new RevertClosedDistributionInnerCommand(
        distributionInner.getId().value(),
        "PATCH");
  }

  public static Stream<Arguments> provideStatusCases() {
    return Stream.of(
        Arguments.of(OrderMother.closedWith(UUID.randomUUID(), true)),
        Arguments.of(OrderMother.cancelledWith(UUID.randomUUID(), true)));
  }

}
