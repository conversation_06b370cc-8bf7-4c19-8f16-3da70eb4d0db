package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.amigafwk.test.logcaptor.LogCaptor;
import com.inditex.amigafwk.test.logcaptor.LogCaptorExtension;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.DistributionStartDate;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.TrackingCode;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLines;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerLineMother;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseInner;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseInnerId;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseInnerLineId;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseQuantity;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTimestamp;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTrackingCode;
import com.inditex.icdmdemg.domain.shipmentwarehouse.mother.ShipmentWarehouseMother;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import ch.qos.logback.classic.Level;
import lombok.RequiredArgsConstructor;
import org.instancio.Instancio;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.ArgumentCaptor;

@ExtendWith(LogCaptorExtension.class)
@RequiredArgsConstructor
class UpdateDistributionInnerFromShipmentWarehouseCommandHandlerTest {

  private ArgumentCaptor<Collection<DomainEvent<?>>> eventCaptor = ArgumentCaptor.forClass(Collection.class);

  private ArgumentCaptor<DistributionInner> distributionInnerCaptor = ArgumentCaptor.forClass(DistributionInner.class);

  private final DistributionInnerRepository distributionInnerRepository = mock();

  private final EventBus eventBus = mock();

  private final Transaction transaction = spy();

  private final ClockUtils clockUtils = mock();

  private final UpdateDistributionInnerFromShipmentWarehouseCommandHandler sut =
      new UpdateDistributionInnerFromShipmentWarehouseCommandHandler(
          this.distributionInnerRepository, this.clockUtils, this.eventBus, this.transaction);

  private OffsetDateTime now;

  private final LogCaptor logCaptor;

  @BeforeEach
  void setUp() {
    reset(this.distributionInnerRepository, this.eventBus, this.clockUtils);

    this.eventCaptor = ArgumentCaptor.forClass(Collection.class);
    this.distributionInnerCaptor = ArgumentCaptor.forClass(DistributionInner.class);

    doReturn(OffsetDateTime.now()).when(this.clockUtils).getCurrentOffsetDateTime();

    this.now = this.clockUtils.getCurrentOffsetDateTime();
  }

  @Test
  void should_throw_illegal_argument_exception_when_dI_not_found() {
    final ShipmentWarehouse shipmentWarehouse = Instancio.of(ShipmentWarehouse.class).create();

    doReturn(Optional.empty()).when(this.distributionInnerRepository).findById(any());
    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);

    Assertions.assertThrows(IllegalArgumentException.class, () -> {
      this.sut.doHandle(command);
    });
  }

  @Test
  void should_act_properly_when_shipment_warehouse_from_shipment_created() {
    final ShipmentWarehouse shipmentWarehouse = ShipmentWarehouseMother.randomCreated();
    final DistributionInnerLine lineToAddTrackingCode = DistributionInnerLineMother.created()
        .id(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .build();
    final DistributionInnerLine randomLine = DistributionInnerLineMother.created()
        .build();
    final DistributionInner distributionInner = DistributionInnerMother.pending()
        .id(new Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerId().value())))
        .lines(new DistributionInnerLines(List.of(randomLine, lineToAddTrackingCode)))
        .build();
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository)
        .findById(distributionInner.getId());
    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);

    this.sut.doHandle(command);

    verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    verify(this.eventBus).send(this.eventCaptor.capture());
    final var updatedDistribution = this.distributionInnerCaptor.getValue();
    final var updatedLine = updatedDistribution.lines()
        .findLineById(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .orElseThrow();
    assertThat(updatedDistribution.lines().value()).hasSize(distributionInner.lines().value().size());
    assertThat(updatedLine.trackingCode().value()).isEqualTo(shipmentWarehouse.getTrackingCode().value());
    assertThat(updatedLine.distributedQuantity().value()).isEqualTo(NumericUtils.roundUpScale2(BigDecimal.ZERO));
    assertThat(updatedLine.distributionStartDate()).isNull();
    assertThat(updatedLine.distributionEndDate()).isNull();
    assertThat(updatedDistribution.distributedQuantity().value()).isEqualTo(NumericUtils.roundUpScale2(BigDecimal.ZERO));
    assertThat(updatedDistribution.status()).isEqualTo(DistributionInnerStatus.PENDING);
    assertThat(updatedDistribution.audit().updatedBy())
        .isEqualTo(UpdateDistributionInnerFromShipmentWarehouseCommandHandler.SHIPMENT_WAREHOUSE_EVENT);

    final var event = this.eventCaptor.getValue().stream().findFirst().orElseThrow();
    assertThat(((DistributionInnerUnifiedEvent) event).type()).isEqualTo(EventType.DISTRIBUTED);
  }

  @Test
  void should_act_properly_when_shipment_warehouse_from_shipment_created_for_closed_di() {
    final ShipmentWarehouse shipmentWarehouse = ShipmentWarehouseMother.randomCreated();
    final DistributionInnerLine lineToAddTrackingCode = DistributionInnerLineMother.created()
        .id(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .build();
    final DistributionInnerLine randomLine = DistributionInnerLineMother.created()
        .build();
    final DistributionInner distributionInner = DistributionInnerMother.closed()
        .id(new Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerId().value())))
        .lines(new DistributionInnerLines(List.of(randomLine, lineToAddTrackingCode)))
        .build();
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository)
        .findById(distributionInner.getId());
    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);

    this.sut.doHandle(command);

    verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    verify(this.eventBus).send(this.eventCaptor.capture());

    final var updatedDistribution = this.distributionInnerCaptor.getValue();

    final var updatedLine = updatedDistribution.lines()
        .findLineById(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .orElseThrow();

    assertThat(updatedDistribution.lines().value()).hasSize(distributionInner.lines().value().size());
    assertThat(updatedLine.trackingCode().value()).isEqualTo(shipmentWarehouse.getTrackingCode().value());
    assertThat(updatedLine.distributedQuantity().value()).isEqualTo(NumericUtils.roundUpScale2(BigDecimal.ZERO));
    assertThat(updatedLine.distributionStartDate()).isNull();
    assertThat(updatedLine.distributionEndDate()).isNull();
    assertThat(updatedDistribution.distributedQuantity().value()).isEqualTo(NumericUtils.roundUpScale2(BigDecimal.ZERO));
    assertThat(updatedDistribution.status()).isEqualTo(DistributionInnerStatus.CLOSED);
    assertThat(updatedDistribution.audit().updatedBy())
        .isEqualTo(UpdateDistributionInnerFromShipmentWarehouseCommandHandler.SHIPMENT_WAREHOUSE_EVENT);

    final var event = this.eventCaptor.getValue().stream().findFirst().orElseThrow();
    assertThat(((DistributionInnerUnifiedEvent) event).type()).isEqualTo(EventType.DISTRIBUTED);
  }

  @Test
  void should_act_properly_when_shipment_warehouse_from_shipment_created_for_nondistr_di() {
    final ShipmentWarehouse shipmentWarehouse = ShipmentWarehouseMother.randomCreated();
    final DistributionInnerLine lineToAddTrackingCode = DistributionInnerLineMother.created()
        .id(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .build();
    final DistributionInnerLine randomLine = DistributionInnerLineMother.created()
        .build();
    final DistributionInner distributionInner = DistributionInnerMother.nonDistributable()
        .id(new Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerId().value())))
        .lines(new DistributionInnerLines(List.of(randomLine, lineToAddTrackingCode)))
        .build();
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository)
        .findById(distributionInner.getId());
    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);

    this.sut.execute(command);

    verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    verify(this.eventBus).send(this.eventCaptor.capture());

    final var updatedDistribution = this.distributionInnerCaptor.getValue();
    final var updatedLine = updatedDistribution.lines()
        .findLineById(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .orElseThrow();
    assertThat(updatedDistribution.lines().value()).hasSize(distributionInner.lines().value().size());
    assertThat(updatedLine.trackingCode().value()).isEqualTo(shipmentWarehouse.getTrackingCode().value());
    assertThat(updatedLine.distributedQuantity().value()).isEqualTo(NumericUtils.roundUpScale2(BigDecimal.ZERO));
    assertThat(updatedLine.distributionStartDate()).isNull();
    assertThat(updatedLine.distributionEndDate()).isNull();
    assertThat(updatedDistribution.distributedQuantity().value()).isEqualTo(NumericUtils.roundUpScale2(BigDecimal.ZERO));
    assertThat(updatedDistribution.status()).isEqualTo(DistributionInnerStatus.NON_DISTRIBUTABLE);
    assertThat(updatedDistribution.audit().updatedBy())
        .isEqualTo(UpdateDistributionInnerFromShipmentWarehouseCommandHandler.SHIPMENT_WAREHOUSE_EVENT);
    final var event = this.eventCaptor.getValue().stream().findFirst().orElseThrow();
    assertThat(((DistributionInnerUnifiedEvent) event).type()).isEqualTo(EventType.DISTRIBUTED);
  }

  @Test
  void should_act_properly_when_shipment_warehouse_from_shipment_created_for_canceled_di() {
    final ShipmentWarehouse shipmentWarehouse = ShipmentWarehouseMother.randomCreated();
    final DistributionInnerLine lineToAddTrackingCode = DistributionInnerLineMother.created()
        .id(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .build();
    final DistributionInnerLine randomLine = DistributionInnerLineMother.created()
        .build();
    final DistributionInner distributionInner = DistributionInnerMother.canceled()
        .id(new Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerId().value())))
        .lines(new DistributionInnerLines(List.of(randomLine, lineToAddTrackingCode)))
        .build();
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository)
        .findById(distributionInner.getId());

    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);

    this.sut.execute(command);

    verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    verify(this.eventBus).send(this.eventCaptor.capture());
    final var updatedDistribution = this.distributionInnerCaptor.getValue();
    final var updatedLine = updatedDistribution.lines()
        .findLineById(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .orElseThrow();
    assertThat(updatedDistribution.lines().value()).hasSize(distributionInner.lines().value().size());
    assertThat(updatedLine.trackingCode().value()).isEqualTo(shipmentWarehouse.getTrackingCode().value());
    assertThat(updatedLine.distributedQuantity().value()).isEqualTo(NumericUtils.roundUpScale2(BigDecimal.ZERO));
    assertThat(updatedLine.distributionStartDate()).isNull();
    assertThat(updatedLine.distributionEndDate()).isNull();
    assertThat(updatedDistribution.distributedQuantity().value()).isEqualTo(NumericUtils.roundUpScale2(BigDecimal.ZERO));
    assertThat(updatedDistribution.status()).isEqualTo(DistributionInnerStatus.CANCELED);
    assertThat(updatedDistribution.audit().updatedBy())
        .isEqualTo(UpdateDistributionInnerFromShipmentWarehouseCommandHandler.SHIPMENT_WAREHOUSE_EVENT);

    final var event = this.eventCaptor.getValue().stream().findFirst().orElseThrow();
    assertThat(((DistributionInnerUnifiedEvent) event).type()).isEqualTo(EventType.DISTRIBUTED);
  }

  @Test
  void should_act_properly_when_shipment_warehouse_from_shipment_updated_from_stock() {
    final ShipmentWarehouse shipmentWarehouse = ShipmentWarehouseMother.with(
        new ShipmentWarehouseQuantity(BigDecimal.valueOf(5000.00)));
    final DistributionInnerLine lineToEdit = DistributionInnerLineMother.created()
        .id(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .trackingCode(new TrackingCode(shipmentWarehouse.getTrackingCode().value()))
        .build();
    final DistributionInnerLine randomLine = DistributionInnerLineMother.distributed().build();
    final DistributionInner distributionInner = DistributionInnerMother.pending()
        .id(new Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerId().value())))
        .lines(new DistributionInnerLines(List.of(randomLine, lineToEdit))).build();
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository)
        .findById(distributionInner.getId());
    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);

    this.sut.execute(command);

    verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    verify(this.eventBus).send(this.eventCaptor.capture());
    final var updatedDistribution = this.distributionInnerCaptor.getValue();
    final var updatedLine = updatedDistribution.lines()
        .findLineById(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .orElseThrow();
    assertThat(updatedDistribution.lines().value()).hasSize(distributionInner.lines().value().size());
    assertThat(updatedLine.distributedQuantity().value()).isEqualTo(shipmentWarehouse.getQuantity().value());
    assertThat(updatedLine.trackingCode().value()).isEqualTo(shipmentWarehouse.getTrackingCode().value());
    assertThat(updatedLine.distributionStartDate().value()).isEqualTo(shipmentWarehouse.getStartDate().value());
    assertThat(updatedLine.distributionEndDate()).isNull();
    assertThat(updatedDistribution.distributedQuantity().value())
        .isEqualTo(shipmentWarehouse.getQuantity().value().add(randomLine.distributedQuantity().value()));
    assertThat(updatedDistribution.status()).isEqualTo(DistributionInnerStatus.IN_PROGRESS);
    assertThat(updatedDistribution.audit().updatedBy())
        .isEqualTo(UpdateDistributionInnerFromShipmentWarehouseCommandHandler.SHIPMENT_WAREHOUSE_EVENT);

    final var event = this.eventCaptor.getValue().stream().findFirst().orElseThrow();
    assertThat(((DistributionInnerUnifiedEvent) event).type()).isEqualTo(EventType.DISTRIBUTED);
  }

  @Test
  void should_act_properly_when_shipment_warehouse_from_shipment_started() {
    final ShipmentWarehouse shipmentWarehouse = ShipmentWarehouseMother.with(
        new ShipmentWarehouseTimestamp(OffsetDateTime.now()),
        new ShipmentWarehouseQuantity(BigDecimal.ZERO));
    final DistributionInnerLine lineToEdit = DistributionInnerLineMother.created()
        .id(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .trackingCode(new TrackingCode(shipmentWarehouse.getTrackingCode().value()))
        .build();
    final DistributionInnerLine randomLine = DistributionInnerLineMother.created().build();
    final DistributionInner distributionInner = DistributionInnerMother.pending()
        .id(new Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerId().value())))
        .lines(new DistributionInnerLines(List.of(randomLine, lineToEdit))).build();
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository)
        .findById(distributionInner.getId());
    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);

    this.sut.execute(command);

    verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    verify(this.eventBus).send(this.eventCaptor.capture());
    final var updatedDistribution = this.distributionInnerCaptor.getValue();
    final var updatedLine = updatedDistribution.lines()
        .findLineById(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .orElseThrow();
    assertThat(updatedDistribution.lines().value()).hasSize(distributionInner.lines().value().size());
    assertThat(updatedLine.trackingCode().value()).isEqualTo(shipmentWarehouse.getTrackingCode().value());
    assertThat(updatedLine.distributionStartDate().value()).isEqualTo(shipmentWarehouse.getStartDate().value());
    assertThat(updatedLine.distributionEndDate()).isNull();
    assertThat(updatedDistribution.distributedQuantity().value()).isEqualTo(shipmentWarehouse.getQuantity().value());
    assertThat(updatedDistribution.status()).isEqualTo(DistributionInnerStatus.IN_PROGRESS);
    assertThat(updatedDistribution.audit().updatedBy())
        .isEqualTo(UpdateDistributionInnerFromShipmentWarehouseCommandHandler.SHIPMENT_WAREHOUSE_EVENT);

    final var event = this.eventCaptor.getValue().stream().findFirst().orElseThrow();
    assertThat(((DistributionInnerUnifiedEvent) event).type()).isEqualTo(EventType.DISTRIBUTED);
  }

  @Test
  void should_not_change_status_when_shipment_warehouse_from_shipment_updated_but_date_not_received() {
    final ShipmentWarehouse shipmentWarehouse = ShipmentWarehouseMother.with(
        null,
        new ShipmentWarehouseQuantity(BigDecimal.TEN));
    final DistributionInnerLine lineToEdit = DistributionInnerLineMother.created()
        .id(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .trackingCode(new TrackingCode(shipmentWarehouse.getTrackingCode().value()))
        .build();
    final DistributionInnerLine randomLine = DistributionInnerLineMother.created().build();
    final DistributionInner distributionInner = DistributionInnerMother.pending()
        .id(new Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerId().value())))
        .lines(new DistributionInnerLines(List.of(randomLine, lineToEdit))).build();
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository)
        .findById(distributionInner.getId());
    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);

    this.sut.execute(command);

    verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    verify(this.eventBus).send(this.eventCaptor.capture());
    final var updatedDistribution = this.distributionInnerCaptor.getValue();
    final var updatedLine = updatedDistribution.lines()
        .findLineById(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .orElseThrow();
    assertThat(updatedDistribution.lines().value()).hasSize(distributionInner.lines().value().size());
    assertThat(updatedLine.trackingCode().value()).isEqualTo(shipmentWarehouse.getTrackingCode().value());
    assertThat(updatedLine.distributionStartDate()).isNull();
    assertThat(updatedLine.distributionEndDate()).isNull();
    assertThat(updatedDistribution.distributedQuantity().value()).isEqualTo(shipmentWarehouse.getQuantity().value());
    assertThat(updatedDistribution.status()).isEqualTo(DistributionInnerStatus.PENDING);
    assertThat(updatedDistribution.audit().updatedBy())
        .isEqualTo(UpdateDistributionInnerFromShipmentWarehouseCommandHandler.SHIPMENT_WAREHOUSE_EVENT);

    final var event = this.eventCaptor.getValue().stream().findFirst().orElseThrow();
    assertThat(((DistributionInnerUnifiedEvent) event).type()).isEqualTo(EventType.DISTRIBUTED);
  }

  @Test
  void should_act_properly_when_shipment_warehouse_from_shipment_updated_from_stock_when_there_is_already_a_distributed_line() {
    final ShipmentWarehouse shipmentWarehouse = ShipmentWarehouseMother.with(
        new ShipmentWarehouseQuantity(BigDecimal.valueOf(5000.00)));
    final DistributionInnerLine lineToEdit = DistributionInnerLineMother.created()
        .id(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .trackingCode(new TrackingCode(shipmentWarehouse.getTrackingCode().value()))
        .build();
    final DistributionInnerLine randomLine = DistributionInnerLineMother.distributed().build();
    final DistributionInner distributionInner = DistributionInnerMother.pending()
        .id(new Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerId().value())))
        .lines(new DistributionInnerLines(List.of(randomLine, lineToEdit)))
        .build();

    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository)
        .findById(distributionInner.getId());

    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);

    this.sut.execute(command);

    verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    verify(this.eventBus).send(this.eventCaptor.capture());
    final var updatedDistribution = this.distributionInnerCaptor.getValue();
    final var updatedLine = updatedDistribution.lines()
        .findLineById(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .orElseThrow();
    assertThat(updatedDistribution.lines().value()).hasSize(distributionInner.lines().value().size());
    assertThat(updatedLine.distributedQuantity().value()).isEqualTo(shipmentWarehouse.getQuantity().value());
    assertThat(updatedLine.trackingCode().value()).isEqualTo(shipmentWarehouse.getTrackingCode().value());
    assertThat(updatedLine.distributionStartDate().value()).isEqualTo(shipmentWarehouse.getStartDate().value());
    assertThat(updatedLine.distributionEndDate()).isNull();
    assertThat(updatedDistribution.distributedQuantity().value())
        .isEqualTo(shipmentWarehouse.getQuantity().value().add(randomLine.distributedQuantity().value()));
    assertThat(updatedDistribution.status()).isEqualTo(DistributionInnerStatus.IN_PROGRESS);
    assertThat(updatedDistribution.audit().updatedBy())
        .isEqualTo(UpdateDistributionInnerFromShipmentWarehouseCommandHandler.SHIPMENT_WAREHOUSE_EVENT);
    final var event = this.eventCaptor.getValue().stream().findFirst().orElseThrow();
    assertThat(((DistributionInnerUnifiedEvent) event).type()).isEqualTo(EventType.DISTRIBUTED);
  }

  @Test
  void should_act_properly_when_shipment_warehouse_from_shipment_updated_and_no_changes() {
    final ShipmentWarehouse shipmentWarehouse = ShipmentWarehouseMother.with(
        new ShipmentWarehouseQuantity(BigDecimal.valueOf(5000.00)));
    final DistributionInnerLine lineWithTrackingCode = DistributionInnerLineMother.distributed()
        .id(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .distributedQuantity(new DistributedQuantity(shipmentWarehouse.getQuantity().value()))
        .distributionStartDate(new DistributionStartDate(shipmentWarehouse.getStartDate().value()))
        .distributionEndDate(null)
        .audit(BasicAudit.create(this.now))
        .trackingCode(new TrackingCode(shipmentWarehouse.getTrackingCode().value()))
        .build();
    final DistributionInnerLine randomLine = DistributionInnerLineMother.distributed().build();
    final DistributionInner distributionInner = DistributionInnerMother.inProgress()
        .id(new Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerId().value())))
        .lines(new DistributionInnerLines(List.of(randomLine, lineWithTrackingCode)))
        .distributedQuantity(new DistributionInner.DistributedQuantity(
            lineWithTrackingCode.distributedQuantity().value().add(randomLine.distributedQuantity().value())))
        .build();
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository)
        .findById(distributionInner.getId());
    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);

    this.sut.execute(command);

    verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    verify(this.eventBus).send(List.of());
    final var updatedDistribution = this.distributionInnerCaptor.getValue();
    final var notUpdatedLine = updatedDistribution.lines()
        .findLineById(new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())))
        .orElseThrow();
    assertThat(updatedDistribution.lines().value()).hasSize(distributionInner.lines().value().size());
    assertThat(notUpdatedLine.trackingCode().value()).isEqualTo(shipmentWarehouse.getTrackingCode().value());
    assertThat(notUpdatedLine.distributedQuantity().value())
        .isEqualTo(shipmentWarehouse.getQuantity().value());
    assertThat(notUpdatedLine.distributionStartDate().value()).isEqualTo(shipmentWarehouse.getStartDate().value());
    assertThat(notUpdatedLine.distributionEndDate()).isNull();
    final var expectedDistributedQuantity =
        lineWithTrackingCode.distributedQuantity().value().add(randomLine.distributedQuantity().value());
    assertThat(updatedDistribution.distributedQuantity().value()).isEqualTo(expectedDistributedQuantity);
    assertThat(updatedDistribution.status()).isEqualTo(DistributionInnerStatus.IN_PROGRESS);
    assertThat(updatedDistribution.audit().updatedBy()).isEqualTo("CREATED_BY");
  }

  @Test
  void should_modify_the_DI_state_to_sent_when_ProcessCompleted_event_and_all_lines_with_distributionEndDate() {
    final var inProgressLine = DistributionInnerLineMother.inProgress().build();
    final var sentLine = DistributionInnerLineMother.sent().build();
    final var distributionInner = DistributionInnerMother.inProgress()
        .lines(new DistributionInnerLines(List.of(inProgressLine, sentLine)))
        .build();
    final var shipmentWarehouse = ShipmentWarehouseMother.with(
        new ShipmentWarehouseInner(
            new ShipmentWarehouseInnerId(distributionInner.getId().value().toString()),
            new ShipmentWarehouseInnerLineId(inProgressLine.id().value().toString())),
        new ShipmentWarehouseTrackingCode(inProgressLine.trackingCode().value()),
        new ShipmentWarehouseTimestamp(OffsetDateTime.now()));
    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository)
        .findById(distributionInner.getId());

    this.sut.execute(command);

    verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    verify(this.eventBus).send(this.eventCaptor.capture());
    final var updatedDistribution = this.distributionInnerCaptor.getValue();
    assertThat(updatedDistribution.status()).isEqualTo(DistributionInnerStatus.SENT);
    final var sentLines = updatedDistribution.lines().value().stream()
        .filter(line -> line.distributionEndDate() != null)
        .toList();
    assertThat(sentLines).hasSize(2);
  }

  @Test
  void should_not_modify_the_DI_state_when_ProcessCompleted_event_and_not_all_lines_with_distributionEndDate() {
    final var inProgressLine1 = DistributionInnerLineMother.inProgress().build();
    final var inProgressLine2 = DistributionInnerLineMother.inProgress().build();
    final var distributionInner = DistributionInnerMother.inProgress()
        .lines(new DistributionInnerLines(List.of(inProgressLine1, inProgressLine2)))
        .build();
    final var shipmentWarehouse = ShipmentWarehouseMother.with(
        new ShipmentWarehouseInner(
            new ShipmentWarehouseInnerId(distributionInner.getId().value().toString()),
            new ShipmentWarehouseInnerLineId(inProgressLine1.id().value().toString())),
        new ShipmentWarehouseTrackingCode(inProgressLine1.trackingCode().value()),
        new ShipmentWarehouseTimestamp(OffsetDateTime.now()));
    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository)
        .findById(distributionInner.getId());

    this.sut.execute(command);

    verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    verify(this.eventBus).send(this.eventCaptor.capture());
    final var updatedDistribution = this.distributionInnerCaptor.getValue();
    assertThat(updatedDistribution.status()).isEqualTo(DistributionInnerStatus.IN_PROGRESS);
    final var sentLines = updatedDistribution.lines().value().stream()
        .filter(line -> line.distributionEndDate() != null)
        .toList();
    assertThat(sentLines).hasSize(1);
  }

  @ParameterizedTest
  @EnumSource(value = DistributionInnerStatus.class, names = {"CANCELED", "CLOSED",})
  void should_maintain_state_cancel_update_end_date(final DistributionInnerStatus status) {
    final var inProgressLine = DistributionInnerLineMother.inProgress().build();
    final var distributionInner = DistributionInnerMother.inProgress().status(status)
        .lines(new DistributionInnerLines(List.of(inProgressLine)))
        .build();
    final var shipmentWarehouse = ShipmentWarehouseMother.with(
        new ShipmentWarehouseInner(
            new ShipmentWarehouseInnerId(distributionInner.getId().value().toString()),
            new ShipmentWarehouseInnerLineId(inProgressLine.id().value().toString())),
        new ShipmentWarehouseTrackingCode(inProgressLine.trackingCode().value()),
        new ShipmentWarehouseTimestamp(OffsetDateTime.now()));
    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository)
        .findById(distributionInner.getId());

    this.sut.execute(command);

    verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    verify(this.eventBus).send(this.eventCaptor.capture());
    final var updatedDistribution = this.distributionInnerCaptor.getValue();
    assertThat(updatedDistribution.status()).isEqualTo(status);
    final var sentLines = updatedDistribution.lines().value().stream()
        .filter(line -> line.distributionEndDate() != null)
        .toList();
    assertThat(sentLines).hasSize(1);
    this.logCaptor
        .verifyMessagesContaining(
            String.format("Updating distribution end date for di %s whose status is %s", distributionInner.getId().value(),
                status.value()))
        .withLevel(Level.WARN)
        .areExactly(1);
  }

  @ParameterizedTest
  @EnumSource(value = DistributionInnerStatus.class, names = {"PENDING", "NON_DISTRIBUTABLE"})
  void should_maintain_state_pending_cancel_update_end_date(final DistributionInnerStatus status) {
    final var createdLine = DistributionInnerLineMother.created().trackingCode(new TrackingCode(UUID.randomUUID().toString())).build();
    final var distributionInner = DistributionInnerMother.pending().status(status)
        .lines(new DistributionInnerLines(List.of(createdLine)))
        .build();
    final var shipmentWarehouse = ShipmentWarehouseMother.with(
        new ShipmentWarehouseInner(
            new ShipmentWarehouseInnerId(distributionInner.getId().value().toString()),
            new ShipmentWarehouseInnerLineId(createdLine.id().value().toString())),
        new ShipmentWarehouseTrackingCode(createdLine.trackingCode().value()),
        null,
        new ShipmentWarehouseTimestamp(OffsetDateTime.now()));
    final var command = new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouse);
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository)
        .findById(distributionInner.getId());

    this.sut.execute(command);

    verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    verify(this.eventBus).send(this.eventCaptor.capture());
    final var updatedDistribution = this.distributionInnerCaptor.getValue();
    assertThat(updatedDistribution.status()).isEqualTo(status);
    final var sentLines = updatedDistribution.lines().value().stream()
        .filter(line -> line.distributionEndDate() != null)
        .toList();
    assertThat(sentLines).hasSize(1);
    this.logCaptor
        .verifyMessagesContaining(
            String.format("Updating distribution end date for di %s whose status is %s", distributionInner.getId().value(),
                status.value()))
        .withLevel(Level.WARN)
        .areExactly(1);
  }
}
