package com.inditex.icdmdemg.application.use.command;

import static java.util.Objects.isNull;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.application.use.command.UseRequestNames.UseRequestName;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.Use.Id;
import com.inditex.icdmdemg.domain.use.UseRepository;
import com.inditex.icdmdemg.domain.use.UseUnifiedEvent;
import com.inditex.icdmdemg.domain.use.UseUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.use.entity.AssignableType;
import com.inditex.icdmdemg.domain.use.entity.UseName;
import com.inditex.icdmdemg.domain.use.entity.UseName.Lang;
import com.inditex.icdmdemg.domain.use.entity.UseName.Name;
import com.inditex.icdmdemg.domain.use.entity.UseNames;
import com.inditex.icdmdemg.domain.use.mother.UseMother;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.LocaleUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PatchUseCommandHandlerTest {

  @Mock
  private UseRepository useRepository;

  @Mock
  private ClockUtils clockUtils;

  @Mock
  private UuidGenerator uuidGenerator;

  @Mock
  private EventBus eventBus;

  @Spy
  private Transaction transaction = new Transaction();

  @InjectMocks
  private PatchUseCommandHandler sut;

  @Test
  void should_not_update_when_use_not_found() {
    final var command = new PatchUseCommand(UUID.randomUUID(), new UseRequestNames(List.of()), List.of(), "admin");

    doReturn(Optional.empty()).when(this.useRepository).findById(new Id(command.useId()));

    final Throwable result = catchThrowable(() -> this.sut.doHandle(command));

    assertThat(result).isInstanceOf(ErrorException.class);
  }

  @Test
  void should_not_update_when_use_exist_but_not_valid_assignable_value() {
    final var command = new PatchUseCommand(
        UUID.randomUUID(),
        new UseRequestNames(List.of(
            new UseRequestName(LocaleUtils.createLocale("es"), "descripcion es"),
            new UseRequestName(LocaleUtils.createLocale("en"), "description en"))),
        List.of("NO_VALID"),
        "admin");

    final var use = UseMother.generateBuild().build();

    doReturn(Optional.of(use)).when(this.useRepository).findById(new Id(command.useId()));

    final Throwable result = catchThrowable(() -> this.sut.doHandle(command));

    assertThat(result).isInstanceOf(ErrorException.class);
  }

  @ParameterizedTest
  @MethodSource("provideInvalidNamesTestData")
  void should_not_update_when_use_exist_but_not_valid_names(final List<UseRequestName> names) {
    final var use = UseMother.generateBuild().build();

    final var command = new PatchUseCommand(UUID.randomUUID(),
        new UseRequestNames(names), isNull(use.assignable()) ? List.of() : use.assignable().getStringList(),
        use.audit().updatedBy());

    doReturn(Optional.of(use)).when(this.useRepository).findById(new Id(command.useId()));
    doReturn(OffsetDateTime.now()).when(this.clockUtils).getCurrentOffsetDateTime();

    final Throwable result = catchThrowable(() -> this.sut.doHandle(command));

    assertThat(result).isInstanceOf(ErrorException.class);
  }

  @Test
  void should_not_update_when_no_modify_assignable() {

    final OffsetDateTime now = OffsetDateTime.now();

    final var use = UseMother.withAllArgumentsOnlyMandatoryNames(now);

    final List<UseRequestName> names = use.names().value().stream()
        .map(useName -> new UseRequestName(
            LocaleUtils.createLocale(useName.lang().value()),
            useName.name().value()))
        .toList();

    final var command = new PatchUseCommand(UUID.randomUUID(),
        new UseRequestNames(names),
        isNull(use.assignable()) ? List.of() : use.assignable().getStringList(),
        use.audit().updatedBy());

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(use)).when(this.useRepository).findById(new Id(command.useId()));

    this.sut.doHandle(command);

    verifyNoInteractions(this.uuidGenerator);
    verify(this.useRepository).save(use);
    verify(this.eventBus).send(List.of());
  }

  @Test
  void should_update_use_when_new_name() {

    final OffsetDateTime now = OffsetDateTime.now();

    final var use = UseMother.generateBuild().build();

    final var command = new PatchUseCommand(UUID.randomUUID(), new UseRequestNames(List.of(
        new UseRequestName(LocaleUtils.createLocale("es"), "descripcion uso"),
        new UseRequestName(LocaleUtils.createLocale("pt"), "descripcio do usinho"),
        new UseRequestName(LocaleUtils.createLocale("en"), "use description"))),
        isNull(use.assignable()) ? List.of() : use.assignable().getStringList(),
        use.audit().updatedBy());

    final var useUpdated = UseMother.generateBuild()
        .id(use.getId())
        .assignable(use.assignable())
        .useNames(new UseNames(List.of(new UseName(new Name("descripcion uso"), new Lang("es"), new BasicAudit(now, now)),
            new UseName(new Name("descripcio do usinho"), new Lang("pt"), new BasicAudit(now, now)),
            new UseName(new Name("use description"), new Lang("en"), new BasicAudit(now, now)))))
        .taxonomy(use.taxonomy())
        .purchaseType(use.purchaseType())
        .customer(use.customer())
        .conditions(use.conditions())
        .audit(
            new CompleteAudit(use.audit().createdBy(), use.audit().createdAt(), use.audit().updatedBy(), now, null, use.audit().version()))
        .build();

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(use)).when(this.useRepository).findById(new Id(command.useId()));

    this.sut.doHandle(command);

    verifyNoInteractions(this.uuidGenerator);
    verify(this.eventBus).send(List.of(new UseUnifiedEvent(useUpdated, EventType.UPDATE_TRANSLATIONS)));
    verify(this.useRepository).save(useUpdated);

  }

  @Test
  void should_update_use_when_new_name_with_region() {

    final OffsetDateTime now = OffsetDateTime.now();

    final var use = UseMother.withAllArgumentsAndTreeNamesWithRegion(now);

    final var command = new PatchUseCommand(UUID.randomUUID(), new UseRequestNames(List.of(
        new UseRequestName(LocaleUtils.createLocale("es"), "descripcion uso"),
        new UseRequestName(LocaleUtils.createLocale("pt-PT"), "descripcio do usinho"),
        new UseRequestName(LocaleUtils.createLocale("en"), "use description"))),
        isNull(use.assignable()) ? List.of() : use.assignable().getStringList(),
        use.audit().updatedBy());

    final var useUpdated = new Use(
        use.getId(),
        use.assignable(),
        use.taxonomy(),
        use.customer(),
        use.purchaseType(),
        new UseNames(List.of(new UseName(new Name("descripcion uso"), new Lang("es"), new BasicAudit(now, now)),
            new UseName(new Name("descripcio do usinho"), new Lang("pt"), new BasicAudit(now, now)),
            new UseName(new Name("use description"), new Lang("en"), new BasicAudit(now, now)))),
        use.conditions(),
        new CompleteAudit(use.audit().createdBy(), use.audit().createdAt(), use.audit().updatedBy(), now, null, use.audit().version()));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(use)).when(this.useRepository).findById(new Id(command.useId()));

    this.sut.doHandle(command);

    verifyNoInteractions(this.uuidGenerator);
    verify(this.eventBus).send(List.of(new UseUnifiedEvent(useUpdated, EventType.UPDATE_TRANSLATIONS)));
    verify(this.useRepository).save(useUpdated);

  }

  @Test
  void should_update_use_when_delete_no_mandatory_translations() {

    final OffsetDateTime now = OffsetDateTime.now();

    final var use = UseMother.withAllArgumentsAndTreeNamesAndTwoConditions(now);

    final var command = new PatchUseCommand(UUID.randomUUID(), new UseRequestNames(List.of(
        new UseRequestName(LocaleUtils.createLocale("es"), "descripcion uso"),
        new UseRequestName(LocaleUtils.createLocale("en"), "use description"))),
        isNull(use.assignable()) ? List.of() : use.assignable().getStringList(),
        use.audit().updatedBy());

    final var useUpdated = new Use(
        use.getId(),
        use.assignable(),
        use.taxonomy(),
        use.customer(),
        use.purchaseType(),
        new UseNames(List.of(new UseName(new Name("descripcion uso"), new Lang("es"), new BasicAudit(now, now)),
            new UseName(new Name("use description"), new Lang("en"), new BasicAudit(now, now)))),
        use.conditions(),
        new CompleteAudit(use.audit().createdBy(), use.audit().createdAt(), use.audit().updatedBy(), now, null, use.audit().version()));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(use)).when(this.useRepository).findById(new Id(command.useId()));

    this.sut.doHandle(command);

    verifyNoInteractions(this.uuidGenerator);
    verify(this.eventBus).send(List.of(new UseUnifiedEvent(useUpdated, EventType.UPDATE_TRANSLATIONS)));
    verify(this.useRepository).save(useUpdated);
  }

  @Test
  void should_update_use_when_same_names_different_assignable() {
    final OffsetDateTime now = OffsetDateTime.now();
    final var use = UseMother.withAllArgumentsOnlyMandatoryNames(now);

    final var command = new PatchUseCommand(UUID.randomUUID(), new UseRequestNames(List.of(
        new UseRequestName(LocaleUtils.createLocale("es"), "descripcion uso"),
        new UseRequestName(LocaleUtils.createLocale("en"), "use description"))),
        List.of(AssignableType.INNER.name()),
        use.audit().updatedBy());

    final var useUpdated = UseMother.generateBuild()
        .id(use.getId())
        .assignable(AssignableType.INNER)
        .useNames(use.names())
        .taxonomy(use.taxonomy())
        .purchaseType(use.purchaseType())
        .customer(use.customer())
        .conditions(use.conditions())
        .audit(
            new CompleteAudit(use.audit().createdBy(), use.audit().createdAt(), use.audit().updatedBy(), now, null, use.audit().version()))
        .build();

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(use)).when(this.useRepository).findById(new Id(command.useId()));

    this.sut.doHandle(command);

    verifyNoInteractions(this.uuidGenerator);
    verify(this.eventBus).send(List.of(new UseUnifiedEvent(useUpdated, EventType.UPDATE_ASSIGNABLE)));
    verify(this.useRepository).save(useUpdated);
  }

  private static Stream<Arguments> provideInvalidNamesTestData() {
    return Stream.of(
        Arguments.of(List.of(new UseRequestName(LocaleUtils.createLocale("es"), "descripcion es"))),
        Arguments.of(List.of(new UseRequestName(LocaleUtils.createLocale("en"), "description en"))),
        Arguments.of(
            List.of(new UseRequestName(LocaleUtils.createLocale("es"), "descripcion es"),
                new UseRequestName(LocaleUtils.createLocale("pt"), "descricao pt"))));
  }
}
