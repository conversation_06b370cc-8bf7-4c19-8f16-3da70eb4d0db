package com.inditex.icdmdemg.application.distributionnominated.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.inditex.icdmdemg.application.product.service.ProductAlternativeProvider;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseMaterialReferenceId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseUseId;
import com.inditex.icdmdemg.domain.product.Product;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AlternativeSharedRawMaterialProviderTest {

  @Mock
  private ProductAlternativeProvider productAlternativeProvider;

  @InjectMocks
  private AlternativeSharedRawMaterialProvider sut;

  @Test
  void should_provide_alternatives_for_one() {
    final var srm1 = MaterialCommitmentUseSharedRawMaterial.of(
        new MaterialCommitmentUseUseId(UuidMother.fromInteger(101).toString()),
        new MaterialCommitmentUseMaterialReferenceId(UuidMother.fromInteger(201).toString()),
        new MaterialCommitmentUseBudgetId(UuidMother.fromInteger(301).toString()));

    doReturn(Map.of(
        new ProductReferenceId(UuidMother.fromInteger(201).toString()), Optional.of(this.product(211))))
            .when(this.productAlternativeProvider)
            .alternativeProductReferences(List.of(
                new ProductReferenceId(UuidMother.fromInteger(201).toString())));

    final var actual = this.sut.alternatives(srm1);

    assertThat(actual).containsExactlyInAnyOrderElementsOf(List.of(
        srm1,
        MaterialCommitmentUseSharedRawMaterial.of(
            new MaterialCommitmentUseUseId(UuidMother.fromInteger(101).toString()),
            new MaterialCommitmentUseMaterialReferenceId(UuidMother.fromInteger(211).toString()),
            new MaterialCommitmentUseBudgetId(UuidMother.fromInteger(301).toString()))));
  }

  @Test
  void should_provide_alternatives() {
    final var srm1 = MaterialCommitmentUseSharedRawMaterial.of(
        new MaterialCommitmentUseUseId(UuidMother.fromInteger(101).toString()),
        new MaterialCommitmentUseMaterialReferenceId(UuidMother.fromInteger(201).toString()),
        new MaterialCommitmentUseBudgetId(UuidMother.fromInteger(301).toString()));
    final var srm2 = MaterialCommitmentUseSharedRawMaterial.of(
        new MaterialCommitmentUseUseId(UuidMother.fromInteger(102).toString()),
        new MaterialCommitmentUseMaterialReferenceId(UuidMother.fromInteger(202).toString()),
        new MaterialCommitmentUseBudgetId(UuidMother.fromInteger(302).toString()));

    doReturn(Map.of(
        new ProductReferenceId(UuidMother.fromInteger(201).toString()), Optional.of(this.product(211)),
        new ProductReferenceId(UuidMother.fromInteger(202).toString()), Optional.of(this.product(212))))
            .when(this.productAlternativeProvider)
            .alternativeProductReferences(List.of(
                new ProductReferenceId(UuidMother.fromInteger(201).toString()),
                new ProductReferenceId(UuidMother.fromInteger(202).toString())));

    final var actual = this.sut.alternatives(List.of(srm1, srm2));

    assertThat(actual).containsExactlyInAnyOrderElementsOf(List.of(
        srm1,
        srm2,
        MaterialCommitmentUseSharedRawMaterial.of(
            new MaterialCommitmentUseUseId(UuidMother.fromInteger(101).toString()),
            new MaterialCommitmentUseMaterialReferenceId(UuidMother.fromInteger(211).toString()),
            new MaterialCommitmentUseBudgetId(UuidMother.fromInteger(301).toString())),
        MaterialCommitmentUseSharedRawMaterial.of(
            new MaterialCommitmentUseUseId(UuidMother.fromInteger(102).toString()),
            new MaterialCommitmentUseMaterialReferenceId(UuidMother.fromInteger(212).toString()),
            new MaterialCommitmentUseBudgetId(UuidMother.fromInteger(302).toString()))));
  }

  public Product product(Integer referenceId) {
    final var product = mock(Product.class);
    doReturn(new ProductReferenceId(UuidMother.fromInteger(referenceId).toString())).when(product).getReferenceId();
    return product;
  }

}
