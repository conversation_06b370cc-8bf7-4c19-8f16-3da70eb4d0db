package com.inditex.icdmdemg.application.distributioninner.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

import java.time.Clock;
import java.time.ZoneId;
import java.util.Collection;
import java.util.List;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = DeleteDistributionInnerStatusCommandHandlerTest.Config.class)
class DeleteDistributionInnerStatusCommandHandlerTest {
  @MockitoBean
  private DistributionInnerRepository distributionInnerRepository;

  @MockitoBean
  private EventBus eventBus;

  @Autowired
  private ClockUtils clockUtils;

  @Captor
  ArgumentCaptor<DistributionInner> saveCaptor;

  @Captor
  ArgumentCaptor<Collection<DomainEvent<?>>> eventsCaptor;

  @Autowired
  private DeleteDistributionInnerStatusCommandHandler sut;

  @ParameterizedTest
  @MethodSource("provideStatusCases")
  void should_update_when_delete_order_and_state_di(final DistributionInnerStatus currentStatus,
      final DistributionInnerStatus expectedStatus, final String expectedEventType) {
    final var orderId = UuidMother.fromInteger(101);

    final var now = OffsetDateTimeMother.fromInteger(2);
    this.clockUtils.setClock(Clock.fixed(now.toInstant(), ZoneId.of("UTC")));

    final var productOrderId = new ProductOrderId(orderId);

    final var distributionInner = DistributionInnerMother.withStatus(currentStatus)
        .productOrderId(productOrderId)
        .build();

    doReturn(List.of(distributionInner)).when(this.distributionInnerRepository).findByProductOrderId(productOrderId);

    this.sut.execute(new DeleteDistributionInnerStatusCommand(orderId.toString(), "test"));

    verify(this.distributionInnerRepository).save(this.saveCaptor.capture());
    verify(this.eventBus).send(this.eventsCaptor.capture());

    assertThat(this.saveCaptor.getValue()).extracting(DistributionInner::status).isEqualTo(expectedStatus);
    assertThat(this.saveCaptor.getValue()).extracting(DistributionInner::audit).extracting(CompleteAudit::updatedAt)
        .isEqualTo(now);
    assertThat(this.saveCaptor.getValue()).extracting(DistributionInner::audit).extracting(CompleteAudit::updatedBy)
        .isEqualTo("test");
    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(domainEvent -> domainEvent instanceof DistributionInnerUnifiedEvent)
        .allMatch(domainEvent -> domainEvent.eventType().equals(expectedEventType));
  }

  public Stream<Arguments> provideStatusCases() {
    return Stream.of(
        Arguments.of(DistributionInnerStatus.IN_PROGRESS, DistributionInnerStatus.CLOSED, EventType.CLOSED.value),
        Arguments.of(DistributionInnerStatus.SENT, DistributionInnerStatus.CLOSED, EventType.CLOSED.value),
        Arguments.of(DistributionInnerStatus.PENDING, DistributionInnerStatus.PENDING, EventType.DELETED.value),
        Arguments.of(DistributionInnerStatus.NON_DISTRIBUTABLE, DistributionInnerStatus.NON_DISTRIBUTABLE,
            EventType.DELETED.value));
  }

  @Configuration
  @Import({ClockUtils.class, Transaction.class, DeleteDistributionInnerStatusCommandHandler.class})
  public static class Config {
  }
}
