package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import static org.instancio.Select.field;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.util.Optional;

import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseStartedCommand.ShipmentWarehouseStarted;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouseRepository;
import com.inditex.icdmdemg.domain.shipmentwarehouse.mother.ShipmentWarehouseMother;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;

import org.assertj.core.api.Assertions;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class ProjectShipmentWarehouseStartedCommandHandlerTest {

  ArgumentCaptor<ShipmentWarehouse> shipmentWarehouseCaptor = ArgumentCaptor.forClass(ShipmentWarehouse.class);

  ClockUtils clockUtils = mock();

  private final ShipmentWarehouseRepository shipmentWarehouseRepository = mock();

  private final Transaction transaction = spy();

  private final ProjectShipmentWarehouseStartedCommandHandler sut = new ProjectShipmentWarehouseStartedCommandHandler(
      this.shipmentWarehouseRepository, this.clockUtils, this.transaction);

  @Test
  void should_project_shipment_warehouse_started() {
    final var now = OffsetDateTime.now();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    final ShipmentWarehouse shipmentWarehouse = ShipmentWarehouseMother.randomCreated();
    final var shipmentWarehouseCopy = ShipmentWarehouseMother.of(shipmentWarehouse);
    final ShipmentWarehouseStarted shipmentWarehouseUpdated = Instancio.of(ShipmentWarehouseStarted.class)
        .set(field("distributionTrackingCode"), shipmentWarehouse.getTrackingCode().value())
        .create();
    doReturn(Optional.of(shipmentWarehouseCopy)).when(this.shipmentWarehouseRepository)
        .findByTrackingCode(shipmentWarehouse.getTrackingCode());
    final var command = new ProjectShipmentWarehouseStartedCommand(shipmentWarehouseUpdated);

    this.sut.execute(command);

    verify(this.shipmentWarehouseRepository).save(this.shipmentWarehouseCaptor.capture());

    final var capturedShipmentWarehouse = this.shipmentWarehouseCaptor.getValue();
    Assertions.assertThat(capturedShipmentWarehouse.getStartDate().value()).isNotNull()
        .isEqualTo(shipmentWarehouseUpdated.distributionStartDate());
  }

  @Test
  void should_throw_exception_shipment_warehouse_started_when_not_exists() {
    final var shipmentWarehouse = ShipmentWarehouseMother.randomCreated();
    final var shipmentWarehouseUpdated = Instancio.of(ShipmentWarehouseStarted.class)
        .set(field("distributionTrackingCode"), shipmentWarehouse.getTrackingCode().value())
        .create();
    doReturn(Optional.empty()).when(this.shipmentWarehouseRepository).findByTrackingCode(shipmentWarehouse.getTrackingCode());
    final var command = new ProjectShipmentWarehouseStartedCommand(shipmentWarehouseUpdated);

    Assertions.assertThatThrownBy(() -> this.sut.execute(command))
        .isInstanceOf(IllegalArgumentException.class);
  }

}
