package com.inditex.icdmdemg.application.order.command;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.inditex.icdmdemg.application.order.command.ProjectOrderDeletedCommand.ProductOrderDeleted;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.shared.utils.Transaction;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class ProjectOrderDeletedCommandHandlerTest {

  private OrderRepository orderRepository;

  private ProjectOrderDeletedCommandHandler sut;

  @BeforeAll
  void setUp() {
    this.orderRepository = mock(OrderRepository.class);
    final Transaction transaction = new Transaction();
    this.sut = new ProjectOrderDeletedCommandHandler(this.orderRepository, transaction);
  }

  @Test
  void should_delete_when_order_exists() {
    final var order = Instancio.create(Order.class);
    final var productOrderDeleted = new ProductOrderDeleted(order.getId().value());
    final var command = new ProjectOrderDeletedCommand(productOrderDeleted);

    this.sut.execute(command);

    verify(this.orderRepository).deleteById(order.getId());
  }
}
