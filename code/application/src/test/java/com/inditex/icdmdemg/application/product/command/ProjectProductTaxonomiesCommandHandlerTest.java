package com.inditex.icdmdemg.application.product.command;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.product.command.ProjectProductTaxonomiesCommand.ProductTaxonomy;
import com.inditex.icdmdemg.domain.product.ProductTaxonomyRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductTaxonomy.ProductId;
import com.inditex.icdmdemg.domain.product.entity.ProductTaxonomy.ProductTaxonomyValue;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.ClockUtils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class})
class ProjectProductTaxonomiesCommandHandlerTest {
  @Mock
  ProductTaxonomyRepository productTaxonomyRepository;

  @Mock
  ClockUtils clockUtils;

  @InjectMocks
  ProjectProductTaxonomiesCommandHandler sut;

  @Test
  void should_call_save_when_create() {
    final var now = OffsetDateTime.now();
    final var productId = UUID.randomUUID();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();

    doReturn(Optional.empty()).when(this.productTaxonomyRepository).findByProductId(new ProductId(productId));
    final var command = new ProjectProductTaxonomiesCommand(new ProductTaxonomy(productId, "CLOTHES"));

    this.sut.doHandle(command);

    final var expectedSaved = new com.inditex.icdmdemg.domain.product.entity.ProductTaxonomy(
        new ProductId(productId),
        new ProductTaxonomyValue(command.productTaxonomy().taxonomy()),
        new BasicAudit(now, now));
    verify(this.productTaxonomyRepository).save(expectedSaved);
  }

  @Test
  void should_call_save_when_update() {
    final var now = OffsetDateTime.now();
    final var productId = UUID.randomUUID();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();

    final var existing = new com.inditex.icdmdemg.domain.product.entity.ProductTaxonomy(
        new ProductId(productId),
        new ProductTaxonomyValue("RAW_MATERIAL"),
        new BasicAudit(OffsetDateTime.MIN, OffsetDateTime.MIN));
    doReturn(Optional.of(existing)).when(this.productTaxonomyRepository).findByProductId(new ProductId(productId));
    final var command = new ProjectProductTaxonomiesCommand(new ProductTaxonomy(productId, "CLOTHES"));

    this.sut.doHandle(command);

    final var expectedSaved = new com.inditex.icdmdemg.domain.product.entity.ProductTaxonomy(
        existing.productId(),
        new ProductTaxonomyValue(command.productTaxonomy().taxonomy()),
        new BasicAudit(existing.audit().createdAt(), now));
    verify(this.productTaxonomyRepository).save(expectedSaved);
  }

  @Test
  void should_do_nothing_when_no_changes() {
    final var now = OffsetDateTime.now();
    final var productId = UUID.randomUUID();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();

    final var existing = new com.inditex.icdmdemg.domain.product.entity.ProductTaxonomy(
        new ProductId(productId),
        new ProductTaxonomyValue("RAW_MATERIAL"),
        new BasicAudit(OffsetDateTime.MIN, OffsetDateTime.MIN));
    doReturn(Optional.of(existing)).when(this.productTaxonomyRepository).findByProductId(new ProductId(productId));
    final var command = new ProjectProductTaxonomiesCommand(new ProductTaxonomy(productId, existing.taxonomyValue().value()));

    this.sut.doHandle(command);

    verify(this.productTaxonomyRepository, never()).save(any());
  }
}
