package com.inditex.icdmdemg.application.use.command;

import static java.time.OffsetDateTime.now;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.application.use.command.CreateUseCommand.UseRequest;
import com.inditex.icdmdemg.application.use.command.CreateUseCommand.UseRequest.Condition;
import com.inditex.icdmdemg.application.use.mother.UseRequestMother;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.domain.taxonomyuse.TaxonomyUse;
import com.inditex.icdmdemg.domain.taxonomyuse.TaxonomyUseRepository;
import com.inditex.icdmdemg.domain.taxonomyuse.entity.TaxonomyCode;
import com.inditex.icdmdemg.domain.taxonomyuse.entity.TaxonomyPath;
import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.Use.Customer;
import com.inditex.icdmdemg.domain.use.Use.Id;
import com.inditex.icdmdemg.domain.use.Use.Taxonomy;
import com.inditex.icdmdemg.domain.use.UseRepository;
import com.inditex.icdmdemg.domain.use.entity.AssignableType;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeCondition;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues.ConditionValue;
import com.inditex.icdmdemg.domain.use.entity.PurchaseType;
import com.inditex.icdmdemg.domain.use.entity.UseConditions;
import com.inditex.icdmdemg.domain.use.entity.UseNames;
import com.inditex.icdmdemg.domain.use.entity.UsePurchasePurposeCondition;
import com.inditex.icdmdemg.domain.use.mother.UseMother;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.LocaleUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CreateUseCommandHandlerTest {

  @Mock
  private UuidGenerator uuidGenerator;

  @Mock
  private ClockUtils clockUtils;

  @Mock
  private UseRepository useRepository;

  @Mock
  private TaxonomyUseRepository taxonomyUseRepository;

  @Spy
  private Transaction transaction = new Transaction();

  @Mock
  private EventBus eventBus;

  @InjectMocks
  private CreateUseCommandHandler sut;

  @ParameterizedTest
  @MethodSource("provideTestData")
  void should_execute_create_use(
      final OffsetDateTime now,
      UseRequest useRequest,
      Use expectedUse) {

    final var generatedUuid = UuidMother.fromInteger(1);

    final TaxonomyCode taxonomyCode = new TaxonomyCode(useRequest.taxonomy());
    final TaxonomyPath taxonomyPath = new TaxonomyPath(useRequest.taxonomy());
    final TaxonomyUse taxonomyUse = new TaxonomyUse(taxonomyCode, taxonomyPath);

    doReturn(generatedUuid).when(this.uuidGenerator).generate();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(taxonomyUse)).when(this.taxonomyUseRepository).findByCode(new TaxonomyCode(useRequest.taxonomy()));
    doReturn(List.of()).when(this.useRepository).findBySingleTaxonomyCustomerAndPurchaseType(new Taxonomy(useRequest.taxonomy()),
        new Customer(useRequest.customer()), PurchaseType.fromValues(useRequest.purchaseType()));

    final var result = this.sut.execute(new CreateUseCommand(useRequest));

    assertThat(generatedUuid).isEqualTo(result.useId().value());
    final ArgumentCaptor<Use> useCaptor = ArgumentCaptor.forClass(Use.class);
    verify(this.useRepository).save(useCaptor.capture());
    final var capturedUse = useCaptor.getValue();
    assertThat(expectedUse).isEqualTo(capturedUse);
  }

  @ParameterizedTest
  @MethodSource("provideUseExistsTestData")
  void should_not_create_use_that_exists(final List<String> requestDescriptions, final List<ConditionValue> conditionValues) {
    final var names = UseRequestMother.generateUseRequestNames();

    final List<String> assignable = UseRequestMother.generateAssignable();
    final String taxonomy = UseRequestMother.generateTaxonomy();
    final String customer = UseRequestMother.generateCustomer();
    final List<String> purchaseType = UseRequestMother.generatePurchaseType();
    final String condition = "EQUALS";
    final String name = "BUYERGROUP";

    final var rootId = UUID.randomUUID();
    final OffsetDateTime now = now();
    final String triggeredBy = UseRequestMother.generateTriggeredBy();

    final AssignableType expectedAssignable = AssignableType.valueOf(assignable.getFirst());
    final Taxonomy expectedTaxonomy = new Taxonomy(taxonomy);
    final Customer expectedCustomer = new Customer(customer);
    final PurchaseType expectedPurchaseType = PurchaseType.fromValues(purchaseType);

    final UseNames useNames = new UseNames(names.toDomain(now));
    final var purchasePurposeConditionValues = new PurchasePurposeConditionValues(conditionValues);

    final var useConditions = new UseConditions(List.of(new UsePurchasePurposeCondition(
        PurchasePurposeCondition.valueOf(condition),
        PurchasePurposeConditionName.valueOf(name),
        purchasePurposeConditionValues,
        new BasicAudit(now, now))));
    final var requestConditions = new Condition(condition, name, requestDescriptions);

    final CompleteAudit completeAudit = new CompleteAudit(triggeredBy, now, triggeredBy, now, null, (short) 0);

    final UseRequest useRequest =
        new UseRequest(names, assignable, taxonomy, customer, purchaseType, List.of(requestConditions), triggeredBy);
    final Use use =
        new Use(new Id(rootId), expectedAssignable, expectedTaxonomy, expectedCustomer, expectedPurchaseType, useNames,
            useConditions, completeAudit);

    doReturn(rootId).when(this.uuidGenerator).generate();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(List.of(use)).when(this.useRepository).findBySingleTaxonomyCustomerAndPurchaseType(expectedTaxonomy,
        expectedCustomer, expectedPurchaseType);

    final var command = new CreateUseCommand(useRequest);

    final Throwable result = catchThrowable(() -> this.sut.execute(command));

    assertThat(result).isInstanceOf(ErrorException.class);

  }

  @ParameterizedTest
  @MethodSource("provideInvalidAssignableCustomerPurchaseTypeData")
  void should_not_create_use_with_invalid_assignable_customer_purchase_type(final List<String> assignable, final String customer,
      final List<String> purchaseType) {
    final var names = UseRequestMother.generateUseRequestNames();
    final String taxonomyId = UseRequestMother.generateTaxonomy();
    final String triggeredBy = UseRequestMother.generateTriggeredBy();
    final var conditions = UseRequestMother.generateOneCondition();

    final UseRequest useRequest =
        new UseRequest(names, assignable, taxonomyId, customer, purchaseType, conditions, triggeredBy);

    final var command = new CreateUseCommand(useRequest);

    final Throwable result = catchThrowable(() -> this.sut.execute(command));

    assertThat(result).isInstanceOf(ErrorException.class);

  }

  @ParameterizedTest
  @MethodSource("provideInvalidLocalesData")
  void should_not_create_use_with_invalid_locales(final List<UseRequestNames.UseRequestName> names) {
    final List<String> assignable = UseRequestMother.generateAssignable();
    final String taxonomyId = UseRequestMother.generateTaxonomy();
    final String customer = UseRequestMother.generateCustomer();
    final List<String> purchaseType = UseRequestMother.generatePurchaseType();
    final String triggeredBy = UseRequestMother.generateTriggeredBy();
    final var conditions = UseRequestMother.generateOneCondition();

    final UseRequest useRequest =
        new UseRequest(new UseRequestNames(names), assignable, taxonomyId, customer, purchaseType, conditions, triggeredBy);

    doReturn(OffsetDateTime.now()).when(this.clockUtils).getCurrentOffsetDateTime();

    final var command = new CreateUseCommand(useRequest);

    final Throwable result = catchThrowable(() -> this.sut.execute(command));

    assertThat(result).isInstanceOf(ErrorException.class);

  }

  @ParameterizedTest
  @MethodSource("provideInvalidUsePurchasePurposeConditions")
  void should_not_create_use_with_invalid_use_purchase_purpose_conditions(final String condition, final String conditionName) {
    final OffsetDateTime now = now();
    final var names = UseRequestMother.generateUseRequestNames();
    final List<String> assignable = UseRequestMother.generateAssignable();
    final String taxonomyId = UseRequestMother.generateTaxonomy();
    final String customer = UseRequestMother.generateCustomer();
    final List<String> purchaseType = UseRequestMother.generatePurchaseType();
    final String triggeredBy = UseRequestMother.generateTriggeredBy();
    final List<String> conditionValues = List.of("urn:BUYERGROUP:123e4567-e89b-12d3-a456-526655440002");

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();

    final UseRequest useRequest =
        new UseRequest(names, assignable, taxonomyId, customer, purchaseType,
            List.of(new Condition(condition, conditionName, conditionValues)), triggeredBy);

    final var command = new CreateUseCommand(useRequest);

    final Throwable result = catchThrowable(() -> this.sut.execute(command));

    assertThat(result).isInstanceOf(ErrorException.class);
  }

  private static Stream<Arguments> provideTestData() {
    final var now = OffsetDateTime.now();
    return Stream.of(
        // Happy path. One use, 2 languages, 1 condition
        Arguments.of(now,
            UseRequestMother.withAllArguments(),
            UseMother.withAllArgumentsOnlyMandatoryNames(now)),

        // 3 languages, 2 condition
        Arguments.of(now,
            UseRequestMother.withTreeNamesAndTwoConditions(),
            UseMother.withAllArgumentsAndTreeNamesAndTwoConditions(now)),

        // 3 languages, 2 condition, null assignable
        Arguments.of(now,
            UseRequestMother.withTreeNamesAndTwoConditionsNullAssignable(),
            UseMother.withTreeNamesAndTwoConditionsNullAssignable(now)));
  }

  private static Stream<Arguments> provideUseExistsTestData() {
    return Stream.of(
        oneSameCondition(),
        twoSameConditionsInSameOrder(),
        twoSameConditionsInDistinctOrder());
  }

  private static Arguments oneSameCondition() {
    final var conditionRequested1 = "urn:BUYERGROUP:123e4567-e89b-12d3-a456-526655440002";
    final var conditionExists1 = new ConditionValue(conditionRequested1);
    return Arguments.of(
        List.of(conditionRequested1),
        List.of(conditionExists1));
  }

  private static Arguments twoSameConditionsInSameOrder() {
    final var conditionRequested1 = "urn:BUYERGROUP:123e4567-e89b-12d3-a456-526655440002";
    final var conditionRequested2 = "urn:BUYERGROUP:123e4567-e89b-12d3-a456-526655440003";
    final var conditionExists1 = new ConditionValue(conditionRequested1);
    final var conditionExists2 = new ConditionValue(conditionRequested2);
    return Arguments.of(
        List.of(conditionRequested1, conditionRequested2),
        List.of(conditionExists1, conditionExists2));
  }

  private static Arguments twoSameConditionsInDistinctOrder() {
    final var conditionRequested1 = "urn:BUYERGROUP:123e4567-e89b-12d3-a456-526655440002";
    final var conditionRequested2 = "urn:BUYERGROUP:123e4567-e89b-12d3-a456-526655440003";
    final var conditionExists1 = new ConditionValue(conditionRequested1);
    final var conditionExists2 = new ConditionValue(conditionRequested2);
    return Arguments.of(
        List.of(conditionRequested1, conditionRequested2),
        List.of(conditionExists2, conditionExists1));
  }

  private static Stream<Arguments> provideInvalidAssignableCustomerPurchaseTypeData() {
    return Stream.of(

        Arguments.of(List.of("NO_VALID"),
            "urn:BUYER:123e4567-e89b-12d3-a456-526655440002",
            List.of("NOMINATED")),

        Arguments.of(List.of("NOMINATED"),
            "",
            List.of("NOMINATED")),

        Arguments.of(List.of("NOMINATED"),
            "urn:BUYER:111",
            List.of("NOMINATED")),

        Arguments.of(List.of("NOMINATED"),
            "urn:buyer:123e4567-e89b-12d3-a456-526655440002",
            List.of("NOMINATED")),

        Arguments.of(List.of("NOMINATED"),
            "urn:BUYER:123e4567-e89b-12d3-a456-526655440002",
            List.of("NO_VALID")));
  }

  private static Stream<Arguments> provideInvalidLocalesData() {
    return Stream.of(
        Arguments.of(List.of()),
        Arguments.of(List.of(new UseRequestNames.UseRequestName(LocaleUtils.createLocale("es"), "ejemplo"))),
        Arguments.of(List.of(new UseRequestNames.UseRequestName(LocaleUtils.createLocale("en"), "example"))));
  }

  private static Stream<Arguments> provideInvalidUsePurchasePurposeConditions() {
    return Stream.of(
        Arguments.of("NO_VALID", "BUYERGROUP"),
        Arguments.of("ALL_IN", "NO_VALID"));
  }
}
