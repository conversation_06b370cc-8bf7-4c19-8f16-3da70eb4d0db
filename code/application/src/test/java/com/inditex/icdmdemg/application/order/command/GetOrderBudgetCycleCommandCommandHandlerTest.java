package com.inditex.icdmdemg.application.order.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

import java.util.Optional;

import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class})
class GetOrderBudgetCycleCommandCommandHandlerTest {

  @Mock
  private OrderRepository orderRepository;

  @InjectMocks
  private GetOrderBudgetCycleCommandCommandHandler sut;

  @Test
  void should_call_to_order_repository() {
    final var order = Instancio.create(Order.class);
    final var command = new GetOrderBudgetCycleCommand(order.getId().value());

    doReturn(Optional.of(order)).when(this.orderRepository).find(order.getId());

    final var result = this.sut.execute(command);

    verify(this.orderRepository).find(order.getId());
    assertThat(result).isEqualTo(order.firstBudgetId());
  }
}
