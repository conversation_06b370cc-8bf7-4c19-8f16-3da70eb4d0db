package com.inditex.icdmdemg.application.shared.idempotent;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.time.OffsetDateTime;
import java.util.function.Consumer;

import com.inditex.icdmdemg.application.shared.idempotent.stubs.FakeEnvelope;
import com.inditex.icdmdemg.domain.consumermessage.ConsumerMessage;
import com.inditex.icdmdemg.domain.consumermessage.DuplicatedMessageException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@SpringBootTest(classes = IdempotentConsumerAspectTest.Config.class)
class IdempotentConsumerAspectTest {

  @Autowired
  @Qualifier("consumerOk")
  private IdempotentConsumer consumerOk;

  @Autowired
  @Qualifier("consumerNotImplementInterface")
  private Consumer consumerNotImplementInterface;

  @MockitoBean
  private IdempotentConsumerService idempotentConsumerService;

  @MockitoBean
  private ClockUtils clockUtils;

  @MockitoBean
  private Logger logger;

  @Test
  void should_cover_pointcut_methods() {
    final var aspect = new IdempotentConsumerAspect(this.idempotentConsumerService);
    aspect.beanAnnotatedWith();
    aspect.publicAcceptMethod();
    assertThat(aspect).isNotNull();
  }

  @Test
  void should_log_event() {
    final var event = MessageBuilder.withPayload(new FakeEnvelope()).build();
    final var now = OffsetDateTime.now();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();

    try (final var loggerFactoryMockedStatic = mockStatic(LoggerFactory.class)) {
      loggerFactoryMockedStatic.when(() -> LoggerFactory.getLogger((Class<?>) any())).thenReturn(this.logger);

      this.consumerOk.accept(event);

      verify(this.logger).info("Received event {} with payload {}", event.getClass().getSimpleName(), event);
    }
  }

  @Test
  void should_register_message() {
    final var event = MessageBuilder.withPayload(new FakeEnvelope()).build();
    final var now = OffsetDateTime.now();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();

    this.consumerOk.accept(event);

    verify(this.idempotentConsumerService).registerMessageAndProceed(any(), any());

  }

  @Test
  void should_fail_register_message() {
    final var event = MessageBuilder.withPayload(new FakeEnvelope()).build();
    final var now = OffsetDateTime.now();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    final ConsumerMessage expectedMessage = (ConsumerMessage) this.consumerOk.buildConsumerMessage().apply(event);
    doThrow(new IdempotentConsumerException(new DuplicatedMessageException(expectedMessage))).when(this.idempotentConsumerService)
        .registerMessageAndProceed(any(), any());

    assertThatThrownBy(() -> this.consumerOk.accept(event)).isInstanceOf(IdempotentConsumerException.class);

    verify(this.idempotentConsumerService).registerMessageAndProceed(any(), any());
  }

  @Test
  void should_fail_when_consumer_not_implement_idempotent_interface() {
    final var event = MessageBuilder.withPayload(new FakeEnvelope()).build();
    final var now = OffsetDateTime.now();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();

    assertThatThrownBy(() -> this.consumerNotImplementInterface.accept(event)).isInstanceOf(IdempotentConsumerException.class);

    verifyNoInteractions(this.idempotentConsumerService);
  }

  @Configuration
  @EnableAspectJAutoProxy
  @ComponentScan(basePackageClasses = {IdempotentConsumerAspect.class})
  public static class Config {

  }

}
