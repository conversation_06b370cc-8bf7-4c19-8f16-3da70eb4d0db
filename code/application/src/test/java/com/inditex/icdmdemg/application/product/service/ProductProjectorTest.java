package com.inditex.icdmdemg.application.product.service;

import static com.inditex.icdmdemg.shared.utils.Nullables.safeListWithEmptyList;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.instancio.Select.field;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.product.command.ProjectProductCommand.Attributes;
import com.inditex.icdmdemg.application.product.command.ProjectProductCommand.ProductReference;
import com.inditex.icdmdemg.domain.product.Product;
import com.inditex.icdmdemg.domain.product.ProductRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductCampaign;
import com.inditex.icdmdemg.domain.product.entity.ProductColor;
import com.inditex.icdmdemg.domain.product.entity.ProductFamilies;
import com.inditex.icdmdemg.domain.product.entity.ProductFamily;
import com.inditex.icdmdemg.domain.product.entity.ProductFamily.CampaignId;
import com.inditex.icdmdemg.domain.product.entity.ProductFamily.FamilyId;
import com.inditex.icdmdemg.domain.product.entity.ProductId;
import com.inditex.icdmdemg.domain.product.entity.ProductOriginMarket;
import com.inditex.icdmdemg.domain.product.entity.ProductOwner;
import com.inditex.icdmdemg.domain.product.entity.ProductOwner.OwnerId;
import com.inditex.icdmdemg.domain.product.entity.ProductOwners;
import com.inditex.icdmdemg.domain.product.entity.ProductQuality;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.domain.product.entity.ProductSupplier;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;

import org.assertj.core.api.AssertionsForClassTypes;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class})
class ProductProjectorTest {

  @Mock
  private ProductRepository productRepository;

  @Mock
  private ClockUtils clockUtils;

  @Spy
  private Transaction transaction = new Transaction();

  @InjectMocks
  private ProductProjector sut;

  @Test
  void should_save_product() {
    final var now = OffsetDateTime.MIN;
    final var productReference = Instancio.create(ProductReference.class);
    final var families = new ProductFamilies(safeListWithEmptyList(productReference.families()).stream()
        .map(s -> ProductFamily.create(new CampaignId(productReference.campaignId()), new FamilyId(s), now)).toList());
    final var owners = new ProductOwners(safeListWithEmptyList(productReference.attributes().owners()).stream()
        .map(s -> ProductOwner.create(new OwnerId(s), now)).toList());

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();

    this.sut.fromProductCreatedOrUpdated(productReference);

    verify(this.productRepository).save(Product.create(
        new ProductId(productReference.productId()),
        new ProductReferenceId(productReference.referenceId()),
        new ProductColor(productReference.attributes().color()),
        new ProductSupplier(productReference.supplierId()),
        new ProductCampaign(productReference.campaignId()),
        new ProductOriginMarket(productReference.originMarketId()),
        new ProductQuality(productReference.attributes().quality()),
        families,
        owners,
        now));
  }

  @Test
  void should_update_product_if_exists() {
    final var productReferenceId = UUID.randomUUID().toString();
    final var now = OffsetDateTime.MIN;
    final var existingProductReference =
        new ProductReference(String.valueOf(UUID.randomUUID()), productReferenceId,
            String.valueOf(UUID.randomUUID()), String.valueOf(UUID.randomUUID()), UUID.randomUUID(),
            List.of(UrnConstantsEnum.FAMILY.prefixWithUrn(UUID.randomUUID())),
            new Attributes(UUID.randomUUID(), 555, List.of(UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID()))));

    final var newFamily = ProductFamily.create(new CampaignId(existingProductReference.campaignId()),
        new FamilyId(UrnConstantsEnum.FAMILY.prefixWithUrn(UUID.randomUUID())), now);
    final var newOwner = ProductOwner.create(new OwnerId(UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID())), now);

    final var toUpdateProductReference = new ProductReference(
        existingProductReference.productId(),
        existingProductReference.referenceId(),
        String.valueOf(UUID.randomUUID()),
        existingProductReference.campaignId(),
        existingProductReference.originMarketId(),
        List.of(newFamily.familyId().value()),
        new Attributes(existingProductReference.attributes().color(), existingProductReference.attributes().quality(),
            List.of(newOwner.ownerId().value())));
    final var expected = Product.create(
        new ProductId(existingProductReference.productId()),
        new ProductReferenceId(existingProductReference.referenceId()),
        new ProductColor(existingProductReference.attributes().color()),
        new ProductSupplier(existingProductReference.supplierId()),
        new ProductCampaign(existingProductReference.campaignId()),
        new ProductOriginMarket(existingProductReference.originMarketId()),
        new ProductQuality(existingProductReference.attributes().quality()),
        new ProductFamilies(List.of(newFamily)),
        new ProductOwners(List.of(newOwner)),
        now);

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(expected)).when(this.productRepository).findByReferenceId(new ProductReferenceId(productReferenceId));
    doReturn(expected).when(this.productRepository).save(any());

    final var result = this.sut.fromProductCreatedOrUpdated(toUpdateProductReference);

    verify(this.productRepository).save(any());

    assertThat(result.getProductId().value()).isEqualTo(existingProductReference.productId());
    assertThat(result.getReferenceId().value()).isEqualTo(existingProductReference.referenceId());
  }

  @Test
  void should_not_update_product_if_exists_but_not_modified() {
    final var productReferenceId = UUID.randomUUID().toString();
    final var now = OffsetDateTime.MIN;
    final var existingProductReference =
        new ProductReference(String.valueOf(UUID.randomUUID()), productReferenceId, String.valueOf(UUID.randomUUID()),
            String.valueOf(UUID.randomUUID()), UUID.randomUUID(),
            List.of(),
            new Attributes(UUID.randomUUID(), 555, List.of()));
    final var toUpdateProductReference = new ProductReference(
        existingProductReference.productId(),
        existingProductReference.referenceId(), existingProductReference.supplierId(), existingProductReference.campaignId(),
        existingProductReference.originMarketId(),
        List.of(),
        new Attributes(existingProductReference.attributes().color(), existingProductReference.attributes().quality(), List.of()));
    final var optionalProduct = Optional.of(Product.create(
        new ProductId(existingProductReference.productId()),
        new ProductReferenceId(existingProductReference.referenceId()),
        new ProductColor(existingProductReference.attributes().color()),
        new ProductSupplier(existingProductReference.supplierId()),
        new ProductCampaign(existingProductReference.campaignId()),
        new ProductOriginMarket(existingProductReference.originMarketId()),
        new ProductQuality(existingProductReference.attributes().quality()),
        new ProductFamilies(List.of()),
        new ProductOwners(List.of()),
        now));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(optionalProduct).when(this.productRepository).findByReferenceId(new ProductReferenceId(productReferenceId));

    this.sut.fromProductCreatedOrUpdated(toUpdateProductReference);

    verify(this.productRepository, never()).save(any());
  }

  @Test
  void should_update_product_if_exists_and_modified_owners_and_families() {
    final var productReferenceId = UUID.randomUUID().toString();
    final var now = OffsetDateTime.MIN;
    final var existingProductReference =
        new ProductReference(String.valueOf(UUID.randomUUID()), productReferenceId, String.valueOf(UUID.randomUUID()),
            String.valueOf(UUID.randomUUID()), UUID.randomUUID(),
            List.of(),
            new Attributes(UUID.randomUUID(), 555, List.of()));
    final var toUpdateProductReference = new ProductReference(
        existingProductReference.productId(),
        existingProductReference.referenceId(), existingProductReference.supplierId(), existingProductReference.campaignId(),
        existingProductReference.originMarketId(),
        List.of("a"),
        new Attributes(existingProductReference.attributes().color(), existingProductReference.attributes().quality(), List.of("b")));
    final var optionalProduct = Optional.of(Product.create(
        new ProductId(existingProductReference.productId()),
        new ProductReferenceId(existingProductReference.referenceId()),
        new ProductColor(existingProductReference.attributes().color()),
        new ProductSupplier(existingProductReference.supplierId()),
        new ProductCampaign(existingProductReference.campaignId()),
        new ProductOriginMarket(existingProductReference.originMarketId()),
        new ProductQuality(existingProductReference.attributes().quality()),
        new ProductFamilies(List.of()),
        new ProductOwners(List.of()),
        now));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(optionalProduct).when(this.productRepository).findByReferenceId(new ProductReferenceId(productReferenceId));

    final var result = this.sut.fromProductCreatedOrUpdated(toUpdateProductReference);

    verify(this.productRepository).save(any());

    AssertionsForClassTypes.assertThat(result.getProductId().value()).isEqualTo(existingProductReference.productId());
    AssertionsForClassTypes.assertThat(result.getReferenceId().value()).isEqualTo(existingProductReference.referenceId());
  }

  @Test
  void should_delete_product() {
    final var productReference = Instancio.of(ProductReference.class)
        .set(field("referenceId"), UUID.randomUUID().toString())
        .create();
    final var product = Instancio.create(Product.class);
    doReturn(Optional.of(product)).when(this.productRepository)
        .findByReferenceId(new ProductReferenceId(productReference.referenceId()));

    this.sut.deleteReference(UUID.fromString(productReference.referenceId()));

    verify(this.productRepository).delete(product);
  }

}
