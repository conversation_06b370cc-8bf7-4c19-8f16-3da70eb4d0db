package com.inditex.icdmdemg.application.distributioninner.command;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.assertj.core.api.Assertions;
import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

@TestInstance(Lifecycle.PER_CLASS)
class DeleteDistributionInnerCommandHandlerTest {

  DistributionInnerRepository distributionInnerRepository;

  ClockUtils clockUtils;

  DeleteDistributionInnerCommandHandler sut;

  private EventBus eventBus;

  @BeforeEach
  void setup() {
    final var transaction = new Transaction();
    this.clockUtils = mock(ClockUtils.class);
    this.distributionInnerRepository = mock(DistributionInnerRepository.class);
    this.eventBus = mock(EventBus.class);
    this.sut = new DeleteDistributionInnerCommandHandler(transaction, this.clockUtils, this.distributionInnerRepository,
        this.eventBus);
  }

  @AfterEach
  void resetMocks() {
    reset(this.clockUtils, this.distributionInnerRepository, this.eventBus);
  }

  @Test
  void should_throw_exception_when_empty_find_by_id() {
    final var command = Instancio.create(DeleteDistributionInnerCommand.class);
    doReturn(Optional.empty()).when(this.distributionInnerRepository).findById(any());

    Assertions.assertThatThrownBy(() -> this.sut.execute(command)).isInstanceOf(ErrorException.class);

    verify(this.distributionInnerRepository, never()).save(any());
    verify(this.eventBus, never()).send(any());
  }

  @ParameterizedTest
  @MethodSource("incorrectDistributions")
  void should_throw_exception_when_status_is_incorrect(final Optional<DistributionInner> distribution) {
    final var command = Instancio.create(DeleteDistributionInnerCommand.class);
    doReturn(distribution).when(this.distributionInnerRepository).findById(any());

    Assertions.assertThatThrownBy(() -> this.sut.execute(command)).isInstanceOf(ErrorException.class);

    verify(this.distributionInnerRepository, never()).save(any());
    verify(this.eventBus, never()).send(any());
  }

  private static Stream<Arguments> incorrectDistributions() {
    return Stream.of(
        Arguments.of(Optional.of(DistributionInnerMother.inProgress().build())),
        Arguments.of(Optional.of(DistributionInnerMother.canceled().build())),
        Arguments.of(Optional.of(DistributionInnerMother.closed().build())),
        Arguments.of(Optional.of(DistributionInnerMother.sent().build())));
  }

  @Test
  void should_delete() {
    final var now = OffsetDateTime.now();
    final var command = Instancio.create(DeleteDistributionInnerCommand.class);
    final var distributionInner = DistributionInnerMother.pending().build();
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository).findById(any());
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();

    final var result = this.sut.execute(command);

    Assertions.assertThat(result.audit().updatedAt()).isEqualTo(now);
    Assertions.assertThat(result.audit().deletedAt()).isEqualTo(now);
    Assertions.assertThat(result.audit().updatedBy()).isEqualTo(command.deleteInnerRequest().triggeredBy());
    verify(this.distributionInnerRepository).save(result);
    verify(this.eventBus).send(List.of(new DistributionInnerUnifiedEvent(result, EventType.DELETED)));
  }

}
