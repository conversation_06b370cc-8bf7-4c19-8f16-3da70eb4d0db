package com.inditex.icdmdemg.application.distributioninner.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

import java.util.Collection;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = SendAdminEventDistributionInnerCommandHandlerTest.Config.class)
class SendAdminEventDistributionInnerCommandHandlerTest {
  @MockitoBean
  private EventBus eventBus;

  @Captor
  ArgumentCaptor<Collection<DomainEvent<?>>> eventsCaptor;

  @MockitoBean
  private DistributionInnerRepository distributionInnerRepository;

  @Autowired
  private SendAdminEventDistributionInnerCommandHandler sut;

  @Test
  void should_throw_exception_when_not_found_id() {
    final var id = UUID.randomUUID();
    final var command = new SendAdminEventDistributionInnerCommand(
        new SendAdminEventDistributionInnerCommand.SendEventRequest(id, "DistributionRequested"));
    doReturn(Optional.empty()).when(this.distributionInnerRepository).findById(new Id(id));

    assertThatThrownBy(() -> this.sut.execute(command))
        .isInstanceOf(ErrorException.class);
  }

  @Test
  void should_throw_exception_when_not_accepted_event() {
    final var distributionInner = DistributionInnerMother.inProgress().build();
    final var command = new SendAdminEventDistributionInnerCommand(
        new SendAdminEventDistributionInnerCommand.SendEventRequest(distributionInner.getId().value(),
            "DistributionProductUpdated"));
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository).findById(distributionInner.getId());

    assertThatThrownBy(() -> this.sut.execute(command))
        .isInstanceOf(ErrorException.class);
  }

  @Test
  void should_send_event() {
    final var distributionInner = DistributionInnerMother.inProgress().build();
    final var eventType = "DistributionRequested";
    final var command = new SendAdminEventDistributionInnerCommand(
        new SendAdminEventDistributionInnerCommand.SendEventRequest(distributionInner.getId().value(),
            eventType));
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository).findById(distributionInner.getId());

    final var result = this.sut.execute(command);

    assertThat(result.distributionInnerId()).isEqualTo(distributionInner.getId());
    verify(this.eventBus).send(this.eventsCaptor.capture());
    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(DistributionInnerUnifiedEvent.class::isInstance)
        .allMatch(domainEvent -> domainEvent.eventType().equals(eventType));
  }

  @Configuration
  @Import({ClockUtils.class, Transaction.class, SendAdminEventDistributionInnerCommandHandler.class})
  public static class Config {
  }
}
