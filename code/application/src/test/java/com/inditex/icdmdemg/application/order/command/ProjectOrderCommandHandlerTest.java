package com.inditex.icdmdemg.application.order.command;

import static org.mockito.Mockito.verify;

import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderCreatedOrUpdated;
import com.inditex.icdmdemg.application.order.service.OrderProjector;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class})
class ProjectOrderCommandHandlerTest {

  @Mock
  private OrderProjector orderProjector;

  @InjectMocks
  private ProjectOrderCommandHandler sut;

  @Test
  void should_call_to_order_projector() {
    final var productOrderEvent = Instancio.create(ProductOrderCreatedOrUpdated.class);
    final var command = new ProjectOrderCommand(productOrderEvent);

    this.sut.execute(command);

    verify(this.orderProjector).projectFromProductOrder(productOrderEvent);
  }
}
