package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.util.Optional;

import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseUpdatedCommand.ShipmentWarehouseUpdated;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouseRepository;
import com.inditex.icdmdemg.domain.shipmentwarehouse.mother.ShipmentWarehouseMother;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;

import org.assertj.core.api.Assertions;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class ProjectShipmentWarehouseUpdatedCommandHandlerTest {

  ArgumentCaptor<ShipmentWarehouse> shipmentWarehouseCaptor = ArgumentCaptor.forClass(ShipmentWarehouse.class);

  private final ShipmentWarehouseRepository shipmentWarehouseRepository = mock();

  private final ClockUtils clockUtils = mock();

  private final Transaction transaction = spy();

  private final ProjectShipmentWarehouseUpdatedCommandHandler sut =
      new ProjectShipmentWarehouseUpdatedCommandHandler(this.shipmentWarehouseRepository, this.clockUtils, this.transaction);

  @Test
  void should_throw_exception_shipment_warehouse_updated_when_not_exists() {
    final var shipmentWarehouse = ShipmentWarehouseMother.randomCreated();
    final var shipmentWarehouseUpdated = Instancio.of(ShipmentWarehouseUpdated.class)
        .set(field("distributionTrackingCode"), shipmentWarehouse.getTrackingCode().value())
        .create();
    doReturn(Optional.empty()).when(this.shipmentWarehouseRepository).findByTrackingCode(shipmentWarehouse.getTrackingCode());
    final var command = new ProjectShipmentWarehouseUpdatedCommand(shipmentWarehouseUpdated);

    Assertions.assertThatThrownBy(() -> this.sut.execute(command))
        .isInstanceOf(IllegalArgumentException.class);
  }

  @Test
  void should_not_update_create_date_when_not_null_shipment_warehouse_updated() {
    final var now = OffsetDateTime.now();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    final ShipmentWarehouse shipmentWarehouse = ShipmentWarehouseMother.randomStarted();
    final var shipmentWarehouseCopy = ShipmentWarehouseMother.of(shipmentWarehouse);
    final var shipmentWarehouseUpdated = Instancio.of(ShipmentWarehouseUpdated.class)
        .set(field("distributionTrackingCode"), shipmentWarehouse.getTrackingCode().value())
        .create();
    doReturn(Optional.of(shipmentWarehouseCopy)).when(this.shipmentWarehouseRepository)
        .findByTrackingCode(shipmentWarehouse.getTrackingCode());
    final var command = new ProjectShipmentWarehouseUpdatedCommand(shipmentWarehouseUpdated);

    this.sut.execute(command);

    verify(this.shipmentWarehouseRepository).save(this.shipmentWarehouseCaptor.capture());
    final var capturedShipmentWarehouse = this.shipmentWarehouseCaptor.getValue();
    assertThat(capturedShipmentWarehouse.getId()).isNotNull();
    assertThat(capturedShipmentWarehouse.getTrackingCode()).isEqualTo(shipmentWarehouse.getTrackingCode());
    assertThat(capturedShipmentWarehouse.getDistributionInner()).isEqualTo(shipmentWarehouse.getDistributionInner());
    assertThat(capturedShipmentWarehouse.getStartDate()).isNotNull().isEqualTo(shipmentWarehouse.getStartDate());
    assertThat(capturedShipmentWarehouse.getEndDate()).isNull();
    assertThat(capturedShipmentWarehouse.getLastUpdateDate().value()).isNotNull()
        .isEqualTo(shipmentWarehouseUpdated.distributionLastUpdateDate());
    assertThat(capturedShipmentWarehouse.getQuantity().value())
        .isEqualTo(shipmentWarehouseUpdated.quantity());
    assertThat(capturedShipmentWarehouse.getVersion()).isEqualTo(shipmentWarehouse.getVersion());
    assertThat(capturedShipmentWarehouse.getUpdatedAt()).isNotNull().hasFieldOrPropertyWithValue("value", now);
    assertThat(capturedShipmentWarehouse.getCreatedAt()).isEqualTo(shipmentWarehouse.getCreatedAt());
  }

  @Test
  void should_project_shipment_warehouse_updated() {
    final var now = OffsetDateTime.now();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    final var shipmentWarehouse = ShipmentWarehouseMother.randomStarted();
    final var shipmentWarehouseCopy = ShipmentWarehouseMother.of(shipmentWarehouse);
    final var shipmentWarehouseUpdated = Instancio.of(ShipmentWarehouseUpdated.class)
        .set(field("distributionTrackingCode"), shipmentWarehouse.getTrackingCode().value())
        .create();
    doReturn(Optional.of(shipmentWarehouseCopy)).when(this.shipmentWarehouseRepository)
        .findByTrackingCode(shipmentWarehouse.getTrackingCode());
    final var command = new ProjectShipmentWarehouseUpdatedCommand(shipmentWarehouseUpdated);

    this.sut.execute(command);

    verify(this.shipmentWarehouseRepository).save(this.shipmentWarehouseCaptor.capture());
    final var capturedShipmentWarehouse = this.shipmentWarehouseCaptor.getValue();
    assertThat(capturedShipmentWarehouse.getId()).isNotNull();
    assertThat(capturedShipmentWarehouse.getTrackingCode()).isEqualTo(shipmentWarehouse.getTrackingCode());
    assertThat(capturedShipmentWarehouse.getQuantity().value()).isEqualTo(shipmentWarehouseUpdated.quantity());
    assertThat(capturedShipmentWarehouse.getDistributionInner()).isEqualTo(shipmentWarehouse.getDistributionInner());
    assertThat(capturedShipmentWarehouse.getStartDate()).isEqualTo(shipmentWarehouse.getStartDate());
    assertThat(capturedShipmentWarehouse.getEndDate()).isNull();
    assertThat(capturedShipmentWarehouse.getLastUpdateDate().value()).isNotNull()
        .isEqualTo(shipmentWarehouseUpdated.distributionLastUpdateDate());
    assertThat(capturedShipmentWarehouse.getVersion()).isEqualTo(shipmentWarehouse.getVersion());
    assertThat(capturedShipmentWarehouse.getUpdatedAt()).isNotNull().hasFieldOrPropertyWithValue("value", now);
    assertThat(capturedShipmentWarehouse.getCreatedAt()).isEqualTo(shipmentWarehouse.getCreatedAt());
  }
}
