package com.inditex.icdmdemg.application.process;

import static com.inditex.icdmdemg.application.product.command.ProjectProductCommand.ProductReference;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import java.util.UUID;

import com.inditex.icdmdemg.application.distributioninner.command.UpdateDistributionInnerProductVariantCommand;
import com.inditex.icdmdemg.application.distributioninner.command.UpdateDistributionInnerReferenceCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedProductVariantCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedReferenceCommand;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary;
import com.inditex.icdmdemg.application.product.command.DeleteProductProjectionCommand;
import com.inditex.icdmdemg.application.product.command.DeleteProductProjectionCommand.ProductReferenceIdDeleted;
import com.inditex.icdmdemg.application.product.command.DeleteProductTaxonomiesCommand;
import com.inditex.icdmdemg.application.product.command.DeleteProductTaxonomiesCommand.DeletedProduct;
import com.inditex.icdmdemg.application.product.command.ProjectProductCommand;
import com.inditex.icdmdemg.application.product.command.ProjectProductTaxonomiesCommand;
import com.inditex.icdmdemg.application.product.command.ProjectProductTaxonomiesCommand.ProductTaxonomy;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class})
class ProductProcessManagerTest {

  @Mock
  CommandBus commandBus;

  @InjectMocks
  ProductProcessManager sut;

  @Test
  void should_execute_when_product_reference_deleted() {
    final var productReferenceId = UUID.randomUUID();
    final var productReferenceIdDeleted = new ProductReferenceIdDeleted(productReferenceId);
    final var deleteProductProjectionCommand = new DeleteProductProjectionCommand(productReferenceIdDeleted);

    this.sut.execute(productReferenceIdDeleted);

    verify(this.commandBus).execute(deleteProductProjectionCommand);
    verify(this.commandBus, never()).execute(any(ProjectProductCommand.class));
    verify(this.commandBus, never()).execute(any(UpdateDistributionNominatedProductVariantCommand.class));
    verify(this.commandBus, never()).execute(any(UpdateDistributionInnerProductVariantCommand.class));
  }

  @Test
  void should_execute_when_product_reference() {
    final var productReference = Instancio.create(ProductReference.class);
    final var projectProductCommand = new ProjectProductCommand(productReference);

    this.sut.execute(productReference);

    verify(this.commandBus, never()).execute(any(DeleteProductProjectionCommand.class));
    verify(this.commandBus).execute(projectProductCommand);
    verify(this.commandBus, never()).execute(any(UpdateDistributionNominatedProductVariantCommand.class));
    verify(this.commandBus, never()).execute(any(UpdateDistributionInnerProductVariantCommand.class));
  }

  @Test
  void should_execute_when_product_taxonomy() {
    final var productTaxonomy = Instancio.create(ProductTaxonomy.class);
    final var projectProductTaxonomiesCommand = new ProjectProductTaxonomiesCommand(productTaxonomy);

    this.sut.execute(productTaxonomy);

    verify(this.commandBus, never()).execute(any(DeleteProductTaxonomiesCommand.class));
    verify(this.commandBus).execute(projectProductTaxonomiesCommand);
  }

  @Test
  void should_execute_when_product_taxonomy_delete() {
    final var deletedProduct = Instancio.create(DeletedProduct.class);
    final var deleteProductTaxonomiesCommand = new DeleteProductTaxonomiesCommand(deletedProduct);

    this.sut.execute(deletedProduct);

    verify(this.commandBus, never()).execute(any(ProjectProductTaxonomiesCommand.class));
    verify(this.commandBus).execute(deleteProductTaxonomiesCommand);
  }

  @Test
  void should_execute_when_update_distributions_product_variants() {
    final var mergeSplitInput = Instancio.create(MergeSplitSummary.class);
    final var updateDistributionNominatedProductVariantCommand = new UpdateDistributionNominatedProductVariantCommand(
        mergeSplitInput.updateVariantGroupInput(), mergeSplitInput.triggeredBy());
    final var updateDistributionInnerProductVariantCommand = new UpdateDistributionInnerProductVariantCommand(
        mergeSplitInput.updateVariantGroupInput(), mergeSplitInput.triggeredBy());
    final var updateDistributionNominatedReferenceCommand = new UpdateDistributionNominatedReferenceCommand(
        mergeSplitInput.updateReferenceInput(), mergeSplitInput.triggeredBy());
    final var updateDistributionInnerReferenceCommand = new UpdateDistributionInnerReferenceCommand(
        mergeSplitInput.updateReferenceInput(), mergeSplitInput.triggeredBy());

    this.sut.execute(mergeSplitInput);

    verify(this.commandBus, never()).execute(any(DeleteProductProjectionCommand.class));
    verify(this.commandBus, never()).execute(any(ProjectProductCommand.class));
    verify(this.commandBus).execute(updateDistributionNominatedProductVariantCommand);
    verify(this.commandBus).execute(updateDistributionInnerProductVariantCommand);
    verify(this.commandBus).execute(updateDistributionNominatedReferenceCommand);
    verify(this.commandBus).execute(updateDistributionInnerReferenceCommand);
  }
}
