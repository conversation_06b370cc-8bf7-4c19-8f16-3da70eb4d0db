package com.inditex.icdmdemg.application.distributionnominated.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

import java.time.Clock;
import java.time.ZoneId;
import java.util.Collection;
import java.util.List;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedCloseConfigProvider;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedStatus;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = DeleteDistributionNominatedStatusCommandHandlerTest.Config.class)
class DeleteDistributionNominatedStatusCommandHandlerTest {

  @MockitoBean
  private DistributionNominatedRepository distributionNominatedRepository;

  @MockitoBean
  private DistributionNominatedCloseConfigProvider distributionNominatedCloseConfigProvider;

  @MockitoBean
  private EventBus eventBus;

  @Autowired
  private ClockUtils clockUtils;

  @Captor
  ArgumentCaptor<DistributionNominated> saveCaptor;

  @Captor
  ArgumentCaptor<Collection<DomainEvent<?>>> eventsCaptor;

  @Autowired
  private DeleteDistributionNominatedStatusCommandHandler sut;

  @ParameterizedTest
  @EnumSource(DistributionNominatedStatus.class)
  void should_delete_when_delete_order_and_state_dn(final DistributionNominatedStatus currentStatus) {
    final var orderId = UuidMother.fromInteger(101);

    final var now = OffsetDateTimeMother.fromInteger(2);
    this.clockUtils.setClock(Clock.fixed(now.toInstant(), ZoneId.of("UTC")));

    final var productOrderId = new ProductOrderId(orderId);

    final var distributionNominated = DistributionNominatedMother.withStatus(currentStatus)
        .productOrderId(productOrderId)
        .build();

    doReturn(List.of(distributionNominated)).when(this.distributionNominatedRepository).findByProductOrderId(productOrderId);

    final var result = this.sut.execute(new DeleteDistributionNominatedStatusCommand(orderId.toString(), "test"));

    verify(this.distributionNominatedRepository).save(this.saveCaptor.capture());
    verify(this.eventBus).send(this.eventsCaptor.capture());

    assertThat(this.saveCaptor.getValue()).extracting(DistributionNominated::status).isEqualTo(currentStatus);
    assertThat(this.saveCaptor.getValue()).extracting(DistributionNominated::audit).extracting(CompleteAudit::deletedAt)
        .isEqualTo(now);

    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(domainEvent -> domainEvent instanceof DistributionNominatedUnifiedEvent)
        .allMatch(domainEvent -> domainEvent.eventType().equals(EventType.DELETED.value));
    assertThat(result).containsExactlyInAnyOrderElementsOf(List.of(distributionNominated));
    assertThat(this.saveCaptor.getValue()).extracting(DistributionNominated::audit).extracting(CompleteAudit::updatedBy)
        .isEqualTo("test");
  }

  @Configuration
  @Import({ClockUtils.class, Transaction.class, DeleteDistributionNominatedStatusCommandHandler.class})
  public static class Config {
  }
}
