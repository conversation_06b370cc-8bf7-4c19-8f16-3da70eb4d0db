package com.inditex.icdmdemg.application.distributionnominated.command;

import static com.inditex.icdmdemg.application.distributionnominated.command.RegularizeColorAndProjectCommitmentCommandHandler.REGULARIZATION_USER;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.math.BigDecimal;
import java.time.Clock;
import java.time.ZoneId;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.OrderUse;
import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.Use;
import com.inditex.icdmdemg.application.commitmentuse.service.MaterialCommitmentUseProjector;
import com.inditex.icdmdemg.application.distributionnominated.command.RegularizeColorAndProjectCommitmentCommand.LineToRegularize;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus.MaterialCommitmentUseStatusEnum;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.Id;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedLineMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.shared.EntityAndActionResult;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = RegularizeColorAndProjectCommitmentCommandHandlerTest.Config.class)

class RegularizeColorAndProjectCommitmentCommandHandlerTest {

  @MockitoBean
  private DistributionNominatedRepository distributionNominatedRepository;

  @MockitoBean
  private MaterialCommitmentUseProjector materialCommitmentUseProjector;

  @MockitoBean
  private EventBus eventBus;

  @Autowired
  private ClockUtils clockUtils;

  @Captor
  ArgumentCaptor<DistributionNominated> saveCaptor;

  @Captor
  ArgumentCaptor<Collection<DomainEvent<?>>> eventsCaptor;

  @Autowired
  private Transaction transaction;

  @Autowired
  private RegularizeColorAndProjectCommitmentCommandHandler sut;

  @Test
  void should_do_nothing_when_dn_to_regularize_does_not_exist() {
    final var uses = List.of(
        new Use(String.valueOf(UUID.randomUUID()),
            MaterialCommitmentUseStatusEnum.OPEN.name(),
            null,
            String.valueOf(UUID.randomUUID()),
            BigDecimal.ONE));

    final var useCommitmentOrders = Instancio.of(OrderUse.class)
        .set(field("orderId"), String.valueOf(UUID.randomUUID()))
        .set(field("orderLineId"), String.valueOf(UUID.randomUUID()))
        .set(field("uses"), uses)
        .create();
    final var lineId = UuidMother.fromInteger(101);

    final var now = OffsetDateTimeMother.fromInteger(2);
    this.clockUtils.setClock(Clock.fixed(now.toInstant(), ZoneId.of("UTC")));

    doReturn(Optional.empty()).when(this.distributionNominatedRepository).findByLineId(new Id(lineId));

    final var result =
        this.sut.execute(new RegularizeColorAndProjectCommitmentCommand(new LineToRegularize(new Id(lineId)), useCommitmentOrders));

    verifyNoInteractions(this.eventBus, this.materialCommitmentUseProjector);
    verify(this.distributionNominatedRepository, never()).save(any());
    assertThat(result.materialCommitmentUsesProjected()).isEmpty();
  }

  @Test
  void should_save_regularized_line() {
    final var uses = List.of(
        new Use(String.valueOf(UUID.randomUUID()),
            MaterialCommitmentUseStatusEnum.OPEN.name(),
            null,
            String.valueOf(UUID.randomUUID()),
            BigDecimal.ONE));

    final var useCommitmentOrders = Instancio.of(OrderUse.class)
        .set(field("orderId"), String.valueOf(UUID.randomUUID()))
        .set(field("orderLineId"), String.valueOf(UUID.randomUUID()))
        .set(field("uses"), uses)
        .create();
    final var commitment = Instancio.create(MaterialCommitmentUse.class);
    final var lineId = UuidMother.fromInteger(101);
    final var line = DistributionNominatedLineMother.createdWithIdAndAlternativeReference(new Id(lineId)).build();
    final var distributionNominated = DistributionNominatedMother.pendingWithLines(List.of(line)).build();
    final var expectedRegularizedRequestedQ = new RequestedQuantity(line.requestedQuantity()
        .value().add(line.alternativeReference().requestedQuantity().value()));

    final var now = OffsetDateTimeMother.fromInteger(2);
    this.clockUtils.setClock(Clock.fixed(now.toInstant(), ZoneId.of("UTC")));

    doReturn(Optional.of(distributionNominated)).when(this.distributionNominatedRepository).findByLineId(new Id(lineId));
    doReturn(List.of(EntityAndActionResult.updated(commitment))).when(this.materialCommitmentUseProjector)
        .projectFromUseToOrder(useCommitmentOrders);

    final var result =
        this.sut.execute(new RegularizeColorAndProjectCommitmentCommand(new LineToRegularize(new Id(lineId)), useCommitmentOrders));

    verify(this.materialCommitmentUseProjector).projectFromUseToOrder(useCommitmentOrders);
    verify(this.distributionNominatedRepository).save(this.saveCaptor.capture());
    verify(this.eventBus).send(this.eventsCaptor.capture());

    final var savedDN = this.saveCaptor.getValue();
    final var savedDNLine = savedDN.lines().getLineById(new Id(lineId));
    assertThat(savedDN).extracting(DistributionNominated::audit).extracting(
        CompleteAudit::updatedAt)
        .isEqualTo(now);
    assertThat(savedDN).extracting(DistributionNominated::audit).extracting(CompleteAudit::updatedBy)
        .isEqualTo(REGULARIZATION_USER);
    assertThat(savedDNLine).isNotEmpty()
        .hasValueSatisfying(savedLine -> assertThat(savedLine)
            .extracting(DistributionNominatedLine::alternativeReference)
            .isNull())
        .hasValueSatisfying(savedLine -> assertThat(savedLine)
            .extracting(DistributionNominatedLine::requestedQuantity)
            .isEqualTo(expectedRegularizedRequestedQ));

    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(domainEvent -> domainEvent instanceof DistributionNominatedUnifiedEvent)
        .allMatch(domainEvent -> domainEvent.eventType().equals(EventType.DISTRIBUTION_RECONCILED.value));

    assertThat(result.materialCommitmentUsesProjected()).containsExactly(commitment);
  }

  @Test
  void should_save_regularized_line_and_leave_untouched_not_regularized() {
    final var uses = List.of(
        new Use(String.valueOf(UUID.randomUUID()),
            MaterialCommitmentUseStatusEnum.OPEN.name(),
            null,
            String.valueOf(UUID.randomUUID()),
            BigDecimal.ONE));

    final var useCommitmentOrders = Instancio.of(OrderUse.class)
        .set(field("orderId"), String.valueOf(UUID.randomUUID()))
        .set(field("orderLineId"), String.valueOf(UUID.randomUUID()))
        .set(field("uses"), uses)
        .create();
    final var commitment = Instancio.create(MaterialCommitmentUse.class);
    final var lineId = UuidMother.fromInteger(101);
    final var lineToRegularize = DistributionNominatedLineMother.createdWithIdAndAlternativeReference(new Id(lineId)).build();
    final var line = DistributionNominatedLineMother.created().build();
    final var distributionNominated = DistributionNominatedMother.pendingWithLines(List.of(lineToRegularize, line)).build();
    final var expectedRegularizedRequestedQ = new RequestedQuantity(lineToRegularize.requestedQuantity()
        .value().add(lineToRegularize.alternativeReference().requestedQuantity().value()));

    final var now = OffsetDateTimeMother.fromInteger(2);
    this.clockUtils.setClock(Clock.fixed(now.toInstant(), ZoneId.of("UTC")));

    doReturn(Optional.of(distributionNominated)).when(this.distributionNominatedRepository).findByLineId(new Id(lineId));
    doReturn(List.of(EntityAndActionResult.updated(commitment))).when(this.materialCommitmentUseProjector)
        .projectFromUseToOrder(useCommitmentOrders);

    final var result =
        this.sut.execute(new RegularizeColorAndProjectCommitmentCommand(new LineToRegularize(new Id(lineId)), useCommitmentOrders));

    verify(this.materialCommitmentUseProjector).projectFromUseToOrder(useCommitmentOrders);
    verify(this.distributionNominatedRepository).save(this.saveCaptor.capture());
    verify(this.eventBus).send(this.eventsCaptor.capture());

    final var savedDN = this.saveCaptor.getValue();
    final var savedDNLine = savedDN.lines().getLineById(new Id(lineId));
    final var savedDNNotRegularizedLine = savedDN.lines().getLineById(line.id());

    assertThat(savedDN.lines().value()).hasSize(distributionNominated.lines().value().size());
    assertThat(savedDN).extracting(DistributionNominated::audit).extracting(
        CompleteAudit::updatedAt)
        .isEqualTo(now);
    assertThat(savedDN).extracting(DistributionNominated::audit).extracting(CompleteAudit::updatedBy)
        .isEqualTo(REGULARIZATION_USER);
    assertThat(savedDNNotRegularizedLine).isNotEmpty().hasValue(line);
    assertThat(savedDNLine).isNotEmpty()
        .hasValueSatisfying(savedLine -> assertThat(savedLine)
            .extracting(DistributionNominatedLine::alternativeReference)
            .isNull())
        .hasValueSatisfying(savedLine -> assertThat(savedLine)
            .extracting(DistributionNominatedLine::requestedQuantity)
            .isEqualTo(expectedRegularizedRequestedQ));

    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(domainEvent -> domainEvent instanceof DistributionNominatedUnifiedEvent)
        .allMatch(domainEvent -> domainEvent.eventType().equals(EventType.DISTRIBUTION_RECONCILED.value));

    assertThat(result.materialCommitmentUsesProjected()).containsExactly(commitment);
  }

  @Configuration
  @Import({ClockUtils.class, RegularizeColorAndProjectCommitmentCommandHandler.class, Transaction.class})
  public static class Config {
  }
}
