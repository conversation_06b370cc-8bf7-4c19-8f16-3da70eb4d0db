package com.inditex.icdmdemg.application.commitmentuse.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.commitmentuse.command.CalculateNominatedProvisionCommand;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseCollection;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseTimestamp;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MaterialCommitmentUseExpiredProcessorTest {

  @Mock
  private CommandBus commandBus;

  @Mock
  private ClockUtils clockUtils;

  @Mock
  private MaterialCommitmentUseRepository materialCommitmentUseRepository;

  @InjectMocks
  private MaterialCommitmentUseExpiredProcessor sut;

  @Test
  void should_save_expired_mcu_and_calc_nominated_provision() {
    final var now = OffsetDateTime.now();
    final var nowMcu = MaterialCommitmentUseTimestamp.of(now);
    final var materialCommitmentUse1 = MaterialCommitmentUseMother.with("1", "1", BigDecimal.ZERO, now.minusDays(1));
    final var materialCommitmentUse2 = MaterialCommitmentUseMother.with("1", "1", BigDecimal.ZERO, now.minusDays(5));
    final var materialCommitmentUseCollection =
        new MaterialCommitmentUseCollection(List.of(materialCommitmentUse1, materialCommitmentUse2));

    final var materialCommitmentUsesProcessed = materialCommitmentUseCollection.materialCommitmentUses().stream()
        .map(materialCommitmentUse -> materialCommitmentUse.setAsProcessed(nowMcu)).toList();

    final var command = new CalculateNominatedProvisionCommand(List.of(
        this.mapToNominated(materialCommitmentUse1.sharedRawMaterial()),
        this.mapToNominated(materialCommitmentUse2.sharedRawMaterial())),
        "MaterialCommitmentUseProcessor");

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(materialCommitmentUseCollection).when(this.materialCommitmentUseRepository).findNotProcessedAndExpectedDateIsLessThan(now);

    this.sut.processExpiredExpectedDate();

    verify(this.commandBus).execute(command);
    verify(this.materialCommitmentUseRepository).saveAll(materialCommitmentUsesProcessed);
  }

  @Test
  void should_not_save_expired_mcu_and_calc_nominated_provision() {
    final var now = OffsetDateTime.now();
    final var materialCommitmentUseCollection = new MaterialCommitmentUseCollection(List.of());

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(materialCommitmentUseCollection).when(this.materialCommitmentUseRepository).findNotProcessedAndExpectedDateIsLessThan(now);

    this.sut.processExpiredExpectedDate();

    verify(this.commandBus, times(0)).execute(any());
    verify(this.materialCommitmentUseRepository, times(0)).saveAll(any());
  }

  private SharedRawMaterialNominated mapToNominated(final MaterialCommitmentUseSharedRawMaterial commitmentUseSharedRawMaterial) {
    return new SharedRawMaterialNominated(
        UUID.fromString(commitmentUseSharedRawMaterial.materialCommitmentMaterialReferenceId().value()),
        UUID.fromString(commitmentUseSharedRawMaterial.materialCommitmentUseId().value()),
        commitmentUseSharedRawMaterial.materialCommitmentBudgetId().value());
  }

}
