package com.inditex.icdmdemg.application.distributioninner.command;

import static org.mockito.ArgumentMatchers.any;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.OrderLine;
import com.inditex.icdmdemg.domain.order.entity.OrderLines;
import com.inditex.icdmdemg.domain.order.mother.OrderLineMother;
import com.inditex.icdmdemg.domain.order.mother.OrderMother;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

class UpdateDistributionInnerFromOrderCommandHandlerTest {

  private final ArgumentCaptor<DistributionInner> distributionInnerCaptor = ArgumentCaptor.forClass(DistributionInner.class);

  private final ArgumentCaptor<Collection<DomainEvent<?>>> eventsCaptor = ArgumentCaptor.forClass(Collection.class);

  private final DistributionInnerRepository distributionInnerRepository = Mockito.mock(DistributionInnerRepository.class);

  private final OrderRepository orderRepository = Mockito.mock(OrderRepository.class);

  private final Transaction transaction = new Transaction();

  private final ClockUtils clockUtils = Mockito.mock(ClockUtils.class);

  private final EventBus eventBus = Mockito.mock(EventBus.class);

  private final UpdateDistributionInnerFromOrderCommandHandler sut = new UpdateDistributionInnerFromOrderCommandHandler(
      this.distributionInnerRepository, this.orderRepository, this.transaction, this.clockUtils, this.eventBus);

  @Test
  void should_update_distribution_order_budget_cycle() {
    final var now = OffsetDateTime.now();
    this.mockGetCurrentOffsetDateTime(now);

    final OrderLine orderLine = OrderLineMother.random().build();
    final var orderLines = new OrderLines(List.of(orderLine));
    final var order = OrderMother.random().orderLines(orderLines).build();
    this.orderExists(order);

    final var distributionBuilder =
        DistributionInnerMother.inProgress().productOrderId(new ProductOrderId(UUID.fromString(order.getId().value())));
    final var distribution = distributionBuilder.build();
    final var distributions = List.of(distribution);
    this.distributionsExist(distributions);

    final var expectedDistribution =
        distributionBuilder.status(DistributionInnerStatus.CLOSED).budgetCycle(new BudgetCycle(orderLine.budgetId().value()))
            .audit(distribution.audit().update("triggered-by", now))
            .build();

    final var command = new UpdateDistributionInnerFromOrderCommand(order.getId().value(), "triggered-by");

    this.sut.doHandle(command);

    Mockito.verify(this.distributionInnerRepository).save(this.distributionInnerCaptor.capture());
    Mockito.verify(this.eventBus).send(this.eventsCaptor.capture());

    final var updatedDistribution = this.distributionInnerCaptor.getValue();
    Assertions.assertThat(updatedDistribution).isEqualTo(expectedDistribution);

    final var event = (DistributionInnerUnifiedEvent) this.eventsCaptor.getValue().stream().findFirst().get();
    Assertions.assertThat(event.type()).isEqualTo(EventType.CLOSED);

  }

  @Test
  void should_do_nothing_when_budget_cycle_does_not_change() {
    final OrderLine orderLine = OrderLineMother.random().build();
    final var orderLines = new OrderLines(List.of(orderLine));
    final var order = OrderMother.random().orderLines(orderLines).build();
    this.orderExists(order);

    final DistributionInner distribution =
        DistributionInnerMother.inProgress().budgetCycle(new BudgetCycle(orderLine.budgetId().value()))
            .productOrderId(new ProductOrderId(UUID.fromString(order.getId().value()))).build();
    final var distributions = List.of(distribution);
    this.distributionsExist(distributions);

    final var command = new UpdateDistributionInnerFromOrderCommand(order.getId().value(), "triggered-by");

    this.sut.doHandle(command);

    Mockito.verify(this.distributionInnerRepository, Mockito.never()).save(any());
    Mockito.verify(this.eventBus, Mockito.never()).send(any());

  }

  @Test
  void should_do_nothing_when_order_not_found() {
    final var command = new UpdateDistributionInnerFromOrderCommand(UUID.randomUUID().toString(), "triggered-by");

    this.sut.doHandle(command);

    Mockito.verify(this.distributionInnerRepository, Mockito.never()).save(any());
    Mockito.verify(this.eventBus, Mockito.never()).send(any());
  }

  @Test
  void should_do_nothing_when_order_has_no_budget_cycle() {
    final var now = OffsetDateTime.now();
    this.mockGetCurrentOffsetDateTime(now);

    final var emptyOrderLines = new OrderLines(List.of());
    final var order = OrderMother.random().orderLines(emptyOrderLines).build();
    this.orderExists(order);

    final var command = new UpdateDistributionInnerFromOrderCommand(order.getId().value(), "triggered-by");

    this.sut.doHandle(command);

    Mockito.verify(this.distributionInnerRepository, Mockito.never()).save(any());
    Mockito.verify(this.eventBus, Mockito.never()).send(any());

  }

  private void orderExists(Order order) {
    Mockito.doReturn(Optional.of(order)).when(this.orderRepository).find(new OrderId(order.getId().value()));
  }

  private void distributionsExist(List<DistributionInner> distributions) {
    Mockito.doReturn(distributions).when(this.distributionInnerRepository).findByProductOrderId(distributions.getFirst().productOrderId());
  }

  private void mockGetCurrentOffsetDateTime(OffsetDateTime offsetDateTime) {
    Mockito.doReturn(offsetDateTime).when(this.clockUtils).getCurrentOffsetDateTime();
  }
}
