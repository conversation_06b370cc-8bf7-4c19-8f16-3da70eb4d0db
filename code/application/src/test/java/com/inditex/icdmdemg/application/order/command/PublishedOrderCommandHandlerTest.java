package com.inditex.icdmdemg.application.order.command;

import static org.mockito.Mockito.verify;

import com.inditex.icdmdemg.application.order.command.PublishedOrderCommand.PublishedOrder;
import com.inditex.icdmdemg.application.order.service.OrderProjector;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class})
class PublishedOrderCommandHandlerTest {

  @Mock
  private OrderProjector orderProjector;

  @InjectMocks
  private PublishedOrderCommandHandler sut;

  @Test
  void should_call_to_order_projector() {
    final var publishedOrder = Instancio.create(PublishedOrder.class);
    final var command = new PublishedOrderCommand(publishedOrder);

    this.sut.execute(command);

    verify(this.orderProjector).publishOrder(publishedOrder);
  }
}
