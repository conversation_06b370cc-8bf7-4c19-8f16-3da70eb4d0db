package com.inditex.icdmdemg.application.shipmentcommitment.command;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.util.Optional;

import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentModifiedCommand.ShipmentModified;
import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitment;
import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitmentRepository;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentMeasurementUnitId;
import com.inditex.icdmdemg.domain.shipmentcommitment.mother.ShipmentCommitmentMother;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class ProjectCommitmentShipmentModifiedCommandHandlerTest {

  private ClockUtils clock;

  private ShipmentCommitmentRepository repository;

  private ProjectCommitmentShipmentModifiedCommandHandler sut;

  @BeforeEach
  void setUp() {
    this.clock = mock(ClockUtils.class);
    this.repository = mock(ShipmentCommitmentRepository.class);
    final Transaction transaction = new Transaction();
    this.sut = new ProjectCommitmentShipmentModifiedCommandHandler(this.clock, this.repository, transaction);
  }

  @Test
  void should_update_when_shipment_commitment_exists() {
    final var now = OffsetDateTime.now().plusMinutes(5);
    final var existingShipmentCommitment = ShipmentCommitmentMother.random();
    final var updated = ShipmentCommitmentMother.updated(existingShipmentCommitment, now);
    final var command = this.shipmentCommitmentToCommand(updated);

    doReturn(now).when(this.clock).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingShipmentCommitment)).when(this.repository).findById(any());
    doReturn(updated).when(this.repository).save(any());

    final var result = this.sut.execute(command);

    assertThat(result).hasValue(updated);
    verify(this.repository).findById(existingShipmentCommitment.getId());
    verify(this.repository).save(updated);
  }

  @Test
  void should_not_update_when_shipmentCommitment_does_not_exist() {
    final var shipmentModified = ShipmentCommitmentMother.random();
    final var command = this.shipmentCommitmentToCommand(shipmentModified);

    doReturn(Optional.empty()).when(this.repository).findById(any());

    final var result = this.sut.execute(command);

    assertThat(result).isEmpty();
    verify(this.repository).findById(shipmentModified.getId());
    verify(this.repository, never()).save(any());
  }

  private ProjectCommitmentShipmentModifiedCommand shipmentCommitmentToCommand(final ShipmentCommitment from) {
    return new ProjectCommitmentShipmentModifiedCommand(
        new ShipmentModified(from.getId().value(),
            from.getDistributionNominatedLineId().value(),
            from.getDistributedQuantity().quantity().value(),
            acceptNullElseMap(from.getDistributedQuantity().measurementUnitId(), ShipmentCommitmentMeasurementUnitId::value)));
  }

}
