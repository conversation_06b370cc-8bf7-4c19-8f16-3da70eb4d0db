package com.inditex.icdmdemg.application.shared.bus;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

import java.util.function.Supplier;

import com.inditex.icdmdemg.shared.utils.RandomValue;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class})
class TransactionalCommandBusTest {

  @Mock
  CommandBus commandBus;

  @Mock
  Transaction transaction;

  @InjectMocks
  TransactionalCommandBus sut;

  @Captor
  ArgumentCaptor<Supplier> captor;

  @Test
  @DisplayName("should call delegate inside transaction")
  void shouldCallDelegateInsideTransaction() {
    final BaseCommand command = new BaseCommand() {};
    final int expectedTransactionResult = RandomValue.randomPositiveInteger();
    final int expectedCommandBusResult = RandomValue.randomPositiveInteger();
    doReturn(expectedTransactionResult).when(this.transaction).run(any(Supplier.class));
    doReturn(expectedCommandBusResult).when(this.commandBus).execute(command);

    final Object result = this.sut.execute(command);

    assertThat(result).isEqualTo(expectedTransactionResult);
    verify(this.transaction).run(this.captor.capture());
    this.verifyCommandBusCall(expectedCommandBusResult, command);
  }

  private void verifyCommandBusCall(final int commandBusResult, final BaseCommand command) {
    final Object supplierResult = this.captor.getValue().get();
    assertThat(supplierResult).isEqualTo(commandBusResult);
    verify(this.commandBus).execute(command);
  }
}
