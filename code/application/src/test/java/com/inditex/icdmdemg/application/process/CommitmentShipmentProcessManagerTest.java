package com.inditex.icdmdemg.application.process;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.util.Optional;

import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedFromShipmentCommand;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCancelledCommand;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCancelledCommand.ShipmentCancelled;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCreatedCommand;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCreatedCommand.ShipmentCreated;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentModifiedCommand;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentModifiedCommand.ShipmentModified;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedDistributed;
import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitment;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CommitmentShipmentProcessManagerTest {
  private static final String TRIGGERED_BY = "Shipment Updated Event";

  @Mock
  private CommandBus commandBus;

  @InjectMocks
  private CommitmentShipmentProcessManager processManager;

  @Test
  void should_project_distribution_shipment_created() {
    final var distributionCreated = Instancio.create(ShipmentCreated.class);
    final var shipmentCommitment = Instancio.create(ShipmentCommitment.class);
    final var projectCommitmentShipmentCreatedCommand =
        new ProjectCommitmentShipmentCreatedCommand(distributionCreated);
    final var updateDistributionCommitmentFromShipmentCommand =
        new UpdateDistributionNominatedFromShipmentCommand(new DistributionNominatedDistributed(
            distributionCreated.distributionNominatedLineId(), TRIGGERED_BY));

    doReturn(shipmentCommitment).when(this.commandBus).execute(projectCommitmentShipmentCreatedCommand);

    this.processManager.execute(distributionCreated);

    verify(this.commandBus).execute(projectCommitmentShipmentCreatedCommand);
    verify(this.commandBus).execute(updateDistributionCommitmentFromShipmentCommand);
  }

  @Test
  void should_project_distribution_shipment_modified() {
    final var shipmentModified = Instancio.create(ShipmentModified.class);
    final var shipmentCommitment = Instancio.create(ShipmentCommitment.class);
    final var projectCommitmentShipmentModifiedCommand =
        new ProjectCommitmentShipmentModifiedCommand(shipmentModified);
    final var updateDistributionNominatedFromShipmentCommand =
        new UpdateDistributionNominatedFromShipmentCommand(
            new DistributionNominatedDistributed(shipmentModified.distributionNominatedLineId(), TRIGGERED_BY));

    doReturn(Optional.of(shipmentCommitment)).when(this.commandBus).execute(projectCommitmentShipmentModifiedCommand);

    this.processManager.execute(shipmentModified);

    verify(this.commandBus).execute(projectCommitmentShipmentModifiedCommand);
    verify(this.commandBus).execute(updateDistributionNominatedFromShipmentCommand);
  }

  @Test
  void should_project_distribution_shipment_modified_should_not_call_updater() {
    final var shipmentModified = Instancio.create(ShipmentModified.class);
    final var projectCommitmentShipmentCreatedCommand =
        new ProjectCommitmentShipmentModifiedCommand(shipmentModified);

    doReturn(Optional.empty()).when(this.commandBus).execute(projectCommitmentShipmentCreatedCommand);
    this.processManager.execute(shipmentModified);

    verify(this.commandBus).execute(projectCommitmentShipmentCreatedCommand);
    verify(this.commandBus, times(0)).execute(any(UpdateDistributionNominatedFromShipmentCommand.class));
  }

  @Test
  void should_project_distribution_shipment_cancelled() {
    final var shipmentCancelled = Instancio.create(ShipmentCancelled.class);
    final var projectCommitmentShipmentCreatedCommand =
        new ProjectCommitmentShipmentCancelledCommand(shipmentCancelled);
    final var updateDistributionCommitmentFromShipmentCommand =
        new UpdateDistributionNominatedFromShipmentCommand(
            new DistributionNominatedDistributed(shipmentCancelled.distributionNominatedLineId(), TRIGGERED_BY));

    this.processManager.execute(shipmentCancelled);

    verify(this.commandBus).execute(projectCommitmentShipmentCreatedCommand);
    verify(this.commandBus).execute(updateDistributionCommitmentFromShipmentCommand);
  }
}
