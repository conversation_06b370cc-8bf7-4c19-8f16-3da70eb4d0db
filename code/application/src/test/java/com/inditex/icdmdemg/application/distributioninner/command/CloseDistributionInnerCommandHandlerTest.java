package com.inditex.icdmdemg.application.distributioninner.command;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.assertj.core.api.Assertions;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CloseDistributionInnerCommandHandlerTest {

  @Mock
  private DistributionInnerRepository distributionInnerRepository;

  @Mock
  private ClockUtils clockUtils;

  @Spy
  private Transaction transaction = new Transaction();

  @Mock
  private EventBus eventBus;

  @InjectMocks
  private CloseDistributionInnerCommandHandler sut;

  @Test
  void should_fail_when_distribution_inner_does_not_exists() {
    final var distributionInnerId = "75e1ecfe-77cc-442c-9b93-41b5de71be56";
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.empty());

    final var command = new CloseDistributionInnerCommand(UUID.fromString(distributionInnerId),
        "triggeredBy");
    assertThatThrownBy(() -> this.sut.doHandle(command))
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("Distribution Inner 75e1ecfe-77cc-442c-9b93-41b5de71be56 not found");

    verifyNoInteractions(this.eventBus);
    verify(this.distributionInnerRepository, never()).save(any());
  }

  @ParameterizedTest
  @MethodSource("correctDistributions")
  void should_work_when_distribution_inner_is_pending_or_sent_or_in_progress(final DistributionInnerMother.Builder diBuilder) {
    final var now = OffsetDateTime.now();
    final var updatedBy = "triggeredBy";
    final var distributionInner = diBuilder.build();
    final var command = new CloseDistributionInnerCommand(distributionInner.getId().value(), updatedBy);
    final var expectedDistributionInner = diBuilder
        .status(DistributionInnerStatus.CLOSED)
        .audit(diBuilder.build().audit().update(updatedBy, now))
        .build();

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(distributionInner)).when(this.distributionInnerRepository).findById(any());

    this.sut.doHandle(command);

    verify(this.eventBus).send(List.of(new DistributionInnerUnifiedEvent(expectedDistributionInner, EventType.CLOSED)));
    verify(this.distributionInnerRepository).save(expectedDistributionInner);
  }

  @ParameterizedTest
  @MethodSource("incorrectDistributions")
  void should_fail_when_distribution_inner_different_from_pending_or_sent(final Optional<DistributionInner> distribution) {
    final var command = Instancio.create(CloseDistributionInnerCommand.class);
    doReturn(distribution).when(this.distributionInnerRepository).findById(any());

    Assertions.assertThatThrownBy(() -> this.sut.execute(command)).isInstanceOf(ErrorException.class);

    verify(this.distributionInnerRepository, never()).save(any());
    verify(this.eventBus, never()).send(any());
  }

  private static Stream<Arguments> correctDistributions() {
    return Stream.of(
        Arguments.of(DistributionInnerMother.inProgress()),
        Arguments.of(DistributionInnerMother.pending()),
        Arguments.of(DistributionInnerMother.sent()));
  }

  private static Stream<Arguments> incorrectDistributions() {
    return Stream.of(
        Arguments.of(Optional.of(DistributionInnerMother.canceled().build())),
        Arguments.of(Optional.of(DistributionInnerMother.nonDistributable().build())),
        Arguments.of(Optional.of(DistributionInnerMother.closed().build())));
  }

}
