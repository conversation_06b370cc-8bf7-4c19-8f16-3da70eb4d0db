package com.inditex.icdmdemg.application.distributionnominated.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateVariantGroupInput;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateVariantGroupInput.VariantGroup;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@SpringBootTest(classes = UpdateDistributionNominatedProductVariantCommandHandlerTest.Config.class)
class UpdateDistributionNominatedProductVariantCommandHandlerTest {

  @MockitoBean
  private DistributionNominatedRepository distributionNominatedRepository;

  @MockitoSpyBean
  private ClockUtils clockUtils;

  @MockitoSpyBean
  private Transaction transaction;

  @MockitoBean
  private EventBus eventBus;

  @Autowired
  private UpdateDistributionNominatedProductVariantCommandHandler handler;

  @Captor
  ArgumentCaptor<DistributionNominated> savedDistributionNominatedCaptor;

  @Captor
  ArgumentCaptor<java.util.Collection<com.inditex.iopcmmnt.ddd.core.DomainEvent<?>>> sentDistEventsCaptor;

  private static final String TARGET_VARIANT_GROUP = "COLOR_GROUP";

  private static final String NOT_TARGET_VARIANT_GROUP = "ANOTHER_NAME_GROUP";

  private static final String TRIGGERED_BY = "triggered_by";

  private static final DistributionNominated DISTRIBUTION_NOMINATED_1 = Instancio.of(DistributionNominated.class).create();

  private static final DistributionNominated DISTRIBUTION_NOMINATED_2 = Instancio.of(DistributionNominated.class).create();

  private static final VariantGroup VARIANT_GROUP_1 =
      new VariantGroup(UUID.randomUUID(), DISTRIBUTION_NOMINATED_1.productVariantGroupId().value(), TARGET_VARIANT_GROUP);

  private static final VariantGroup VARIANT_GROUP_2 = new VariantGroup(UUID.randomUUID(), DISTRIBUTION_NOMINATED_2.productVariantGroupId()
      .value(), TARGET_VARIANT_GROUP);

  private static final VariantGroup VARIANT_GROUP_5 = new VariantGroup(UUID.randomUUID(), UUID.randomUUID(), NOT_TARGET_VARIANT_GROUP);

  private static final List<VariantGroup> MERGED_VARIANT_GROUPS =
      List.of(VARIANT_GROUP_1, VARIANT_GROUP_2, VARIANT_GROUP_5);

  private static final List<DistributionNominated> DISTRIBUTION_NOMINATED_LIST =
      List.of(DISTRIBUTION_NOMINATED_1, DISTRIBUTION_NOMINATED_2);

  @BeforeEach
  void setUp() {
    reset(this.distributionNominatedRepository, this.clockUtils, this.transaction, this.eventBus);
  }

  @Test
  void should_filter_color_group_variants_and_update_distributions() {
    final var mockedNow = OffsetDateTime.MIN;
    doReturn(mockedNow).when(this.clockUtils).getCurrentOffsetDateTime();

    final var command = new UpdateDistributionNominatedProductVariantCommand(
        new UpdateVariantGroupInput(MERGED_VARIANT_GROUPS), TRIGGERED_BY);

    final List<ProductVariantGroupId> lstVariantNominated = List.of(
        new ProductVariantGroupId(VARIANT_GROUP_1.variantGroupSourceId()),
        new ProductVariantGroupId(VARIANT_GROUP_2.variantGroupSourceId()));

    doReturn(DISTRIBUTION_NOMINATED_LIST).when(this.distributionNominatedRepository)
        .findByProductVariantGroupIds(argThat(l -> l.containsAll(lstVariantNominated)));

    this.handler.doHandle(command);

    verify(this.distributionNominatedRepository, times(2)).save(this.savedDistributionNominatedCaptor.capture());

    final List<ProductVariantGroupId> resultNominated = this.savedDistributionNominatedCaptor.getAllValues().stream()
        .map(DistributionNominated::productVariantGroupId)
        .toList();
    final List<ProductVariantGroupId> expectedNominated = List.of(
        new ProductVariantGroupId(VARIANT_GROUP_1.variantGroupId()),
        new ProductVariantGroupId(VARIANT_GROUP_2.variantGroupId()));

    assertThat(resultNominated).containsExactlyInAnyOrderElementsOf(expectedNominated);

    verify(this.eventBus, times(2)).send(this.sentDistEventsCaptor.capture());

    assertThat(this.sentDistEventsCaptor.getAllValues()).hasSize(2);

  }

  @Test
  void should_do_nothing_if_no_variant_groups_with_name_color_group() {
    final VariantGroup VARIANT_GROUP_3 = new VariantGroup(UUID.randomUUID(), UUID.randomUUID(), NOT_TARGET_VARIANT_GROUP);
    final var command = new UpdateDistributionNominatedProductVariantCommand(
        new UpdateVariantGroupInput(List.of(VARIANT_GROUP_3)), TRIGGERED_BY);

    this.handler.doHandle(command);

    verify(this.distributionNominatedRepository, times(0)).findByProductVariantGroupIds(anyList());
    verify(this.distributionNominatedRepository, times(0)).save(this.savedDistributionNominatedCaptor.capture());

    verify(this.eventBus, times(0)).send(this.sentDistEventsCaptor.capture());
  }

  @Configuration
  @Import({ClockUtils.class, Transaction.class, UpdateDistributionNominatedProductVariantCommandHandler.class})
  public static class Config {

  }
}
