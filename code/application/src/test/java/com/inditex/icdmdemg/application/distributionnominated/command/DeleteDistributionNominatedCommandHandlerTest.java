package com.inditex.icdmdemg.application.distributionnominated.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycleChangePendingQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

@TestInstance(Lifecycle.PER_CLASS)
class DeleteDistributionNominatedCommandHandlerTest {
  DistributionNominatedRepository distributionNominatedRepository;

  ClockUtils clockUtils;

  DeleteDistributionNominatedCommandHandler sut;

  private EventBus eventBus;

  @BeforeEach
  void setup() {
    final var transaction = new Transaction();
    this.clockUtils = mock(ClockUtils.class);
    this.distributionNominatedRepository = mock(DistributionNominatedRepository.class);
    this.eventBus = mock(EventBus.class);
    this.sut = new DeleteDistributionNominatedCommandHandler(transaction, this.clockUtils, this.distributionNominatedRepository,
        this.eventBus);
  }

  @AfterEach
  void resetMocks() {
    reset(this.clockUtils, this.distributionNominatedRepository);
  }

  @ParameterizedTest
  @MethodSource("incorrectDistributions")
  void should_throw_exception_when_status_is_incorrect_or_if_empty(final Optional<DistributionNominated> distribution) {
    final var command = Instancio.create(DeleteDistributionNominatedCommand.class);
    doReturn(distribution).when(this.distributionNominatedRepository).findById(any());

    assertThatThrownBy(() -> this.sut.execute(command))
        .isInstanceOf(ErrorException.class);

    verify(this.distributionNominatedRepository, never()).save(any());
    verify(this.eventBus, never()).send(any());
  }

  private static Stream<Arguments> incorrectDistributions() {
    return Stream.of(
        Arguments.of(Optional.empty()));
  }

  @Test
  void should_delete() {
    final var now = OffsetDateTime.now();
    final var command = Instancio.create(DeleteDistributionNominatedCommand.class);
    final var distributionNominated = DistributionNominatedMother.pending()
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .build();
    when(this.distributionNominatedRepository.findById(new Id(command.deleteNominatedRequest().distributionNominatedId())))
        .thenReturn(Optional.of(distributionNominated));
    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);

    final var result = this.sut.execute(command);

    verify(this.distributionNominatedRepository).save(result);
    assertThat(result.audit().updatedAt()).isEqualTo(now);
    assertThat(result.audit().deletedAt()).isEqualTo(now);
    assertThat(result.audit().updatedBy()).isEqualTo(command.deleteNominatedRequest().triggeredBy());
    verify(this.eventBus).send(List.of(new DistributionNominatedUnifiedEvent(result, EventType.DELETED)));
  }

  @Test
  void should_delete_when_is_closed() {
    final var now = OffsetDateTime.now();
    final var command = Instancio.create(DeleteDistributionNominatedCommand.class);
    final var distributionNominated = DistributionNominatedMother.closed()
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO)).build();
    when(this.distributionNominatedRepository.findById(new Id(command.deleteNominatedRequest().distributionNominatedId())))
        .thenReturn(Optional.of(distributionNominated));
    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);

    final var result = this.sut.execute(command);

    verify(this.distributionNominatedRepository).save(result);
    assertThat(result.audit().updatedAt()).isEqualTo(now);
    assertThat(result.audit().deletedAt()).isEqualTo(now);
    assertThat(result.audit().updatedBy()).isEqualTo(command.deleteNominatedRequest().triggeredBy());
    verify(this.eventBus).send(List.of(new DistributionNominatedUnifiedEvent(result, EventType.DELETED)));
  }
}
