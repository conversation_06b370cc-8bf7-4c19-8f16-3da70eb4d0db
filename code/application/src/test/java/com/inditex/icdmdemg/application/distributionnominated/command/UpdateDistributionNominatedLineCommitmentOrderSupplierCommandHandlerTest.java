package com.inditex.icdmdemg.application.distributionnominated.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.math.BigDecimal;
import java.time.Clock;
import java.time.ZoneOffset;
import java.util.Collection;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseCollection;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderIdAndOrderLineId;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.Id;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLines;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedLineMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = UpdateDistributionNominatedLineCommitmentOrderSupplierCommandHandlerTest.Config.class)
class UpdateDistributionNominatedLineCommitmentOrderSupplierCommandHandlerTest {

  @MockitoBean
  private DistributionNominatedRepository distributionNominatedRepository;

  @MockitoBean
  private MaterialCommitmentUseRepository materialCommitmentUseRepository;

  @Autowired
  private Transaction transaction;

  @Autowired
  private ClockUtils clockUtils;

  @MockitoBean
  private EventBus eventBus;

  @Captor
  ArgumentCaptor<Collection<DomainEvent<?>>> eventsCaptor;

  @Autowired
  private UpdateDistributionNominatedLineCommitmentOrderSupplierCommandHandler sut;

  @Test
  void shouldDoNothingWhenNoCommitment() {
    final var dateTime = OffsetDateTimeMother.fromInteger(1);
    this.clockUtils.setClock(Clock.fixed(dateTime.toInstant(), ZoneOffset.UTC));

    final var command = new UpdateDistributionNominatedLineCommitmentOrderSupplierCommand(
        UuidMother.fromInteger(101).toString(),
        UuidMother.fromInteger(201).toString(),
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(301).toString()),
        "test");

    doReturn(MaterialCommitmentUseCollection.empty()).when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(List.of(MaterialCommitmentUseOrderIdAndOrderLineId.of(command.orderId(), command.orderLineId())));

    this.sut.doHandle(command);

    verifyNoInteractions(this.distributionNominatedRepository, this.eventBus);
  }

  @Test
  void shouldUpdateSupplier() {
    final var dateTime = OffsetDateTimeMother.fromInteger(1);
    this.clockUtils.setClock(Clock.fixed(dateTime.toInstant(), ZoneOffset.UTC));

    final var orderId = UuidMother.fromInteger(101);
    final var orderLineId = UuidMother.fromInteger(201);

    final var command = new UpdateDistributionNominatedLineCommitmentOrderSupplierCommand(
        orderId.toString(),
        orderLineId.toString(),
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(301).toString()),
        "test");

    final var mcu = MaterialCommitmentUseMother.with(orderId, orderLineId, UUID.randomUUID(), BigDecimal.TEN);
    doReturn(new MaterialCommitmentUseCollection(List.of(mcu))).when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(anyList());

    final var line1 = DistributionNominatedLineMother.distributed().commitmentOrder(new CommitmentOrder(new Id(orderId),
        new LineId(orderLineId), new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()))))
        .build();
    final var line2 =
        DistributionNominatedLineMother.distributed().commitmentOrder(new CommitmentOrder(new Id(UUID.randomUUID()),
            new LineId(UUID.randomUUID()), new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()))))
            .build();
    final var distribution = DistributionNominatedMother.inProgress()
        .lines(new DistributionNominatedLines(List.of(
            line1,
            line2)))
        .build();
    doReturn(List.of(distribution)).when(this.distributionNominatedRepository)
        .findByCommitmentOrderIdAndOrderLineId(new Id(orderId), new LineId(orderLineId));

    this.sut.doHandle(command);

    final var updated = DistributionNominatedMother.of(distribution)
        .lines(new DistributionNominatedLines(
            List.of(
                line1.updateCommitmentSupplierId(new SupplierId(mcu.getServiceLocalizationId().value()), dateTime),
                line2)))
        .build();
    verify(this.distributionNominatedRepository).save(updated);
    verify(this.eventBus).send(this.eventsCaptor.capture());

    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(DistributionNominatedUnifiedEvent.class::isInstance)
        .allMatch(domainEvent -> domainEvent.eventType().equals(EventType.UPDATED.value));
  }

  @Test
  void shouldNotEmitEventIfNoUpdateSupplier() {
    final var dateTime = OffsetDateTimeMother.fromInteger(1);
    this.clockUtils.setClock(Clock.fixed(dateTime.toInstant(), ZoneOffset.UTC));

    final var orderId = UuidMother.fromInteger(101);
    final var orderLineId = UuidMother.fromInteger(201);

    final var command = new UpdateDistributionNominatedLineCommitmentOrderSupplierCommand(
        orderId.toString(),
        orderLineId.toString(),
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(301).toString()),
        "test");

    final var mcu = MaterialCommitmentUseMother.with(orderId, orderLineId, UUID.randomUUID(), BigDecimal.TEN);
    doReturn(new MaterialCommitmentUseCollection(List.of(mcu))).when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(anyList());

    final var line1 = DistributionNominatedLineMother.distributed().commitmentOrder(new CommitmentOrder(new Id(orderId),
        new LineId(orderLineId), new SupplierId(mcu.getServiceLocalizationId().value())))
        .build();
    final var line2 =
        DistributionNominatedLineMother.distributed().commitmentOrder(new CommitmentOrder(new Id(UUID.randomUUID()),
            new LineId(UUID.randomUUID()), new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()))))
            .build();
    final var distribution = DistributionNominatedMother.inProgress()
        .lines(new DistributionNominatedLines(List.of(
            line1,
            line2)))
        .build();
    doReturn(List.of(distribution)).when(this.distributionNominatedRepository)
        .findByCommitmentOrderIdAndOrderLineId(new Id(orderId), new LineId(orderLineId));

    this.sut.doHandle(command);

    verify(this.distributionNominatedRepository).save(distribution);
    verify(this.eventBus).send(this.eventsCaptor.capture());

    assertThat(this.eventsCaptor.getValue())
        .isEmpty();

  }

  @Configuration
  @Import({ClockUtils.class, UpdateDistributionNominatedLineCommitmentOrderSupplierCommandHandler.class, Transaction.class})
  public static class Config {

  }
}
