package com.inditex.icdmdemg.application.distributionsummary.query;

import static org.mockito.Mockito.doReturn;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.GetUsesAvailabilityRequest;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailability;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityResponse;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityTotalInnerQuantity;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityTotalNominatedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUseQuantitiesDTO;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.BudgetId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvisionRepository;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedTotalQuantityByUseDTO;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedTotalQuantityByUseDTO.TotalQuantity;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseInnerPendingByUseDTO;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseInnerPendingByUseDTO.Pending;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseInnerPendingByUseDTO.UseId;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseRepository;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.BudgetCycle;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.ProductReferenceId;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseInnerStockByUseDTO;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseInnerStockByUseDTO.Stock;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseRepository;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO;
import com.inditex.icdmdemg.provis.domain.use.UseDTO;
import com.inditex.icdmdemg.provis.domain.use.UseDTO.Assignable;
import com.inditex.icdmdemg.provis.domain.use.UseDTO.Id;
import com.inditex.icdmdemg.provis.domain.use.UseDTO.Name;
import com.inditex.icdmdemg.provis.domain.use.UseDTO.UsePurchaseType;
import com.inditex.icdmdemg.provis.domain.use.UsesRepository;
import com.inditex.icdmdemg.shared.utils.NumericUtils;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class GetUsesAvailabilityQueryHandlerTest {

  final UsesRepository usesRepository = Mockito.mock(UsesRepository.class);

  final OrderUseRepository orderUseRepository = Mockito.mock(OrderUseRepository.class);

  final ProductUseRepository productUseRepository = Mockito.mock(ProductUseRepository.class);

  final NominatedProvisionRepository nominatedProvisionRepository = Mockito.mock(NominatedProvisionRepository.class);

  final DistributionInnerRepository distributionInnerRepository = Mockito.mock(DistributionInnerRepository.class);

  final GetUsesAvailabilityQueryHandler sut = new GetUsesAvailabilityQueryHandler(
      this.usesRepository,
      this.orderUseRepository,
      this.productUseRepository,
      this.nominatedProvisionRepository,
      this.distributionInnerRepository);

  @Test
  void should_return_uses_availability_response_when_no_related_uses() {
    final UUID referenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();

    final var use1Id = new Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));

    doReturn(List.of()).when(this.usesRepository).findRelatedUsesByUseId(use1Id);
    doReturn(List.of()).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        List.of(), new BudgetCycle(budgetCycle));

    doReturn(List.of()).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ProductUseSummaryDTO.ReferenceId(referenceId), List.of(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    doReturn(List.of()).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            List.of(), new BudgetId(budgetCycle));

    doReturn(List.of()).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        List.of(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityQuery(
        new GetUsesAvailabilityRequest(
            referenceId,
            use1Id.value(),
            budgetCycle));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityResponse(
        List.of());

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

  @Test
  void should_return_uses_availability_response_when_no_nominated_provision() {
    final UUID referenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final Pending pendingUse1 = new Pending(BigDecimal.valueOf(100));
    final Pending pendingUse2 = new Pending(BigDecimal.valueOf(80));
    final Stock stockUse1 = new Stock(BigDecimal.valueOf(70));
    final Stock stockUse2 = new Stock(BigDecimal.valueOf(50));
    final RequestedQuantity requestedQuantityUse1 = new RequestedQuantity(BigDecimal.valueOf(30));
    final RequestedQuantity requestedQuantityUse2 = new RequestedQuantity(BigDecimal.valueOf(10));
    final DistributedQuantity distributedQuantityUse1 = new DistributedQuantity(BigDecimal.valueOf(25));
    final DistributedQuantity distributedQuantityUse2 = new DistributedQuantity(BigDecimal.valueOf(20));

    final var use1Id = new Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));
    final var use1 = new UseDTO(
        use1Id,
        new Name("use1"),
        new Assignable(true),
        UsePurchaseType.NOMINATED_INNER);

    final var use2Id = new Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba269"));
    final var use2 = new UseDTO(
        use2Id,
        new Name("use2"),
        new Assignable(false),
        UsePurchaseType.INNER);

    final var uses = List.of(use1, use2);
    final List<UUID> orderUseIdList = List.of(use1.id().value(), use2.id().value());
    doReturn(uses).when(this.usesRepository).findRelatedUsesByUseId(use1Id);

    final List<OrderUseInnerPendingByUseDTO> orderUses = List.of(
        new OrderUseInnerPendingByUseDTO(new UseId(use1Id.value()), pendingUse1),
        new OrderUseInnerPendingByUseDTO(new UseId(use2Id.value()), pendingUse2));
    doReturn(orderUses).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    final List<ProductUseInnerStockByUseDTO> innerProductUseSummaries = List.of(
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use1.id().value()), stockUse1),
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use2.id().value()), stockUse2));
    doReturn(innerProductUseSummaries).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ProductUseSummaryDTO.ReferenceId(referenceId), orderUseIdList.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    doReturn(List.of()).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            orderUseIdList.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO1 =
        new DistributionInnerUseQuantitiesDTO(
            new DistributionInner.UseId(use1.id().value()),
            new RequestedQuantity(requestedQuantityUse1.value()),
            new DistributedQuantity(distributedQuantityUse1.value())

        );

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO2 = new DistributionInnerUseQuantitiesDTO(
        new DistributionInner.UseId(use2.id().value()),
        new RequestedQuantity(requestedQuantityUse2.value()),
        new DistributedQuantity(distributedQuantityUse2.value()));

    final var distributionInnerUseQuantitiesDTOList = List.of(distributionInnerUseQuantitiesDTO1, distributionInnerUseQuantitiesDTO2);

    doReturn(distributionInnerUseQuantitiesDTOList).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        orderUseIdList.stream().map(DistributionInner.UseId::new).toList(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityQuery(
        new GetUsesAvailabilityRequest(
            referenceId,
            use1Id.value(),
            budgetCycle));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityResponse(
        List.of(
            new UsesAvailability(
                use1Id.value(),
                use1.name().value(),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                true,
                new UsesAvailabilityTotalNominatedQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(0))),
                new UsesAvailabilityTotalInnerQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(165)))),
            new UsesAvailability(
                use2Id.value(),
                use2.name().value(),
                List.of(UsePurchaseType.INNER.value()),
                false,
                new UsesAvailabilityTotalNominatedQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(0))),
                new UsesAvailabilityTotalInnerQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(140))))));

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

  @Test
  void should_return_uses_availability_response_when_no_order_use() {
    final UUID referenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final Stock stockUse1 = new Stock(BigDecimal.valueOf(70));
    final Stock stockUse2 = new Stock(BigDecimal.valueOf(50));
    final RequestedQuantity requestedQuantityUse1 = new RequestedQuantity(BigDecimal.valueOf(30));
    final RequestedQuantity requestedQuantityUse2 = new RequestedQuantity(BigDecimal.valueOf(10));
    final DistributedQuantity distributedQuantityUse1 = new DistributedQuantity(BigDecimal.valueOf(25));
    final DistributedQuantity distributedQuantityUse2 = new DistributedQuantity(BigDecimal.valueOf(20));
    final TotalQuantity totalQuantityUse1 = new TotalQuantity(BigDecimal.valueOf(75));
    final TotalQuantity totalQuantityUse2 = new TotalQuantity(BigDecimal.valueOf(60));

    final var use1Id = new Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));
    final var use1 = new UseDTO(
        use1Id,
        new Name("use1"),
        new Assignable(true),
        UsePurchaseType.NOMINATED_INNER);

    final var use2Id = new Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba269"));
    final var use2 = new UseDTO(
        use2Id,
        new Name("use2"),
        new Assignable(false),
        UsePurchaseType.INNER);

    final var uses = List.of(use1, use2);
    final List<UUID> orderUseIdList = List.of(use1.id().value(), use2.id().value());
    doReturn(uses).when(this.usesRepository).findRelatedUsesByUseId(use1Id);

    doReturn(List.of()).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    final List<ProductUseInnerStockByUseDTO> innerProductUseSummaries = List.of(
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use1.id().value()), stockUse1),
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use2.id().value()), stockUse2));
    doReturn(innerProductUseSummaries).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ProductUseSummaryDTO.ReferenceId(referenceId), orderUseIdList.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    final List<NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseDTOList = List.of(
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use1.id().value()),
            totalQuantityUse1),
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use2.id().value()),
            totalQuantityUse2));
    doReturn(nominatedTotalQuantityByUseDTOList).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            orderUseIdList.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO1 =
        new DistributionInnerUseQuantitiesDTO(
            new DistributionInner.UseId(use1.id().value()),
            new RequestedQuantity(requestedQuantityUse1.value()),
            new DistributedQuantity(distributedQuantityUse1.value())

        );

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO2 = new DistributionInnerUseQuantitiesDTO(
        new DistributionInner.UseId(use2.id().value()),
        new RequestedQuantity(requestedQuantityUse2.value()),
        new DistributedQuantity(distributedQuantityUse2.value()));

    final var distributionInnerUseQuantitiesDTOList = List.of(distributionInnerUseQuantitiesDTO1, distributionInnerUseQuantitiesDTO2);

    doReturn(distributionInnerUseQuantitiesDTOList).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        orderUseIdList.stream().map(DistributionInner.UseId::new).toList(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityQuery(
        new GetUsesAvailabilityRequest(
            referenceId,
            use1Id.value(),
            budgetCycle));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityResponse(
        List.of(
            new UsesAvailability(
                use1Id.value(),
                use1.name().value(),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                true,
                new UsesAvailabilityTotalNominatedQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(75))),
                new UsesAvailabilityTotalInnerQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(65)))),
            new UsesAvailability(
                use2Id.value(),
                use2.name().value(),
                List.of(UsePurchaseType.INNER.value()),
                false,
                new UsesAvailabilityTotalNominatedQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(60))),
                new UsesAvailabilityTotalInnerQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(60))))));

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

  @Test
  void should_return_uses_availability_response_when_no_product_use() {
    final UUID referenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final Pending pendingUse1 = new Pending(BigDecimal.valueOf(100));
    final Pending pendingUse2 = new Pending(BigDecimal.valueOf(80));
    final RequestedQuantity requestedQuantityUse1 = new RequestedQuantity(BigDecimal.valueOf(30));
    final RequestedQuantity requestedQuantityUse2 = new RequestedQuantity(BigDecimal.valueOf(10));
    final DistributedQuantity distributedQuantityUse1 = new DistributedQuantity(BigDecimal.valueOf(25));
    final DistributedQuantity distributedQuantityUse2 = new DistributedQuantity(BigDecimal.valueOf(20));
    final TotalQuantity totalQuantityUse1 = new TotalQuantity(BigDecimal.valueOf(75));
    final TotalQuantity totalQuantityUse2 = new TotalQuantity(BigDecimal.valueOf(60));

    final var use1Id = new Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));
    final var use1 = new UseDTO(
        use1Id,
        new Name("use1"),
        new Assignable(true),
        UsePurchaseType.NOMINATED_INNER);

    final var use2Id = new Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba269"));
    final var use2 = new UseDTO(
        use2Id,
        new Name("use2"),
        new Assignable(false),
        UsePurchaseType.INNER);

    final var uses = List.of(use1, use2);
    final List<UUID> orderUseIdList = List.of(use1.id().value(), use2.id().value());
    doReturn(uses).when(this.usesRepository).findRelatedUsesByUseId(use1Id);

    final List<OrderUseInnerPendingByUseDTO> orderUses = List.of(
        new OrderUseInnerPendingByUseDTO(new UseId(use1Id.value()), pendingUse1),
        new OrderUseInnerPendingByUseDTO(new UseId(use2Id.value()), pendingUse2));
    doReturn(orderUses).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    doReturn(List.of()).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ProductUseSummaryDTO.ReferenceId(referenceId), orderUseIdList.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    final List<NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseDTOList = List.of(
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use1.id().value()),
            totalQuantityUse1),
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use2.id().value()),
            totalQuantityUse2));
    doReturn(nominatedTotalQuantityByUseDTOList).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            orderUseIdList.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO1 =
        new DistributionInnerUseQuantitiesDTO(
            new DistributionInner.UseId(use1.id().value()),
            new RequestedQuantity(requestedQuantityUse1.value()),
            new DistributedQuantity(distributedQuantityUse1.value())

        );

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO2 = new DistributionInnerUseQuantitiesDTO(
        new DistributionInner.UseId(use2.id().value()),
        new RequestedQuantity(requestedQuantityUse2.value()),
        new DistributedQuantity(distributedQuantityUse2.value()));

    final var distributionInnerUseQuantitiesDTOList = List.of(distributionInnerUseQuantitiesDTO1, distributionInnerUseQuantitiesDTO2);

    doReturn(distributionInnerUseQuantitiesDTOList).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        orderUseIdList.stream().map(DistributionInner.UseId::new).toList(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityQuery(
        new GetUsesAvailabilityRequest(
            referenceId,
            use1Id.value(),
            budgetCycle));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityResponse(
        List.of(
            new UsesAvailability(
                use1Id.value(),
                use1.name().value(),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                true,
                new UsesAvailabilityTotalNominatedQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(75))),
                new UsesAvailabilityTotalInnerQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(95)))),
            new UsesAvailability(
                use2Id.value(),
                use2.name().value(),
                List.of(UsePurchaseType.INNER.value()),
                false,
                new UsesAvailabilityTotalNominatedQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(60))),
                new UsesAvailabilityTotalInnerQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(90))))));

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

  @Test
  void should_return_uses_availability_response_when_no_distribution_inner() {
    final UUID referenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final Pending pendingUse1 = new Pending(BigDecimal.valueOf(100));
    final Pending pendingUse2 = new Pending(BigDecimal.valueOf(80));
    final Stock stockUse1 = new Stock(BigDecimal.valueOf(70));
    final Stock stockUse2 = new Stock(BigDecimal.valueOf(50));
    final TotalQuantity totalQuantityUse1 = new TotalQuantity(BigDecimal.valueOf(75));
    final TotalQuantity totalQuantityUse2 = new TotalQuantity(BigDecimal.valueOf(60));

    final var use1Id = new Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));
    final var use1 = new UseDTO(
        use1Id,
        new Name("use1"),
        new Assignable(true),
        UsePurchaseType.NOMINATED_INNER);

    final var use2Id = new Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba269"));
    final var use2 = new UseDTO(
        use2Id,
        new Name("use2"),
        new Assignable(false),
        UsePurchaseType.INNER);

    final var uses = List.of(use1, use2);
    final List<UUID> orderUseIdList = List.of(use1.id().value(), use2.id().value());
    doReturn(uses).when(this.usesRepository).findRelatedUsesByUseId(use1Id);

    final List<OrderUseInnerPendingByUseDTO> orderUses = List.of(
        new OrderUseInnerPendingByUseDTO(new UseId(use1Id.value()), pendingUse1),
        new OrderUseInnerPendingByUseDTO(new UseId(use2Id.value()), pendingUse2));
    doReturn(orderUses).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    final List<ProductUseInnerStockByUseDTO> innerProductUseSummaries = List.of(
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use1.id().value()), stockUse1),
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use2.id().value()), stockUse2));
    doReturn(innerProductUseSummaries).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ProductUseSummaryDTO.ReferenceId(referenceId), orderUseIdList.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    final List<NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseDTOList = List.of(
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use1.id().value()),
            totalQuantityUse1),
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use2.id().value()),
            totalQuantityUse2));
    doReturn(nominatedTotalQuantityByUseDTOList).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            orderUseIdList.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    doReturn(List.of()).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        orderUseIdList.stream().map(DistributionInner.UseId::new).toList(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityQuery(
        new GetUsesAvailabilityRequest(
            referenceId,
            use1Id.value(),
            budgetCycle));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityResponse(
        List.of(
            new UsesAvailability(
                use1Id.value(),
                use1.name().value(),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                true,
                new UsesAvailabilityTotalNominatedQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(75))),
                new UsesAvailabilityTotalInnerQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(170)))),
            new UsesAvailability(
                use2Id.value(),
                use2.name().value(),
                List.of(UsePurchaseType.INNER.value()),
                false,
                new UsesAvailabilityTotalNominatedQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(60))),
                new UsesAvailabilityTotalInnerQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(130))))));

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

  @Test
  void should_return_uses_availability_response_when_no_order_use_and_no_product_use_and_no_distribution_inner() {
    final UUID referenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final TotalQuantity totalQuantityUse1 = new TotalQuantity(BigDecimal.valueOf(75));
    final TotalQuantity totalQuantityUse2 = new TotalQuantity(BigDecimal.valueOf(60));

    final var use1Id = new Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));
    final var use1 = new UseDTO(
        use1Id,
        new Name("use1"),
        new Assignable(true),
        UsePurchaseType.NOMINATED_INNER);

    final var use2Id = new Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba269"));
    final var use2 = new UseDTO(
        use2Id,
        new Name("use2"),
        new Assignable(false),
        UsePurchaseType.INNER);

    final var uses = List.of(use1, use2);
    final List<UUID> orderUseIdList = List.of(use1.id().value(), use2.id().value());
    doReturn(uses).when(this.usesRepository).findRelatedUsesByUseId(use1Id);

    doReturn(List.of()).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    doReturn(List.of()).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ProductUseSummaryDTO.ReferenceId(referenceId), orderUseIdList.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    final List<NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseDTOList = List.of(
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use1.id().value()),
            totalQuantityUse1),
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use2.id().value()),
            totalQuantityUse2));
    doReturn(nominatedTotalQuantityByUseDTOList).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            orderUseIdList.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    doReturn(List.of()).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        orderUseIdList.stream().map(DistributionInner.UseId::new).toList(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityQuery(
        new GetUsesAvailabilityRequest(
            referenceId,
            use1Id.value(),
            budgetCycle));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityResponse(
        List.of(
            new UsesAvailability(
                use1Id.value(),
                use1.name().value(),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                true,
                new UsesAvailabilityTotalNominatedQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(75))),
                new UsesAvailabilityTotalInnerQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(0)))),
            new UsesAvailability(
                use2Id.value(),
                use2.name().value(),
                List.of(UsePurchaseType.INNER.value()),
                false,
                new UsesAvailabilityTotalNominatedQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(60))),
                new UsesAvailabilityTotalInnerQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(0))))));

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

  @Test
  void should_return_uses_availability_response() {
    final UUID referenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final Pending pendingUse1 = new Pending(BigDecimal.valueOf(100));
    final Pending pendingUse2 = new Pending(BigDecimal.valueOf(80));
    final Stock stockUse1 = new Stock(BigDecimal.valueOf(70));
    final Stock stockUse2 = new Stock(BigDecimal.valueOf(50));
    final RequestedQuantity requestedQuantityUse1 = new RequestedQuantity(BigDecimal.valueOf(30));
    final RequestedQuantity requestedQuantityUse2 = new RequestedQuantity(BigDecimal.valueOf(10));
    final DistributedQuantity distributedQuantityUse1 = new DistributedQuantity(BigDecimal.valueOf(25));
    final DistributedQuantity distributedQuantityUse2 = new DistributedQuantity(BigDecimal.valueOf(20));
    final TotalQuantity totalQuantityUse1 = new TotalQuantity(BigDecimal.valueOf(75));
    final TotalQuantity totalQuantityUse2 = new TotalQuantity(BigDecimal.valueOf(60));

    final var use1Id = new Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));
    final var use1 = new UseDTO(
        use1Id,
        new Name("use1"),
        new Assignable(true),
        UsePurchaseType.NOMINATED_INNER);

    final var use2Id = new Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba269"));
    final var use2 = new UseDTO(
        use2Id,
        new Name("use2"),
        new Assignable(false),
        UsePurchaseType.INNER);

    final var uses = List.of(use1, use2);
    final List<UUID> orderUseIdList = List.of(use1.id().value(), use2.id().value());
    doReturn(uses).when(this.usesRepository).findRelatedUsesByUseId(use1Id);

    final List<OrderUseInnerPendingByUseDTO> orderUses = List.of(
        new OrderUseInnerPendingByUseDTO(new UseId(use1Id.value()), pendingUse1),
        new OrderUseInnerPendingByUseDTO(new UseId(use2Id.value()), pendingUse2));
    doReturn(orderUses).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    final List<ProductUseInnerStockByUseDTO> innerProductUseSummaries = List.of(
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use1.id().value()), stockUse1),
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use2.id().value()), stockUse2));
    doReturn(innerProductUseSummaries).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ProductUseSummaryDTO.ReferenceId(referenceId), orderUseIdList.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    final List<NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseDTOList = List.of(
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use1.id().value()),
            totalQuantityUse1),
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use2.id().value()),
            totalQuantityUse2));
    doReturn(nominatedTotalQuantityByUseDTOList).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            orderUseIdList.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO1 =
        new DistributionInnerUseQuantitiesDTO(
            new DistributionInner.UseId(use1.id().value()),
            new RequestedQuantity(requestedQuantityUse1.value()),
            new DistributedQuantity(distributedQuantityUse1.value())

        );

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO2 = new DistributionInnerUseQuantitiesDTO(
        new DistributionInner.UseId(use2.id().value()),
        new RequestedQuantity(requestedQuantityUse2.value()),
        new DistributedQuantity(distributedQuantityUse2.value()));

    final var distributionInnerUseQuantitiesDTOList = List.of(distributionInnerUseQuantitiesDTO1, distributionInnerUseQuantitiesDTO2);

    doReturn(distributionInnerUseQuantitiesDTOList).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        orderUseIdList.stream().map(DistributionInner.UseId::new).toList(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityQuery(
        new GetUsesAvailabilityRequest(
            referenceId,
            use1Id.value(),
            budgetCycle));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityResponse(
        List.of(
            new UsesAvailability(
                use1Id.value(),
                use1.name().value(),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                true,
                new UsesAvailabilityTotalNominatedQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(75))),
                new UsesAvailabilityTotalInnerQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(165)))),
            new UsesAvailability(
                use2Id.value(),
                use2.name().value(),
                List.of(UsePurchaseType.INNER.value()),
                false,
                new UsesAvailabilityTotalNominatedQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(60))),
                new UsesAvailabilityTotalInnerQuantity(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(140))))));

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

}
