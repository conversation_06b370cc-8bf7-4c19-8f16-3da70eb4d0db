package com.inditex.icdmdemg.application.commitmentuse.command;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

import com.inditex.amigafwk.test.logcaptor.LogCaptor;
import com.inditex.amigafwk.test.logcaptor.LogCaptorExtension;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseCollection;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedLineMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.BudgetId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.DistributionDistributed;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.DistributionRequested;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Entered;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.LocalizationId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Ordered;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Pending;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.ReferenceId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Stock;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvisionCollection;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvisionRepository;
import com.inditex.icdmdemg.domain.nominatedprovision.mother.NominatedProvisionAuditMother;
import com.inditex.icdmdemg.domain.nominatedprovision.mother.NominatedProvisionMother;
import com.inditex.icdmdemg.domain.product.Product;
import com.inditex.icdmdemg.domain.product.ProductCollection;
import com.inditex.icdmdemg.domain.product.ProductRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductCampaign;
import com.inditex.icdmdemg.domain.product.entity.ProductId;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.shared.utils.ClockUtils;

import ch.qos.logback.classic.Level;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

@ExtendWith(LogCaptorExtension.class)
@RequiredArgsConstructor
class CalculateNominatedProvisionCommandHandlerTest {

  public static final String TRIGGERED_BY = "TRIGGERED_BY";

  private final MaterialCommitmentUseRepository materialCommitmentUseRepository = mock();

  private final NominatedProvisionRepository nominatedProvisionRepository = mock();

  private final DistributionNominatedRepository distributionNominatedRepository = mock();

  private final ProductRepository productRepository = mock();

  private final ClockUtils clockUtils = mock();

  private final LogCaptor logCaptor;

  private final CalculateNominatedProvisionCommandHandler handler = new CalculateNominatedProvisionCommandHandler(
      this.materialCommitmentUseRepository,
      this.nominatedProvisionRepository,
      this.distributionNominatedRepository,
      this.productRepository,
      this.clockUtils);

  @Test
  void should_create_nominated_provision() {
    final var now = OffsetDateTime.now();

    final var materialCommitmentUse = MaterialCommitmentUseMother.randomExisting();
    final var nominatedProvision = this.nominatedProvisionFromMaterialCommitment(materialCommitmentUse, now);
    final var product = this.productFromNominatedProvision(nominatedProvision);
    final var command = this.commandFromNominatedProvision(nominatedProvision);

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.materialCommitmentUseRepository.findByAnySharedRawMaterial(any()))
        .thenReturn(new MaterialCommitmentUseCollection(List.of(materialCommitmentUse)));
    when(this.distributionNominatedRepository.findByAnySharedRawMaterial(any())).thenReturn(List.of());
    when(this.nominatedProvisionRepository.findByAnySharedRawMaterial(any())).thenReturn(NominatedProvisionCollection.empty());
    when(this.productRepository.findByReferenceIds(any())).thenReturn(new ProductCollection(List.of(product)));

    this.handler.doHandle(command);

    verify(this.nominatedProvisionRepository).save(nominatedProvision);
    verify(this.distributionNominatedRepository).findByAnySharedRawMaterial(command.sharedRawMaterials());
    verify(this.materialCommitmentUseRepository)
        .findByAnySharedRawMaterial(this.sharedRawMaterialFromNominatedProvision(command.sharedRawMaterials()));
  }

  @Test
  void should_create_nominated_provision_with_distribution() {
    final var now = OffsetDateTime.now();
    final var materialCommitmentUse = MaterialCommitmentUseMother.randomExisting();
    final var nominatedProvision = this.nominatedProvisionFromMaterialCommitment(materialCommitmentUse, now);
    final var distributionNominated = this.distributionNominatedFromNominatedProvision(
        nominatedProvision,
        materialCommitmentUse.getOrderLine().orderId().value(),
        materialCommitmentUse.getOrderLine().orderLineId().value());
    final var product = this.productFromNominatedProvision(nominatedProvision);
    final var command = this.commandFromNominatedProvision(nominatedProvision);

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.materialCommitmentUseRepository.findByAnySharedRawMaterial(any()))
        .thenReturn(new MaterialCommitmentUseCollection(List.of(materialCommitmentUse)));
    when(this.distributionNominatedRepository.findByAnySharedRawMaterial(any())).thenReturn(List.of(distributionNominated));
    when(this.nominatedProvisionRepository.findByAnySharedRawMaterial(any())).thenReturn(NominatedProvisionCollection.empty());
    when(this.productRepository.findByReferenceIds(any())).thenReturn(new ProductCollection(List.of(product)));

    this.handler.doHandle(command);

    verify(this.nominatedProvisionRepository).save(nominatedProvision);
    verify(this.distributionNominatedRepository).findByAnySharedRawMaterial(command.sharedRawMaterials());
    verify(this.materialCommitmentUseRepository)
        .findByAnySharedRawMaterial(this.sharedRawMaterialFromNominatedProvision(command.sharedRawMaterials()));
  }

  @Test
  void should_not_fail_when_product_is_null() {
    final var now = OffsetDateTime.now();

    final var materialCommitmentUse = MaterialCommitmentUseMother.randomExisting();
    final var nominatedProvision = this.nominatedProvisionFromMaterialCommitment(materialCommitmentUse, now);
    final var command = this.commandFromNominatedProvision(nominatedProvision);

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.materialCommitmentUseRepository.findByAnySharedRawMaterial(any()))
        .thenReturn(new MaterialCommitmentUseCollection(List.of(materialCommitmentUse)));
    when(this.distributionNominatedRepository.findByAnySharedRawMaterial(any())).thenReturn(List.of());
    when(this.nominatedProvisionRepository.findByAnySharedRawMaterial(any())).thenReturn(NominatedProvisionCollection.empty());
    when(this.productRepository.findByReferenceIds(any())).thenReturn(new ProductCollection(List.of()));

    this.handler.doHandle(command);

    verify(this.nominatedProvisionRepository, never()).save(any());
    verify(this.distributionNominatedRepository).findByAnySharedRawMaterial(command.sharedRawMaterials());
    verify(this.materialCommitmentUseRepository)
        .findByAnySharedRawMaterial(this.sharedRawMaterialFromNominatedProvision(command.sharedRawMaterials()));
    this.logCaptor
        .verifyMessagesContaining(String.format(
            "NominatedProvision [referenceId: %s, useId: %s, budgetId: %s]  could not be created because product with referenceId does "
                + "not exist",
            nominatedProvision.referenceId().value(), nominatedProvision.useId().value(), nominatedProvision.budgetId().value()))
        .withLevel(Level.ERROR)
        .areExactly(1);
  }

  private NominatedProvision nominatedProvisionFromMaterialCommitment(MaterialCommitmentUse materialCommitmentUse, OffsetDateTime now) {
    return NominatedProvisionMother.random()
        .referenceId(new ReferenceId(materialCommitmentUse.getMaterialReferenceId().value()))
        .useId(new NominatedProvision.UseId(materialCommitmentUse.getUseId().value()))
        .budgetId(new BudgetId(materialCommitmentUse.getOrderLine().budgetId().value()))
        .localizationId(new LocalizationId(materialCommitmentUse.getServiceLocalizationId().value()))
        .ordered(new Ordered(materialCommitmentUse.getQuantity().value()))
        .stock(new Stock(materialCommitmentUse.getQuantity().value()))
        .entered(new Entered(materialCommitmentUse.getQuantity().value()))
        .pending(new Pending(BigDecimal.ZERO))
        .distributionRequested(new DistributionRequested(BigDecimal.ZERO))
        .distributed(new DistributionDistributed(BigDecimal.ZERO))
        .audit(NominatedProvisionAuditMother.created(now, TRIGGERED_BY).build())
        .build();
  }

  private Product productFromNominatedProvision(NominatedProvision nominatedProvision) {
    return ProductMother.created()
        .referenceId(new ProductReferenceId(nominatedProvision.referenceId().value()))
        .productId(new ProductId(nominatedProvision.productId().value()))
        .campaignId(new ProductCampaign(nominatedProvision.budgetId().value()))
        .build();
  }

  private List<MaterialCommitmentUseSharedRawMaterial> sharedRawMaterialFromNominatedProvision(
      List<SharedRawMaterialNominated> nominatedProvision) {
    return List.of(MaterialCommitmentUseSharedRawMaterial.of(nominatedProvision.getFirst()));
  }

  private CalculateNominatedProvisionCommand commandFromNominatedProvision(NominatedProvision nominatedProvision) {
    return new CalculateNominatedProvisionCommand(
        List.of(new SharedRawMaterialNominated(
            UUID.fromString(nominatedProvision.referenceId().value()),
            UUID.fromString(nominatedProvision.useId().value()),
            nominatedProvision.budgetId().value())),
        nominatedProvision.audit().updatedBy().value());
  }

  private DistributionNominated distributionNominatedFromNominatedProvision(
      NominatedProvision nominatedProvision,
      String orderId,
      String orderLineId) {
    return DistributionNominatedMother.distributed()
        .budgetCycle(new BudgetCycle(nominatedProvision.budgetId().value()))
        .referenceId(new DistributionNominated.ReferenceId(UUID.fromString(nominatedProvision.referenceId().value())))
        .useId(new DistributionNominated.UseId(UUID.fromString(nominatedProvision.useId().value())))
        .lines(List.of(
            DistributionNominatedLineMother.distributed(new DistributedQuantity(nominatedProvision.distributionRequested().value()))
                .requestedQuantity(new RequestedQuantity(nominatedProvision.distributionRequested().value()))
                .commitmentOrder(CommitmentOrder.builder()
                    .id(new CommitmentOrder.Id(UUID.fromString(orderId)))
                    .lineId(new CommitmentOrder.LineId(UUID.fromString(orderLineId)))
                    .supplierId(new SupplierId(UUID.randomUUID().toString())).build())
                .build()))
        .build();
  }

}
