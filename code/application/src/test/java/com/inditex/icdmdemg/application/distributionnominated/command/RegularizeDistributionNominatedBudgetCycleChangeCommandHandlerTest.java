package com.inditex.icdmdemg.application.distributionnominated.command;

import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.created;
import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.kept;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.inditex.icdmdemg.application.distributionnominated.service.DistributionNominatedLinesIdentifier;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjustedProvider;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse.MaterialCommitmentUseCompositeId;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseCollection;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLineId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycleChangePendingQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.UseId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.Id;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLines;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedLineMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Line;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidGeneratorMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = RegularizeDistributionNominatedBudgetCycleChangeCommandHandlerTest.Config.class)
class RegularizeDistributionNominatedBudgetCycleChangeCommandHandlerTest {

  @MockitoBean
  private DistributionNominatedRepository distributionNominatedRepository;

  @MockitoBean
  private MaterialCommitmentUseRepository materialCommitmentUseRepository;

  @MockitoBean
  private CommitmentAdjustedProvider commitmentAdjustedProvider;

  @MockitoBean
  private DistributionNominatedLinesIdentifier linesIdentifier;

  @MockitoSpyBean
  private ClockUtils clockUtils;

  @MockitoSpyBean
  private Transaction transaction;

  @MockitoBean
  private EventBus eventBus;

  @Captor
  ArgumentCaptor<Collection<DomainEvent<?>>> eventsCaptor;

  @Autowired
  private RegularizeDistributionNominatedBudgetCycleChangeCommandHandler sut;

  @Test
  void should_regularize_lines_when_commitment_full_quantity() {
    final var rootId = UuidMother.fromInteger(901);
    final var useId = UuidMother.fromInteger(101);
    final var referenceId = UuidMother.fromInteger(201);
    final var budgetCycle = UuidMother.fromInteger(301);
    final var commitmentOrderId = UuidMother.fromInteger(401);
    final var commitmentOrderLineId = UuidMother.fromInteger(501);
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601));
    final var creationDate = OffsetDateTimeMother.fromInteger(1);
    final var now = OffsetDateTimeMother.fromInteger(10);
    final var commitmentOrderOld = new CommitmentOrder(new Id(UuidMother.fromInteger(899)), new LineId(UuidMother.fromInteger(898)),
        new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(897))));
    final var commitmentOrder = new CommitmentOrder(new Id(commitmentOrderId), new LineId(commitmentOrderLineId),
        new SupplierId(commitmentOrderSupplierId));

    final var line =
        DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(0), creationDate)
            .commitmentOrder(commitmentOrderOld)
            .build();
    final var nominated = DistributionNominatedMother.inProgress()
        .id(new DistributionNominated.Id(rootId))
        .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(1000)))
        .theoreticalQuantity(new DistributionNominated.TheoreticalQuantity(BigDecimal.valueOf(1200)))
        .useId(new UseId(useId))
        .referenceId(new ReferenceId(referenceId))
        .budgetCycle(new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetCycle)))
        .lines(new ArrayList<>(List.of(line)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.valueOf(1000)))
        .audit(CompleteAudit.create("test", creationDate))
        .build();

    final var commitment = CommitmentAdjusted.of(
        MaterialCommitmentUseMother.withCompositeIdAndQuantityAndBudgetCycleChange(commitmentOrderId, commitmentOrderLineId, budgetCycle,
            referenceId, useId,
            BigDecimal.valueOf(1500), commitmentOrderSupplierId),
        BigDecimal.valueOf(1000));
    final var commitmentOld = CommitmentAdjusted.of(
        MaterialCommitmentUseMother.withCompositeIdAndQuantity(commitmentOrderOld
            .id().value(),
            commitmentOrderOld.lineId()
                .value(),
            UuidMother.fromInteger(120),
            referenceId, useId,
            BigDecimal.valueOf(1500), commitmentOrderOld
                .supplierId().value()),
        BigDecimal.valueOf(1000));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(List.of(nominated))
        .when(this.distributionNominatedRepository)
        .findByAnySharedRawMaterial(List.of(nominated.sharedRawMaterial()));
    doReturn(List.of(commitment, commitmentOld)).when(this.commitmentAdjustedProvider).findByAnySharedRawMaterial(
        List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())));

    final var adjustedLine1 = created(new Line(new DistributionNominated.Id(rootId), commitmentOrder, new ReferenceId(referenceId),
        new ReferenceId(referenceId), NumericUtils.roundUpScale2(BigDecimal.valueOf(1000)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.empty()));
    final var adjustedLine2 = kept(new Line(new DistributionNominated.Id(rootId), commitmentOrderOld, new ReferenceId(referenceId),
        new ReferenceId(referenceId), NumericUtils.roundUpScale2(BigDecimal.valueOf(0)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.of(creationDate)));

    final var expectedLine =
        DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(1000), creationDate)
            .commitmentOrder(commitmentOrder)
            .build();
    final var expectedDN = DistributionNominatedMother.of(nominated)
        .lines(new DistributionNominatedLines(List.of(expectedLine, line)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .audit(nominated.audit().update("test", now))
        .build();

    doReturn(Map.of(new DistributionNominated.Id(rootId), List.of(expectedLine, line)))
        .when(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine1, adjustedLine2), now);

    final var mcuUsed = commitment.materialCommitmentUse();
    final var mcuUsedInResultingLineId = MaterialCommitmentUseCompositeId.of(nominated.sharedRawMaterial(),
        new MaterialCommitmentUseOrderId(expectedLine.commitmentOrder().id().value().toString()),
        new MaterialCommitmentUseOrderLineId(expectedLine.commitmentOrder().lineId().value().toString()));
    doReturn(new MaterialCommitmentUseCollection(List.of(mcuUsed)))
        .when(this.materialCommitmentUseRepository)
        .findByAnyCompositeId(List.of(mcuUsedInResultingLineId));
    this.sut.doHandle(new RegularizeDistributionNominatedBudgetCycleChangeCommand(List.of(nominated.sharedRawMaterial()), "test"));

    verify(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine1, adjustedLine2), now);
    verify(this.distributionNominatedRepository).save(expectedDN);
    verify(this.materialCommitmentUseRepository).saveAll(List.of(mcuUsed.executeBudgetCycleChange(now)));
    verify(this.eventBus).send(this.eventsCaptor.capture());
    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(DistributionNominatedUnifiedEvent.class::isInstance)
        .allMatch(domainEvent -> domainEvent.eventType().equals(EventType.UPDATED.value));
  }

  @Test
  void should_only_do_first_regularization() {
    final var rootId = UuidMother.fromInteger(901);
    final var useId = UuidMother.fromInteger(101);
    final var referenceId = UuidMother.fromInteger(201);
    final var budgetCycle = UuidMother.fromInteger(301);
    final var commitmentOrderId = UuidMother.fromInteger(401);
    final var commitmentOrderLineId = UuidMother.fromInteger(501);
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601));
    final var creationDate = OffsetDateTimeMother.fromInteger(1);
    final var now = OffsetDateTimeMother.fromInteger(10);
    final var commitmentOrderOld = new CommitmentOrder(new Id(UuidMother.fromInteger(899)), new LineId(UuidMother.fromInteger(898)),
        new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(897))));
    final var commitmentOrder = new CommitmentOrder(new Id(commitmentOrderId), new LineId(commitmentOrderLineId),
        new SupplierId(commitmentOrderSupplierId));

    final var line =
        DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(0), creationDate)
            .commitmentOrder(commitmentOrderOld)
            .build();
    final var nominated = DistributionNominatedMother.inProgress()
        .id(new DistributionNominated.Id(rootId))
        .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(1000)))
        .theoreticalQuantity(new DistributionNominated.TheoreticalQuantity(BigDecimal.valueOf(1200)))
        .useId(new UseId(useId))
        .referenceId(new ReferenceId(referenceId))
        .budgetCycle(new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetCycle)))
        .lines(new ArrayList<>(List.of(line)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.valueOf(1000)))
        .audit(CompleteAudit.create("test", creationDate))
        .build();

    final var commitment = CommitmentAdjusted.of(
        MaterialCommitmentUseMother.withCompositeIdAndQuantityAndBudgetCycleChange(commitmentOrderId, commitmentOrderLineId, budgetCycle,
            referenceId, useId,
            BigDecimal.valueOf(1500), commitmentOrderSupplierId),
        BigDecimal.valueOf(500));
    final var commitmentOld = CommitmentAdjusted.of(
        MaterialCommitmentUseMother.withCompositeIdAndQuantity(commitmentOrderOld
            .id().value(),
            commitmentOrderOld.lineId()
                .value(),
            UuidMother.fromInteger(120),
            referenceId, useId,
            BigDecimal.valueOf(1500), commitmentOrderOld
                .supplierId().value()),
        BigDecimal.valueOf(1000));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(List.of(nominated))
        .when(this.distributionNominatedRepository)
        .findByAnySharedRawMaterial(List.of(nominated.sharedRawMaterial()));
    doReturn(List.of(commitment, commitmentOld)).when(this.commitmentAdjustedProvider).findByAnySharedRawMaterial(
        List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())));

    final var adjustedLine1 = created(new Line(new DistributionNominated.Id(rootId), commitmentOrder, new ReferenceId(referenceId),
        new ReferenceId(referenceId), NumericUtils.roundUpScale2(BigDecimal.valueOf(500)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.empty()));
    final var adjustedLine2 = kept(new Line(new DistributionNominated.Id(rootId), commitmentOrderOld, new ReferenceId(referenceId),
        new ReferenceId(referenceId), NumericUtils.roundUpScale2(BigDecimal.valueOf(0)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.of(creationDate)));

    final var expectedLine =
        DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(500), creationDate)
            .commitmentOrder(commitmentOrder)
            .build();
    final var expectedDN = DistributionNominatedMother.of(nominated)
        .lines(new DistributionNominatedLines(List.of(expectedLine, line)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.valueOf(500)))
        .audit(nominated.audit().update("test", now))
        .build();

    doReturn(Map.of(new DistributionNominated.Id(rootId), List.of(expectedLine, line)))
        .when(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine1, adjustedLine2), now);

    final var mcuUsed = commitment.materialCommitmentUse();
    final var mcuUsedInResultingLineId = MaterialCommitmentUseCompositeId.of(nominated.sharedRawMaterial(),
        new MaterialCommitmentUseOrderId(expectedLine.commitmentOrder().id().value().toString()),
        new MaterialCommitmentUseOrderLineId(expectedLine.commitmentOrder().lineId().value().toString()));
    doReturn(new MaterialCommitmentUseCollection(List.of(mcuUsed)))
        .when(this.materialCommitmentUseRepository)
        .findByAnyCompositeId(List.of(mcuUsedInResultingLineId));
    this.sut.doHandle(new RegularizeDistributionNominatedBudgetCycleChangeCommand(List.of(nominated.sharedRawMaterial()), "test"));

    verify(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine1, adjustedLine2), now);
    verify(this.distributionNominatedRepository).save(expectedDN);
    verify(this.materialCommitmentUseRepository).saveAll(List.of(mcuUsed.executeBudgetCycleChange(now)));
    verify(this.eventBus).send(this.eventsCaptor.capture());
    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(DistributionNominatedUnifiedEvent.class::isInstance)
        .allMatch(domainEvent -> domainEvent.eventType().equals(EventType.UPDATED.value));
  }

  @Test
  void should_do_both_regularization() {
    final var rootId = UuidMother.fromInteger(901);
    final var useId = UuidMother.fromInteger(101);
    final var referenceId = UuidMother.fromInteger(201);
    final var budgetCycle = UuidMother.fromInteger(301);
    final var commitmentOrderId = UuidMother.fromInteger(401);
    final var commitmentOrderLineId = UuidMother.fromInteger(501);
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601));
    final var creationDate = OffsetDateTimeMother.fromInteger(1);
    final var now = OffsetDateTimeMother.fromInteger(10);
    final var commitmentOrderOld = new CommitmentOrder(new Id(UuidMother.fromInteger(899)), new LineId(UuidMother.fromInteger(898)),
        new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(897))));
    final var commitmentOrder = new CommitmentOrder(new Id(commitmentOrderId), new LineId(commitmentOrderLineId),
        new SupplierId(commitmentOrderSupplierId));
    final var commitmentOrder2 = new CommitmentOrder(new Id(UuidMother.fromInteger(123)), new LineId(UuidMother.fromInteger(124)),
        new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(125))));

    final var line =
        DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(0), creationDate)
            .commitmentOrder(commitmentOrderOld)
            .build();
    final var line2 = DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(500), creationDate)
        .commitmentOrder(commitmentOrder)
        .build();
    final var nominated = DistributionNominatedMother.inProgress()
        .id(new DistributionNominated.Id(rootId))
        .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(1000)))
        .theoreticalQuantity(new DistributionNominated.TheoreticalQuantity(BigDecimal.valueOf(1200)))
        .useId(new UseId(useId))
        .referenceId(new ReferenceId(referenceId))
        .budgetCycle(new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetCycle)))
        .lines(new ArrayList<>(List.of(line, line2)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.valueOf(500)))
        .audit(CompleteAudit.create("test", creationDate))
        .build();

    final var commitment = CommitmentAdjusted.of(
        MaterialCommitmentUseMother.withCompositeIdAndQuantityAndBudgetCycleChange(commitmentOrderId, commitmentOrderLineId, budgetCycle,
            referenceId, useId,
            BigDecimal.valueOf(1500), commitmentOrderSupplierId),
        BigDecimal.valueOf(500));
    final var commitment2 = CommitmentAdjusted.of(
        MaterialCommitmentUseMother.withCompositeIdAndQuantityAndBudgetCycleChange(commitmentOrder2.id().value(), commitmentOrder2.lineId()
            .value(),
            budgetCycle,
            referenceId, useId,
            BigDecimal.valueOf(1500), commitmentOrder2.supplierId().value()),
        BigDecimal.valueOf(500));
    final var commitmentOld = CommitmentAdjusted.of(
        MaterialCommitmentUseMother.withCompositeIdAndQuantity(commitmentOrderOld
            .id().value(),
            commitmentOrderOld.lineId()
                .value(),
            UuidMother.fromInteger(120),
            referenceId, useId,
            BigDecimal.valueOf(1500), commitmentOrderOld
                .supplierId().value()),
        BigDecimal.valueOf(1000));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(List.of(nominated))
        .when(this.distributionNominatedRepository)
        .findByAnySharedRawMaterial(List.of(nominated.sharedRawMaterial()));
    doReturn(List.of(commitment, commitment2, commitmentOld)).when(this.commitmentAdjustedProvider).findByAnySharedRawMaterial(
        List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())));

    final var keptLine1 = kept(new Line(new DistributionNominated.Id(rootId), commitmentOrder, new ReferenceId(referenceId),
        new ReferenceId(referenceId), NumericUtils.roundUpScale2(BigDecimal.valueOf(500)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.of(creationDate)));
    final var adjustedLine2 = created(new Line(new DistributionNominated.Id(rootId), commitmentOrder2, new ReferenceId(referenceId),
        new ReferenceId(referenceId), NumericUtils.roundUpScale2(BigDecimal.valueOf(500)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.empty()));
    final var keptLine2 = kept(new Line(new DistributionNominated.Id(rootId), commitmentOrderOld, new ReferenceId(referenceId),
        new ReferenceId(referenceId), NumericUtils.roundUpScale2(BigDecimal.valueOf(0)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.of(creationDate)));

    final var expectedLine =
        DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(500), creationDate)
            .commitmentOrder(commitmentOrder)
            .build();
    final var expectedLine2 =
        DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(500), creationDate)
            .commitmentOrder(commitmentOrder2)
            .build();
    final var expectedDN = DistributionNominatedMother.of(nominated)
        .lines(new DistributionNominatedLines(List.of(expectedLine, expectedLine2, line)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.valueOf(0)))
        .audit(nominated.audit().update("test", now))
        .build();

    final var mcuUsed2 = commitment2.materialCommitmentUse();
    final var mcuUsedInResultingLineId2 = MaterialCommitmentUseCompositeId.of(nominated.sharedRawMaterial(),
        new MaterialCommitmentUseOrderId(expectedLine2.commitmentOrder().id().value().toString()),
        new MaterialCommitmentUseOrderLineId(expectedLine2.commitmentOrder().lineId().value().toString()));
    doReturn(new MaterialCommitmentUseCollection(List.of(mcuUsed2)))
        .when(this.materialCommitmentUseRepository)
        .findByAnyCompositeId(List.of(mcuUsedInResultingLineId2));
    doReturn(Map.of(new DistributionNominated.Id(rootId), List.of(expectedLine, expectedLine2, line)))
        .when(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine2, keptLine2, keptLine1), now);

    this.sut.doHandle(new RegularizeDistributionNominatedBudgetCycleChangeCommand(List.of(nominated.sharedRawMaterial()), "test"));

    verify(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine2, keptLine2, keptLine1), now);
    verify(this.materialCommitmentUseRepository)
        .saveAll(List.of(mcuUsed2.executeBudgetCycleChange(now)));
    verify(this.distributionNominatedRepository).save(expectedDN);
    verify(this.eventBus).send(this.eventsCaptor.capture());
    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(DistributionNominatedUnifiedEvent.class::isInstance)
        .allMatch(domainEvent -> domainEvent.eventType().equals(EventType.UPDATED.value));
  }

  @Test
  void should_regularize_lines_when_commitment_completes_full_quantity() {
    final var rootId = UuidMother.fromInteger(901);
    final var useId = UuidMother.fromInteger(101);
    final var referenceId = UuidMother.fromInteger(201);
    final var budgetCycle = UuidMother.fromInteger(301);
    final var commitmentOrderId = UuidMother.fromInteger(401);
    final var commitmentOrderLineId = UuidMother.fromInteger(501);
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601));
    final var creationDate = OffsetDateTimeMother.fromInteger(1);
    final var now = OffsetDateTimeMother.fromInteger(10);
    final var commitmentOrderOld = new CommitmentOrder(new Id(UuidMother.fromInteger(899)), new LineId(UuidMother.fromInteger(898)),
        new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(897))));
    final var commitmentOrder = new CommitmentOrder(new Id(commitmentOrderId), new LineId(commitmentOrderLineId),
        new SupplierId(commitmentOrderSupplierId));

    final var line =
        DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(1000), creationDate)
            .commitmentOrder(commitmentOrderOld)
            .build();
    final var nominated = DistributionNominatedMother.inProgress()
        .id(new DistributionNominated.Id(rootId))
        .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(2000)))
        .theoreticalQuantity(new DistributionNominated.TheoreticalQuantity(BigDecimal.valueOf(1200)))
        .useId(new UseId(useId))
        .referenceId(new ReferenceId(referenceId))
        .budgetCycle(new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetCycle)))
        .lines(new ArrayList<>(List.of(line)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.valueOf(2000)))
        .audit(CompleteAudit.create("test", creationDate))
        .build();

    final var commitmentOld = CommitmentAdjusted.of(
        MaterialCommitmentUseMother.withCompositeIdAndQuantityAndBudgetCycleChange(commitmentOrderOld.id().value(),
            commitmentOrderOld.lineId()
                .value(),
            budgetCycle,
            referenceId, useId,
            BigDecimal.valueOf(1000), commitmentOrderOld.supplierId().value()),
        BigDecimal.valueOf(1000));
    final var commitment = CommitmentAdjusted.of(
        MaterialCommitmentUseMother.withCompositeIdAndQuantityAndBudgetCycleChange(commitmentOrderId, commitmentOrderLineId, budgetCycle,
            referenceId, useId,
            BigDecimal.valueOf(1500), commitmentOrderSupplierId),
        BigDecimal.valueOf(1000));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(List.of(nominated))
        .when(this.distributionNominatedRepository)
        .findByAnySharedRawMaterial(List.of(nominated.sharedRawMaterial()));
    doReturn(List.of(commitment, commitmentOld)).when(this.commitmentAdjustedProvider).findByAnySharedRawMaterial(
        List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())));

    final var adjustedLine1 = created(new Line(new DistributionNominated.Id(rootId), commitmentOrder, new ReferenceId(referenceId),
        new ReferenceId(referenceId), NumericUtils.roundUpScale2(BigDecimal.valueOf(1000)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.empty()));
    final var adjustedLine2 = kept(new Line(new DistributionNominated.Id(rootId), commitmentOrderOld, new ReferenceId(referenceId),
        new ReferenceId(referenceId), NumericUtils.roundUpScale2(BigDecimal.valueOf(1000)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.of(creationDate)));

    final var expectedLine =
        DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(1000), creationDate)
            .commitmentOrder(commitmentOrder)
            .build();
    final var expectedDN = DistributionNominatedMother.of(nominated)
        .lines(new DistributionNominatedLines(List.of(expectedLine, line)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .audit(nominated.audit().update("test", now))
        .build();

    final var mcuUsed = commitment.materialCommitmentUse();
    final var mcuUsedInResultingLineId = MaterialCommitmentUseCompositeId.of(nominated.sharedRawMaterial(),
        new MaterialCommitmentUseOrderId(expectedLine.commitmentOrder().id().value().toString()),
        new MaterialCommitmentUseOrderLineId(expectedLine.commitmentOrder().lineId().value().toString()));
    doReturn(new MaterialCommitmentUseCollection(List.of(mcuUsed)))
        .when(this.materialCommitmentUseRepository)
        .findByAnyCompositeId(List.of(mcuUsedInResultingLineId));
    doReturn(Map.of(new DistributionNominated.Id(rootId), List.of(expectedLine, line)))
        .when(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine1, adjustedLine2), now);

    this.sut.doHandle(new RegularizeDistributionNominatedBudgetCycleChangeCommand(List.of(nominated.sharedRawMaterial()), "test"));

    verify(this.linesIdentifier).identifyAdjustedLines(List.of(nominated), List.of(adjustedLine1, adjustedLine2), now);
    verify(this.distributionNominatedRepository).save(expectedDN);
    verify(this.materialCommitmentUseRepository).saveAll(List.of(mcuUsed.executeBudgetCycleChange(now)));
    verify(this.eventBus).send(this.eventsCaptor.capture());
    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(DistributionNominatedUnifiedEvent.class::isInstance)
        .allMatch(domainEvent -> domainEvent.eventType().equals(EventType.UPDATED.value));
  }

  @Test
  void should_do_nothing_if_regularize_did_not_do_anything() {
    final var rootId = UuidMother.fromInteger(901);
    final var useId = UuidMother.fromInteger(101);
    final var referenceId = UuidMother.fromInteger(201);
    final var budgetCycle = UuidMother.fromInteger(301);
    final var creationDate = OffsetDateTimeMother.fromInteger(1);
    final var now = OffsetDateTimeMother.fromInteger(10);
    final var commitmentOrderOld = new CommitmentOrder(new Id(UuidMother.fromInteger(899)), new LineId(UuidMother.fromInteger(898)),
        new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(897))));

    final var line =
        DistributionNominatedLineMother.createdWithQuantityAndDate(BigDecimal.valueOf(2000), creationDate)
            .commitmentOrder(commitmentOrderOld)
            .build();
    final var nominated = DistributionNominatedMother.inProgress()
        .id(new DistributionNominated.Id(rootId))
        .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(2000)))
        .theoreticalQuantity(new DistributionNominated.TheoreticalQuantity(BigDecimal.valueOf(1200)))
        .useId(new UseId(useId))
        .referenceId(new ReferenceId(referenceId))
        .budgetCycle(new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetCycle)))
        .lines(new ArrayList<>(List.of(line)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.valueOf(0)))
        .audit(CompleteAudit.create("test", creationDate))
        .build();

    final var commitmentOld = CommitmentAdjusted.of(
        MaterialCommitmentUseMother.withCompositeIdAndQuantity(commitmentOrderOld.id().value(),
            commitmentOrderOld.lineId()
                .value(),
            budgetCycle,
            referenceId, useId,
            BigDecimal.valueOf(2000), commitmentOrderOld.supplierId().value()),
        BigDecimal.valueOf(2000));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(List.of(nominated))
        .when(this.distributionNominatedRepository)
        .findByAnySharedRawMaterial(List.of(nominated.sharedRawMaterial()));
    doReturn(List.of(commitmentOld)).when(this.commitmentAdjustedProvider).findByAnySharedRawMaterial(
        List.of(MaterialCommitmentUseSharedRawMaterial.of(nominated.sharedRawMaterial())));

    this.sut.doHandle(new RegularizeDistributionNominatedBudgetCycleChangeCommand(List.of(nominated.sharedRawMaterial()), "test"));

    verify(this.linesIdentifier, never()).identifyAdjustedLines(any(), any(), any());
    verify(this.distributionNominatedRepository, never()).save(any());
    verify(this.eventBus, never()).send(anyCollection());
  }

  @Configuration
  @Import({ClockUtils.class, Transaction.class, RegularizeDistributionNominatedBudgetCycleChangeCommandHandler.class})
  public static class Config {

    @Bean
    UuidGenerator uuidGenerator() {
      return UuidGeneratorMother.fromStartingInteger(1001);
    }

  }
}
