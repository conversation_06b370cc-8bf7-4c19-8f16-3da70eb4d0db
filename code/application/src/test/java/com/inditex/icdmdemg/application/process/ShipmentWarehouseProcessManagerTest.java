package com.inditex.icdmdemg.application.process;

import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import java.util.Set;

import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCompletedCommand;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCompletedCommand.ShipmentWarehouseCompleted;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCreatedCommand;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCreatedCommand.ShipmentWarehouseCreated;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseStartedCommand;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseStartedCommand.ShipmentWarehouseStarted;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseUpdatedCommand;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseUpdatedCommand.ShipmentWarehouseUpdated;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.UpdateDistributionInnerFromShipmentWarehouseCommand;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ShipmentWarehouseProcessManagerTest {

  @Mock
  private CommandBus commandBus;

  @Mock
  private Validator validator;

  @InjectMocks
  private ShipmentWarehouseProcessManager processManager;

  @Test
  void should_do_nothing_if_invalid_distribution_shipment_created() {
    final var shipmentWarehouseCreated = Instancio.create(ShipmentWarehouseCreated.class);
    final var shipmentWarehouseCreatedCommand = new ProjectShipmentWarehouseCreatedCommand(shipmentWarehouseCreated);
    final var projectionResult = Instancio.create(ShipmentWarehouse.class);
    final var updateDistributionInnerLineTackingCodeCommand =
        new UpdateDistributionInnerFromShipmentWarehouseCommand(projectionResult);

    final var violation = mock(ConstraintViolation.class);
    doReturn(Set.of(violation)).when(this.validator).validate(shipmentWarehouseCreated);

    this.processManager.execute(shipmentWarehouseCreated);

    verify(this.commandBus, never()).execute(shipmentWarehouseCreatedCommand);
    verify(this.commandBus, never()).execute(updateDistributionInnerLineTackingCodeCommand);
  }

  @Test
  void should_project_distribution_shipment_created() {
    final var shipmentWarehouseCreated = Instancio.create(ShipmentWarehouseCreated.class);
    final var shipmentWarehouseCreatedCommand = new ProjectShipmentWarehouseCreatedCommand(shipmentWarehouseCreated);
    final var projectionResult = Instancio.create(ShipmentWarehouse.class);
    final var updateDistributionInnerLineTackingCodeCommand =
        new UpdateDistributionInnerFromShipmentWarehouseCommand(projectionResult);

    doReturn(projectionResult).when(this.commandBus).execute(shipmentWarehouseCreatedCommand);

    this.processManager.execute(shipmentWarehouseCreated);

    verify(this.commandBus).execute(shipmentWarehouseCreatedCommand);
    verify(this.commandBus).execute(updateDistributionInnerLineTackingCodeCommand);
  }

  @Test
  void should_do_nothing_if_invalid_shipment_warehouse_started() {
    final var shipmentWarehouseStarted = Instancio.create(ShipmentWarehouseStarted.class);
    final var projectShipmentWarehouseStartedCommand = new ProjectShipmentWarehouseStartedCommand(shipmentWarehouseStarted);
    final var projectionResult = Instancio.create(ShipmentWarehouse.class);
    final var updateDICommand = new UpdateDistributionInnerFromShipmentWarehouseCommand(projectionResult);

    final var violation = mock(ConstraintViolation.class);
    doReturn(Set.of(violation)).when(this.validator).validate(shipmentWarehouseStarted);

    this.processManager.execute(shipmentWarehouseStarted);

    verify(this.commandBus, never()).execute(projectShipmentWarehouseStartedCommand);
    verify(this.commandBus, never()).execute(updateDICommand);
  }

  @Test
  void should_call_project_shipment_warehouse_started() {
    final var shipmentWarehouseStarted = Instancio.create(ShipmentWarehouseStarted.class);
    final var projectShipmentWarehouseStartedCommand = new ProjectShipmentWarehouseStartedCommand(shipmentWarehouseStarted);
    final var projectionResult = Instancio.create(ShipmentWarehouse.class);
    doReturn(projectionResult).when(this.commandBus).execute(projectShipmentWarehouseStartedCommand);
    final var updateDICommand = new UpdateDistributionInnerFromShipmentWarehouseCommand(projectionResult);

    this.processManager.execute(shipmentWarehouseStarted);

    verify(this.commandBus).execute(projectShipmentWarehouseStartedCommand);
    verify(this.commandBus).execute(updateDICommand);
  }

  @Test
  void should_do_nothing_if_invalid_shipment_warehouse_updated() {
    final var shipmentWarehouseUpdated = Instancio.create(ShipmentWarehouseUpdated.class);
    final var projectShipmentWarehouseUpdatedCommand = new ProjectShipmentWarehouseUpdatedCommand(shipmentWarehouseUpdated);
    final var projectionResult = Instancio.create(ShipmentWarehouse.class);
    final var updateDICommand = new UpdateDistributionInnerFromShipmentWarehouseCommand(projectionResult);

    final var violation = mock(ConstraintViolation.class);
    doReturn(Set.of(violation)).when(this.validator).validate(shipmentWarehouseUpdated);

    this.processManager.execute(shipmentWarehouseUpdated);

    verify(this.commandBus, never()).execute(projectShipmentWarehouseUpdatedCommand);
    verify(this.commandBus, never()).execute(updateDICommand);
  }

  @Test
  void should_call_project_shipment_warehouse_updated() {
    final var shipmentWarehouseUpdated = Instancio.create(ShipmentWarehouseUpdated.class);
    final var projectShipmentWarehouseUpdatedCommand = new ProjectShipmentWarehouseUpdatedCommand(shipmentWarehouseUpdated);
    final var projectionResult = Instancio.create(ShipmentWarehouse.class);
    doReturn(projectionResult).when(this.commandBus).execute(projectShipmentWarehouseUpdatedCommand);
    final var updateDICommand = new UpdateDistributionInnerFromShipmentWarehouseCommand(projectionResult);

    this.processManager.execute(shipmentWarehouseUpdated);

    verify(this.commandBus).execute(projectShipmentWarehouseUpdatedCommand);
    verify(this.commandBus).execute(updateDICommand);
  }

  @Test
  void should_do_nothing_if_invalid_shipment_warehouse_completed() {
    final var shipmentWarehouseCompleted = Instancio.create(ShipmentWarehouseCompleted.class);
    final var projectShipmentWarehouseCompletedCommand = new ProjectShipmentWarehouseCompletedCommand(shipmentWarehouseCompleted);
    final var projectionResult = Instancio.create(ShipmentWarehouse.class);
    final var updateDICommand = new UpdateDistributionInnerFromShipmentWarehouseCommand(projectionResult);

    final var violation = mock(ConstraintViolation.class);
    doReturn(Set.of(violation)).when(this.validator).validate(shipmentWarehouseCompleted);

    this.processManager.execute(shipmentWarehouseCompleted);

    verify(this.commandBus, never()).execute(projectShipmentWarehouseCompletedCommand);
    verify(this.commandBus, never()).execute(updateDICommand);
  }

  @Test
  void should_call_project_shipment_warehouse_completed() {
    final var shipmentWarehouseCompleted = Instancio.create(ShipmentWarehouseCompleted.class);
    final var projectShipmentWarehouseCompletedCommand = new ProjectShipmentWarehouseCompletedCommand(shipmentWarehouseCompleted);
    final var projectionResult = Instancio.create(ShipmentWarehouse.class);
    doReturn(projectionResult).when(this.commandBus).execute(projectShipmentWarehouseCompletedCommand);
    final var updateDICommand = new UpdateDistributionInnerFromShipmentWarehouseCommand(projectionResult);

    this.processManager.execute(shipmentWarehouseCompleted);

    verify(this.commandBus).execute(projectShipmentWarehouseCompletedCommand);
    verify(this.commandBus).execute(updateDICommand);
  }

}
