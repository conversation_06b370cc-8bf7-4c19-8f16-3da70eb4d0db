package com.inditex.icdmdemg.application.distributioninner.query;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verifyNoInteractions;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.SliceImpl;

@ExtendWith(MockitoExtension.class)
class GetDistributionInnerByCriteriaQueryHandlerTest {

  @Mock
  private DistributionInnerRepository distributionInnerRepository;

  @InjectMocks
  private GetDistributionInnerByCriteriaQueryHandler sut;

  @Test
  void should_throw_when_all_filters_are_empty() {
    final var query = new GetDistributionInnerByCriteriaQuery(List.of(), List.of(), List.of(), List.of(), 0, 50);
    assertThatThrownBy(() -> this.sut.ask(query))
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("Some filter is required");
    verifyNoInteractions(this.distributionInnerRepository);
  }

  @Test
  void should_map_params_and_return_inner_distributions() {
    final var referenceId = UUID.randomUUID();
    final var productOrderId = UUID.randomUUID();
    final var budget = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final var status = "PENDING";

    final var pageRequest = PageRequest.of(0, 50);

    final var distributionInner = Instancio.create(DistributionInner.class);
    final var expected = new SliceImpl<>(List.of(distributionInner));

    doReturn(expected).when(this.distributionInnerRepository).findByCriteria(
        List.of(new ReferenceId(referenceId)),
        List.of(new ProductOrderId(productOrderId)),
        List.of(new BudgetCycle(budget)),
        List.of(DistributionInnerStatus.valueOf(status)),
        pageRequest);

    final var actual = this.sut.ask(new GetDistributionInnerByCriteriaQuery(List.of(referenceId),
        List.of(productOrderId), List.of(budget), List.of(status), 0, 50));

    assertThat(actual).isEqualTo(expected);
  }
}
