package com.inditex.icdmdemg.application.distributionnominated.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import java.math.BigDecimal;
import java.time.Clock;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedFromOrderCommand.UpdateDistributionNominatedFromOrderCommandResult;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycleChangePendingQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductSupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.OrderLine;
import com.inditex.icdmdemg.domain.order.entity.OrderLines;
import com.inditex.icdmdemg.domain.order.entity.OrderSupplierId;
import com.inditex.icdmdemg.domain.order.mother.OrderMother;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = UpdateDistributionNominatedFromOrderCommandHandlerTest.Config.class)
class UpdateDistributionNominatedFromOrderCommandHandlerTest {

  @MockitoBean
  private DistributionNominatedRepository distributionNominatedRepository;

  @MockitoBean
  private OrderRepository orderRepository;

  @Autowired
  private Transaction transaction;

  @Autowired
  private ClockUtils clockUtils;

  @MockitoBean
  private EventBus eventBus;

  @Captor
  ArgumentCaptor<Collection<DomainEvent<?>>> eventsCaptor;

  @Autowired
  private UpdateDistributionNominatedFromOrderCommandHandler sut;

  @Test
  void shouldDoNothingWhenNoOrder() {
    final var dateTime = OffsetDateTimeMother.fromInteger(1);
    this.clockUtils.setClock(Clock.fixed(dateTime.toInstant(), ZoneOffset.UTC));

    final var command = new UpdateDistributionNominatedFromOrderCommand(
        UuidMother.fromInteger(101).toString(),
        "test");

    doReturn(Optional.empty()).when(this.orderRepository).find(new OrderId(UuidMother.fromInteger(201).toString()));

    final var result = this.sut.execute(command);

    assertThat(result).isEqualTo(UpdateDistributionNominatedFromOrderCommandResult.empty());
    verify(this.distributionNominatedRepository, never()).findByProductOrderId(any());
    verify(this.distributionNominatedRepository, never()).save(any());
    verify(this.eventBus, never()).send(anyCollection());
  }

  @Test
  void shouldUpdateSupplier() {
    final var dateTime = OffsetDateTimeMother.fromInteger(1);
    this.clockUtils.setClock(Clock.fixed(dateTime.toInstant(), ZoneOffset.UTC));

    final var order = OrderMother.random()
        .orderLines(new OrderLines(List.of()))
        .build();
    doReturn(Optional.of(order)).when(this.orderRepository).find(order.getId());

    final String orderId = order.getId().value();
    final ProductOrderId productOrderId = new ProductOrderId(UUID.fromString(orderId));
    final DistributionNominated distribution = DistributionNominatedMother
        .inProgress()
        .productOrderId(productOrderId)
        .build();
    doReturn(List.of(distribution)).when(this.distributionNominatedRepository)
        .findByProductOrderId(productOrderId);

    final DistributionNominated updatedDistribution = DistributionNominatedMother.of(distribution)
        .productSupplierId(new ProductSupplierId(order.supplierId().value()))
        .budgetCycleChangePendingQuantity(distribution.budgetCycleChangePendingQuantity())
        .build();

    final SharedRawMaterialNominated sharedRawMaterialNominatedOfUpdatedDistribution =
        new SharedRawMaterialNominated(updatedDistribution.referenceId(), updatedDistribution.useId(), updatedDistribution.budgetCycle());

    final UpdateDistributionNominatedFromOrderCommandResult expectedResult =
        new UpdateDistributionNominatedFromOrderCommandResult(List.of(sharedRawMaterialNominatedOfUpdatedDistribution));

    final var command = new UpdateDistributionNominatedFromOrderCommand(
        orderId,
        "test");

    final var result = this.sut.execute(command);

    assertThat(result).isEqualTo(expectedResult);
    verify(this.distributionNominatedRepository).save(updatedDistribution);
    verify(this.eventBus).send(this.eventsCaptor.capture());

    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(DistributionNominatedUnifiedEvent.class::isInstance)
        .allMatch(domainEvent -> domainEvent.eventType().equals(EventType.UPDATED.value));
  }

  @Test
  void shouldUpdateBudgetCycle() {
    final OffsetDateTime dateTime = OffsetDateTimeMother.fromInteger(1);
    this.clockUtils.setClock(Clock.fixed(dateTime.toInstant(), ZoneOffset.UTC));

    final Order order = OrderMother.random().build();
    doReturn(Optional.of(order)).when(this.orderRepository).find(order.getId());

    final OrderLine orderLine = order.orderLines().value().getFirst();
    final String budgetCycleId = orderLine.budgetId().value();

    final OrderSupplierId productSupplierId = order.supplierId();
    final ProductOrderId productOrderId = new ProductOrderId(UUID.fromString(order.getId().value()));

    final DistributionNominated distributionNominated = DistributionNominatedMother
        .inProgress()
        .productOrderId(productOrderId)
        .productSupplierId(new DistributionNominated.ProductSupplierId(productSupplierId.value()))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .build();

    doReturn(List.of(distributionNominated))
        .when(this.distributionNominatedRepository)
        .findByProductOrderId(productOrderId);

    final var command = new UpdateDistributionNominatedFromOrderCommand(
        order.getId().value(),
        this.getClass().getSimpleName());

    final SharedRawMaterialNominated sharedRawMaterialNominatedOfUpdatedDistribution =
        new SharedRawMaterialNominated(
            distributionNominated.referenceId(),
            distributionNominated.useId(),
            new DistributionNominated.BudgetCycle(budgetCycleId));

    final UpdateDistributionNominatedFromOrderCommandResult expectedResult =
        new UpdateDistributionNominatedFromOrderCommandResult(List.of(sharedRawMaterialNominatedOfUpdatedDistribution));

    final UpdateDistributionNominatedFromOrderCommandResult result = this.sut.execute(command);

    final boolean allRequestQuantitiesOfDistributionLinesAreZero = distributionNominated.lines().value().stream()
        .map(line -> line.requestedQuantity().value())
        .allMatch(bd -> bd.compareTo(BigDecimal.ZERO) == 0);

    final boolean allTheoreticalQuantitiesOfDistributionLinesAreZero = distributionNominated.lines().value().stream()
        .map(line -> line.theoreticalQuantity().value())
        .allMatch(bd -> bd.compareTo(BigDecimal.ZERO) == 0);

    assertThat(result).isEqualTo(expectedResult);
    assertThat(distributionNominated.budgetCycleChangePendingQuantity().value())
        .isEqualTo(distributionNominated.requestedQuantity().value());
    assertThat(allRequestQuantitiesOfDistributionLinesAreZero).isTrue();
    assertThat(allTheoreticalQuantitiesOfDistributionLinesAreZero).isTrue();

    verify(this.distributionNominatedRepository).save(distributionNominated);
    verify(this.eventBus).send(this.eventsCaptor.capture());

    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(DistributionNominatedUnifiedEvent.class::isInstance)
        .allMatch(domainEvent -> domainEvent.eventType().equals(EventType.UPDATED.value));
  }

  @Test
  void shouldNotUpdateBudgetCycleAndSupplier() {
    final var dateTime = OffsetDateTimeMother.fromInteger(1);
    this.clockUtils.setClock(Clock.fixed(dateTime.toInstant(), ZoneOffset.UTC));

    final var order = OrderMother.random().build();
    doReturn(Optional.of(order)).when(this.orderRepository).find(order.getId());

    final OrderLine orderLine = order.orderLines().value().getFirst();
    final String budgetCycleId = orderLine.budgetId().value();

    final String orderId = order.getId().value();
    final String productSupplierId = order.supplierId().value();
    final var productOrderId = new ProductOrderId(UUID.fromString(orderId));
    final var distribution = DistributionNominatedMother
        .inProgress()
        .budgetCycle(new BudgetCycle(budgetCycleId))
        .productOrderId(productOrderId)
        .productSupplierId(new ProductSupplierId(productSupplierId))
        .build();
    doReturn(List.of(distribution)).when(this.distributionNominatedRepository)
        .findByProductOrderId(productOrderId);

    final var command = new UpdateDistributionNominatedFromOrderCommand(
        orderId,
        "test");

    final var result = this.sut.execute(command);

    assertThat(result).isEqualTo(UpdateDistributionNominatedFromOrderCommandResult.empty());
    verify(this.distributionNominatedRepository, never()).save(any());
    verify(this.eventBus, never()).send(any());
  }

  @Configuration
  @Import({ClockUtils.class, UpdateDistributionNominatedFromOrderCommandHandler.class, Transaction.class})
  public static class Config {

  }
}
