package com.inditex.icdmdemg.application.distributionnominated.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateReferenceInput;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateReferenceInput.Variant;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@SpringBootTest(classes = UpdateDistributionNominatedReferenceCommandHandlerTest.Config.class)
class UpdateDistributionNominatedReferenceCommandHandlerTest {

  @MockitoBean
  private DistributionNominatedRepository distributionNominatedRepository;

  @MockitoSpyBean
  private ClockUtils clockUtils;

  @MockitoSpyBean
  private Transaction transaction;

  @MockitoBean
  private EventBus eventBus;

  @Autowired
  private UpdateDistributionNominatedReferenceCommandHandler handler;

  @Captor
  ArgumentCaptor<DistributionNominated> savedDistributionNominatedCaptor;

  @Captor
  ArgumentCaptor<java.util.Collection<com.inditex.iopcmmnt.ddd.core.DomainEvent<?>>> sentDistEventsCaptor;

  private static final String TARGET_VARIANT = "EXAMPLE";

  private static final String NOT_TARGET_VARIANT = "CLOTHES";

  private static final String TRIGGERED_BY = "triggered_by";

  private static final UUID PRODUCT_ID = UUID.randomUUID();

  private static final DistributionNominated DISTRIBUTION_NOMINATED_1 = Instancio.of(DistributionNominated.class).create();

  private static final DistributionNominated DISTRIBUTION_NOMINATED_2 = Instancio.of(DistributionNominated.class).create();

  private static final Variant VARIANT_1 =
      new Variant(UUID.randomUUID(), DISTRIBUTION_NOMINATED_1.referenceId().value());

  private static final Variant VARIANT_2 = new Variant(UUID.randomUUID(), DISTRIBUTION_NOMINATED_2.referenceId().value());

  private static final List<Variant> VARIANTS =
      List.of(VARIANT_1, VARIANT_2);

  private static final List<DistributionNominated> DISTRIBUTION_NOMINATED_LIST =
      List.of(DISTRIBUTION_NOMINATED_1, DISTRIBUTION_NOMINATED_2);

  @BeforeEach
  void setUp() {
    reset(this.distributionNominatedRepository, this.clockUtils, this.transaction, this.eventBus);
  }

  @Test
  void should_update_distributions() {
    final var mockedNow = OffsetDateTime.MIN;
    doReturn(mockedNow).when(this.clockUtils).getCurrentOffsetDateTime();

    final var command = new UpdateDistributionNominatedReferenceCommand(
        new UpdateReferenceInput(PRODUCT_ID, TARGET_VARIANT, VARIANTS), TRIGGERED_BY);

    final List<ReferenceId> lstVariantNominated = List.of(
        new ReferenceId(VARIANT_1.referenceSourceId()),
        new ReferenceId(VARIANT_2.referenceSourceId()));

    doReturn(DISTRIBUTION_NOMINATED_LIST).when(this.distributionNominatedRepository)
        .findByReferenceIds(argThat(l -> l.containsAll(lstVariantNominated)));

    this.handler.doHandle(command);

    verify(this.distributionNominatedRepository, times(2)).save(this.savedDistributionNominatedCaptor.capture());

    final List<ReferenceId> resultReferenceNominated = this.savedDistributionNominatedCaptor.getAllValues().stream()
        .map(DistributionNominated::referenceId)
        .toList();
    final List<ReferenceProductId> resultProductNominated = this.savedDistributionNominatedCaptor.getAllValues().stream()
        .map(DistributionNominated::referenceProductId)
        .toList();

    final List<ReferenceId> expectedNominated = List.of(
        new ReferenceId(VARIANT_1.referenceId()),
        new ReferenceId(VARIANT_2.referenceId()));

    assertThat(resultReferenceNominated).containsExactlyInAnyOrderElementsOf(expectedNominated);
    resultProductNominated.forEach(referenceProductId -> assertThat(referenceProductId).isEqualTo(new ReferenceProductId(PRODUCT_ID)));

    verify(this.eventBus, times(2)).send(this.sentDistEventsCaptor.capture());
    assertThat(this.sentDistEventsCaptor.getAllValues()).hasSize(2);
  }

  @Test
  void should_do_nothing_if_variant_with_taxonomy_clothes() {
    final Variant variant3 = new Variant(UUID.randomUUID(), UUID.randomUUID());
    final var command = new UpdateDistributionNominatedReferenceCommand(
        new UpdateReferenceInput(UUID.randomUUID(), NOT_TARGET_VARIANT, List.of(variant3)), TRIGGERED_BY);

    this.handler.doHandle(command);

    verify(this.distributionNominatedRepository, times(0)).findByReferenceIds(anyList());
    verify(this.distributionNominatedRepository, times(0)).save(this.savedDistributionNominatedCaptor.capture());

    verify(this.eventBus, times(0)).send(this.sentDistEventsCaptor.capture());
  }

  @Test
  void should_do_nothing_if_no_variant() {
    final var command = new UpdateDistributionNominatedReferenceCommand(
        new UpdateReferenceInput(UUID.randomUUID(), TARGET_VARIANT, List.of()), TRIGGERED_BY);

    this.handler.doHandle(command);

    verify(this.distributionNominatedRepository, times(0)).findByReferenceIds(anyList());
    verify(this.distributionNominatedRepository, times(0)).save(this.savedDistributionNominatedCaptor.capture());

    verify(this.eventBus, times(0)).send(this.sentDistEventsCaptor.capture());
  }

  @Configuration
  @Import({ClockUtils.class, Transaction.class, UpdateDistributionNominatedReferenceCommandHandler.class})
  public static class Config {

  }
}
