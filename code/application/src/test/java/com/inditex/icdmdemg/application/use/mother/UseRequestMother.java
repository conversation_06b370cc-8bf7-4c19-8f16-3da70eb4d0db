package com.inditex.icdmdemg.application.use.mother;

import java.util.List;

import com.inditex.icdmdemg.application.use.command.CreateUseCommand.UseRequest;
import com.inditex.icdmdemg.application.use.command.CreateUseCommand.UseRequest.Condition;
import com.inditex.icdmdemg.application.use.command.UseRequestNames;
import com.inditex.icdmdemg.application.use.command.UseRequestNames.UseRequestName;
import com.inditex.icdmdemg.shared.utils.LocaleUtils;

public interface UseRequestMother {

  static UseRequestNames generateUseRequestNamesWithOneNoMandatory() {
    return new UseRequestNames(List.of(new UseRequestName(LocaleUtils.createLocale("es-ES"), "descripcion uso"),
        new UseRequestName(LocaleUtils.createLocale("pt-PT"), "descripcio do usinho"),
        new UseRequestName(LocaleUtils.createLocale("en-US"), "use description")));
  }

  static UseRequestNames generateUseRequestNames() {
    return new UseRequestNames(List.of(new UseRequestName(LocaleUtils.createLocale("es-ES"), "descripcion uso"),
        new UseRequestName(LocaleUtils.createLocale("en"), "use description")));
  }

  static List<String> generateAssignable() {
    return List.of("NOMINATED");
  }

  static String generateTaxonomy() {
    return "FABRIC";
  }

  static String generateCustomer() {
    return "urn:BUYER:123e4567-e89b-12d3-a456-526655440002";
  }

  static List<String> generatePurchaseType() {
    return List.of("NOMINATED");
  }

  static List<String> generatePurchaseTypes() {
    return List.of("NOMINATED", "INNER");
  }

  static List<Condition> generateOneCondition() {
    return List.of(new Condition(
        "EQUALS", "BUYERGROUP",
        List.of("urn:BUYERGROUP:123e4567-e89b-12d3-a456-526655440002")));
  }

  static List<Condition> generateConditions() {
    return List.of(new Condition(
        "ALL_IN", "BUYERSUBGROUP",
        List.of("urn:BUYERSUBGROUP:123e4567-e89b-12d3-a456-526655440002",
            "urn:BUYERSUBGROUP:542c8136-b788b-12d3-a456-526655444159")),
        new Condition(
            "EQUALS", "BUYERGROUP",
            List.of("urn:BUYERGROUP:123e4567-e89b-12d3-a456-526655440002")));
  }

  static String generateTriggeredBy() {
    return "admin";
  }

  static UseRequest withTreeNamesAndTwoConditions() {
    return new UseRequest(
        generateUseRequestNamesWithOneNoMandatory(),
        generateAssignable(),
        generateTaxonomy(),
        generateCustomer(),
        generatePurchaseTypes(),
        generateConditions(),
        generateTriggeredBy());
  }

  static UseRequest withTreeNamesAndTwoConditionsNullAssignable() {
    return new UseRequest(
        generateUseRequestNamesWithOneNoMandatory(),
        List.of(),
        generateTaxonomy(),
        generateCustomer(),
        generatePurchaseTypes(),
        generateConditions(),
        generateTriggeredBy());
  }

  static UseRequest withAllArguments() {

    return new UseRequest(
        generateUseRequestNames(),
        generateAssignable(),
        generateTaxonomy(),
        generateCustomer(),
        generatePurchaseType(),
        generateOneCondition(),
        generateTriggeredBy());

  }
}
