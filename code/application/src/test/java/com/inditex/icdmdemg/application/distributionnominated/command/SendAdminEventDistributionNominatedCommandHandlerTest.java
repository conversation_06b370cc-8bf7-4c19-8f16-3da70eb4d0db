package com.inditex.icdmdemg.application.distributionnominated.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

import java.util.Collection;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = SendAdminEventDistributionNominatedCommandHandlerTest.Config.class)
class SendAdminEventDistributionNominatedCommandHandlerTest {
  @MockitoSpyBean
  private Transaction transaction;

  @MockitoBean
  private EventBus eventBus;

  @Captor
  ArgumentCaptor<Collection<DomainEvent<?>>> eventsCaptor;

  @MockitoBean
  private DistributionNominatedRepository distributionNominatedRepository;

  @Autowired
  private SendAdminEventDistributionNominatedCommandHandler sut;

  @Test
  void should_throw_exception_when_not_found_id() {
    final var id = UUID.randomUUID();
    final var command = new SendAdminEventDistributionNominatedCommand(
        new SendAdminEventDistributionNominatedCommand.SendEventRequest(id, "DistributionRequested"));
    doReturn(Optional.empty()).when(this.distributionNominatedRepository).findById(new Id(id));

    assertThatThrownBy(() -> this.sut.execute(command))
        .isInstanceOf(ErrorException.class);
  }

  @Test
  void should_throw_exception_when_not_accepted_event() {
    final var distributionNominated = DistributionNominatedMother.inProgress().build();
    final var command = new SendAdminEventDistributionNominatedCommand(
        new SendAdminEventDistributionNominatedCommand.SendEventRequest(distributionNominated.getId().value(),
            "DistributionProductUpdated"));
    doReturn(Optional.of(distributionNominated)).when(this.distributionNominatedRepository).findById(distributionNominated.getId());

    assertThatThrownBy(() -> this.sut.execute(command))
        .isInstanceOf(ErrorException.class);
  }

  @Test
  void should_send_event() {
    final var distributionNominated = DistributionNominatedMother.inProgress().build();
    final var eventType = "DistributionRequested";
    final var command = new SendAdminEventDistributionNominatedCommand(
        new SendAdminEventDistributionNominatedCommand.SendEventRequest(distributionNominated.getId().value(),
            eventType));
    doReturn(Optional.of(distributionNominated)).when(this.distributionNominatedRepository).findById(distributionNominated.getId());

    final var result = this.sut.execute(command);

    assertThat(result.distributionNominatedId()).isEqualTo(distributionNominated.getId());
    verify(this.eventBus).send(this.eventsCaptor.capture());
    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(DistributionNominatedUnifiedEvent.class::isInstance)
        .allMatch(domainEvent -> domainEvent.eventType().equals(eventType));
  }

  @Configuration
  @Import({ClockUtils.class, Transaction.class, SendAdminEventDistributionNominatedCommandHandler.class})
  public static class Config {
  }
}
