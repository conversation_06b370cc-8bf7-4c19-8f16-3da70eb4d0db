package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.util.Optional;

import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCompletedCommand.ShipmentWarehouseCompleted;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouseRepository;
import com.inditex.icdmdemg.domain.shipmentwarehouse.mother.ShipmentWarehouseMother;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;

import org.assertj.core.api.Assertions;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class ProjectShipmentWarehouseCompletedCommandHandlerTest {

  ArgumentCaptor<ShipmentWarehouse> shipmentWarehouseCaptor = ArgumentCaptor.forClass(ShipmentWarehouse.class);

  ClockUtils clockUtils = mock();

  private final ShipmentWarehouseRepository shipmentWarehouseRepository = mock();

  private final Transaction transaction = spy();

  private final ProjectShipmentWarehouseCompletedCommandHandler sut = new ProjectShipmentWarehouseCompletedCommandHandler(
      this.shipmentWarehouseRepository, this.clockUtils, this.transaction);

  @Test
  void should_project_shipment_warehouse_completed() {
    final var now = OffsetDateTime.now();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    final var shipmentWarehouse = ShipmentWarehouseMother.randomStarted();
    final ShipmentWarehouse existingShipmentWarehouseCopy = ShipmentWarehouseMother.of(shipmentWarehouse);
    final var shipmentWarehouseCompletedCommand =
        new ProjectShipmentWarehouseCompletedCommand(Instancio.of(ShipmentWarehouseCompleted.class)
            .set(field("distributionTrackingCode"), shipmentWarehouse.getTrackingCode().value())
            .create());
    doReturn(Optional.of(existingShipmentWarehouseCopy)).when(this.shipmentWarehouseRepository)
        .findByTrackingCode(shipmentWarehouse.getTrackingCode());

    this.sut.execute(shipmentWarehouseCompletedCommand);

    verify(this.shipmentWarehouseRepository).save(this.shipmentWarehouseCaptor.capture());

    final var capturedShipmentWarehouse = this.shipmentWarehouseCaptor.getValue();
    assertThat(capturedShipmentWarehouse.getEndDate().value()).isNotNull()
        .isEqualTo(shipmentWarehouseCompletedCommand.shipmentWarehouseCompleted().distributionEndDate());
    assertThat(capturedShipmentWarehouse.getId()).isNotNull();
    assertThat(capturedShipmentWarehouse.getTrackingCode()).isEqualTo(shipmentWarehouse.getTrackingCode());
    assertThat(capturedShipmentWarehouse.getQuantity().value()).isEqualTo(shipmentWarehouse.getQuantity().value());
    assertThat(capturedShipmentWarehouse.getDistributionInner()).isEqualTo(shipmentWarehouse.getDistributionInner());
    assertThat(capturedShipmentWarehouse.getStartDate()).isEqualTo(shipmentWarehouse.getStartDate());
    assertThat(capturedShipmentWarehouse.getLastUpdateDate()).isEqualTo(shipmentWarehouse.getLastUpdateDate());
    assertThat(capturedShipmentWarehouse.getVersion()).isEqualTo(shipmentWarehouse.getVersion());
    assertThat(capturedShipmentWarehouse.getUpdatedAt()).isNotNull().hasFieldOrPropertyWithValue("value", now);
    assertThat(capturedShipmentWarehouse.getCreatedAt()).isEqualTo(shipmentWarehouse.getCreatedAt());
  }

  @Test
  void should_throw_exception_shipment_warehouse_completed_when_not_exists() {
    final var shipmentWarehouse = ShipmentWarehouseMother.randomCreated();
    final var shipmentWarehouseCompleted = Instancio.of(ShipmentWarehouseCompleted.class)
        .set(field("distributionTrackingCode"), shipmentWarehouse.getTrackingCode().value())
        .create();
    doReturn(Optional.empty()).when(this.shipmentWarehouseRepository).findByTrackingCode(shipmentWarehouse.getTrackingCode());

    final var command = new ProjectShipmentWarehouseCompletedCommand(shipmentWarehouseCompleted);

    Assertions.assertThatThrownBy(() -> this.sut.execute(command))
        .isInstanceOf(IllegalArgumentException.class);
  }

}
