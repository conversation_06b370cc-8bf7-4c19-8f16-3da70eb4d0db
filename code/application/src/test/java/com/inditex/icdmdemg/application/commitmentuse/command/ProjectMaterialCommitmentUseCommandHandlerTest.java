package com.inditex.icdmdemg.application.commitmentuse.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

import java.util.List;

import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.OrderUse;
import com.inditex.icdmdemg.application.commitmentuse.service.MaterialCommitmentUseProjector;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.shared.EntityAndActionResult;
import com.inditex.icdmdemg.shared.utils.Transaction;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class})
class ProjectMaterialCommitmentUseCommandHandlerTest {

  @Spy
  private final Transaction transaction = new Transaction();

  @Mock
  MaterialCommitmentUseProjector materialCommitmentUseProjector;

  @InjectMocks
  ProjectMaterialCommitmentUseCommandHandler sut;

  @Test
  void should_call_materialcommitmentuse_projector() {
    final var materialCommitmentUse = Instancio.create(MaterialCommitmentUse.class);
    final var useCommitmentOrders = Instancio.create(OrderUse.class);
    final var command = new ProjectMaterialCommitmentUseCommand(useCommitmentOrders);
    doReturn(List.of(EntityAndActionResult.created(materialCommitmentUse))).when(this.materialCommitmentUseProjector)
        .projectFromUseToOrder(useCommitmentOrders);

    final var result = this.sut.execute(command);

    assertThat(result).isNotNull();
    assertThat(result.materialCommitmentUses()).containsExactlyElementsOf(List.of(materialCommitmentUse));
    verify(this.materialCommitmentUseProjector).projectFromUseToOrder(useCommitmentOrders);
  }

}
