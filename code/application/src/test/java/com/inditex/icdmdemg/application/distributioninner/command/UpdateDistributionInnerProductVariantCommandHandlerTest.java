package com.inditex.icdmdemg.application.distributioninner.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateVariantGroupInput;
import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateVariantGroupInput.VariantGroup;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@SpringBootTest(classes = UpdateDistributionInnerProductVariantCommandHandlerTest.Config.class)
class UpdateDistributionInnerProductVariantCommandHandlerTest {

  @MockitoBean
  private DistributionInnerRepository distributionInnerRepository;

  @MockitoSpyBean
  private ClockUtils clockUtils;

  @MockitoSpyBean
  private Transaction transaction;

  @MockitoBean
  private EventBus eventBus;

  @Autowired
  private UpdateDistributionInnerProductVariantCommandHandler handler;

  @Captor
  ArgumentCaptor<DistributionInner> savedDistributionInnerCaptor;

  @Captor
  ArgumentCaptor<java.util.Collection<com.inditex.iopcmmnt.ddd.core.DomainEvent<?>>> sentDistEventsCaptor;

  private static final String TARGET_VARIANT_GROUP = "COLOR_GROUP";

  private static final String NOT_TARGET_VARIANT_GROUP = "ANOTHER_NAME_GROUP";

  private static final String TRIGGERED_BY = "triggered_by";

  private static final DistributionInner DISTRIBUTION_INNER_1 = Instancio.of(DistributionInner.class).create();

  private static final DistributionInner DISTRIBUTION_INNER_2 = Instancio.of(DistributionInner.class).create();

  private static final VariantGroup VARIANT_GROUP_3 =
      new VariantGroup(UUID.randomUUID(), DISTRIBUTION_INNER_1.productVariantGroupId().value(), TARGET_VARIANT_GROUP);

  private static final VariantGroup VARIANT_GROUP_4 = new VariantGroup(UUID.randomUUID(), DISTRIBUTION_INNER_2.productVariantGroupId()
      .value(), TARGET_VARIANT_GROUP);

  private static final VariantGroup VARIANT_GROUP_5 = new VariantGroup(UUID.randomUUID(), UUID.randomUUID(), NOT_TARGET_VARIANT_GROUP);

  private static final List<VariantGroup> MERGED_VARIANT_GROUPS =
      List.of(VARIANT_GROUP_3, VARIANT_GROUP_4, VARIANT_GROUP_5);

  private static final List<DistributionInner> DISTRIBUTION_INNER_LIST = List.of(DISTRIBUTION_INNER_1, DISTRIBUTION_INNER_2);

  @BeforeEach
  void setUp() {
    reset(this.distributionInnerRepository, this.clockUtils, this.transaction, this.eventBus);
  }

  @Test
  void should_filter_color_group_variants_and_update_distributions() {
    final var mockedNow = OffsetDateTime.MIN;
    doReturn(mockedNow).when(this.clockUtils).getCurrentOffsetDateTime();

    final var command =
        new UpdateDistributionInnerProductVariantCommand(new UpdateVariantGroupInput(MERGED_VARIANT_GROUPS), TRIGGERED_BY);

    final List<DistributionInner.ProductVariantGroupId> lstVariantInner = List.of(
        new DistributionInner.ProductVariantGroupId(VARIANT_GROUP_3.variantGroupSourceId()),
        new DistributionInner.ProductVariantGroupId(VARIANT_GROUP_4.variantGroupSourceId()));

    doReturn(DISTRIBUTION_INNER_LIST).when(this.distributionInnerRepository)
        .findByProductVariantGroupIds(argThat(l -> l.containsAll(lstVariantInner)));

    this.handler.doHandle(command);

    verify(this.distributionInnerRepository, times(2)).save(this.savedDistributionInnerCaptor.capture());

    final List<DistributionInner.ProductVariantGroupId> resultInner = this.savedDistributionInnerCaptor.getAllValues().stream()
        .map(DistributionInner::productVariantGroupId)
        .toList();

    final List<DistributionInner.ProductVariantGroupId> expectedInner = List.of(
        new DistributionInner.ProductVariantGroupId(VARIANT_GROUP_3.variantGroupId()),
        new DistributionInner.ProductVariantGroupId(VARIANT_GROUP_4.variantGroupId()));

    assertThat(resultInner).containsExactlyInAnyOrderElementsOf(expectedInner);

    verify(this.eventBus, times(2)).send(this.sentDistEventsCaptor.capture());

    assertThat(this.sentDistEventsCaptor.getAllValues()).hasSize(2);

  }

  @Test
  void should_do_nothing_if_no_variant_groups_with_name_color_group() {
    final VariantGroup VARIANT_GROUP_3 = new VariantGroup(UUID.randomUUID(), UUID.randomUUID(), NOT_TARGET_VARIANT_GROUP);
    final var command = new UpdateDistributionInnerProductVariantCommand(
        new UpdateVariantGroupInput(List.of(VARIANT_GROUP_3)), TRIGGERED_BY);

    this.handler.doHandle(command);

    verify(this.distributionInnerRepository, times(0)).findByProductVariantGroupIds(anyList());
    verify(this.distributionInnerRepository, times(0)).save(this.savedDistributionInnerCaptor.capture());
    verify(this.eventBus, times(0)).send(this.sentDistEventsCaptor.capture());
  }

  @Configuration
  @Import({ClockUtils.class, Transaction.class, UpdateDistributionInnerProductVariantCommandHandler.class})
  public static class Config {

  }
}
