package com.inditex.icdmdemg.application.shared.idempotent.stubs;

import java.util.function.Consumer;

import com.inditex.icdmdemg.application.shared.idempotent.EnableIdempotentConsuming;

import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

@Component
@EnableIdempotentConsuming
public class ConsumerNotImplementInterface implements Consumer<Message<FakeEnvelope>> {

  @Override
  public void accept(final Message<FakeEnvelope> message) {
    System.out.println("Received message");
  }

}
