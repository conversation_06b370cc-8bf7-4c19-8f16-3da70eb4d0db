package com.inditex.icdmdemg.application.product.query;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.mockito.Mockito.when;

import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.product.query.GetAlternativeProductReferenceQuery.GetAlternativeProductReferenceResponse;
import com.inditex.icdmdemg.application.product.service.ProductAlternativeProvider;
import com.inditex.icdmdemg.domain.product.Product;
import com.inditex.icdmdemg.domain.product.entity.ProductColor;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GetAlternativeProductReferenceQueryHandlerTest {
  @Mock
  private ProductAlternativeProvider productAlternativeProvider;

  @InjectMocks
  private GetAlternativeProductReferenceQueryHandler sut;

  @Test
  void test_get_alternative_product_reference() {
    final var alternativeReferenceId = UUID.randomUUID();
    final var productReferenceId = new ProductReferenceId(alternativeReferenceId.toString());
    final var alternativeProduct = Instancio.of(Product.class)
        .set(field("referenceId"), new ProductReferenceId(alternativeReferenceId.toString()))
        .set(field("colorId"), new ProductColor(UUID.fromString("59b26ca9-1146-4e0e-bf53-f6e01b6670dd")))
        .create();

    when(this.productAlternativeProvider.alternativeProductReference(productReferenceId))
        .thenReturn(Optional.of(alternativeProduct));

    final var query = new GetAlternativeProductReferenceQuery(alternativeReferenceId);
    final var output = this.sut.ask(query);

    assertThat(output).isNotNull();
    assertThat(output.response()).isNotEmpty();
    final var result = output.response().get();
    assertThat(alternativeReferenceId).isEqualTo(result.alternativeReferenceId());
  }

  @Test
  void test_get_alternative_product_by_alternative_product_not_found() {
    final var alternativeReferenceId = UUID.randomUUID();
    final var productReferenceId = new ProductReferenceId(alternativeReferenceId.toString());

    when(this.productAlternativeProvider.alternativeProductReference(productReferenceId)).thenReturn(Optional.empty());

    final var query = new GetAlternativeProductReferenceQuery(alternativeReferenceId);
    final var output = this.sut.ask(query);

    assertThat(output).isNotNull();
    assertThat(output.error()).isEmpty();
    assertThat(output.response()).isNotEmpty().hasValue(new GetAlternativeProductReferenceResponse(null));
  }
}
