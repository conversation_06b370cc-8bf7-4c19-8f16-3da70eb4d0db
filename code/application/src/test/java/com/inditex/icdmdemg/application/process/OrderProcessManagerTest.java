package com.inditex.icdmdemg.application.process;

import static org.instancio.Select.field;
import static org.instancio.Select.fields;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.application.commitmentuse.command.CalculateNominatedProvisionCommand;
import com.inditex.icdmdemg.application.distributioninner.command.DeleteDistributionInnerStatusCommand;
import com.inditex.icdmdemg.application.distributioninner.command.UpdateDistributionInnerFromOrderCommand;
import com.inditex.icdmdemg.application.distributioninner.command.UpdateDistributionInnerStatusCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.AdjustDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.DeleteDistributionNominatedStatusCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.RegularizeDistributionNominatedBudgetCycleChangeCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedFromOrderCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedFromOrderCommand.UpdateDistributionNominatedFromOrderCommandResult;
import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedStatusCommand;
import com.inditex.icdmdemg.application.order.command.GetOrderBudgetCycleCommand;
import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand;
import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderCreatedOrUpdated;
import com.inditex.icdmdemg.application.order.command.ProjectOrderDeletedCommand;
import com.inditex.icdmdemg.application.order.command.ProjectOrderDeletedCommand.ProductOrderDeleted;
import com.inditex.icdmdemg.application.order.command.PublishedOrderCommand;
import com.inditex.icdmdemg.application.order.command.PublishedOrderCommand.PublishedOrder;
import com.inditex.icdmdemg.application.order.command.UpdateOrderStatusCommand;
import com.inditex.icdmdemg.application.order.command.UpdateOrderStatusCommand.StatusOrderUpdated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.entity.OrderBudgetId;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class})
class OrderProcessManagerTest {

  @Mock
  CommandBus commandBus;

  @InjectMocks
  OrderProcessManager sut;

  @Test
  void should_execute_when_product_order_created() {
    final var orderId = UUID.randomUUID();
    final var productOrderEvent = Instancio.of(ProductOrderCreatedOrUpdated.class)
        .set(field("id"), orderId.toString())
        .set(field("statusKey"), "DRAFT")
        .create();
    final var projectOrderCommand = new ProjectOrderCommand(productOrderEvent);

    this.sut.executeCreate(productOrderEvent);

    verify(this.commandBus).execute(projectOrderCommand);
    verify(this.commandBus, never()).execute(any(UpdateDistributionNominatedFromOrderCommand.class));
    verify(this.commandBus, never()).execute(any(UpdateDistributionNominatedStatusCommand.class));
    verify(this.commandBus, never()).execute(any(UpdateDistributionInnerStatusCommand.class));
    verify(this.commandBus, never()).execute(any(AdjustDistributionNominatedCommand.class));
    verify(this.commandBus, never()).execute(any(CalculateNominatedProvisionCommand.class));
  }

  @Test
  void should_update_order_and_change_budget_cycle() {
    final var orderId = UUID.randomUUID();
    final var productOrderEvent = Instancio.of(ProductOrderCreatedOrUpdated.class)
        .set(field("id"), orderId.toString())
        .set(field("statusKey"), "DRAFT")
        .create();

    final var oldBudgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final var updatedSharedRawMaterials = Instancio.create(UpdateDistributionNominatedFromOrderCommandResult.class);

    final var getOrderBudgetCycleCommand = new GetOrderBudgetCycleCommand(orderId.toString());
    final var projectOrderCommand = new ProjectOrderCommand(productOrderEvent);
    final var updateDistributionInnerFromOrderCommand = new UpdateDistributionInnerFromOrderCommand(orderId.toString(),
        "Order Updated Event");
    final var updateDistributionNominatedFromOrderCommand = new UpdateDistributionNominatedFromOrderCommand(orderId.toString(),
        "Order Updated Event");
    final var rawMaterialNominatedToCalculate = updatedSharedRawMaterials.sharedRawMaterialUpdated().stream()
        .flatMap(sharedRawMaterialNominated -> Stream.of(sharedRawMaterialNominated,
            sharedRawMaterialNominated.doBudgetCycleChange(new BudgetCycle(oldBudgetCycle))))
        .toList();
    final var calculateNominatedProvisionCommand =
        new CalculateNominatedProvisionCommand(rawMaterialNominatedToCalculate, "Order Updated Event");
    final var regularizeDistributionNominatedBudgetCycleChangeCommand =
        new RegularizeDistributionNominatedBudgetCycleChangeCommand(rawMaterialNominatedToCalculate, "Order Updated Event");

    doReturn(Void.class).when(this.commandBus).execute(any());
    doReturn(Optional.of(new OrderBudgetId(oldBudgetCycle))).when(this.commandBus).execute(any(GetOrderBudgetCycleCommand.class));
    doReturn(updatedSharedRawMaterials).when(this.commandBus).execute(any(UpdateDistributionNominatedFromOrderCommand.class));

    this.sut.executeUpdate(productOrderEvent);

    verify(this.commandBus).execute(getOrderBudgetCycleCommand);
    verify(this.commandBus).execute(projectOrderCommand);
    verify(this.commandBus).execute(updateDistributionInnerFromOrderCommand);
    verify(this.commandBus).execute(updateDistributionNominatedFromOrderCommand);
    verify(this.commandBus).execute(regularizeDistributionNominatedBudgetCycleChangeCommand);
    verify(this.commandBus).execute(calculateNominatedProvisionCommand);
    verify(this.commandBus, never()).execute(any(UpdateDistributionNominatedStatusCommand.class));
    verify(this.commandBus, never()).execute(any(UpdateDistributionInnerStatusCommand.class));
  }

  @Test
  void should_update_order_and_not_change_budget_cycle() {
    final var orderId = UUID.randomUUID();
    final var productOrderEvent = Instancio.of(ProductOrderCreatedOrUpdated.class)
        .set(field("id"), orderId.toString())
        .set(field("statusKey"), "DRAFT")
        .create();

    final var oldBudgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final var resultSharedRawMaterial = List.of(
        Instancio.of(SharedRawMaterialNominated.class)
            .set(fields(field -> field.getName().equalsIgnoreCase("budgetCycle")), new BudgetCycle(oldBudgetCycle))
            .create(),
        Instancio.of(SharedRawMaterialNominated.class)
            .set(fields(field -> field.getName().equalsIgnoreCase("budgetCycle")), new BudgetCycle(oldBudgetCycle))
            .create());

    final var getOrderBudgetCycleCommand = new GetOrderBudgetCycleCommand(orderId.toString());
    final var projectOrderCommand = new ProjectOrderCommand(productOrderEvent);
    final var updateDistributionNominatedFromOrderCommand = new UpdateDistributionNominatedFromOrderCommand(orderId.toString(),
        "Order Updated Event");
    final var calculateNominatedProvisionCommand = new CalculateNominatedProvisionCommand(List.of(), "Order Updated Event");
    final var regularizeDistributionNominatedBudgetCycleChangeCommand =
        new RegularizeDistributionNominatedBudgetCycleChangeCommand(List.of(), "Order Updated Event");

    doReturn(Void.class).when(this.commandBus).execute(any());
    doReturn(Optional.of(new OrderBudgetId(oldBudgetCycle))).when(this.commandBus).execute(any(GetOrderBudgetCycleCommand.class));
    doReturn(new UpdateDistributionNominatedFromOrderCommandResult(resultSharedRawMaterial)).when(this.commandBus)
        .execute(any(UpdateDistributionNominatedFromOrderCommand.class));

    this.sut.executeUpdate(productOrderEvent);

    verify(this.commandBus).execute(getOrderBudgetCycleCommand);
    verify(this.commandBus).execute(projectOrderCommand);
    verify(this.commandBus).execute(updateDistributionNominatedFromOrderCommand);
    verify(this.commandBus).execute(regularizeDistributionNominatedBudgetCycleChangeCommand);
    verify(this.commandBus).execute(calculateNominatedProvisionCommand);
    verify(this.commandBus, never()).execute(any(UpdateDistributionNominatedStatusCommand.class));
    verify(this.commandBus, never()).execute(any(UpdateDistributionInnerStatusCommand.class));
  }

  @Test
  void should_execute_when_product_order_deleted() {
    final var orderDeletedTriggeredBy = "OrderDeleted";
    final var order = Instancio.create(Order.class);
    final var productOrderEvent = Instancio.of(ProductOrderDeleted.class).set(field("id"), order.getId().value()).create();
    final var deleteOrderCommand = new ProjectOrderDeletedCommand(productOrderEvent);
    final var deleteDistributionNominatedStatusCommand =
        new DeleteDistributionNominatedStatusCommand(order.getId().value(), orderDeletedTriggeredBy);
    final var deleteDistributionInnerStatusCommand =
        new DeleteDistributionInnerStatusCommand(order.getId().value(), orderDeletedTriggeredBy);
    final var distributionNominated = Instancio.create(DistributionNominated.class);
    final var adjustDistributionNominatedCommand =
        new AdjustDistributionNominatedCommand(List.of(distributionNominated.sharedRawMaterial()), orderDeletedTriggeredBy);
    final var calculateNominatedProvision =
        new CalculateNominatedProvisionCommand(List.of(distributionNominated.sharedRawMaterial()), orderDeletedTriggeredBy);

    doReturn(null).when(this.commandBus).execute(any(ProjectOrderDeletedCommand.class));
    doReturn(List.of(distributionNominated)).when(this.commandBus).execute(any(DeleteDistributionNominatedStatusCommand.class));
    doReturn(null).when(this.commandBus).execute(any(DeleteDistributionInnerStatusCommand.class));

    this.sut.execute(productOrderEvent);

    verify(this.commandBus).execute(deleteOrderCommand);
    verify(this.commandBus).execute(deleteDistributionNominatedStatusCommand);
    verify(this.commandBus).execute(deleteDistributionInnerStatusCommand);
    verify(this.commandBus).execute(adjustDistributionNominatedCommand);
    verify(this.commandBus).execute(calculateNominatedProvision);
  }

  @ParameterizedTest
  @CsvSource(value = {"CANCELLED,", "CLOSED", "FORMALIZED", "DRAFT"})
  void should_execute_when_order_status_updated(final String statusKey) {
    final var statusOrderUpdated = Instancio.of(StatusOrderUpdated.class)
        .set(field("id"), UUID.randomUUID().toString())
        .set(field("statusKey"), statusKey)
        .create();
    final var updateOrderStatusCommand = new UpdateOrderStatusCommand(statusOrderUpdated);
    final var orderStatusUpdatedTriggeredBy = "OrderStatusUpdated";
    final var updateDNStatusCommand =
        new UpdateDistributionNominatedStatusCommand(UUID.fromString(statusOrderUpdated.id()), orderStatusUpdatedTriggeredBy);
    final var updateDIStatusCommand =
        new UpdateDistributionInnerStatusCommand(UUID.fromString(statusOrderUpdated.id()), orderStatusUpdatedTriggeredBy);
    final var distributionNominated = Instancio.create(DistributionNominated.class);
    final var adjustDistributionNominatedCommand =
        new AdjustDistributionNominatedCommand(List.of(distributionNominated.sharedRawMaterial()), orderStatusUpdatedTriggeredBy);
    final var calculateNominatedProvision =
        new CalculateNominatedProvisionCommand(List.of(distributionNominated.sharedRawMaterial()), orderStatusUpdatedTriggeredBy);

    doReturn(null).when(this.commandBus).execute(any(UpdateOrderStatusCommand.class));
    doReturn(null).when(this.commandBus).execute(any(UpdateDistributionInnerStatusCommand.class));
    doReturn(List.of(distributionNominated)).when(this.commandBus).execute(any(UpdateDistributionNominatedStatusCommand.class));

    this.sut.execute(statusOrderUpdated);

    verify(this.commandBus).execute(updateOrderStatusCommand);
    verify(this.commandBus).execute(updateDNStatusCommand);
    verify(this.commandBus).execute(updateDIStatusCommand);
    verify(this.commandBus).execute(adjustDistributionNominatedCommand);
    verify(this.commandBus).execute(calculateNominatedProvision);

  }

  @Test
  void should_execute_when_order_published() {
    final var publishedOrder = Instancio.of(PublishedOrder.class).set(field("id"), UUID.randomUUID().toString()).create();
    final var publishedOrderCommand = new PublishedOrderCommand(publishedOrder);
    final var updateDNStatusCommand =
        new UpdateDistributionNominatedStatusCommand(UUID.fromString(publishedOrder.id()), "Order Published Event");

    this.sut.execute(publishedOrder);

    verify(this.commandBus).execute(publishedOrderCommand);
    verify(this.commandBus).execute(updateDNStatusCommand);
  }

}
