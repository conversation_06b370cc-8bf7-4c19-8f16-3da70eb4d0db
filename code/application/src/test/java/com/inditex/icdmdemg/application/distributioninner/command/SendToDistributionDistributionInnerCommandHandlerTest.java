package com.inditex.icdmdemg.application.distributioninner.command;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SendToDistributionDistributionInnerCommandHandlerTest {

  @Mock
  private DistributionInnerRepository distributionInnerRepository;

  @Mock
  private ClockUtils clockUtils;

  @Spy
  private Transaction transaction = new Transaction();

  @Mock
  private EventBus eventBus;

  @InjectMocks
  private SendToDistributionDistributionInnerCommandHandler sut;

  @Test
  void should_fail_when_distribution_inner_does_not_exists() {
    final var distributionInnerId = "75e1ecfe-77cc-442c-9b93-41b5de71be56";
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.empty());

    final var command = new SendToDistributionDistributionInnerCommand(UUID.fromString(distributionInnerId),
        "triggeredBy");
    assertThatThrownBy(() -> this.sut.doHandle(command))
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("Distribution Inner 75e1ecfe-77cc-442c-9b93-41b5de71be56 not found");

    verifyNoInteractions(this.eventBus);
    verify(this.distributionInnerRepository, never()).save(any());
  }

  @Test
  void should_fail_when_distribution_inner_not_in_non_distributable_state() {
    final var distributionInnerId = "75e1ecfe-77cc-442c-9b93-41b5de71be56";
    final var di = DistributionInnerMother.pending().build();
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.of(di));

    final var command = new SendToDistributionDistributionInnerCommand(UUID.fromString(distributionInnerId),
        "triggeredBy");
    assertThatThrownBy(() -> this.sut.doHandle(command))
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining(
            "Distribution inner cannot be set as ready to distribute since its status PENDING is different from NON_DISTRIBUTABLE");

    verifyNoInteractions(this.eventBus);
    verify(this.distributionInnerRepository, never()).save(any());
  }

  @Test
  void should_update_status_to_pending() {
    final var now = OffsetDateTime.now();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    final var updatedBy = "triggeredBy";
    final var distributionInnerId = "75e1ecfe-77cc-442c-9b93-41b5de71be56";
    final var di = DistributionInnerMother.nonDistributable();
    when(this.distributionInnerRepository.findById(any())).thenReturn(Optional.of(di.build()));

    final var expectedDistributionInner = di
        .status(DistributionInnerStatus.PENDING)
        .audit(di.build().audit().update(updatedBy, now))
        .build();
    final var command = new SendToDistributionDistributionInnerCommand(UUID.fromString(distributionInnerId),
        updatedBy);

    this.sut.doHandle(command);

    verify(this.eventBus).send(List.of(new DistributionInnerUnifiedEvent(expectedDistributionInner, EventType.UPDATED_PENDING)));
    verify(this.distributionInnerRepository).save(expectedDistributionInner);
  }

}
