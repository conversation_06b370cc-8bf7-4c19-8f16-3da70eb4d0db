package com.inditex.icdmdemg.application.shared.idempotent;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import com.inditex.icdmdemg.domain.consumermessage.ConsumerMessage;
import com.inditex.icdmdemg.domain.consumermessage.ConsumerMessageRepository;
import com.inditex.icdmdemg.domain.consumermessage.DuplicatedMessageException;

import org.aspectj.lang.ProceedingJoinPoint;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class IdempotentConsumerServiceTest {
  @Mock
  private ConsumerMessageRepository messageRepository;

  @InjectMocks
  private IdempotentConsumerService sut;

  @Test
  void should_create_message_and_proceed() throws Throwable {
    final var message = Instancio.create(ConsumerMessage.class);
    final var proceedJoinPoint = mock(ProceedingJoinPoint.class);

    this.sut.registerMessageAndProceed(message, proceedJoinPoint);

    verify(this.messageRepository).create(message);
    verify(proceedJoinPoint).proceed();
  }

  @Test
  void should_not_proceed_when_message_creation_fails() throws Throwable {
    final var message = Instancio.create(ConsumerMessage.class);
    final var proceedJoinPoint = mock(ProceedingJoinPoint.class);
    doThrow(new DuplicatedMessageException(message)).when(this.messageRepository).create(message);

    this.sut.registerMessageAndProceed(message, proceedJoinPoint);

    verify(this.messageRepository).create(message);
    verifyNoInteractions(proceedJoinPoint);
  }

  @Test
  void should_fail_when_proceed_fails() throws Throwable {
    final var message = Instancio.create(ConsumerMessage.class);
    final var proceedJoinPoint = mock(ProceedingJoinPoint.class);
    doThrow(new RuntimeException()).when(proceedJoinPoint).proceed();

    assertThatThrownBy(() -> this.sut.registerMessageAndProceed(message, proceedJoinPoint)).isInstanceOf(IdempotentConsumerException.class);

    verify(this.messageRepository).create(message);
    verify(proceedJoinPoint).proceed();
  }
}
