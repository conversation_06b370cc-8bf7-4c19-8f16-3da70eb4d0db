package com.inditex.icdmdemg.application.distributionnominated.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;

import java.math.BigDecimal;
import java.util.Set;

import com.inditex.icdmdemg.application.distributionnominated.command.CreateDistributionNominatedCommand.NominatedRequest;
import com.inditex.icdmdemg.application.shared.validator.ConstraintValidatorConfiguration;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = {ConstraintValidatorConfiguration.class})
class CreateDistributionNominatedCommandTest {

  @MockitoBean
  private DistributionNominatedRepository nominatedRepository;

  @Autowired
  protected Validator validator;

  @Test
  void should_return_violations_when_quantities_not_positive() {
    final var nominatedRequest = Instancio.of(NominatedRequest.class)
        .set(field("theoreticalQuantity"), BigDecimal.ZERO)
        .set(field("requestedQuantity"), BigDecimal.ZERO)
        .set(field("consumptionFactor"), BigDecimal.ZERO)
        .create();

    final Set<ConstraintViolation<NominatedRequest>> violations = this.validator.validate(nominatedRequest);

    assertThat(violations).hasSize(3);
  }
}
