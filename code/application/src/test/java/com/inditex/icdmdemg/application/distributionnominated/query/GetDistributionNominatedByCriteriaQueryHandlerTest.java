package com.inditex.icdmdemg.application.distributionnominated.query;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verifyNoInteractions;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.SliceImpl;

@ExtendWith(MockitoExtension.class)
class GetDistributionNominatedByCriteriaQueryHandlerTest {
  @Mock
  private DistributionNominatedRepository distributionNominatedRepository;

  @InjectMocks
  private GetDistributionNominatedByCriteriaQueryHandler sut;

  @Test
  void should_throw_when_all_filters_are_empty() {
    final var query = new GetDistributionNominatedByCriteriaQuery(
        List.of(), null, List.of(), List.of(), List.of(), List.of(), 0, 50);
    assertThatThrownBy(() -> this.sut.ask(query))
        .isInstanceOf(ErrorException.class)
        .hasMessageContaining("Some filter is required");
    verifyNoInteractions(this.distributionNominatedRepository);
  }

  @Test
  void should_map_params_and_return_nominated_distributions() {
    final var referenceId = UUID.randomUUID();
    final var productOrderId = UUID.randomUUID();
    final var lineId = UUID.randomUUID();
    final var commitmentOrderId = UUID.randomUUID();
    final var pageRequest = PageRequest.of(0, 50);

    final var distributionNominated = Instancio.create(DistributionNominated.class);
    final var expected = new SliceImpl<>(List.of(distributionNominated));

    doReturn(expected).when(this.distributionNominatedRepository).findByCriteria(
        List.of(new ReferenceId(referenceId)),
        List.of(new ProductOrderId(productOrderId)),
        List.of(new DistributionNominatedLine.Id(lineId)),
        List.of(new CommitmentOrder.Id(commitmentOrderId)),
        List.of(), List.of(),
        pageRequest);

    final var actual = this.sut.ask(new GetDistributionNominatedByCriteriaQuery(
        List.of(referenceId), lineId, List.of(productOrderId), List.of(commitmentOrderId), List.of(), List.of(), 0, 50));

    assertThat(actual).isEqualTo(expected);
  }

}
