package com.inditex.icdmdemg.application.shipmentcommitment.command;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;

import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCancelledCommand.ShipmentCancelled;
import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitmentRepository;
import com.inditex.icdmdemg.domain.shipmentcommitment.mother.ShipmentCommitmentMother;
import com.inditex.icdmdemg.shared.utils.Transaction;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class ProjectCommitmentShipmentCancelledCommandHandlerTest {

  private ShipmentCommitmentRepository repository;

  private ProjectCommitmentShipmentCancelledCommandHandler sut;

  @BeforeAll
  void setUp() {
    this.repository = mock(ShipmentCommitmentRepository.class);
    final Transaction transaction = new Transaction();
    this.sut = new ProjectCommitmentShipmentCancelledCommandHandler(this.repository, transaction);
  }

  @Test
  void should_delete_when_shipment_commitment_exists() {
    final var shipmentCommitment = ShipmentCommitmentMother.random();
    final var shipmentCancelled = new ShipmentCancelled(
        shipmentCommitment.getId().value(),
        shipmentCommitment.getDistributionNominatedLineId().value());
    final var command = new ProjectCommitmentShipmentCancelledCommand(shipmentCancelled);

    this.sut.execute(command);

    verify(this.repository).delete(shipmentCommitment.getId());
  }

}
