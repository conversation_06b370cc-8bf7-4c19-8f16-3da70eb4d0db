package com.inditex.icdmdemg.application.distributionnominated.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.math.BigDecimal;
import java.time.Clock;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.UseId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedCloseConfigProvider;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedStatus;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.OrderLines;
import com.inditex.icdmdemg.domain.order.entity.OrderStatusKey;
import com.inditex.icdmdemg.domain.order.entity.OrderSupplierId;
import com.inditex.icdmdemg.domain.order.entity.OrderVersion;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = UpdateDistributionNominatedStatusCommandHandlerTest.Config.class)
class UpdateDistributionNominatedStatusCommandHandlerTest {

  @MockitoBean
  private DistributionNominatedRepository distributionNominatedRepository;

  @MockitoBean
  private DistributionNominatedCloseConfigProvider distributionNominatedCloseConfigProvider;

  @MockitoBean
  private OrderRepository orderRepository;

  @MockitoBean
  private EventBus eventBus;

  @Autowired
  private ClockUtils clockUtils;

  @Captor
  ArgumentCaptor<DistributionNominated> saveCaptor;

  @Captor
  ArgumentCaptor<Collection<DomainEvent<?>>> eventsCaptor;

  @Autowired
  private UpdateDistributionNominatedStatusCommandHandler sut;

  private static final BigDecimal INITIAL_REQUESTED_QUANTITY = BigDecimal.valueOf(1500, 2);

  private static final UseId USE_ID = new UseId(UUID.randomUUID());

  @BeforeEach
  void setUp() {
    reset(this.distributionNominatedRepository, this.distributionNominatedCloseConfigProvider, this.orderRepository, this.eventBus);
  }

  @Test
  void should_do_nothing_when_order_does_not_exist() {
    final var orderId = UuidMother.fromInteger(101);

    final var now = OffsetDateTimeMother.fromInteger(2);
    this.clockUtils.setClock(Clock.fixed(now.toInstant(), ZoneId.of("UTC")));

    doReturn(Optional.empty()).when(this.orderRepository).find(new OrderId(orderId.toString()));

    this.sut.execute(new UpdateDistributionNominatedStatusCommand(orderId, "test"));

    verifyNoInteractions(this.distributionNominatedRepository, this.eventBus);
  }

  @ParameterizedTest
  @MethodSource("provideStatusCases")
  void should_update_status_with_order_state(final OrderStatusKey orderStatusKey, final DistributionNominatedStatus currentStatus,
      final DistributionNominatedStatus expectedStatus, final String expectedEventType, final boolean isPublished,
      final BigDecimal distributedQuantity, final Boolean shouldAdjustRequestedQuantityOnClose,
      final BigDecimal expectedRequestedQuantity) {

    final var orderId = UuidMother.fromInteger(101);

    doReturn(shouldAdjustRequestedQuantityOnClose).when(this.distributionNominatedCloseConfigProvider).shouldAdjustFor(any(), any());

    final var now = OffsetDateTimeMother.fromInteger(2);
    this.clockUtils.setClock(Clock.fixed(now.toInstant(), ZoneId.of("UTC")));

    final var productOrderId = new OrderId(orderId.toString());
    final var order = new Order(productOrderId, orderStatusKey, new OrderLines(new ArrayList<>()),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(2)),
        new OrderSupplierId(UUID.randomUUID().toString()), isPublished, OrderVersion.firstVersion());

    final var distributionNominated = DistributionNominatedMother
        .withStatus(currentStatus)
        .requestedQuantity(new DistributionNominated.RequestedQuantity(INITIAL_REQUESTED_QUANTITY))
        .distributedQuantity(new DistributedQuantity(distributedQuantity))
        .productOrderId(new ProductOrderId(orderId))
        .useId(USE_ID)
        .build();

    doReturn(Optional.of(order)).when(this.orderRepository).find(productOrderId);
    doReturn(List.of(distributionNominated)).when(this.distributionNominatedRepository).findByProductOrderId(new ProductOrderId(orderId));

    final List<DistributionNominated> result = this.sut.execute(new UpdateDistributionNominatedStatusCommand(orderId, "test"));

    verify(this.distributionNominatedRepository).save(this.saveCaptor.capture());
    verify(this.eventBus).send(this.eventsCaptor.capture());

    assertThat(this.saveCaptor.getValue()).extracting(DistributionNominated::status).isEqualTo(expectedStatus);
    if (orderStatusKey.equals(OrderStatusKey.CANCELLED)) {
      assertThat(this.saveCaptor.getValue()).extracting(DistributionNominated::audit).extracting(CompleteAudit::deletedAt)
          .isEqualTo(now);
    } else {
      assertThat(this.saveCaptor.getValue()).extracting(DistributionNominated::audit).extracting(CompleteAudit::updatedAt)
          .isEqualTo(now);
      assertThat(this.saveCaptor.getValue()).extracting(DistributionNominated::audit).extracting(CompleteAudit::updatedBy)
          .isEqualTo("test");
    }

    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(domainEvent -> domainEvent instanceof DistributionNominatedUnifiedEvent)
        .allMatch(domainEvent -> domainEvent.eventType().equals(expectedEventType));
    assertThat(this.saveCaptor.getValue()).extracting(DistributionNominated::requestedQuantity)
        .extracting(DistributionNominated.RequestedQuantity::value).isEqualTo(expectedRequestedQuantity);
    assertThat(result).containsExactlyInAnyOrderElementsOf(List.of(distributionNominated));
  }

  public Stream<Arguments> provideStatusCases() {
    return Stream.of(
        Arguments.of(OrderStatusKey.DRAFT, DistributionNominatedStatus.NON_DISTRIBUTABLE, DistributionNominatedStatus.IN_PROGRESS,
            EventType.UPDATED.value, true, BigDecimal.TEN, null, INITIAL_REQUESTED_QUANTITY),

        Arguments.of(OrderStatusKey.DRAFT, DistributionNominatedStatus.CLOSED, DistributionNominatedStatus.IN_PROGRESS,
            EventType.UPDATED.value, true, BigDecimal.TEN, null, INITIAL_REQUESTED_QUANTITY),

        Arguments.of(OrderStatusKey.DRAFT, DistributionNominatedStatus.CLOSED, DistributionNominatedStatus.NON_DISTRIBUTABLE,
            EventType.UPDATED.value, false, BigDecimal.ZERO, null, INITIAL_REQUESTED_QUANTITY),

        Arguments.of(OrderStatusKey.DRAFT, DistributionNominatedStatus.CLOSED, DistributionNominatedStatus.PENDING,
            EventType.UPDATED.value, true, BigDecimal.ZERO, null, INITIAL_REQUESTED_QUANTITY),

        Arguments.of(OrderStatusKey.CANCELLED, DistributionNominatedStatus.IN_PROGRESS, DistributionNominatedStatus.IN_PROGRESS,
            EventType.DELETED.value, false, BigDecimal.ZERO, true, INITIAL_REQUESTED_QUANTITY),

        Arguments.of(OrderStatusKey.CANCELLED, DistributionNominatedStatus.PENDING, DistributionNominatedStatus.PENDING,
            EventType.DELETED.value, false, BigDecimal.ZERO, true, INITIAL_REQUESTED_QUANTITY),

        Arguments.of(OrderStatusKey.CANCELLED, DistributionNominatedStatus.NON_DISTRIBUTABLE, DistributionNominatedStatus.NON_DISTRIBUTABLE,
            EventType.DELETED.value, false, BigDecimal.ZERO, true, INITIAL_REQUESTED_QUANTITY),

        Arguments.of(OrderStatusKey.CLOSED, DistributionNominatedStatus.IN_PROGRESS, DistributionNominatedStatus.CLOSED,
            EventType.CLOSED.value, false, BigDecimal.ZERO, false, INITIAL_REQUESTED_QUANTITY),

        Arguments.of(OrderStatusKey.CLOSED, DistributionNominatedStatus.PENDING, DistributionNominatedStatus.CLOSED,
            EventType.CLOSED.value, false, BigDecimal.ZERO, false, INITIAL_REQUESTED_QUANTITY),

        Arguments.of(OrderStatusKey.CLOSED, DistributionNominatedStatus.NON_DISTRIBUTABLE, DistributionNominatedStatus.CLOSED,
            EventType.CLOSED.value, false, BigDecimal.ZERO, false, INITIAL_REQUESTED_QUANTITY));

  }

  @Configuration
  @Import({ClockUtils.class, Transaction.class, UpdateDistributionNominatedStatusCommandHandler.class})
  public static class Config {
  }
}
