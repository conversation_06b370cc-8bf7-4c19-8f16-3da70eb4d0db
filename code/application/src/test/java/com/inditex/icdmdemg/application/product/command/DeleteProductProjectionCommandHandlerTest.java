package com.inditex.icdmdemg.application.product.command;

import static org.mockito.Mockito.verify;

import java.util.UUID;

import com.inditex.icdmdemg.application.product.command.DeleteProductProjectionCommand.ProductReferenceIdDeleted;
import com.inditex.icdmdemg.application.product.service.ProductProjector;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith({MockitoExtension.class})
class DeleteProductProjectionCommandHandlerTest {

  @Mock
  ProductProjector productProjector;

  @InjectMocks
  DeleteProductProjectionCommandHandler sut;

  @Test
  void should_call_product_projector() {
    final var referenceId = UUID.randomUUID();
    final var command = new DeleteProductProjectionCommand(new ProductReferenceIdDeleted(referenceId));

    this.sut.doHandle(command);

    verify(this.productProjector).deleteReference(referenceId);
  }
}
