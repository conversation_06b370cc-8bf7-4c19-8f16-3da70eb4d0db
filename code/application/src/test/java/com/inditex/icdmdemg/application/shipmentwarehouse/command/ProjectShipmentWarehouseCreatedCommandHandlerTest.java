package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.verify;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCreatedCommand.ShipmentWarehouseCreated;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouseRepository;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseId;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseInner;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseInnerId;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseInnerLineId;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseQuantity;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTrackingCode;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseVersion;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

class ProjectShipmentWarehouseCreatedCommandHandlerTest {

  ArgumentCaptor<ShipmentWarehouse> shipmentWarehouseCaptor = ArgumentCaptor.forClass(ShipmentWarehouse.class);

  private final ShipmentWarehouseRepository shipmentWarehouseRepository = mock();

  private final ClockUtils clockUtils = mock();

  private final Transaction transaction = spy();

  private final UuidGenerator uuidGenerator = mock();

  private final ProjectShipmentWarehouseCreatedCommandHandler sut =
      new ProjectShipmentWarehouseCreatedCommandHandler(this.shipmentWarehouseRepository, this.clockUtils, this.transaction,
          this.uuidGenerator);

  @Test
  void should_project_shipment_warehouse() {
    final var uuid = UuidMother.fromInteger(111);
    final var now = OffsetDateTime.now();
    doReturn(uuid).when(this.uuidGenerator).generate();
    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    final var trackingCode = "1";
    final ShipmentWarehouseCreated shipmentWarehouseCreated = Instancio.of(ShipmentWarehouseCreated.class)
        .set(field("trackingCode"), trackingCode)
        .create();
    final var command = new ProjectShipmentWarehouseCreatedCommand(shipmentWarehouseCreated);

    this.sut.execute(command);

    verify(this.shipmentWarehouseRepository).save(this.shipmentWarehouseCaptor.capture());

    final var capturedShipmentWarehouse = this.shipmentWarehouseCaptor.getValue();
    assertThat(capturedShipmentWarehouse.getId()).isNotNull().isEqualTo(new ShipmentWarehouseId(uuid.toString()));
    assertThat(capturedShipmentWarehouse.getTrackingCode()).isEqualTo(new ShipmentWarehouseTrackingCode(trackingCode));
    assertThat(capturedShipmentWarehouse.getDistributionInner())
        .isEqualTo(new ShipmentWarehouseInner(
            new ShipmentWarehouseInnerId(shipmentWarehouseCreated.distributionInnerId().toString()),
            new ShipmentWarehouseInnerLineId(shipmentWarehouseCreated.distributionInnerLineId().toString())));
    assertThat(capturedShipmentWarehouse.getQuantity()).isEqualTo(new ShipmentWarehouseQuantity(BigDecimal.ZERO));
    assertThat(capturedShipmentWarehouse.getStartDate()).isNull();
    assertThat(capturedShipmentWarehouse.getEndDate()).isNull();
    assertThat(capturedShipmentWarehouse.getLastUpdateDate()).isNull();
    assertThat(capturedShipmentWarehouse.getVersion()).isEqualTo(ShipmentWarehouseVersion.firstVersion());
    assertThat(capturedShipmentWarehouse.getUpdatedAt()).isNotNull().hasFieldOrPropertyWithValue("value", now);
    assertThat(capturedShipmentWarehouse.getCreatedAt()).isNotNull().hasFieldOrPropertyWithValue("value", now);
  }

}
