package com.inditex.icdmdemg.application.distributionsummary.query;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doReturn;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.commitmentuse.command.ProductMother;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.GetUsesAvailabilityV2Request;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityTotalInnerQuantityV2;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityTotalNominatedQuantityV2;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityV2;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityV2Response;
import com.inditex.icdmdemg.application.use.evaluation.PurchasePurposeEvaluationResults;
import com.inditex.icdmdemg.application.use.mother.PurchasePurposeEvaluationResultMother;
import com.inditex.icdmdemg.application.use.service.GetOrderedValidUseProvider;
import com.inditex.icdmdemg.application.use.service.PurchasePurposeParameter;
import com.inditex.icdmdemg.domain.category.CategoryRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUseQuantitiesDTO;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.BudgetId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvisionRepository;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedTotalQuantityByUseDTO;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedTotalQuantityByUseDTO.TotalQuantity;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.OrderSupplierId;
import com.inditex.icdmdemg.domain.order.mother.OrderMother;
import com.inditex.icdmdemg.domain.product.ProductRepository;
import com.inditex.icdmdemg.domain.product.ProductTaxonomyRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductFamilies;
import com.inditex.icdmdemg.domain.product.entity.ProductFamily;
import com.inditex.icdmdemg.domain.product.entity.ProductFamily.CampaignId;
import com.inditex.icdmdemg.domain.product.entity.ProductFamily.FamilyId;
import com.inditex.icdmdemg.domain.product.entity.ProductOwner;
import com.inditex.icdmdemg.domain.product.entity.ProductOwner.OwnerId;
import com.inditex.icdmdemg.domain.product.entity.ProductOwners;
import com.inditex.icdmdemg.domain.product.entity.ProductTaxonomy;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.Use.Taxonomy;
import com.inditex.icdmdemg.domain.use.UseRepository;
import com.inditex.icdmdemg.domain.use.entity.AssignableType;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;
import com.inditex.icdmdemg.domain.use.entity.PurchaseType;
import com.inditex.icdmdemg.domain.use.entity.UseName;
import com.inditex.icdmdemg.domain.use.entity.UseName.Lang;
import com.inditex.icdmdemg.domain.use.entity.UseName.Name;
import com.inditex.icdmdemg.domain.use.entity.UseNames;
import com.inditex.icdmdemg.domain.use.mother.UseMother;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseInnerPendingByUseDTO;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseInnerPendingByUseDTO.Pending;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseInnerPendingByUseDTO.UseId;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseRepository;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.BudgetCycle;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.ProductReferenceId;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseInnerStockByUseDTO;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseInnerStockByUseDTO.Stock;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseRepository;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO.ReferenceId;
import com.inditex.icdmdemg.provis.domain.use.UseDTO.UsePurchaseType;
import com.inditex.icdmdemg.shared.utils.LocaleUtils;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;

import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class GetUsesAvailabilityV2QueryHandlerTest {

  final UseRepository useRepository = Mockito.mock(UseRepository.class);

  final OrderRepository orderRepository = Mockito.mock(OrderRepository.class);

  final ProductRepository productRepository = Mockito.mock(ProductRepository.class);

  final OrderUseRepository orderUseRepository = Mockito.mock(OrderUseRepository.class);

  final CategoryRepository categoryRepository = Mockito.mock(CategoryRepository.class);

  final GetOrderedValidUseProvider useProvider = Mockito.mock(GetOrderedValidUseProvider.class);

  final ProductUseRepository productUseRepository = Mockito.mock(ProductUseRepository.class);

  final ProductTaxonomyRepository productTaxonomyRepository = Mockito.mock(ProductTaxonomyRepository.class);

  final DistributionInnerRepository distributionInnerRepository = Mockito.mock(DistributionInnerRepository.class);

  final NominatedProvisionRepository nominatedProvisionRepository = Mockito.mock(NominatedProvisionRepository.class);

  final GetUsesAvailabilityV2QueryHandler sut = new GetUsesAvailabilityV2QueryHandler(
      this.useRepository,
      this.orderRepository,
      this.productRepository,
      this.orderUseRepository,
      this.categoryRepository,
      this.useProvider,
      this.productUseRepository,
      this.productTaxonomyRepository,
      this.distributionInnerRepository,
      this.nominatedProvisionRepository);

  @Test
  void should_fail_when_taxonomy_MP_not_valid() {
    final UUID referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final UUID productOrderId = UUID.randomUUID();
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var languageEN = "en";

    doReturn(Optional.empty()).when(this.productTaxonomyRepository).findByReferenceId(referenceId);

    final var query = new GetUsesAvailabilityV2Query(new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, false, purchaseType, languageEN));

    assertThatThrownBy(() -> this.sut.ask(query)).isInstanceOf(ErrorException.class);
  }

  @Test
  void should_fail_when_referencePT_not_valid() {
    final UUID referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final UUID productOrderId = UUID.randomUUID();
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var languageEN = "en";

    doReturn(Optional.empty()).when(this.productTaxonomyRepository).findByReferenceId(referenceId);

    final var query = new GetUsesAvailabilityV2Query(new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, false, purchaseType, languageEN));

    assertThatThrownBy(() -> this.sut.ask(query)).isInstanceOf(ErrorException.class);
  }

  @Test
  void should_fail_when_order_not_valid() {
    final UUID referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final UUID productOrderId = UUID.randomUUID();
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var languageEN = "en";
    final var taxonomy = "TRIMMING";
    final var now = OffsetDateTime.now();

    final var taxonomyMP = ProductTaxonomy.create(UUID.randomUUID(), taxonomy, now, now);

    doReturn(Optional.of(taxonomyMP)).when(this.productTaxonomyRepository).findByReferenceId(referenceId);
    doReturn(Optional.empty()).when(this.productRepository)
        .findByReferenceId(new com.inditex.icdmdemg.domain.product.entity.ProductReferenceId(productReferenceId.toString()));

    final var query = new GetUsesAvailabilityV2Query(new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, false, purchaseType, languageEN));

    assertThatThrownBy(() -> this.sut.ask(query)).isInstanceOf(ErrorException.class);
  }

  @Test
  void should_fail_when_referenceMP_not_valid() {
    final UUID referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final UUID productOrderId = UUID.randomUUID();
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var languageEN = "en";
    final var taxonomy = "TRIMMING";
    final var family = UrnConstantsEnum.FAMILY.prefixWithUrn(UUID.randomUUID());
    final var owner = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var campaign = UrnConstantsEnum.CAMPAIGN.prefixWithUrn(UUID.randomUUID());
    final var now = OffsetDateTime.now();

    final var taxonomyMP = ProductTaxonomy.create(UUID.randomUUID(), taxonomy, now, now);
    final var referencePT = ProductMother.created()
        .owners(new ProductOwners(List.of(ProductOwner.create(new OwnerId(owner), now))))
        .families(new ProductFamilies(List.of(ProductFamily.create(new CampaignId(campaign), new FamilyId(family), now))))
        .build();

    doReturn(Optional.of(taxonomyMP)).when(this.productTaxonomyRepository).findByReferenceId(referenceId);
    doReturn(Optional.of(referencePT)).when(this.productRepository)
        .findByReferenceId(new com.inditex.icdmdemg.domain.product.entity.ProductReferenceId(productReferenceId.toString()));
    doReturn(Optional.empty()).when(this.orderRepository).find(new OrderId(productOrderId.toString()));

    final var query = new GetUsesAvailabilityV2Query(new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, false, purchaseType, languageEN));

    assertThatThrownBy(() -> this.sut.ask(query)).isInstanceOf(ErrorException.class);
  }

  @Test
  void should_fail_when_not_BUYER_config() {
    final UUID referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final UUID productOrderId = UUID.randomUUID();
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var languageEN = "en";
    final var taxonomy = "TRIMMING";
    final var family = UrnConstantsEnum.FAMILY.prefixWithUrn(UUID.randomUUID());
    final var owner = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var campaign = UrnConstantsEnum.CAMPAIGN.prefixWithUrn(UUID.randomUUID());
    final var buyerGroup = UrnConstantsEnum.BUYER_GROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerSubgroup = UrnConstantsEnum.BUYER_SUBGROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerCode = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var supplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var now = OffsetDateTime.now();

    final var taxonomyMP = ProductTaxonomy.create(UUID.randomUUID(), taxonomy, now, now);
    final var referencePT = ProductMother.created()
        .owners(new ProductOwners(List.of(ProductOwner.create(new OwnerId(owner), now))))
        .families(new ProductFamilies(List.of(ProductFamily.create(new CampaignId(campaign), new FamilyId(family), now))))
        .build();
    final var order =
        OrderMother.random().orderId(new OrderId(productOrderId.toString())).supplierId(new OrderSupplierId(supplierId)).build();
    final var parents = List.of(buyerGroup, buyerSubgroup, buyerCode);

    doReturn(Optional.of(taxonomyMP)).when(this.productTaxonomyRepository).findByReferenceId(referenceId);
    doReturn(Optional.of(referencePT)).when(this.productRepository)
        .findByReferenceId(new com.inditex.icdmdemg.domain.product.entity.ProductReferenceId(productReferenceId.toString()));
    doReturn(Optional.of(order)).when(this.orderRepository).find(new OrderId(productOrderId.toString()));
    doReturn(parents).when(this.categoryRepository).getParents(List.of(owner));

    final var query = new GetUsesAvailabilityV2Query(new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, false, purchaseType, languageEN));

    assertThatThrownBy(() -> this.sut.ask(query)).isInstanceOf(ErrorException.class);
  }

  @Test
  void should_fail_when_has_more_than_one_BUYER_config() {
    final UUID referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final UUID productOrderId = UUID.randomUUID();
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var languageEN = "en";
    final var taxonomy = "TRIMMING";
    final var family = UrnConstantsEnum.FAMILY.prefixWithUrn(UUID.randomUUID());
    final var owner = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var campaign = UrnConstantsEnum.CAMPAIGN.prefixWithUrn(UUID.randomUUID());
    final var buyer1 = UrnConstantsEnum.BUYER.prefixWithUrn(UUID.randomUUID());
    final var buyer2 = UrnConstantsEnum.BUYER.prefixWithUrn(UUID.randomUUID());
    final var buyerGroup = UrnConstantsEnum.BUYER_GROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerSubgroup = UrnConstantsEnum.BUYER_SUBGROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerCode = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var supplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var now = OffsetDateTime.now();

    final var taxonomyMP = ProductTaxonomy.create(UUID.randomUUID(), taxonomy, now, now);
    final var referencePT = ProductMother.created()
        .owners(new ProductOwners(List.of(ProductOwner.create(new OwnerId(owner), now))))
        .families(new ProductFamilies(List.of(ProductFamily.create(new CampaignId(campaign), new FamilyId(family), now))))
        .build();
    final var order =
        OrderMother.random().orderId(new OrderId(productOrderId.toString())).supplierId(new OrderSupplierId(supplierId)).build();
    final var parents = List.of(buyer1, buyer2, buyerGroup, buyerSubgroup, buyerCode);

    doReturn(Optional.of(taxonomyMP)).when(this.productTaxonomyRepository).findByReferenceId(referenceId);
    doReturn(Optional.of(referencePT)).when(this.productRepository)
        .findByReferenceId(new com.inditex.icdmdemg.domain.product.entity.ProductReferenceId(productReferenceId.toString()));
    doReturn(Optional.of(order)).when(this.orderRepository).find(new OrderId(productOrderId.toString()));
    doReturn(parents).when(this.categoryRepository).getParents(List.of(owner));

    final var query = new GetUsesAvailabilityV2Query(new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, false, purchaseType, languageEN));

    assertThatThrownBy(() -> this.sut.ask(query)).isInstanceOf(ErrorException.class);
  }

  @Test
  void should_return_uses_availability_response_when_no_nominated_provision() {
    final UUID referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final UUID productOrderId = UUID.randomUUID();
    final Pending pendingUse1 = new Pending(BigDecimal.valueOf(100));
    final Pending pendingUse2 = new Pending(BigDecimal.valueOf(80));
    final Stock stockUse1 = new Stock(BigDecimal.valueOf(70));
    final Stock stockUse2 = new Stock(BigDecimal.valueOf(50));
    final RequestedQuantity requestedQuantityUse1 = new RequestedQuantity(BigDecimal.valueOf(30));
    final RequestedQuantity requestedQuantityUse2 = new RequestedQuantity(BigDecimal.valueOf(10));
    final DistributedQuantity distributedQuantityUse1 = new DistributedQuantity(BigDecimal.valueOf(25));
    final DistributedQuantity distributedQuantityUse2 = new DistributedQuantity(BigDecimal.valueOf(20));

    final var taxonomy = "TRIMMING";
    final var owner = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var campaign = UrnConstantsEnum.CAMPAIGN.prefixWithUrn(UUID.randomUUID());
    final var family = UrnConstantsEnum.FAMILY.prefixWithUrn(UUID.randomUUID());
    final var buyer = UrnConstantsEnum.BUYER.prefixWithUrn(UUID.randomUUID());
    final var buyerGroup = UrnConstantsEnum.BUYER_GROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerSubgroup = UrnConstantsEnum.BUYER_SUBGROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerCode = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var supplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var nameEN = "example";
    final var nameES = "ejemplo";
    final var languageEN = "en";
    final var languageES = "es";
    final var now = OffsetDateTime.now();

    final var taxonomyMP = ProductTaxonomy.create(UUID.randomUUID(), taxonomy, now, now);
    final var referencePT = ProductMother.created()
        .owners(new ProductOwners(List.of(ProductOwner.create(new OwnerId(owner), now))))
        .families(new ProductFamilies(List.of(ProductFamily.create(new CampaignId(campaign), new FamilyId(family), now))))
        .build();
    final var order =
        OrderMother.random().orderId(new OrderId(productOrderId.toString())).supplierId(new OrderSupplierId(supplierId)).build();
    final var parents = List.of(buyer, buyerGroup, buyerSubgroup, buyerCode);
    final var campaignsBudgetCycle = List.of(campaign);

    final var params = List.of(
        new PurchasePurposeParameter(PurchasePurposeConditionName.BUYERCODE, List.of(buyerCode)),
        new PurchasePurposeParameter(PurchasePurposeConditionName.BUYERSUBGROUP, List.of(buyerSubgroup)),
        new PurchasePurposeParameter(PurchasePurposeConditionName.BUYERGROUP, List.of(buyerGroup)),
        new PurchasePurposeParameter(PurchasePurposeConditionName.FAMILY, List.of(family)),
        new PurchasePurposeParameter(PurchasePurposeConditionName.SUPPLIER_PT, List.of(supplierId)));

    final var useNames = new UseNames(List.of(
        new UseName(new Name(nameEN), new Lang(languageEN), BasicAudit.create(now)),
        new UseName(new Name(nameES), new Lang(languageES), BasicAudit.create(now))));

    final var use1Id = new Use.Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));
    final var use1 = UseMother.generateBuild()
        .id(use1Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var use2Id = new Use.Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba269"));
    final var use2 = UseMother.generateBuild()
        .id(use2Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var uses = List.of(use1, use2);
    final var orderedValidUses = new PurchasePurposeEvaluationResults(List.of(
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use1.getId(), PurchasePurposeConditionName.BUYERGROUP),
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use2.getId(), PurchasePurposeConditionName.BUYERSUBGROUP)));
    final List<UUID> orderUseIdList = List.of(use1.getId().value(), use2.getId().value());

    final List<OrderUseInnerPendingByUseDTO> orderUses = List.of(
        new OrderUseInnerPendingByUseDTO(new UseId(use1Id.value()), pendingUse1),
        new OrderUseInnerPendingByUseDTO(new UseId(use2Id.value()), pendingUse2));

    final List<ProductUseInnerStockByUseDTO> innerProductUseSummaries = List.of(
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use1.getId().value()), stockUse1),
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use2.getId().value()), stockUse2));

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO1 =
        new DistributionInnerUseQuantitiesDTO(
            new DistributionInner.UseId(use1.getId().value()),
            new RequestedQuantity(requestedQuantityUse1.value()),
            new DistributedQuantity(distributedQuantityUse1.value()));

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO2 = new DistributionInnerUseQuantitiesDTO(
        new DistributionInner.UseId(use2.getId().value()),
        new RequestedQuantity(requestedQuantityUse2.value()),
        new DistributedQuantity(distributedQuantityUse2.value()));

    final var distributionInnerUseQuantitiesDTOList = List.of(distributionInnerUseQuantitiesDTO1, distributionInnerUseQuantitiesDTO2);

    doReturn(Optional.of(taxonomyMP)).when(this.productTaxonomyRepository).findByReferenceId(referenceId);
    doReturn(Optional.of(referencePT)).when(this.productRepository)
        .findByReferenceId(new com.inditex.icdmdemg.domain.product.entity.ProductReferenceId(productReferenceId.toString()));
    doReturn(Optional.of(order)).when(this.orderRepository).find(new OrderId(productOrderId.toString()));
    doReturn(parents).when(this.categoryRepository).getParents(List.of(owner));
    doReturn(campaignsBudgetCycle).when(this.categoryRepository).getEquivalent(List.of(budgetCycle));

    doReturn(orderedValidUses).when(this.useProvider).getOrderedValidUse(any(), any(), any(), anyList());
    doReturn(uses).when(this.useRepository).findByIds(List.of(use1Id, use2Id));

    doReturn(orderUses).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    doReturn(innerProductUseSummaries).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ReferenceId(referenceId), orderUseIdList.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));
    doReturn(List.of()).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            orderUseIdList.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));
    doReturn(distributionInnerUseQuantitiesDTOList).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        orderUseIdList.stream().map(DistributionInner.UseId::new).toList(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityV2Query(new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, false, purchaseType, languageEN));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityV2Response(
        List.of(
            new UsesAvailabilityV2(
                use1Id.value(),
                use1.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use1.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(0))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(165)))),
            new UsesAvailabilityV2(
                use2Id.value(),
                use2.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use2.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(0))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(130))))));

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

  @Test
  void should_return_uses_availability_response_when_no_order_use() {
    final UUID referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final UUID productOrderId = UUID.randomUUID();
    final Stock stockUse1 = new Stock(BigDecimal.valueOf(70));
    final Stock stockUse2 = new Stock(BigDecimal.valueOf(50));
    final RequestedQuantity requestedQuantityUse1 = new RequestedQuantity(BigDecimal.valueOf(30));
    final RequestedQuantity requestedQuantityUse2 = new RequestedQuantity(BigDecimal.valueOf(10));
    final DistributedQuantity distributedQuantityUse1 = new DistributedQuantity(BigDecimal.valueOf(25));
    final DistributedQuantity distributedQuantityUse2 = new DistributedQuantity(BigDecimal.valueOf(20));
    final TotalQuantity totalQuantityUse1 = new TotalQuantity(BigDecimal.valueOf(75));
    final TotalQuantity totalQuantityUse2 = new TotalQuantity(BigDecimal.valueOf(60));

    final var taxonomy = "TRIMMING";
    final var owner = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var campaign = UrnConstantsEnum.CAMPAIGN.prefixWithUrn(UUID.randomUUID());
    final var family = UrnConstantsEnum.FAMILY.prefixWithUrn(UUID.randomUUID());
    final var buyer = UrnConstantsEnum.BUYER.prefixWithUrn(UUID.randomUUID());
    final var buyerGroup = UrnConstantsEnum.BUYER_GROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerSubgroup = UrnConstantsEnum.BUYER_SUBGROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerCode = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var supplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var nameEN = "example";
    final var nameES = "ejemplo";
    final var languageEN = "en";
    final var languageES = "es";
    final var now = OffsetDateTime.now();

    final var taxonomyMP = ProductTaxonomy.create(UUID.randomUUID(), taxonomy, now, now);
    final var referencePT = ProductMother.created()
        .owners(new ProductOwners(List.of(ProductOwner.create(new OwnerId(owner), now))))
        .families(new ProductFamilies(List.of(ProductFamily.create(new CampaignId(campaign), new FamilyId(family), now))))
        .build();
    final var order =
        OrderMother.random().orderId(new OrderId(productOrderId.toString())).supplierId(new OrderSupplierId(supplierId)).build();
    final var parents = List.of(buyer, buyerGroup, buyerSubgroup, buyerCode);
    final var campaignsBudgetCycle = List.of(campaign);

    final var useNames = new UseNames(List.of(
        new UseName(new Name(nameEN), new Lang(languageEN), BasicAudit.create(now)),
        new UseName(new Name(nameES), new Lang(languageES), BasicAudit.create(now))));

    final var use1Id = new Use.Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));
    final var use1 = UseMother.generateBuild()
        .id(use1Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var use2Id = new Use.Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba269"));
    final var use2 = UseMother.generateBuild()
        .id(use2Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var uses = List.of(use1, use2);
    final var orderedValidUses = new PurchasePurposeEvaluationResults(List.of(
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use1.getId(), PurchasePurposeConditionName.BUYERGROUP),
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use2.getId(), PurchasePurposeConditionName.BUYERSUBGROUP)));
    final List<UUID> orderUseIdList = List.of(use1.getId().value(), use2.getId().value());

    final List<ProductUseInnerStockByUseDTO> innerProductUseSummaries = List.of(
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use1.getId().value()), stockUse1),
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use2.getId().value()), stockUse2));

    final List<NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseDTOList = List.of(
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use1.getId().value()),
            totalQuantityUse1),
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use2.getId().value()),
            totalQuantityUse2));

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO1 =
        new DistributionInnerUseQuantitiesDTO(
            new DistributionInner.UseId(use1.getId().value()),
            new RequestedQuantity(requestedQuantityUse1.value()),
            new DistributedQuantity(distributedQuantityUse1.value()));

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO2 = new DistributionInnerUseQuantitiesDTO(
        new DistributionInner.UseId(use2.getId().value()),
        new RequestedQuantity(requestedQuantityUse2.value()),
        new DistributedQuantity(distributedQuantityUse2.value()));

    final var distributionInnerUseQuantitiesDTOList = List.of(distributionInnerUseQuantitiesDTO1, distributionInnerUseQuantitiesDTO2);

    doReturn(Optional.of(taxonomyMP)).when(this.productTaxonomyRepository).findByReferenceId(referenceId);
    doReturn(Optional.of(referencePT)).when(this.productRepository)
        .findByReferenceId(new com.inditex.icdmdemg.domain.product.entity.ProductReferenceId(productReferenceId.toString()));
    doReturn(Optional.of(order)).when(this.orderRepository).find(new OrderId(productOrderId.toString()));
    doReturn(parents).when(this.categoryRepository).getParents(List.of(owner));
    doReturn(campaignsBudgetCycle).when(this.categoryRepository).getEquivalent(List.of(budgetCycle));

    doReturn(orderedValidUses).when(this.useProvider).getOrderedValidUse(
        any(), any(), any(), anyList());
    doReturn(uses).when(this.useRepository).findByIds(List.of(use1Id, use2Id));

    doReturn(List.of()).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    doReturn(innerProductUseSummaries).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ProductUseSummaryDTO.ReferenceId(referenceId), orderUseIdList.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    doReturn(List.of()).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    doReturn(nominatedTotalQuantityByUseDTOList).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            orderUseIdList.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    doReturn(distributionInnerUseQuantitiesDTOList).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        orderUseIdList.stream().map(DistributionInner.UseId::new).toList(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityV2Query(new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, false, purchaseType, languageEN));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityV2Response(
        List.of(
            new UsesAvailabilityV2(
                use1Id.value(),
                use1.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use1.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(75))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(65)))),
            new UsesAvailabilityV2(
                use2Id.value(),
                use2.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use2.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(60))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(50))))));

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

  @Test
  void should_return_uses_availability_response_when_no_product_use() {
    final UUID referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final UUID productOrderId = UUID.randomUUID();
    final Pending pendingUse1 = new Pending(BigDecimal.valueOf(100));
    final Pending pendingUse2 = new Pending(BigDecimal.valueOf(80));
    final RequestedQuantity requestedQuantityUse1 = new RequestedQuantity(BigDecimal.valueOf(30));
    final RequestedQuantity requestedQuantityUse2 = new RequestedQuantity(BigDecimal.valueOf(10));
    final DistributedQuantity distributedQuantityUse1 = new DistributedQuantity(BigDecimal.valueOf(25));
    final DistributedQuantity distributedQuantityUse2 = new DistributedQuantity(BigDecimal.valueOf(20));
    final TotalQuantity totalQuantityUse1 = new TotalQuantity(BigDecimal.valueOf(75));
    final TotalQuantity totalQuantityUse2 = new TotalQuantity(BigDecimal.valueOf(60));

    final var taxonomy = "TRIMMING";
    final var owner = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var campaign = UrnConstantsEnum.CAMPAIGN.prefixWithUrn(UUID.randomUUID());
    final var family = UrnConstantsEnum.FAMILY.prefixWithUrn(UUID.randomUUID());
    final var buyer = UrnConstantsEnum.BUYER.prefixWithUrn(UUID.randomUUID());
    final var buyerGroup = UrnConstantsEnum.BUYER_GROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerSubgroup = UrnConstantsEnum.BUYER_SUBGROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerCode = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var supplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var nameEN = "example";
    final var nameES = "ejemplo";
    final var languageEN = "en";
    final var languageES = "es";
    final var now = OffsetDateTime.now();

    final var taxonomyMP = ProductTaxonomy.create(UUID.randomUUID(), taxonomy, now, now);
    final var referencePT = ProductMother.created()
        .owners(new ProductOwners(List.of(ProductOwner.create(new OwnerId(owner), now))))
        .families(new ProductFamilies(List.of(ProductFamily.create(new CampaignId(campaign), new FamilyId(family), now))))
        .build();
    final var order =
        OrderMother.random().orderId(new OrderId(productOrderId.toString())).supplierId(new OrderSupplierId(supplierId)).build();
    final var parents = List.of(buyer, buyerGroup, buyerSubgroup, buyerCode);
    final var campaignsBudgetCycle = List.of(campaign);
    final var useNames = new UseNames(List.of(
        new UseName(new Name(nameEN), new Lang(languageEN), BasicAudit.create(now)),
        new UseName(new Name(nameES), new Lang(languageES), BasicAudit.create(now))));

    final var use1Id = new Use.Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));
    final var use1 = UseMother.generateBuild()
        .id(use1Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var use2Id = new Use.Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba269"));
    final var use2 = UseMother.generateBuild()
        .id(use2Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var uses = List.of(use1, use2);
    final var orderedValidUses = new PurchasePurposeEvaluationResults(List.of(
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use1.getId(), PurchasePurposeConditionName.BUYERGROUP),
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use2.getId(), PurchasePurposeConditionName.BUYERSUBGROUP)));
    final List<UUID> orderUseIdList = List.of(use1.getId().value(), use2.getId().value());

    final List<OrderUseInnerPendingByUseDTO> orderUses = List.of(
        new OrderUseInnerPendingByUseDTO(new UseId(use1Id.value()), pendingUse1),
        new OrderUseInnerPendingByUseDTO(new UseId(use2Id.value()), pendingUse2));

    final List<NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseDTOList = List.of(
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use1.getId().value()),
            totalQuantityUse1),
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use2.getId().value()),
            totalQuantityUse2));

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO1 =
        new DistributionInnerUseQuantitiesDTO(
            new DistributionInner.UseId(use1.getId().value()),
            new RequestedQuantity(requestedQuantityUse1.value()),
            new DistributedQuantity(distributedQuantityUse1.value()));

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO2 = new DistributionInnerUseQuantitiesDTO(
        new DistributionInner.UseId(use2.getId().value()),
        new RequestedQuantity(requestedQuantityUse2.value()),
        new DistributedQuantity(distributedQuantityUse2.value()));

    final var distributionInnerUseQuantitiesDTOList = List.of(distributionInnerUseQuantitiesDTO1, distributionInnerUseQuantitiesDTO2);

    doReturn(Optional.of(taxonomyMP)).when(this.productTaxonomyRepository).findByReferenceId(referenceId);
    doReturn(Optional.of(referencePT)).when(this.productRepository)
        .findByReferenceId(new com.inditex.icdmdemg.domain.product.entity.ProductReferenceId(productReferenceId.toString()));
    doReturn(Optional.of(order)).when(this.orderRepository).find(new OrderId(productOrderId.toString()));
    doReturn(parents).when(this.categoryRepository).getParents(List.of(owner));
    doReturn(campaignsBudgetCycle).when(this.categoryRepository).getEquivalent(List.of(budgetCycle));

    doReturn(orderedValidUses).when(this.useProvider).getOrderedValidUse(any(), any(), any(), anyList());
    doReturn(uses).when(this.useRepository).findByIds(List.of(use1Id, use2Id));

    doReturn(orderUses).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    doReturn(List.of()).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ProductUseSummaryDTO.ReferenceId(referenceId), orderUseIdList.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    doReturn(nominatedTotalQuantityByUseDTOList).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            orderUseIdList.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    doReturn(distributionInnerUseQuantitiesDTOList).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        orderUseIdList.stream().map(DistributionInner.UseId::new).toList(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityV2Query(new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, false, purchaseType, languageEN));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityV2Response(
        List.of(
            new UsesAvailabilityV2(
                use1Id.value(),
                use1.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use1.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(75))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(95)))),
            new UsesAvailabilityV2(
                use2Id.value(),
                use2.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use2.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(60))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(80))))));

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

  @Test
  void should_return_uses_availability_response_when_no_distribution_inner() {
    final UUID referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final UUID productOrderId = UUID.randomUUID();
    final Pending pendingUse1 = new Pending(BigDecimal.valueOf(100));
    final Pending pendingUse2 = new Pending(BigDecimal.valueOf(80));
    final Stock stockUse1 = new Stock(BigDecimal.valueOf(70));
    final Stock stockUse2 = new Stock(BigDecimal.valueOf(50));
    final TotalQuantity totalQuantityUse1 = new TotalQuantity(BigDecimal.valueOf(75));
    final TotalQuantity totalQuantityUse2 = new TotalQuantity(BigDecimal.valueOf(60));

    final var taxonomy = "TRIMMING";
    final var owner = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var campaign = UrnConstantsEnum.CAMPAIGN.prefixWithUrn(UUID.randomUUID());
    final var family = UrnConstantsEnum.FAMILY.prefixWithUrn(UUID.randomUUID());
    final var buyer = UrnConstantsEnum.BUYER.prefixWithUrn(UUID.randomUUID());
    final var buyerGroup = UrnConstantsEnum.BUYER_GROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerSubgroup = UrnConstantsEnum.BUYER_SUBGROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerCode = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var supplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var nameEN = "example";
    final var nameES = "ejemplo";
    final var languageEN = "en";
    final var languageES = "es";
    final var now = OffsetDateTime.now();

    final var taxonomyMP = ProductTaxonomy.create(UUID.randomUUID(), taxonomy, now, now);
    final var referencePT = ProductMother.created()
        .owners(new ProductOwners(List.of(ProductOwner.create(new OwnerId(owner), now))))
        .families(new ProductFamilies(List.of(ProductFamily.create(new CampaignId(campaign), new FamilyId(family), now))))
        .build();
    final var order =
        OrderMother.random().orderId(new OrderId(productOrderId.toString())).supplierId(new OrderSupplierId(supplierId)).build();
    final var parents = List.of(buyer, buyerGroup, buyerSubgroup, buyerCode);
    final var campaignsBudgetCycle = List.of(campaign);

    final var useNames = new UseNames(List.of(
        new UseName(new Name(nameEN), new Lang(languageEN), BasicAudit.create(now)),
        new UseName(new Name(nameES), new Lang(languageES), BasicAudit.create(now))));

    final var use1Id = new Use.Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));
    final var use1 = UseMother.generateBuild()
        .id(use1Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var use2Id = new Use.Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba269"));
    final var use2 = UseMother.generateBuild()
        .id(use2Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var uses = List.of(use1, use2);
    final var orderedValidUses = new PurchasePurposeEvaluationResults(List.of(
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use1.getId(), PurchasePurposeConditionName.BUYERGROUP),
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use2.getId(), PurchasePurposeConditionName.BUYERSUBGROUP)));
    final List<UUID> orderUseIdList = List.of(use1.getId().value(), use2.getId().value());

    final List<OrderUseInnerPendingByUseDTO> orderUses = List.of(
        new OrderUseInnerPendingByUseDTO(new UseId(use1Id.value()), pendingUse1),
        new OrderUseInnerPendingByUseDTO(new UseId(use2Id.value()), pendingUse2));

    final List<ProductUseInnerStockByUseDTO> innerProductUseSummaries = List.of(
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use1.getId().value()), stockUse1),
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use2.getId().value()), stockUse2));

    final List<NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseDTOList = List.of(
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use1.getId().value()),
            totalQuantityUse1),
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use2.getId().value()),
            totalQuantityUse2));

    doReturn(Optional.of(taxonomyMP)).when(this.productTaxonomyRepository).findByReferenceId(referenceId);
    doReturn(Optional.of(referencePT)).when(this.productRepository)
        .findByReferenceId(new com.inditex.icdmdemg.domain.product.entity.ProductReferenceId(productReferenceId.toString()));
    doReturn(Optional.of(order)).when(this.orderRepository).find(new OrderId(productOrderId.toString()));
    doReturn(parents).when(this.categoryRepository).getParents(List.of(owner));
    doReturn(campaignsBudgetCycle).when(this.categoryRepository).getEquivalent(List.of(budgetCycle));

    doReturn(orderedValidUses).when(this.useProvider).getOrderedValidUse(any(), any(), any(), anyList());
    doReturn(uses).when(this.useRepository).findByIds(List.of(use1Id, use2Id));

    doReturn(orderUses).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    doReturn(innerProductUseSummaries).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ProductUseSummaryDTO.ReferenceId(referenceId), orderUseIdList.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    doReturn(nominatedTotalQuantityByUseDTOList).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            orderUseIdList.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    doReturn(List.of()).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        orderUseIdList.stream().map(DistributionInner.UseId::new).toList(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityV2Query(new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, false, purchaseType, languageEN));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityV2Response(
        List.of(
            new UsesAvailabilityV2(
                use1Id.value(),
                use1.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use1.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(75))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(170)))),
            new UsesAvailabilityV2(
                use2Id.value(),
                use2.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use2.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(60))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(130))))));

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

  @Test
  void should_return_uses_availability_response_when_no_order_use_and_no_product_use_and_no_distribution_inner() {
    final UUID referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final UUID productOrderId = UUID.randomUUID();
    final TotalQuantity totalQuantityUse1 = new TotalQuantity(BigDecimal.valueOf(75));
    final TotalQuantity totalQuantityUse2 = new TotalQuantity(BigDecimal.valueOf(60));

    final var taxonomy = "TRIMMING";
    final var owner = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var campaign = UrnConstantsEnum.CAMPAIGN.prefixWithUrn(UUID.randomUUID());
    final var family = UrnConstantsEnum.FAMILY.prefixWithUrn(UUID.randomUUID());
    final var buyer = UrnConstantsEnum.BUYER.prefixWithUrn(UUID.randomUUID());
    final var buyerGroup = UrnConstantsEnum.BUYER_GROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerSubgroup = UrnConstantsEnum.BUYER_SUBGROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerCode = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var supplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var nameEN = "example";
    final var nameES = "ejemplo";
    final var languageEN = "en";
    final var languageES = "es";
    final var now = OffsetDateTime.now();

    final var taxonomyMP = ProductTaxonomy.create(UUID.randomUUID(), taxonomy, now, now);
    final var referencePT = ProductMother.created()
        .owners(new ProductOwners(List.of(ProductOwner.create(new OwnerId(owner), now))))
        .families(new ProductFamilies(List.of(ProductFamily.create(new CampaignId(campaign), new FamilyId(family), now))))
        .build();
    final var order =
        OrderMother.random().orderId(new OrderId(productOrderId.toString())).supplierId(new OrderSupplierId(supplierId)).build();
    final var parents = List.of(buyer, buyerGroup, buyerSubgroup, buyerCode);
    final var campaignsBudgetCycle = List.of(campaign);

    final var useNames = new UseNames(List.of(
        new UseName(new Name(nameEN), new Lang(languageEN), BasicAudit.create(now)),
        new UseName(new Name(nameES), new Lang(languageES), BasicAudit.create(now))));

    final var use1Id = new Use.Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));
    final var use1 = UseMother.generateBuild()
        .id(use1Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var use2Id = new Use.Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba269"));
    final var use2 = UseMother.generateBuild()
        .id(use2Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var uses = List.of(use1, use2);
    final var orderedValidUses = new PurchasePurposeEvaluationResults(List.of(
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use1.getId(), PurchasePurposeConditionName.BUYERGROUP),
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use2.getId(), PurchasePurposeConditionName.BUYERSUBGROUP)));
    final List<UUID> orderUseIdList = List.of(use1.getId().value(), use2.getId().value());

    final List<NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseDTOList = List.of(
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use1.getId().value()),
            totalQuantityUse1),
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use2.getId().value()),
            totalQuantityUse2));

    doReturn(Optional.of(taxonomyMP)).when(this.productTaxonomyRepository).findByReferenceId(referenceId);
    doReturn(Optional.of(referencePT)).when(this.productRepository)
        .findByReferenceId(new com.inditex.icdmdemg.domain.product.entity.ProductReferenceId(productReferenceId.toString()));
    doReturn(Optional.of(order)).when(this.orderRepository).find(new OrderId(productOrderId.toString()));
    doReturn(parents).when(this.categoryRepository).getParents(List.of(owner));
    doReturn(campaignsBudgetCycle).when(this.categoryRepository).getEquivalent(List.of(budgetCycle));

    doReturn(orderedValidUses).when(this.useProvider).getOrderedValidUse(any(), any(), any(), anyList());
    doReturn(uses).when(this.useRepository).findByIds(List.of(use1Id, use2Id));

    doReturn(List.of()).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    doReturn(List.of()).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ProductUseSummaryDTO.ReferenceId(referenceId), orderUseIdList.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    doReturn(nominatedTotalQuantityByUseDTOList).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            orderUseIdList.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    doReturn(List.of()).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        orderUseIdList.stream().map(DistributionInner.UseId::new).toList(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityV2Query(new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, false, purchaseType, languageEN));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityV2Response(
        List.of(
            new UsesAvailabilityV2(
                use1Id.value(),
                use1.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use1.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(75))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(0)))),
            new UsesAvailabilityV2(
                use2Id.value(),
                use2.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use2.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(60))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(0))))));

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

  @Test
  void should_return_uses_availability_response() {
    final UUID referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final UUID productOrderId = UUID.randomUUID();
    final Pending pendingUse1 = new Pending(BigDecimal.valueOf(100));
    final Pending pendingUse2 = new Pending(BigDecimal.valueOf(80));
    final Stock stockUse1 = new Stock(BigDecimal.valueOf(70));
    final Stock stockUse2 = new Stock(BigDecimal.valueOf(50));
    final RequestedQuantity requestedQuantityUse1 = new RequestedQuantity(BigDecimal.valueOf(30));
    final RequestedQuantity requestedQuantityUse2 = new RequestedQuantity(BigDecimal.valueOf(10));
    final DistributedQuantity distributedQuantityUse1 = new DistributedQuantity(BigDecimal.valueOf(25));
    final DistributedQuantity distributedQuantityUse2 = new DistributedQuantity(BigDecimal.valueOf(20));
    final TotalQuantity totalQuantityUse1 = new TotalQuantity(BigDecimal.valueOf(75));
    final TotalQuantity totalQuantityUse2 = new TotalQuantity(BigDecimal.valueOf(60));

    final var taxonomy = "TRIMMING";
    final var owner = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var campaign = UrnConstantsEnum.CAMPAIGN.prefixWithUrn(UUID.randomUUID());
    final var family = UrnConstantsEnum.FAMILY.prefixWithUrn(UUID.randomUUID());
    final var buyer = UrnConstantsEnum.BUYER.prefixWithUrn(UUID.randomUUID());
    final var buyerGroup = UrnConstantsEnum.BUYER_GROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerSubgroup = UrnConstantsEnum.BUYER_SUBGROUP.prefixWithUrn(UUID.randomUUID());
    final var buyerCode = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var supplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var nameEN = "example";
    final var nameES = "ejemplo";
    final var languageEN = "en";
    final var languageES = "es";
    final var now = OffsetDateTime.now();

    final var taxonomyMP = ProductTaxonomy.create(UUID.randomUUID(), taxonomy, now, now);
    final var referencePT = ProductMother.created()
        .owners(new ProductOwners(List.of(ProductOwner.create(new OwnerId(owner), now))))
        .families(new ProductFamilies(List.of(ProductFamily.create(new CampaignId(campaign), new FamilyId(family), now))))
        .build();
    final var order =
        OrderMother.random().orderId(new OrderId(productOrderId.toString())).supplierId(new OrderSupplierId(supplierId)).build();
    final var parents = List.of(buyer, buyerGroup, buyerSubgroup, buyerCode);
    final var campaignsBudgetCycle = List.of(campaign);

    final var useNames = new UseNames(List.of(
        new UseName(new Name(nameEN), new Lang(languageEN), BasicAudit.create(now)),
        new UseName(new Name(nameES), new Lang(languageES), BasicAudit.create(now))));

    final var use1Id = new Use.Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));
    final var use1 = UseMother.generateBuild()
        .id(use1Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var use2Id = new Use.Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba269"));
    final var use2 = UseMother.generateBuild()
        .id(use2Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var uses = List.of(use1, use2);
    final var orderedValidUses = new PurchasePurposeEvaluationResults(List.of(
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use1.getId(), PurchasePurposeConditionName.BUYERGROUP),
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use2.getId(), PurchasePurposeConditionName.BUYERSUBGROUP)));
    final List<UUID> orderUseIdList = List.of(use1.getId().value(), use2.getId().value());

    final List<OrderUseInnerPendingByUseDTO> orderUses = List.of(
        new OrderUseInnerPendingByUseDTO(new UseId(use1Id.value()), pendingUse1),
        new OrderUseInnerPendingByUseDTO(new UseId(use2Id.value()), pendingUse2));

    final List<ProductUseInnerStockByUseDTO> innerProductUseSummaries = List.of(
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use1.getId().value()), stockUse1),
        new ProductUseInnerStockByUseDTO(new ProductUseInnerStockByUseDTO.UseId(use2.getId().value()), stockUse2));

    final List<NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseDTOList = List.of(
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use1.getId().value()),
            totalQuantityUse1),
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use2.getId().value()),
            totalQuantityUse2));

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO1 =
        new DistributionInnerUseQuantitiesDTO(
            new DistributionInner.UseId(use1.getId().value()),
            new RequestedQuantity(requestedQuantityUse1.value()),
            new DistributedQuantity(distributedQuantityUse1.value()));

    final DistributionInnerUseQuantitiesDTO distributionInnerUseQuantitiesDTO2 = new DistributionInnerUseQuantitiesDTO(
        new DistributionInner.UseId(use2.getId().value()),
        new RequestedQuantity(requestedQuantityUse2.value()),
        new DistributedQuantity(distributedQuantityUse2.value()));

    final var distributionInnerUseQuantitiesDTOList = List.of(distributionInnerUseQuantitiesDTO1, distributionInnerUseQuantitiesDTO2);

    doReturn(Optional.of(taxonomyMP)).when(this.productTaxonomyRepository).findByReferenceId(referenceId);
    doReturn(Optional.of(referencePT)).when(this.productRepository)
        .findByReferenceId(new com.inditex.icdmdemg.domain.product.entity.ProductReferenceId(productReferenceId.toString()));
    doReturn(Optional.of(order)).when(this.orderRepository).find(new OrderId(productOrderId.toString()));
    doReturn(parents).when(this.categoryRepository).getParents(List.of(owner));
    doReturn(campaignsBudgetCycle).when(this.categoryRepository).getEquivalent(List.of(budgetCycle));

    doReturn(orderedValidUses).when(this.useProvider).getOrderedValidUse(any(), any(), any(), anyList());
    doReturn(uses).when(this.useRepository).findByIds(List.of(use1Id, use2Id));

    doReturn(orderUses).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    doReturn(innerProductUseSummaries).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ProductUseSummaryDTO.ReferenceId(referenceId), orderUseIdList.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    doReturn(nominatedTotalQuantityByUseDTOList).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            orderUseIdList.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    doReturn(distributionInnerUseQuantitiesDTOList).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        orderUseIdList.stream().map(DistributionInner.UseId::new).toList(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityV2Query(new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, false, purchaseType, languageEN));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityV2Response(
        List.of(
            new UsesAvailabilityV2(
                use1Id.value(),
                use1.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use1.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(75))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(165)))),
            new UsesAvailabilityV2(
                use2Id.value(),
                use2.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use2.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(60))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(130))))));

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

  @Test
  void should_not_return_uses_no_availability_except_one() {
    final UUID referenceId = UUID.randomUUID();
    final UUID productReferenceId = UUID.randomUUID();
    final String budgetCycle = UUID.randomUUID().toString();
    final UUID productOrderId = UUID.randomUUID();
    final TotalQuantity totalQuantityUse1 = new TotalQuantity(BigDecimal.valueOf(0));
    final TotalQuantity totalQuantityUse2 = new TotalQuantity(BigDecimal.valueOf(0));
    final TotalQuantity totalQuantityUse3 = new TotalQuantity(BigDecimal.valueOf(50));

    final var taxonomy = "TRIMMING";
    final var owner = UrnConstantsEnum.BUYER_CODE.prefixWithUrn(UUID.randomUUID());
    final var campaign = UrnConstantsEnum.CAMPAIGN.prefixWithUrn(UUID.randomUUID());
    final var family = UrnConstantsEnum.FAMILY.prefixWithUrn(UUID.randomUUID());
    final var buyer = UrnConstantsEnum.BUYER.prefixWithUrn(UUID.randomUUID());
    final var supplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var purchaseType = PurchaseType.NOMINATED_INNER.getStringList();
    final var nameEN = "example";
    final var nameES = "ejemplo";
    final var languageEN = "en";
    final var languageES = "es";
    final var now = OffsetDateTime.now();

    final var taxonomyMP = ProductTaxonomy.create(UUID.randomUUID(), taxonomy, now, now);
    final var referencePT = ProductMother.created()
        .owners(new ProductOwners(List.of(ProductOwner.create(new OwnerId(owner), now))))
        .families(new ProductFamilies(List.of(ProductFamily.create(new CampaignId(campaign), new FamilyId(family), now))))
        .build();
    final var order =
        OrderMother.random().orderId(new OrderId(productOrderId.toString())).supplierId(new OrderSupplierId(supplierId)).build();
    final var parents = List.of(buyer);
    final var campaignsBudgetCycle = List.of(campaign);

    final var useNames = new UseNames(List.of(
        new UseName(new Name(nameEN), new Lang(languageEN), BasicAudit.create(now)),
        new UseName(new Name(nameES), new Lang(languageES), BasicAudit.create(now))));

    final var use1Id = new Use.Id(UUID.fromString("6493d7dc-c408-45da-a6f0-26bd1f49e2d9"));
    final var use1 = UseMother.generateBuild()
        .id(use1Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var use2Id = new Use.Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba269"));
    final var use2 = UseMother.generateBuild()
        .id(use2Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var use3Id = new Use.Id(UUID.fromString("afeae856-bdec-4ae2-9126-b207cb2ba270"));
    final var use3 = UseMother.generateBuild()
        .id(use3Id)
        .purchaseType(PurchaseType.NOMINATED_INNER)
        .assignable(AssignableType.NOMINATED_INNER)
        .taxonomy(new Taxonomy(taxonomy))
        .useNames(useNames)
        .build();

    final var uses = List.of(use1, use2, use3);
    final var orderedValidUses = new PurchasePurposeEvaluationResults(List.of(
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use1.getId(), PurchasePurposeConditionName.BUYERGROUP),
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use2.getId(), PurchasePurposeConditionName.BUYERSUBGROUP),
        PurchasePurposeEvaluationResultMother.withUseOkAndParam(use3.getId(), PurchasePurposeConditionName.BUYERCODE)));
    final List<UUID> orderUseIdList = List.of(use1.getId().value(), use2.getId().value(), use3.getId().value());

    final List<NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseDTOList = List.of(
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use1.getId().value()),
            totalQuantityUse1),
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use2.getId().value()),
            totalQuantityUse2),
        new NominatedTotalQuantityByUseDTO(
            new NominatedTotalQuantityByUseDTO.UseId(use3.getId().value()),
            totalQuantityUse3));

    doReturn(Optional.of(taxonomyMP)).when(this.productTaxonomyRepository).findByReferenceId(referenceId);
    doReturn(Optional.of(referencePT)).when(this.productRepository)
        .findByReferenceId(new com.inditex.icdmdemg.domain.product.entity.ProductReferenceId(productReferenceId.toString()));
    doReturn(Optional.of(order)).when(this.orderRepository).find(new OrderId(productOrderId.toString()));
    doReturn(parents).when(this.categoryRepository).getParents(List.of(owner));
    doReturn(campaignsBudgetCycle).when(this.categoryRepository).getEquivalent(List.of(budgetCycle));

    doReturn(orderedValidUses).when(this.useProvider).getOrderedValidUse(any(), any(), any(), anyList());
    doReturn(uses).when(this.useRepository).findByIds(List.of(use1Id, use2Id, use3Id));

    doReturn(List.of()).when(this.orderUseRepository).findOrderUseInnerPendingBySharedRawMaterialUses(new ProductReferenceId(referenceId),
        orderUseIdList.stream().map(UseId::new).toList(), new BudgetCycle(budgetCycle));

    doReturn(List.of()).when(this.productUseRepository).findProductUseInnerStockByUseDTO(
        new ProductUseSummaryDTO.ReferenceId(referenceId), orderUseIdList.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
        new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    doReturn(nominatedTotalQuantityByUseDTOList).when(this.nominatedProvisionRepository)
        .findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            orderUseIdList.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    doReturn(List.of()).when(this.distributionInnerRepository).findDistributionInnerUseQuantities(
        orderUseIdList.stream().map(DistributionInner.UseId::new).toList(),
        new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.BudgetCycle(budgetCycle));

    final var query = new GetUsesAvailabilityV2Query(new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, true, purchaseType, languageEN));

    final var result = this.sut.ask(query);

    final var expectedResult = new UsesAvailabilityV2Response(
        List.of(
            new UsesAvailabilityV2(
                use1Id.value(),
                use1.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use1.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(0))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(0)))),
            new UsesAvailabilityV2(
                use3Id.value(),
                use3.names().getNameTranslated(LocaleUtils.createLocale(languageEN)),
                List.of(UsePurchaseType.NOMINATED.value(), UsePurchaseType.INNER.value()),
                use1.purchaseType().getStringList(),
                true,
                new UsesAvailabilityTotalNominatedQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(50))),
                new UsesAvailabilityTotalInnerQuantityV2(
                    NumericUtils.roundUpScale2(BigDecimal.valueOf(0))))));

    Assertions.assertThat(result.response()).contains(expectedResult);
  }

}
