package com.inditex.icdmdemg.application.distributionnominated.service;

import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.created;
import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.deleted;
import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.kept;
import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.updated;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.AlternativeReference;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedLineMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Line;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidGeneratorMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

import org.junit.jupiter.api.Test;

class DistributionNominatedLinesIdentifierTest {

  @Test
  void should_identify_created_lines_two_target() {
    final var now = OffsetDateTime.now();
    final var uuidGenerator = UuidGeneratorMother.fromStartingInteger(901);
    final var sut = new DistributionNominatedLinesIdentifier(uuidGenerator);

    final var rootId = new Id(UuidMother.fromInteger(101));
    final var targetReferenceId = new ReferenceId(UuidMother.fromInteger(201));
    final var requested = new RequestedQuantity(BigDecimal.valueOf(1000));
    final var theoretical = new TheoreticalQuantity(BigDecimal.valueOf(1100));
    final var commitmentOrder1 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(501)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var referenceId1 = targetReferenceId;
    final var requestedLine1 = BigDecimal.valueOf(400);
    final var line1 = created(new Line(rootId, commitmentOrder1, referenceId1, targetReferenceId, requestedLine1, BigDecimal.ZERO));
    final var commitmentOrder2 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(502)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var referenceId2 = targetReferenceId;
    final var requestedLine2 = BigDecimal.valueOf(600);
    final var line2 = created(new Line(rootId, commitmentOrder2, referenceId2, targetReferenceId, requestedLine2, BigDecimal.ZERO));
    final var line3 = kept(new Line(
        new Id(UuidMother.fromInteger(110)),
        commitmentOrder1, referenceId1, targetReferenceId, requestedLine1, BigDecimal.ZERO,
        Optional.of(OffsetDateTimeMother.fromInteger(2))));

    final var lines = sut.identifyCreatedLines(rootId, requested, theoretical, List.of(line1, line2, line3), now);

    final var audit = new BasicAudit(now, now);
    final var expectedId1 = new DistributionNominatedLine.Id(UuidMother.fromInteger(902));
    final var theo1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(40));
    final var req1 = new DistributionNominatedLine.RequestedQuantity(requestedLine1);
    final var expected1 = new DistributionNominatedLineMother.Builder()
        .id(expectedId1).commitmentOrder(commitmentOrder1).theoreticalQuantity(theo1).requestedQuantity(req1)
        .alternativeReference(null).distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    final var expectedId2 = new DistributionNominatedLine.Id(UuidMother.fromInteger(901));
    final var theo2 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(60));
    final var req2 = new DistributionNominatedLine.RequestedQuantity(requestedLine2);
    final var expected2 = new DistributionNominatedLineMother.Builder()
        .id(expectedId2).commitmentOrder(commitmentOrder2).theoreticalQuantity(theo2).requestedQuantity(req2)
        .alternativeReference(null).distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    assertThat(lines).containsExactlyInAnyOrder(expected1, expected2);
  }

  @Test
  void should_identify_created_lines_one_target_one_alternative_same_commitment_order() {
    final var now = OffsetDateTime.now();
    final var uuidGenerator = UuidGeneratorMother.fromStartingInteger(901);
    final var sut = new DistributionNominatedLinesIdentifier(uuidGenerator);

    final var rootId = new Id(UuidMother.fromInteger(101));
    final var targetReferenceId = new ReferenceId(UuidMother.fromInteger(201));
    final var requested = new RequestedQuantity(BigDecimal.valueOf(1000));
    final var theoretical = new TheoreticalQuantity(BigDecimal.valueOf(1100));
    final var commitmentOrder1 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(501)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var referenceId1 = targetReferenceId;
    final var requestedLine1 = BigDecimal.valueOf(400);
    final var line1 = created(new Line(rootId, commitmentOrder1, referenceId1, targetReferenceId, requestedLine1, BigDecimal.ZERO));
    final var commitmentOrder2 = commitmentOrder1;
    final var referenceId2 = new ReferenceId(UuidMother.fromInteger(211));
    final var requestedLine2 = BigDecimal.valueOf(600);
    final var line2 = created(new Line(rootId, commitmentOrder2, referenceId2, targetReferenceId, requestedLine2, BigDecimal.ZERO));

    final var lines = sut.identifyCreatedLines(rootId, requested, theoretical, List.of(line1, line2), now);

    final var audit = new BasicAudit(now, now);
    final var expectedId1 = new DistributionNominatedLine.Id(UuidMother.fromInteger(901));
    final var theo1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(40));
    final var req1 = new DistributionNominatedLine.RequestedQuantity(requestedLine1);
    final var altRefId = new AlternativeReference.ReferenceId(referenceId2.value());
    final var altReq = new AlternativeReference.RequestedQuantity(requestedLine2);
    final var altTheo = new AlternativeReference.TheoreticalQuantity(BigDecimal.valueOf(60));
    final var expected1 = new DistributionNominatedLineMother.Builder()
        .id(expectedId1).commitmentOrder(commitmentOrder1).theoreticalQuantity(theo1).requestedQuantity(req1)
        .alternativeReference(new AlternativeReference(altRefId, altReq, altTheo))
        .distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    assertThat(lines).containsExactlyElementsOf(List.of(expected1));
  }

  @Test
  void should_identify_created_lines_one_target_one_alternative_different_commitment_order() {
    final var now = OffsetDateTime.now();
    final var uuidGenerator = UuidGeneratorMother.fromStartingInteger(901);
    final var sut = new DistributionNominatedLinesIdentifier(uuidGenerator);

    final var rootId = new Id(UuidMother.fromInteger(101));
    final var targetReferenceId = new ReferenceId(UuidMother.fromInteger(201));
    final var requested = new RequestedQuantity(BigDecimal.valueOf(1000));
    final var theoretical = new TheoreticalQuantity(BigDecimal.valueOf(1100));

    final var commitmentOrder1 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(501)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var referenceId1 = targetReferenceId;
    final var requestedLine1 = BigDecimal.valueOf(400);
    final var line1 = created(new Line(rootId, commitmentOrder1, referenceId1, targetReferenceId, requestedLine1, BigDecimal.ZERO));

    final var commitmentOrder2 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(502)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var referenceId2 = new ReferenceId(UuidMother.fromInteger(211));
    final var requestedLine2 = BigDecimal.valueOf(600);
    final var line2 = created(new Line(rootId, commitmentOrder2, referenceId2, targetReferenceId, requestedLine2, BigDecimal.ZERO));

    final var lines = sut.identifyCreatedLines(rootId, requested, theoretical, List.of(line1, line2), now);

    final var audit = new BasicAudit(now, now);
    final var expectedId1 = new DistributionNominatedLine.Id(UuidMother.fromInteger(902));
    final var theo1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(40));
    final var req1 = new DistributionNominatedLine.RequestedQuantity(requestedLine1);
    final var expected1 = new DistributionNominatedLineMother.Builder()
        .id(expectedId1).commitmentOrder(commitmentOrder1).theoreticalQuantity(theo1).requestedQuantity(req1)
        .alternativeReference(null)
        .distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    final var expectedId2 = new DistributionNominatedLine.Id(UuidMother.fromInteger(901));
    final var theo2 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.ZERO);
    final var req2 = new DistributionNominatedLine.RequestedQuantity(BigDecimal.ZERO);
    final var altRefId2 = new AlternativeReference.ReferenceId(referenceId2.value());
    final var altReq2 = new AlternativeReference.RequestedQuantity(requestedLine2);
    final var altTheo2 = new AlternativeReference.TheoreticalQuantity(BigDecimal.valueOf(60));
    final var expected2 = new DistributionNominatedLineMother.Builder()
        .id(expectedId2).commitmentOrder(commitmentOrder2).theoreticalQuantity(theo2).requestedQuantity(req2)
        .alternativeReference(new AlternativeReference(altRefId2, altReq2, altTheo2))
        .distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    assertThat(lines).containsExactlyInAnyOrderElementsOf(List.of(expected1, expected2));
  }

  @Test
  void should_identify_created_lines_two_alternative() {
    final var now = OffsetDateTime.now();
    final var uuidGenerator = UuidGeneratorMother.fromStartingInteger(901);
    final var sut = new DistributionNominatedLinesIdentifier(uuidGenerator);

    final var rootId = new Id(UuidMother.fromInteger(101));
    final var targetReferenceId = new ReferenceId(UuidMother.fromInteger(201));
    final var requested = new RequestedQuantity(BigDecimal.valueOf(1000));
    final var theoretical = new TheoreticalQuantity(BigDecimal.valueOf(1100));
    final var commitmentOrder1 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(501)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var referenceId1 = new ReferenceId(UuidMother.fromInteger(211));
    final var requestedLine1 = BigDecimal.valueOf(400);
    final var line1 = created(new Line(rootId, commitmentOrder1, referenceId1, targetReferenceId, requestedLine1, BigDecimal.ZERO));
    final var commitmentOrder2 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(502)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var referenceId2 = new ReferenceId(UuidMother.fromInteger(211));
    final var requestedLine2 = BigDecimal.valueOf(600);
    final var line2 = created(new Line(rootId, commitmentOrder2, referenceId2, targetReferenceId, requestedLine2, BigDecimal.ZERO));

    final var lines = sut.identifyCreatedLines(rootId, requested, theoretical, List.of(line1, line2), now);

    final var audit = new BasicAudit(now, now);
    final var expectedId1 = new DistributionNominatedLine.Id(UuidMother.fromInteger(902));
    final var theo1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.ZERO);
    final var req1 = new DistributionNominatedLine.RequestedQuantity(BigDecimal.ZERO);
    final var altRefId1 = new AlternativeReference.ReferenceId(referenceId2.value());
    final var altReq1 = new AlternativeReference.RequestedQuantity(requestedLine1);
    final var altTheo1 = new AlternativeReference.TheoreticalQuantity(BigDecimal.valueOf(40));
    final var expected1 = new DistributionNominatedLineMother.Builder()
        .id(expectedId1).commitmentOrder(commitmentOrder1).theoreticalQuantity(theo1).requestedQuantity(req1)
        .alternativeReference(new AlternativeReference(altRefId1, altReq1, altTheo1))
        .distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    final var expectedId2 = new DistributionNominatedLine.Id(UuidMother.fromInteger(901));
    final var theo2 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.ZERO);
    final var req2 = new DistributionNominatedLine.RequestedQuantity(BigDecimal.ZERO);
    final var altRefId2 = new AlternativeReference.ReferenceId(referenceId2.value());
    final var altReq2 = new AlternativeReference.RequestedQuantity(requestedLine2);
    final var altTheo2 = new AlternativeReference.TheoreticalQuantity(BigDecimal.valueOf(60));
    final var expected2 = new DistributionNominatedLineMother.Builder()
        .id(expectedId2).commitmentOrder(commitmentOrder2).theoreticalQuantity(theo2).requestedQuantity(req2)
        .alternativeReference(new AlternativeReference(altRefId2, altReq2, altTheo2))
        .distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    assertThat(lines).containsExactlyInAnyOrderElementsOf(List.of(expected1, expected2));
  }

  @Test
  void should_identify_updated_lines_one_target_keep() {
    final var now = OffsetDateTime.now();
    final var yesterday = now.minusDays(1);
    final var uuidGenerator = UuidGeneratorMother.fromStartingInteger(901);
    final var sut = new DistributionNominatedLinesIdentifier(uuidGenerator);

    final var auditLine1 = new BasicAudit(yesterday, yesterday);
    final var distributionLineId1 = new DistributionNominatedLine.Id(UuidMother.fromInteger(111));
    final var commitmentOrder1 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(501)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var requestedLine1 = new DistributionNominatedLine.RequestedQuantity(BigDecimal.valueOf(400));
    final var theoreticalLine1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(40));
    final var distributionLine1 = DistributionNominatedLineMother.created()
        .id(distributionLineId1).commitmentOrder(commitmentOrder1).requestedQuantity(requestedLine1).theoreticalQuantity(theoreticalLine1)
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).distributionStartDate(null).alternativeReference(null)
        .audit(auditLine1)
        .build();

    final var distributionAudit = CompleteAudit.create("test", yesterday);
    final var rootId = new Id(UuidMother.fromInteger(101));
    final var targetReferenceId = new ReferenceId(UuidMother.fromInteger(201));
    final var requested = new RequestedQuantity(BigDecimal.valueOf(1000));
    final var theoretical = new TheoreticalQuantity(BigDecimal.valueOf(1100));
    final var distribution = DistributionNominatedMother.pending()
        .id(rootId).referenceId(targetReferenceId).requestedQuantity(requested).theoreticalQuantity(theoretical)
        .lines(List.of(distributionLine1)).audit(distributionAudit).build();

    final var referenceId1 = targetReferenceId;
    final var line1 = kept(new Line(rootId, commitmentOrder1, referenceId1, targetReferenceId, requestedLine1.value(), BigDecimal.ZERO));

    final var lines = sut.identifyUpdatedLines(distribution, requested, theoretical, List.of(line1), now);

    final var audit = auditLine1;
    final var expectedId1 = distributionLineId1;
    final var theo1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(40));
    final var req1 = requestedLine1;
    final var expected1 = new DistributionNominatedLineMother.Builder()
        .id(expectedId1).commitmentOrder(commitmentOrder1).theoreticalQuantity(theo1).requestedQuantity(req1)
        .alternativeReference(null).distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    assertThat(lines).containsExactlyElementsOf(List.of(expected1));
  }

  @Test
  void should_identify_updated_lines_one_target_delete() {
    final var now = OffsetDateTime.now();
    final var yesterday = now.minusDays(1);
    final var uuidGenerator = UuidGeneratorMother.fromStartingInteger(901);
    final var sut = new DistributionNominatedLinesIdentifier(uuidGenerator);

    final var auditLine1 = new BasicAudit(yesterday, yesterday);
    final var distributionLineId1 = new DistributionNominatedLine.Id(UuidMother.fromInteger(111));
    final var commitmentOrder1 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(501)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var requestedLine1 = new DistributionNominatedLine.RequestedQuantity(BigDecimal.valueOf(400));
    final var theoreticalLine1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(440));
    final var distributionLine1 = DistributionNominatedLineMother.created()
        .id(distributionLineId1).commitmentOrder(commitmentOrder1).requestedQuantity(requestedLine1).theoreticalQuantity(theoreticalLine1)
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).distributionStartDate(null).alternativeReference(null)
        .audit(auditLine1)
        .build();

    final var distributionAudit = CompleteAudit.create("test", yesterday);
    final var rootId = new Id(UuidMother.fromInteger(101));
    final var targetReferenceId = new ReferenceId(UuidMother.fromInteger(201));
    final var requested = new RequestedQuantity(BigDecimal.valueOf(1000));
    final var theoretical = new TheoreticalQuantity(BigDecimal.valueOf(1100));
    final var distribution = DistributionNominatedMother.pending()
        .id(rootId).referenceId(targetReferenceId).requestedQuantity(requested).theoreticalQuantity(theoretical)
        .lines(List.of(distributionLine1)).audit(distributionAudit).build();

    final var referenceId1 = targetReferenceId;
    final var line1 = deleted(new Line(rootId, commitmentOrder1, referenceId1, targetReferenceId, requestedLine1.value(), BigDecimal.ZERO));

    final var lines = sut.identifyUpdatedLines(distribution, requested, theoretical, List.of(line1), now);

    assertThat(lines).isEmpty();
  }

  @Test
  void should_identify_updated_lines_one_target_update() {
    final var now = OffsetDateTime.now();
    final var yesterday = now.minusDays(1);
    final var uuidGenerator = UuidGeneratorMother.fromStartingInteger(901);
    final var sut = new DistributionNominatedLinesIdentifier(uuidGenerator);

    final var auditLine1 = new BasicAudit(yesterday, yesterday);
    final var distributionLineId1 = new DistributionNominatedLine.Id(UuidMother.fromInteger(111));
    final var commitmentOrder1 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(501)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var requestedLine1 = new DistributionNominatedLine.RequestedQuantity(BigDecimal.valueOf(400));
    final var theoreticalLine1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(440));
    final var distributionLine1 = DistributionNominatedLineMother.created()
        .id(distributionLineId1).commitmentOrder(commitmentOrder1).requestedQuantity(requestedLine1).theoreticalQuantity(theoreticalLine1)
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).distributionStartDate(null).alternativeReference(null)
        .audit(auditLine1)
        .build();

    final var distributionAudit = CompleteAudit.create("test", yesterday);
    final var rootId = new Id(UuidMother.fromInteger(101));
    final var targetReferenceId = new ReferenceId(UuidMother.fromInteger(201));
    final var requested = new RequestedQuantity(BigDecimal.valueOf(1000));
    final var theoretical = new TheoreticalQuantity(BigDecimal.valueOf(1100));
    final var distribution = DistributionNominatedMother.pending()
        .id(rootId).referenceId(targetReferenceId).requestedQuantity(requested).theoreticalQuantity(theoretical)
        .lines(List.of(distributionLine1)).audit(distributionAudit).build();

    final var requestedRequest = new RequestedQuantity(BigDecimal.valueOf(500));
    final var theoreticalRequest = new TheoreticalQuantity(BigDecimal.valueOf(600));
    final var referenceId1 = targetReferenceId;
    final var line1 =
        updated(new Line(rootId, commitmentOrder1, referenceId1, targetReferenceId, requestedRequest.value(), BigDecimal.ZERO));

    final var lines = sut.identifyUpdatedLines(distribution, requestedRequest, theoreticalRequest, List.of(line1), now);

    final var audit = auditLine1.update(now);
    final var expectedId1 = distributionLineId1;
    final var theo1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(100));
    final var req1 = new DistributionNominatedLine.RequestedQuantity(requestedRequest.value());
    final var expected1 = new DistributionNominatedLineMother.Builder()
        .id(expectedId1).commitmentOrder(commitmentOrder1).theoreticalQuantity(theo1).requestedQuantity(req1)
        .alternativeReference(null).distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    assertThat(lines).containsExactlyElementsOf(List.of(expected1));
  }

  @Test
  void should_identify_updated_lines_one_target_update_one_alternative_create_same_commitment_order() {
    final var now = OffsetDateTime.now();
    final var yesterday = now.minusDays(1);
    final var uuidGenerator = UuidGeneratorMother.fromStartingInteger(901);
    final var sut = new DistributionNominatedLinesIdentifier(uuidGenerator);

    final var auditLine1 = new BasicAudit(yesterday, yesterday);
    final var distributionLineId1 = new DistributionNominatedLine.Id(UuidMother.fromInteger(111));
    final var commitmentOrder1 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(501)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var requestedLine1 = new DistributionNominatedLine.RequestedQuantity(BigDecimal.valueOf(400));
    final var theoreticalLine1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(440));
    final var distributionLine1 = DistributionNominatedLineMother.created()
        .id(distributionLineId1).commitmentOrder(commitmentOrder1).requestedQuantity(requestedLine1).theoreticalQuantity(theoreticalLine1)
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).distributionStartDate(null).alternativeReference(null)
        .audit(auditLine1)
        .build();

    final var distributionAudit = CompleteAudit.create("test", yesterday);
    final var rootId = new Id(UuidMother.fromInteger(101));
    final var targetReferenceId = new ReferenceId(UuidMother.fromInteger(201));
    final var requested = new RequestedQuantity(BigDecimal.valueOf(400));
    final var theoretical = new TheoreticalQuantity(BigDecimal.valueOf(440));
    final var distribution = DistributionNominatedMother.pending()
        .id(rootId).referenceId(targetReferenceId).requestedQuantity(requested).theoreticalQuantity(theoretical)
        .distributedQuantity(new DistributionNominated.DistributedQuantity(BigDecimal.ZERO))
        .lines(List.of(distributionLine1)).audit(distributionAudit).build();

    final var requested1 = BigDecimal.valueOf(500);
    final var referenceId1 = targetReferenceId;
    final var line1 =
        updated(new Line(rootId, commitmentOrder1, referenceId1, targetReferenceId, requested1, BigDecimal.ZERO));

    final var referenceId2 = new ReferenceId(UuidMother.fromInteger(211));
    final var requested2 = BigDecimal.valueOf(500);
    final var line2 =
        created(new Line(rootId, commitmentOrder1, referenceId2, targetReferenceId, requested2, BigDecimal.ZERO));
    final var line3 = kept(new Line(
        new Id(UuidMother.fromInteger(110)),
        commitmentOrder1, referenceId1, targetReferenceId, requested1, BigDecimal.ZERO,
        Optional.of(OffsetDateTimeMother.fromInteger(2))));

    final var requestedRequest = new RequestedQuantity(BigDecimal.valueOf(1000));
    final var theoreticalRequest = new TheoreticalQuantity(BigDecimal.valueOf(1100));
    final var lines = sut.identifyUpdatedLines(distribution, requestedRequest, theoreticalRequest, List.of(line1, line2, line3), now);

    final var audit = auditLine1.update(now);
    final var expectedId1 = distributionLineId1;
    final var req1 = new DistributionNominatedLine.RequestedQuantity(requested1);
    final var theo1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(50));
    final var altRefId2 = new AlternativeReference.ReferenceId(referenceId2.value());
    final var altReq2 = new AlternativeReference.RequestedQuantity(requested2);
    final var altTheo2 = new AlternativeReference.TheoreticalQuantity(BigDecimal.valueOf(50));
    final var expected1 = new DistributionNominatedLineMother.Builder()
        .id(expectedId1).commitmentOrder(commitmentOrder1).theoreticalQuantity(theo1).requestedQuantity(req1)
        .alternativeReference(new AlternativeReference(altRefId2, altReq2, altTheo2))
        .distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    assertThat(lines).containsExactlyElementsOf(List.of(expected1));
  }

  @Test
  void should_identify_updated_lines_one_target_update_one_alternative_create_different_commitment_order() {
    final var now = OffsetDateTime.now();
    final var yesterday = now.minusDays(1);
    final var uuidGenerator = UuidGeneratorMother.fromStartingInteger(901);
    final var sut = new DistributionNominatedLinesIdentifier(uuidGenerator);

    final var auditLine1 = new BasicAudit(yesterday, yesterday);
    final var distributionLineId1 = new DistributionNominatedLine.Id(UuidMother.fromInteger(111));
    final var commitmentOrder1 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(501)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var requestedLine1 = new DistributionNominatedLine.RequestedQuantity(BigDecimal.valueOf(400));
    final var theoreticalLine1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(440));
    final var distributionLine1 = DistributionNominatedLineMother.created()
        .id(distributionLineId1).commitmentOrder(commitmentOrder1).requestedQuantity(requestedLine1).theoreticalQuantity(theoreticalLine1)
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).distributionStartDate(null).alternativeReference(null)
        .audit(auditLine1)
        .build();

    final var distributionAudit = CompleteAudit.create("test", yesterday);
    final var rootId = new Id(UuidMother.fromInteger(101));
    final var targetReferenceId = new ReferenceId(UuidMother.fromInteger(201));
    final var requested = new RequestedQuantity(BigDecimal.valueOf(400));
    final var theoretical = new TheoreticalQuantity(BigDecimal.valueOf(440));
    final var distribution = DistributionNominatedMother.pending()
        .id(rootId).referenceId(targetReferenceId).requestedQuantity(requested).theoreticalQuantity(theoretical)
        .distributedQuantity(new DistributionNominated.DistributedQuantity(BigDecimal.ZERO))
        .lines(List.of(distributionLine1)).audit(distributionAudit).build();

    final var requested1 = BigDecimal.valueOf(500);
    final var referenceId1 = targetReferenceId;
    final var line1 =
        updated(new Line(rootId, commitmentOrder1, referenceId1, targetReferenceId, requested1, BigDecimal.ZERO));

    final var commitmentOrder2 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(402)), new LineId(UuidMother.fromInteger(502)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var referenceId2 = new ReferenceId(UuidMother.fromInteger(211));
    final var requested2 = BigDecimal.valueOf(500);
    final var line2 =
        created(new Line(rootId, commitmentOrder2, referenceId2, targetReferenceId, requested2, BigDecimal.ZERO));
    final var line3 = kept(new Line(
        new Id(UuidMother.fromInteger(110)),
        commitmentOrder2, referenceId1, targetReferenceId, requested1, BigDecimal.ZERO,
        Optional.of(OffsetDateTimeMother.fromInteger(2))));

    final var requestedRequest = new RequestedQuantity(BigDecimal.valueOf(1000));
    final var theoreticalRequest = new TheoreticalQuantity(BigDecimal.valueOf(1100));
    final var lines = sut.identifyUpdatedLines(distribution, requestedRequest, theoreticalRequest, List.of(line1, line2, line3), now);

    final var audit = auditLine1.update(now);
    final var expectedId1 = distributionLineId1;
    final var req1 = new DistributionNominatedLine.RequestedQuantity(requested1);
    final var theo1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(50));
    final var expected1 = new DistributionNominatedLineMother.Builder()
        .id(expectedId1).commitmentOrder(commitmentOrder1).theoreticalQuantity(theo1).requestedQuantity(req1)
        .alternativeReference(null)
        .distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    final var audit2 = new BasicAudit(now, now);
    final var expectedId2 = new DistributionNominatedLine.Id(UuidMother.fromInteger(901));
    final var theo2 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.ZERO);
    final var req2 = new DistributionNominatedLine.RequestedQuantity(BigDecimal.ZERO);
    final var altRefId2 = new AlternativeReference.ReferenceId(referenceId2.value());
    final var altReq2 = new AlternativeReference.RequestedQuantity(requested2);
    final var altTheo2 = new AlternativeReference.TheoreticalQuantity(BigDecimal.valueOf(50));
    final var expected2 = new DistributionNominatedLineMother.Builder()
        .id(expectedId2).commitmentOrder(commitmentOrder2).theoreticalQuantity(theo2).requestedQuantity(req2)
        .alternativeReference(new AlternativeReference(altRefId2, altReq2, altTheo2))
        .distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit2)
        .build();

    assertThat(lines).containsExactlyInAnyOrderElementsOf(List.of(expected1, expected2));
  }

  @Test
  void should_identify_adjusted_lines() {
    final var now = OffsetDateTime.now();
    final var yesterday = now.minusDays(1);
    final var uuidGenerator = UuidGeneratorMother.fromStartingInteger(901);
    final var sut = new DistributionNominatedLinesIdentifier(uuidGenerator);

    final var auditLine = new BasicAudit(yesterday, yesterday);
    final var distributionLineId1 = new DistributionNominatedLine.Id(UuidMother.fromInteger(111));
    final var commitmentOrder1 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(501)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var requestedLine1 = new DistributionNominatedLine.RequestedQuantity(BigDecimal.valueOf(400));
    final var theoreticalLine1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(440));
    final var distributionLine1 = DistributionNominatedLineMother.created()
        .id(distributionLineId1).commitmentOrder(commitmentOrder1).requestedQuantity(requestedLine1).theoreticalQuantity(theoreticalLine1)
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).distributionStartDate(null).alternativeReference(null)
        .audit(auditLine)
        .build();

    final var distributionLineId2 = new DistributionNominatedLine.Id(UuidMother.fromInteger(112));
    final var commitmentOrder2 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(502)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var requestedLine2 = new DistributionNominatedLine.RequestedQuantity(BigDecimal.valueOf(400));
    final var theoreticalLine2 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(440));
    final var distributionLine2 = DistributionNominatedLineMother.created()
        .id(distributionLineId2).commitmentOrder(commitmentOrder2).requestedQuantity(requestedLine2).theoreticalQuantity(theoreticalLine2)
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).distributionStartDate(null).alternativeReference(null)
        .audit(auditLine)
        .build();

    final var distributionAudit = CompleteAudit.create("test", yesterday);
    final var rootId = new Id(UuidMother.fromInteger(101));
    final var targetReferenceId = new ReferenceId(UuidMother.fromInteger(201));
    final var requested = new RequestedQuantity(BigDecimal.valueOf(1000));
    final var theoretical = new TheoreticalQuantity(BigDecimal.valueOf(1100));
    final var distribution = DistributionNominatedMother.pending()
        .id(rootId).referenceId(targetReferenceId).requestedQuantity(requested).theoreticalQuantity(theoretical)
        .lines(List.of(distributionLine1, distributionLine2)).audit(distributionAudit).build();

    final var requested1 = BigDecimal.valueOf(500);
    final var referenceId1 = targetReferenceId;
    final var line1 =
        updated(new Line(rootId, commitmentOrder1, referenceId1, targetReferenceId, requested1, BigDecimal.ZERO));

    final var referenceId2 = targetReferenceId;
    final var requested2 = BigDecimal.valueOf(500);
    final var line2 =
        updated(new Line(rootId, commitmentOrder2, referenceId2, targetReferenceId, requested2, BigDecimal.ZERO));

    final var lines = sut.identifyAdjustedLines(List.of(distribution), List.of(line1, line2), now);

    final var audit = auditLine.update(now);
    final var expectedId1 = distributionLineId1;
    final var req1 = new DistributionNominatedLine.RequestedQuantity(requested1);
    final var theo1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(50));
    final var expected1 = new DistributionNominatedLineMother.Builder()
        .id(expectedId1).commitmentOrder(commitmentOrder1).theoreticalQuantity(theo1).requestedQuantity(req1)
        .alternativeReference(null).distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    final var expectedId2 = distributionLineId2;
    final var req2 = new DistributionNominatedLine.RequestedQuantity(requested2);
    final var theo2 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(50));
    final var expected2 = new DistributionNominatedLineMother.Builder()
        .id(expectedId2).commitmentOrder(commitmentOrder2).theoreticalQuantity(theo2).requestedQuantity(req2)
        .alternativeReference(null).distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    assertThat(lines).isEqualTo(Map.of(rootId, List.of(expected2, expected1)));
  }

  @Test
  void should_identify_adjusted_lines_removing_alternative() {
    final var now = OffsetDateTime.now();
    final var yesterday = now.minusDays(1);
    final var uuidGenerator = UuidGeneratorMother.fromStartingInteger(901);
    final var sut = new DistributionNominatedLinesIdentifier(uuidGenerator);

    final var auditLine = new BasicAudit(yesterday, yesterday);
    final var distributionLineId1 = new DistributionNominatedLine.Id(UuidMother.fromInteger(111));
    final var commitmentOrder1 =
        new CommitmentOrder(new CommitmentOrder.Id(UuidMother.fromInteger(401)), new LineId(UuidMother.fromInteger(501)),
            new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(601))));
    final var requestedLine1 = new DistributionNominatedLine.RequestedQuantity(BigDecimal.valueOf(400));
    final var theoreticalLine1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(440));
    final var altReference = UuidMother.fromInteger(211);
    final var distributionLine1 = DistributionNominatedLineMother.created()
        .id(distributionLineId1).commitmentOrder(commitmentOrder1).requestedQuantity(requestedLine1).theoreticalQuantity(theoreticalLine1)
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).distributionStartDate(null)
        .alternativeReference(new AlternativeReference(
            new AlternativeReference.ReferenceId(altReference),
            new AlternativeReference.RequestedQuantity(BigDecimal.valueOf(400)),
            new AlternativeReference.TheoreticalQuantity(BigDecimal.valueOf(420))))
        .audit(auditLine)
        .build();

    final var distributionAudit = CompleteAudit.create("test", yesterday);
    final var rootId = new Id(UuidMother.fromInteger(101));
    final var targetReferenceId = new ReferenceId(UuidMother.fromInteger(201));
    final var requested = new RequestedQuantity(BigDecimal.valueOf(1000));
    final var theoretical = new TheoreticalQuantity(BigDecimal.valueOf(1100));
    final var distribution = DistributionNominatedMother.pending()
        .id(rootId).referenceId(targetReferenceId).requestedQuantity(requested).theoreticalQuantity(theoretical)
        .lines(List.of(distributionLine1)).audit(distributionAudit).build();

    final var requested1 = BigDecimal.valueOf(500);
    final var line1 =
        kept(new Line(rootId, commitmentOrder1, targetReferenceId, targetReferenceId, requested1, BigDecimal.ZERO));

    final var requested2 = BigDecimal.valueOf(500);
    final var reference2 = new ReferenceId(altReference);
    final var line2 =
        deleted(new Line(rootId, commitmentOrder1, reference2, targetReferenceId, requested2, BigDecimal.ZERO));

    final var lines = sut.identifyAdjustedLines(List.of(distribution), List.of(line1, line2), now);

    final var audit = auditLine.update(now);
    final var expectedId1 = distributionLineId1;
    final var req1 = new DistributionNominatedLine.RequestedQuantity(requested1);
    final var theo1 = new DistributionNominatedLine.TheoreticalQuantity(BigDecimal.valueOf(50));
    final var expected1 = new DistributionNominatedLineMother.Builder()
        .id(expectedId1).commitmentOrder(commitmentOrder1).theoreticalQuantity(theo1).requestedQuantity(req1)
        .alternativeReference(null).distributionStartDate(null).distributedQuantity(new DistributedQuantity(BigDecimal.ZERO)).audit(audit)
        .build();

    assertThat(lines).isEqualTo(Map.of(rootId, List.of(expected1)));
  }

}
