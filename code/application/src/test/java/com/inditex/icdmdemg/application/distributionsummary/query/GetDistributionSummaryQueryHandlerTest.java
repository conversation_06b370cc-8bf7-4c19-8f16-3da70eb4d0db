package com.inditex.icdmdemg.application.distributionsummary.query;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery.DistributionSummaryRequest;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseCollection;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus.MaterialCommitmentUseStatusEnum;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.entity.SharedRawMaterialInner;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GetDistributionSummaryQueryHandlerTest {

  @Mock
  private DistributionNominatedRepository distributionNominatedRepository;

  @Mock
  private DistributionInnerRepository distributionInnerRepository;

  @Mock
  private MaterialCommitmentUseRepository materialCommitmentUseRepository;

  @InjectMocks
  private GetDistributionSummaryQueryHandler sut;

  @Test
  void shouldReturnSummary() {
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());

    final var distributionNominated = Instancio.create(DistributionNominated.class);
    final var materialCommitmentUseCollection = Instancio.create(MaterialCommitmentUseCollection.class);

    when(this.distributionNominatedRepository.findBySharedRawMaterial(new SharedRawMaterialNominated(referenceId, useId, budgetCycle)))
        .thenReturn(List.of(distributionNominated));
    when(this.distributionInnerRepository.findBySharedRawMaterial(new SharedRawMaterialInner(referenceId, useId, budgetCycle)))
        .thenReturn(Collections.emptyList());

    when(this.materialCommitmentUseRepository.findByAnySharedRawMaterial(
        List.of(MaterialCommitmentUseSharedRawMaterial.of(useId.toString(), referenceId.toString(), budgetCycle))))
            .thenReturn(materialCommitmentUseCollection);

    final var query = new GetDistributionSummaryQuery(new DistributionSummaryRequest(referenceId, useId, budgetCycle));
    final var result = this.sut.ask(query);

    assertThat(result).isNotNull();
    assertThat(result.response()).isNotEmpty();
    assertThat(result.error()).isEmpty();
  }

  @Test
  void shouldReturnSummaryGroupedQ() {
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetCycleUUID = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetCycleUUID);

    final var productOrderId = UUID.randomUUID();
    final var productVariantGroupId = UUID.randomUUID();

    final var distributionNominated1 =
        DistributionNominatedMother.pendingWithProductOrderIdAndProductVariantGroupIdAndOneLine(productOrderId, productVariantGroupId)
            .build();
    final var lineDN1 = distributionNominated1.lines().value().get(0);
    final var distributionInner1 =
        DistributionInnerMother.pending()
            .productOrderId(new ProductOrderId(productOrderId))
            .productVariantGroupId(new ProductVariantGroupId(productVariantGroupId))
            .build();
    final var distributionNominated2 =
        DistributionNominatedMother.pendingWithProductOrderIdAndProductVariantGroupIdAndOneLine(productOrderId, productVariantGroupId)
            .build();
    final var lineDN2 = distributionNominated2.lines().value().get(0);
    final var distributionInner2 =
        DistributionInnerMother.pending()
            .productOrderId(new ProductOrderId(productOrderId))
            .productVariantGroupId(new ProductVariantGroupId(productVariantGroupId))
            .build();
    final var distributionNominated3 =
        DistributionNominatedMother.pendingWithProductOrderIdAndProductVariantGroupIdAndOneLine(productOrderId, productVariantGroupId)
            .build();
    final var lineDN3 = distributionNominated3.lines().value().get(0);
    final var distributionInner3 =
        DistributionInnerMother.pending()
            .productOrderId(new ProductOrderId(productOrderId))
            .productVariantGroupId(new ProductVariantGroupId(productVariantGroupId))
            .build();
    final var mcu1 =
        MaterialCommitmentUseMother.withCompositeIdAndStatus(lineDN1.commitmentOrder().id().value(),
            lineDN1.commitmentOrder().lineId().value(), budgetCycleUUID, referenceId, useId, MaterialCommitmentUseStatusEnum.OPEN.name(),
            lineDN1.commitmentOrder().supplierId().value());
    final var mcu2 =
        MaterialCommitmentUseMother.withCompositeIdAndStatus(lineDN2.commitmentOrder().id().value(),
            lineDN2.commitmentOrder().lineId().value(), budgetCycleUUID, referenceId, useId, MaterialCommitmentUseStatusEnum.OPEN.name(),
            lineDN2.commitmentOrder().supplierId().value());
    final var mcu3 =
        MaterialCommitmentUseMother.withCompositeIdAndStatus(lineDN3.commitmentOrder().id().value(),
            lineDN3.commitmentOrder().lineId().value(), budgetCycleUUID, referenceId, useId, MaterialCommitmentUseStatusEnum.OPEN.name(),
            lineDN3.commitmentOrder().supplierId().value());

    when(this.distributionNominatedRepository.findBySharedRawMaterial(new SharedRawMaterialNominated(referenceId, useId, budgetCycle)))
        .thenReturn(List.of(distributionNominated1, distributionNominated2, distributionNominated3));
    when(this.distributionInnerRepository.findBySharedRawMaterial(new SharedRawMaterialInner(referenceId, useId, budgetCycle)))
        .thenReturn(List.of(distributionInner1, distributionInner2, distributionInner3));

    when(this.materialCommitmentUseRepository.findByAnySharedRawMaterial(
        List.of(MaterialCommitmentUseSharedRawMaterial.of(useId.toString(), referenceId.toString(), budgetCycle))))
            .thenReturn(new MaterialCommitmentUseCollection(List.of(mcu1, mcu2, mcu3)));

    final var query = new GetDistributionSummaryQuery(new DistributionSummaryRequest(referenceId, useId, budgetCycle));
    final var result = this.sut.ask(query);

    assertThat(result).isNotNull();
    assertThat(result.response()).isNotEmpty();
    assertThat(result.response().get().distributionNominatesAllocated()).hasSize(1);
    final var resultDn = result.response().get().distributionNominatesAllocated().get(0);
    assertThat(resultDn.requestedQuantity())
        .isEqualTo(
            lineDN1.requestedQuantity().value().add(lineDN2.requestedQuantity().value())
                .add(lineDN3.requestedQuantity().value()));
    assertThat(resultDn.distributedQuantity())
        .isEqualTo(
            lineDN1.distributedQuantity().value().add(lineDN2.distributedQuantity().value())
                .add(lineDN3.distributedQuantity().value()));
    assertThat(result.response().get().distributionInners()).hasSize(1);
    final var resultDi = result.response().get().distributionInners().get(0);
    assertThat(resultDi.requestedQuantity())
        .isEqualTo(
            distributionInner1.requestedQuantity().value().add(distributionInner2.requestedQuantity().value())
                .add(distributionInner3.requestedQuantity().value()));
    assertThat(resultDi.distributedQuantity())
        .isEqualTo(
            distributionInner1.distributedQuantity().value().add(distributionInner2.distributedQuantity().value())
                .add(distributionInner3.distributedQuantity().value()));

    assertThat(result.error()).isEmpty();
  }

  @Test
  void shouldReturnSummaryGroupedQExcludingClosedMCU() {
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetCycleUUID = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetCycleUUID);

    final var productOrderId = UUID.randomUUID();
    final var productVariantGroupId = UUID.randomUUID();

    final var distributionNominated1 =
        DistributionNominatedMother.inProgressWithProductOrderIdAndProductVariantGroupIdAndOneLine(productOrderId, productVariantGroupId)
            .build();
    final var lineDN1 = distributionNominated1.lines().value().get(0);
    final var distributionNominated2 =
        DistributionNominatedMother.inProgressWithProductOrderIdAndProductVariantGroupIdAndOneLine(productOrderId, productVariantGroupId)
            .build();
    final var lineDN2 = distributionNominated2.lines().value().get(0);
    final var distributionNominated3 =
        DistributionNominatedMother.inProgressWithProductOrderIdAndProductVariantGroupIdAndOneLine(productOrderId, productVariantGroupId)
            .build();
    final var lineDN3ToExclude = distributionNominated3.lines().value().get(0);
    final var distributionNominated4 =
        DistributionNominatedMother.inProgressWithProductOrderIdAndProductVariantGroupIdAndOneLine(productOrderId, productVariantGroupId)
            .build();
    final var mcu1 =
        MaterialCommitmentUseMother.withCompositeIdAndStatus(lineDN1.commitmentOrder().id().value(),
            lineDN1.commitmentOrder().lineId().value(), budgetCycleUUID, referenceId, useId, MaterialCommitmentUseStatusEnum.OPEN.name(),
            lineDN1.commitmentOrder().supplierId().value());
    final var mcu2 =
        MaterialCommitmentUseMother.withCompositeIdAndStatus(lineDN2.commitmentOrder().id().value(),
            lineDN2.commitmentOrder().lineId().value(), budgetCycleUUID, referenceId, useId, MaterialCommitmentUseStatusEnum.OPEN.name(),
            lineDN2.commitmentOrder().supplierId().value());
    final var mcu3 =
        MaterialCommitmentUseMother.withCompositeIdAndStatus(lineDN3ToExclude.commitmentOrder().id().value(),
            lineDN3ToExclude.commitmentOrder().lineId().value(), budgetCycleUUID, referenceId, useId,
            MaterialCommitmentUseStatusEnum.CLOSED.name(),
            lineDN3ToExclude.commitmentOrder().supplierId().value());

    when(this.distributionNominatedRepository.findBySharedRawMaterial(new SharedRawMaterialNominated(referenceId, useId, budgetCycle)))
        .thenReturn(List.of(distributionNominated1, distributionNominated2, distributionNominated3, distributionNominated4));
    when(this.distributionInnerRepository.findBySharedRawMaterial(new SharedRawMaterialInner(referenceId, useId, budgetCycle)))
        .thenReturn(Collections.emptyList());

    when(this.materialCommitmentUseRepository.findByAnySharedRawMaterial(
        List.of(MaterialCommitmentUseSharedRawMaterial.of(useId.toString(), referenceId.toString(), budgetCycle))))
            .thenReturn(new MaterialCommitmentUseCollection(List.of(mcu1, mcu2, mcu3)));

    final var query = new GetDistributionSummaryQuery(new DistributionSummaryRequest(referenceId, useId, budgetCycle));
    final var result = this.sut.ask(query);

    assertThat(result).isNotNull();
    assertThat(result.response()).isNotEmpty();
    assertThat(result.response().get().distributionNominatesAllocated()).hasSize(1);
    final var resultDn = result.response().get().distributionNominatesAllocated().get(0);
    assertThat(resultDn.requestedQuantity())
        .isEqualTo(
            lineDN1.requestedQuantity().value().add(lineDN2.requestedQuantity().value()));
    assertThat(resultDn.distributedQuantity())
        .isEqualTo(
            lineDN1.distributedQuantity().value().add(lineDN2.distributedQuantity().value()));

    assertThat(result.error()).isEmpty();
  }

  @Test
  void shouldReturnSummaryGroupedQForCoincidents() {
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);

    final var productOrderId = UUID.randomUUID();
    final var productVariantGroupId = UUID.randomUUID();

    final var distributionNominated1 =
        DistributionNominatedMother.pendingWithProductOrderIdAndProductVariantGroupIdAndOneLine(productOrderId, productVariantGroupId)
            .build();
    final var distributionNominated2 =
        DistributionNominatedMother.pendingWithProductOrderIdAndProductVariantGroupIdAndOneLine(productOrderId, productVariantGroupId)
            .build();
    final var distributionNominated3 =
        DistributionNominatedMother.pendingWithProductOrderIdAndProductVariantGroupIdAndOneLine(productOrderId, productVariantGroupId)
            .build();
    final var distributionNominated4 = DistributionNominatedMother.pending().build();
    final var distributionInner1 = DistributionInnerMother.pending().build();
    final var distributionInner2 = DistributionInnerMother.pending().build();
    final var distributionInner3 = DistributionInnerMother.pending()
        .productOrderId(new ProductOrderId(productOrderId))
        .productVariantGroupId(new ProductVariantGroupId(productVariantGroupId))
        .build();
    final var distributionInner4 = DistributionInnerMother.pending()
        .productOrderId(new ProductOrderId(productOrderId))
        .productVariantGroupId(new ProductVariantGroupId(productVariantGroupId))
        .build();

    final var materialCommitmentUseCollection = this.getMaterialCommitmentUseCollection(budgetId, distributionNominated1,
        distributionNominated2, distributionNominated3, distributionNominated4);

    when(this.distributionNominatedRepository.findBySharedRawMaterial(new SharedRawMaterialNominated(referenceId, useId, budgetCycle)))
        .thenReturn(List.of(distributionNominated1, distributionNominated2, distributionNominated3, distributionNominated4));
    when(this.distributionInnerRepository.findBySharedRawMaterial(new SharedRawMaterialInner(referenceId, useId, budgetCycle)))
        .thenReturn(List.of(distributionInner1, distributionInner2, distributionInner3, distributionInner4));

    when(this.materialCommitmentUseRepository.findByAnySharedRawMaterial(
        List.of(MaterialCommitmentUseSharedRawMaterial.of(useId.toString(), referenceId.toString(), budgetCycle))))
            .thenReturn(materialCommitmentUseCollection);

    final var query = new GetDistributionSummaryQuery(new DistributionSummaryRequest(referenceId, useId, budgetCycle));
    final var result = this.sut.ask(query);

    assertThat(result).isNotNull();
    assertThat(result.response()).isNotEmpty();
    assertThat(result.response().get().distributionNominatesAllocated()).hasSize(2);
    assertThat(result.response().get().distributionInners()).hasSize(3);

    assertThat(result.error()).isEmpty();
  }

  private MaterialCommitmentUseCollection getMaterialCommitmentUseCollection(final UUID budgetId,
      final DistributionNominated distributionNominated1,
      final DistributionNominated distributionNominated2, final DistributionNominated distributionNominated3,
      final DistributionNominated distributionNominated4) {
    final var materialCommitmentUse1 = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        distributionNominated1.lines().value().get(0).commitmentOrder().id().value(),
        distributionNominated1.lines().value().get(0).commitmentOrder().lineId().value(),
        budgetId,
        distributionNominated1.referenceId().value(),
        distributionNominated1.useId().value(),
        BigDecimal.valueOf(1000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
    final var randomCommitment1 = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        UUID.randomUUID(),
        UUID.randomUUID(),
        budgetId,
        distributionNominated1.referenceId().value(),
        distributionNominated1.useId().value(),
        BigDecimal.valueOf(1000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
    final var materialCommitmentUse2 = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        distributionNominated2.lines().value().get(0).commitmentOrder().id().value(),
        distributionNominated2.lines().value().get(0).commitmentOrder().lineId().value(),
        budgetId,
        distributionNominated2.referenceId().value(),
        distributionNominated2.useId().value(),
        BigDecimal.valueOf(2000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
    final var randomCommitment2 = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        UUID.randomUUID(),
        UUID.randomUUID(),
        budgetId,
        distributionNominated2.referenceId().value(),
        distributionNominated2.useId().value(),
        BigDecimal.valueOf(2000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
    final var materialCommitmentUse3 = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        distributionNominated3.lines().value().get(0).commitmentOrder().id().value(),
        distributionNominated3.lines().value().get(0).commitmentOrder().lineId().value(),
        budgetId,
        distributionNominated3.referenceId().value(),
        distributionNominated3.useId().value(),
        BigDecimal.valueOf(3000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
    final var randomCommitment3 = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        UUID.randomUUID(),
        UUID.randomUUID(),
        budgetId,
        distributionNominated3.referenceId().value(),
        distributionNominated3.useId().value(),
        BigDecimal.valueOf(3000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
    final var materialCommitmentUse4 = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        distributionNominated4.lines().value().get(0).commitmentOrder().id().value(),
        distributionNominated4.lines().value().get(0).commitmentOrder().lineId().value(),
        budgetId,
        distributionNominated4.referenceId().value(),
        distributionNominated4.useId().value(),
        BigDecimal.valueOf(4000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
    final var randomCommitment4 = MaterialCommitmentUseMother.withCompositeIdAndQuantity(
        UUID.randomUUID(),
        UUID.randomUUID(),
        budgetId,
        distributionNominated3.referenceId().value(),
        distributionNominated3.useId().value(),
        BigDecimal.valueOf(4000), UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
    return new MaterialCommitmentUseCollection(List.of(
        materialCommitmentUse1, randomCommitment1,
        materialCommitmentUse2, randomCommitment2,
        materialCommitmentUse3, randomCommitment3,
        materialCommitmentUse4, randomCommitment4));
  }
}
