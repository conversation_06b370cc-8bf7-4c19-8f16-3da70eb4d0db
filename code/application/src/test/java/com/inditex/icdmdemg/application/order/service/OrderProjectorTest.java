package com.inditex.icdmdemg.application.order.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderCreatedOrUpdated;
import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderLine;
import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderQuantityDetail;
import com.inditex.icdmdemg.application.order.command.PublishedOrderCommand.PublishedOrder;
import com.inditex.icdmdemg.application.order.command.UpdateOrderStatusCommand.StatusOrderUpdated;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderBudgetId;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.OrderLine;
import com.inditex.icdmdemg.domain.order.entity.OrderLineId;
import com.inditex.icdmdemg.domain.order.entity.OrderLineMeasuringUnitsId;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantity;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetail;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetailReferenceId;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetails;
import com.inditex.icdmdemg.domain.order.entity.OrderLineServiceDate;
import com.inditex.icdmdemg.domain.order.entity.OrderLines;
import com.inditex.icdmdemg.domain.order.entity.OrderStatusKey;
import com.inditex.icdmdemg.domain.order.entity.OrderSupplierId;
import com.inditex.icdmdemg.domain.order.entity.OrderVersion;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;

import org.jspecify.annotations.NonNull;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class OrderProjectorTest {

  @Mock
  private OrderRepository orderRepository;

  @Mock
  private ClockUtils clockUtils;

  @Spy
  private Transaction transaction = new Transaction();

  @InjectMocks
  private OrderProjector sut;

  @Test
  void should_create_order_if_not_exists() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var status = OrderStatusKey.FORMALIZED.value();
    final var now = OffsetDateTime.MIN;
    final var serviceDate = OffsetDateTime.MIN.plusDays(1);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, status, uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.empty()).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.projectFromProductOrder(productOrder);

    final var expectedOrder = Order.create(
        new OrderId(productOrder.id()),
        OrderStatusKey.valueOf(productOrder.statusKey()),
        new OrderLines(productOrder.orderLines().stream()
            .map(productOrderLine -> new OrderLine(
                new OrderLineId(productOrderLine.id()),
                new OrderLineServiceDate(productOrderLine.serviceDate()),
                new OrderLineMeasuringUnitsId(productOrderLine.measuringUnitsId()),
                productOrderLine.budgetId() == null ? null : new OrderBudgetId(productOrderLine.budgetId()),
                new OrderLineQuantityDetails(productOrderLine.productOrderQuantityDetails().stream()
                    .map(productOrderQuantityDetail -> new OrderLineQuantityDetail(
                        new OrderLineQuantity(productOrderQuantityDetail.quantity()), new OrderLineQuantityDetailReferenceId(
                            productOrderQuantityDetail.referenceId())))
                    .toList())))
            .toList()),
        now,
        productOrder.supplierId() == null ? null : new OrderSupplierId(productOrder.supplierId()));

    verify(this.orderRepository).save(expectedOrder);
  }

  @Test
  void should_update_order_if_change_service_date() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));
    final var productOrderToUpdate =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate.plusDays(10), uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, false);
    final var expectedOrder = getOrderFromProductOrder(productOrderToUpdate, createdAt, updatedAt, false);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.projectFromProductOrder(productOrderToUpdate);

    verify(this.orderRepository).save(expectedOrder);
  }

  @Test
  void should_update_order_if_change_supplier_id() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var uuid7 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));
    final var productOrderToUpdate =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid7, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, false);
    final var expectedOrder = getOrderFromProductOrder(productOrderToUpdate, createdAt, updatedAt, false);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.projectFromProductOrder(productOrderToUpdate);

    verify(this.orderRepository).save(expectedOrder);
  }

  @Test
  void should_update_order_if_new_line() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var uuid7 = UUID.randomUUID().toString();
    final var uuid8 = UUID.randomUUID().toString();
    final var uuid9 = UUID.randomUUID().toString();
    final var uuid10 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));
    final var productOrderToUpdate =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6))),
            new ProductOrderLine(uuid7, uuid8, serviceDate, uuid9, List.of(
                new ProductOrderQuantityDetail(1, uuid10)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, false);
    final var expectedOrder = getOrderFromProductOrder(productOrderToUpdate, createdAt, updatedAt, false);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.projectFromProductOrder(productOrderToUpdate);

    verify(this.orderRepository).save(expectedOrder);
  }

  @Test
  void should_update_order_if_deleted_line() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var uuid7 = UUID.randomUUID().toString();
    final var uuid8 = UUID.randomUUID().toString();
    final var uuid9 = UUID.randomUUID().toString();
    final var uuid10 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6))),
            new ProductOrderLine(uuid7, uuid8, serviceDate, uuid9, List.of(
                new ProductOrderQuantityDetail(1, uuid10)))));
    final var productOrderToUpdate =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, false);
    final var expectedOrder = getOrderFromProductOrder(productOrderToUpdate, createdAt, updatedAt, false);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.projectFromProductOrder(productOrderToUpdate);

    verify(this.orderRepository).save(expectedOrder);
  }

  @Test
  void should_update_order_if_change_order_line_measure_unit() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var uuid8 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));
    final var productOrderToUpdate =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid8, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, false);
    final var expectedOrder = getOrderFromProductOrder(productOrderToUpdate, createdAt, updatedAt, false);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.projectFromProductOrder(productOrderToUpdate);

    verify(this.orderRepository).save(expectedOrder);
  }

  @Test
  void should_update_order_if_change_budgetId() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var uuid8 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));
    final var productOrderToUpdate =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid8, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, false);
    final var expectedOrder = getOrderFromProductOrder(productOrderToUpdate, createdAt, updatedAt, false);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.projectFromProductOrder(productOrderToUpdate);

    verify(this.orderRepository).save(expectedOrder);
  }

  @Test
  void should_update_order_if_new_qty() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));
    final var productOrderToUpdate =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(8, uuid6)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, false);
    final var expectedOrder = getOrderFromProductOrder(productOrderToUpdate, createdAt, updatedAt, false);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.projectFromProductOrder(productOrderToUpdate);

    verify(this.orderRepository).save(expectedOrder);
  }

  @Test
  void should_update_order_if_new_quantity_detail() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var uuid8 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));
    final var productOrderToUpdate =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6),
                new ProductOrderQuantityDetail(1, uuid8)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, false);
    final var expectedOrder = getOrderFromProductOrder(productOrderToUpdate, createdAt, updatedAt, false);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.projectFromProductOrder(productOrderToUpdate);

    verify(this.orderRepository).save(expectedOrder);
  }

  @Test
  void should_update_order_if_delete_quantity_detail() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var uuid8 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6),
                new ProductOrderQuantityDetail(1, uuid8)))));
    final var productOrderToUpdate =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, false);
    final var expectedOrder = getOrderFromProductOrder(productOrderToUpdate, createdAt, updatedAt, false);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.projectFromProductOrder(productOrderToUpdate);

    verify(this.orderRepository).save(expectedOrder);
  }

  @Test
  void should_not_update_order_if_exists_but_not_modified() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));
    final var productOrderToUpdate =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, false);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.projectFromProductOrder(productOrderToUpdate);

    verify(this.orderRepository, never()).save(any());
  }

  @Test
  void should_update_order_if_change_status() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));
    final var productOrderToUpdate =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.CANCELLED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, false);
    final var expectedOrder = getOrderFromProductOrder(productOrderToUpdate, createdAt, updatedAt, false);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.updateStatus(new StatusOrderUpdated(productOrder.id(), OrderStatusKey.CANCELLED.value()));

    verify(this.orderRepository).save(expectedOrder);
  }

  @Test
  void should_update_order_if_not_published() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));
    final var productOrderToUpdate =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, false);
    final var expectedOrder = getOrderFromProductOrder(productOrderToUpdate, createdAt, updatedAt, true);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.publishOrder(new PublishedOrder(productOrder.id()));

    verify(this.orderRepository).save(expectedOrder);
  }

  @Test
  void should_not_update_order_if_no_change_status() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, false);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.updateStatus(new StatusOrderUpdated(productOrder.id(), productOrder.statusKey()));

    verify(this.orderRepository, never()).save(any());
  }

  @Test
  void should_not_update_order_if_already_published() {
    final var uuid1 = UUID.randomUUID().toString();
    final var uuid2 = UUID.randomUUID().toString();
    final var uuid3 = UUID.randomUUID().toString();
    final var uuid4 = UUID.randomUUID().toString();
    final var uuid5 = UUID.randomUUID().toString();
    final var uuid6 = UUID.randomUUID().toString();
    final var createdAt = OffsetDateTime.MIN;
    final var updatedAt = OffsetDateTime.MIN.plusDays(5);
    final var serviceDate = OffsetDateTime.MIN.plusDays(10);

    final var productOrder =
        new ProductOrderCreatedOrUpdated(uuid1, OrderStatusKey.FORMALIZED.value(), uuid2, List.of(
            new ProductOrderLine(uuid3, uuid4, serviceDate, uuid5, List.of(
                new ProductOrderQuantityDetail(1, uuid6)))));

    final var existingOrder = getOrderFromProductOrder(productOrder, createdAt, updatedAt, true);

    doReturn(updatedAt).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(Optional.of(existingOrder)).when(this.orderRepository).find(new OrderId(uuid1));

    this.sut.publishOrder(new PublishedOrder(productOrder.id()));

    verify(this.orderRepository, never()).save(any());
  }

  private static Order getOrderFromProductOrder(final ProductOrderCreatedOrUpdated productOrderCreatedOrUpdated,
      final OffsetDateTime createdAt,
      final OffsetDateTime updatedAt, final boolean isPublished) {
    final @NonNull OrderId orderId = new OrderId(productOrderCreatedOrUpdated.id());
    final OrderStatusKey orderStatusKey = OrderStatusKey.valueOf(productOrderCreatedOrUpdated.statusKey());
    final @NonNull OrderLines orderLines = new OrderLines(productOrderCreatedOrUpdated.orderLines().stream()
        .map(productOrderLine -> new OrderLine(
            new OrderLineId(productOrderLine.id()),
            new OrderLineServiceDate(productOrderLine.serviceDate()),
            new OrderLineMeasuringUnitsId(productOrderLine.measuringUnitsId()),
            productOrderLine.budgetId() == null ? null : new OrderBudgetId(productOrderLine.budgetId()),
            new OrderLineQuantityDetails(productOrderLine.productOrderQuantityDetails().stream()
                .map(productOrderQuantityDetail -> new OrderLineQuantityDetail(
                    new OrderLineQuantity(productOrderQuantityDetail.quantity()),
                    new OrderLineQuantityDetailReferenceId(productOrderQuantityDetail.referenceId())))
                .toList())))
        .toList());
    final OrderSupplierId supplierId =
        productOrderCreatedOrUpdated.supplierId() == null ? null : new OrderSupplierId(productOrderCreatedOrUpdated.supplierId());
    final var audit = BasicAudit.builder()
        .createdAt(createdAt)
        .updatedAt(updatedAt)
        .build();
    return new Order(orderId, orderStatusKey, orderLines, audit, supplierId,
        isPublished, OrderVersion.firstVersion());
  }
}
