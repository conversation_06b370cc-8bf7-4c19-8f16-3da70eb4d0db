package com.inditex.icdmdemg.application.distributioninner.query;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.shared.Error.NotFound;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GetDistributionInnerByIdQueryHandlerTest {

  @Mock
  private DistributionInnerRepository distributionInnerRepository;

  @InjectMocks
  private GetDistributionInnerByIdQueryHandler sut;

  @Test
  void testDebtGetById() {
    final var distributionInnerId = UUID.randomUUID();

    final var distributionInner = Instancio.create(DistributionInner.class);

    when(this.distributionInnerRepository.findById(new Id(distributionInnerId)))
        .thenReturn(Optional.of(distributionInner));

    final var query = new GetDistributionInnerByIdQuery(distributionInnerId);
    final var output = this.sut.ask(query);

    assertThat(output).isNotNull();
    assertThat(output.response()).isNotEmpty();
    assertThat(output.error()).isEmpty();
  }

  @Test
  void testDebtGetByNonExistingId() {
    final var distributionInnerId = UUID.randomUUID();

    when(this.distributionInnerRepository.findById(new Id(distributionInnerId)))
        .thenReturn(Optional.empty());

    final var query = new GetDistributionInnerByIdQuery(distributionInnerId);
    final var output = this.sut.ask(query);

    assertThat(output).isNotNull();
    assertThat(output.error()).isNotEmpty().hasValue(new NotFound(distributionInnerId.toString()));
    assertThat(output.response()).isEmpty();
  }
}
