package com.inditex.icdmdemg.application.shipmentcommitment.command;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import java.time.OffsetDateTime;
import java.util.Optional;

import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCreatedCommand.ShipmentCreated;
import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitment;
import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitmentRepository;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentMeasurementUnitId;
import com.inditex.icdmdemg.domain.shipmentcommitment.mother.ShipmentCommitmentMother;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class ProjectCommitmentShipmentCreatedCommandHandlerTest {

  private ClockUtils clock;

  private ShipmentCommitmentRepository repository;

  private ProjectCommitmentShipmentCreatedCommandHandler sut;

  @BeforeEach
  void setUp() {
    this.clock = mock(ClockUtils.class);
    this.repository = mock(ShipmentCommitmentRepository.class);
    final Transaction transaction = new Transaction();
    this.sut = new ProjectCommitmentShipmentCreatedCommandHandler(this.clock, this.repository, transaction);
  }

  @Test
  void should_create_shipment_commitment() {
    final var now = OffsetDateTime.now();
    final var shipmentCommitmentCreated = ShipmentCommitmentMother.created(now);
    final var command = shipmentCommitmentToCommand(shipmentCommitmentCreated);

    doReturn(now).when(this.clock).getCurrentOffsetDateTime();
    doReturn(shipmentCommitmentCreated).when(this.repository).save(shipmentCommitmentCreated);

    final var result = this.sut.execute(command);

    assertThat(result).isEqualTo(shipmentCommitmentCreated);
    verify(this.repository).save(shipmentCommitmentCreated);
  }

  @Test
  void should_return_existing_shipment_commitment_when_already_exists() {
    final var now = OffsetDateTime.now();
    final var shipmentCommitmentCreated = ShipmentCommitmentMother.random();
    final var command = shipmentCommitmentToCommand(shipmentCommitmentCreated);

    doReturn(now).when(this.clock).getCurrentOffsetDateTime();
    doReturn(Optional.of(shipmentCommitmentCreated)).when(this.repository).findById(any());

    final var result = this.sut.execute(command);

    assertThat(result).isEqualTo(shipmentCommitmentCreated);
    verify(this.repository).findById(shipmentCommitmentCreated.getId());
    verify(this.repository, never()).save(any());
  }

  private static ProjectCommitmentShipmentCreatedCommand shipmentCommitmentToCommand(final ShipmentCommitment from) {
    return new ProjectCommitmentShipmentCreatedCommand(new ShipmentCreated(from.getId().value(),
        from.getDistributionNominatedLineId().value(),
        from.getDistributedQuantity().quantity().value(),
        acceptNullElseMap(from.getDistributedQuantity().measurementUnitId(), ShipmentCommitmentMeasurementUnitId::value),
        from.getSentDate().value()));
  }
}
