package com.inditex.icdmdemg.application.use.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.application.use.evaluation.PurchasePurposeEvaluationResults;
import com.inditex.icdmdemg.application.use.evaluation.PurchasePurposeEvaluator;
import com.inditex.icdmdemg.application.use.mother.PurchasePurposeEvaluationResultMother;
import com.inditex.icdmdemg.application.use.mother.UsePurchasePurposeConditionMother;
import com.inditex.icdmdemg.application.use.mother.UsePurchasePurposeConditionMother.Builder;
import com.inditex.icdmdemg.domain.taxonomyuse.TaxonomyUse;
import com.inditex.icdmdemg.domain.taxonomyuse.TaxonomyUseRepository;
import com.inditex.icdmdemg.domain.taxonomyuse.entity.TaxonomyCode;
import com.inditex.icdmdemg.domain.taxonomyuse.entity.TaxonomyPath;
import com.inditex.icdmdemg.domain.use.Use.Customer;
import com.inditex.icdmdemg.domain.use.Use.Id;
import com.inditex.icdmdemg.domain.use.Use.Taxonomy;
import com.inditex.icdmdemg.domain.use.UseRepository;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeCondition;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues.ConditionValue;
import com.inditex.icdmdemg.domain.use.entity.PurchaseType;
import com.inditex.icdmdemg.domain.use.entity.UseConditions;
import com.inditex.icdmdemg.domain.use.mother.UseMother;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GetOrderedValidUseProviderTest {

  @Mock
  private UseRepository useRepository;

  @Mock
  private TaxonomyUseRepository taxonomyUseRepository;

  @InjectMocks
  private GetOrderedValidUseProvider provider;

  @ParameterizedTest
  @MethodSource("provideParamsWithNoCoincidences")
  void should_evaluate_all_uses_and_return_sorted_results(PurchaseType purchaseTypeInput, List<PurchaseType> determinedPurchaseTypeList) {
    final Builder usePurchasePurposeConditionMotherBuilder = UsePurchasePurposeConditionMother.generateBuild();

    final var conditionUse1 = usePurchasePurposeConditionMotherBuilder.purchasePurposeConditionName(PurchasePurposeConditionName.BUYERGROUP)
        .purchasePurposeCondition(
            PurchasePurposeCondition.EQUALS)
        .purchasePurposeConditionValues(new PurchasePurposeConditionValues(List.of(new ConditionValue("urn:BUYERGROUP:b"))))
        .build();
    final var conditionUse2 = usePurchasePurposeConditionMotherBuilder.purchasePurposeConditionName(PurchasePurposeConditionName.FAMILY)
        .purchasePurposeCondition(
            PurchasePurposeCondition.ANY_IN)
        .purchasePurposeConditionValues(new PurchasePurposeConditionValues(List.of(new ConditionValue("urn:FAMILY:1"))))
        .build();
    final var condition2Use2 =
        usePurchasePurposeConditionMotherBuilder.purchasePurposeConditionName(PurchasePurposeConditionName.BUYERGROUP)
            .purchasePurposeCondition(
                PurchasePurposeCondition.EQUALS)
            .purchasePurposeConditionValues(new PurchasePurposeConditionValues(List.of(new ConditionValue("urn:BUYERGROUP:b"))))
            .build();

    final var taxonomy = new TaxonomyUse(new TaxonomyCode("ZIPPER"), new TaxonomyPath("/RAW_MATERIALS/TRIMMING/ZIPPER"));
    final var testcustomer = new Customer("TESTCUSTOMER");

    final var useBuilder = UseMother.generateBuild();
    final var use1 = useBuilder.id(new Id(UUID.fromString("e74c23c5-1086-4821-b7ad-b98157991c55"))).purchaseType(PurchaseType.INNER)
        .conditions(new UseConditions(List.of(conditionUse1))).build();
    final var use2 =
        useBuilder.id(new Id(UUID.fromString("54399d45-ef28-4fbc-a330-bd441ea700b8"))).purchaseType(PurchaseType.NOMINATED_INNER)
            .conditions(new UseConditions(List.of(conditionUse2, condition2Use2))).build();

    final var evaluationResultBuilder = PurchasePurposeEvaluationResultMother.generateBuild();
    final var result1 = evaluationResultBuilder.id(use1.getId()).oks(1).kos(0)
        .paramsWithOk(List.of(PurchasePurposeConditionName.BUYERGROUP)).coincidences(1).build();
    final var result2 =
        evaluationResultBuilder.id(use2.getId()).oks(1).kos(1).paramsWithOk(List.of(PurchasePurposeConditionName.FAMILY)).coincidences(1)
            .build();

    when(this.taxonomyUseRepository.findByCode(any())).thenReturn(Optional.of(taxonomy));
    when(this.useRepository.findByTaxonomiesAndCustomerAndPurchaseTypes(any(), any(), any()))
        .thenReturn(List.of(use1, use2));

    try (final MockedStatic<PurchasePurposeEvaluator> evaluatorMock = mockStatic(PurchasePurposeEvaluator.class)) {
      evaluatorMock.when(() -> PurchasePurposeEvaluator.evaluate(eq(use1.getId()), anyList(), anyList()))
          .thenReturn(result1);
      evaluatorMock.when(() -> PurchasePurposeEvaluator.evaluate(eq(use2.getId()), anyList(), anyList()))
          .thenReturn(result2);

      final var orderResult = this.provider.getOrderedValidUse(taxonomy.getCode(), testcustomer,
          purchaseTypeInput, List.of(new PurchasePurposeParameter(PurchasePurposeConditionName.BUYERGROUP, List.of("urn:BUYERGROUP:b"))));

      assertThat(orderResult).isNotNull();
      final var results = orderResult.results();
      assertThat(results).isNotEmpty();
      assertThat(results.getFirst().useId()).isEqualTo(use1.getId());
      assertThat(results.getLast().useId()).isEqualTo(use2.getId());
      verify(this.taxonomyUseRepository).findByCode(taxonomy.getCode());
      verify(this.useRepository).findByTaxonomiesAndCustomerAndPurchaseTypes(
          taxonomy.mapToTaxonomies().stream().map(Taxonomy::new).toList(), testcustomer,
          determinedPurchaseTypeList);

    }
  }

  private static Stream<Arguments> provideParamsWithNoCoincidences() {
    return Stream.of(
        Arguments.of(PurchaseType.INNER, List.of(PurchaseType.INNER, PurchaseType.NOMINATED_INNER)),
        Arguments.of(PurchaseType.NOMINATED_INNER, List.of(PurchaseType.INNER, PurchaseType.NOMINATED, PurchaseType.NOMINATED_INNER)));
  }

  @Test
  void should_does_not_evaluate_uses_when_taxonomy_is_not_valid() {
    final var taxonomyCode = new TaxonomyCode("CLOTHES");

    when(this.taxonomyUseRepository.findByCode(any())).thenReturn(Optional.empty());

    final var result = this.provider.getOrderedValidUse(taxonomyCode, new Customer("TESTCUSTOMER"),
        PurchaseType.NOMINATED_INNER,
        List.of(new PurchasePurposeParameter(PurchasePurposeConditionName.BUYERGROUP, List.of("urn:BUYERGROUP:b"))));

    assertThat(result).isEqualTo(PurchasePurposeEvaluationResults.empty());
    verify(this.taxonomyUseRepository).findByCode(taxonomyCode);
    verify(this.useRepository, never()).findByTaxonomiesAndCustomerAndPurchaseTypes(any(), any(), any());
  }
}
