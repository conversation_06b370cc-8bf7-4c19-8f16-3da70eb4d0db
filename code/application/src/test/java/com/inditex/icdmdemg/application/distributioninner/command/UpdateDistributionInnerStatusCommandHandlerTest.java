package com.inditex.icdmdemg.application.distributioninner.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;

import java.math.BigDecimal;
import java.time.Clock;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.OrderLines;
import com.inditex.icdmdemg.domain.order.entity.OrderStatusKey;
import com.inditex.icdmdemg.domain.order.entity.OrderSupplierId;
import com.inditex.icdmdemg.domain.order.entity.OrderVersion;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = UpdateDistributionInnerStatusCommandHandlerTest.Config.class)
class UpdateDistributionInnerStatusCommandHandlerTest {

  @MockitoBean
  private DistributionInnerRepository distributionInnerRepository;

  @MockitoBean
  private OrderRepository orderRepository;

  @MockitoBean
  private EventBus eventBus;

  @Autowired
  private ClockUtils clockUtils;

  @Captor
  ArgumentCaptor<DistributionInner> saveCaptor;

  @Captor
  ArgumentCaptor<Collection<DomainEvent<?>>> eventsCaptor;

  @Autowired
  private UpdateDistributionInnerStatusCommandHandler sut;

  @BeforeEach
  void setUp() {
    reset(this.distributionInnerRepository, this.orderRepository, this.eventBus);
  }

  @Test
  void should_do_nothing_when_order_does_not_exist() {
    final var orderId = UuidMother.fromInteger(101);

    final var now = OffsetDateTimeMother.fromInteger(2);
    this.clockUtils.setClock(Clock.fixed(now.toInstant(), ZoneId.of("UTC")));

    doReturn(Optional.empty()).when(this.orderRepository).find(new OrderId(orderId.toString()));

    this.sut.execute(new UpdateDistributionInnerStatusCommand(orderId, "test"));

    verifyNoInteractions(this.distributionInnerRepository, this.eventBus);
  }

  @Test
  void should_do_nothing_when_order_is_published_and_dn_closed_or_cancelled() {
    final var orderId = UuidMother.fromInteger(101);

    final var now = OffsetDateTimeMother.fromInteger(2);
    this.clockUtils.setClock(Clock.fixed(now.toInstant(), ZoneId.of("UTC")));

    final var productOrderId = new OrderId(orderId.toString());
    final var order = new Order(productOrderId, OrderStatusKey.FORMALIZED, new OrderLines(new ArrayList<>()),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(1)),
        new OrderSupplierId(UUID.randomUUID().toString()), true, OrderVersion.firstVersion());

    final var dis = List.of(
        DistributionInnerMother.withStatus(DistributionInnerStatus.CLOSED)
            .distributedQuantity(new DistributionInner.DistributedQuantity(BigDecimal.ZERO))
            .productOrderId(new DistributionInner.ProductOrderId(orderId))
            .build(),
        DistributionInnerMother.withStatus(DistributionInnerStatus.CANCELED)
            .distributedQuantity(new DistributionInner.DistributedQuantity(BigDecimal.ZERO))
            .productOrderId(new DistributionInner.ProductOrderId(orderId))
            .build());

    doReturn(Optional.of(order)).when(this.orderRepository).find(productOrderId);
    doReturn(dis).when(this.distributionInnerRepository).findByProductOrderId(new DistributionInner.ProductOrderId(orderId));

    this.sut.execute(new UpdateDistributionInnerStatusCommand(orderId, "test"));

    verifyNoInteractions(this.eventBus);
    verify(this.distributionInnerRepository, never()).save(any(DistributionInner.class));
  }

  @Test
  void should_do_nothing_when_order_is_reopened_and_di_cancelled_or_closed() {
    final var orderId = UuidMother.fromInteger(101);

    final var now = OffsetDateTimeMother.fromInteger(2);
    this.clockUtils.setClock(Clock.fixed(now.toInstant(), ZoneId.of("UTC")));

    final var productOrderId = new OrderId(orderId.toString());
    final var order = new Order(productOrderId, OrderStatusKey.DRAFT, new OrderLines(new ArrayList<>()),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(1)),
        new OrderSupplierId(UUID.randomUUID().toString()), false, OrderVersion.firstVersion());

    final var dis = List.of(
        DistributionInnerMother.withStatus(DistributionInnerStatus.CLOSED)
            .distributedQuantity(new DistributionInner.DistributedQuantity(BigDecimal.ZERO))
            .productOrderId(new DistributionInner.ProductOrderId(orderId))
            .build(),
        DistributionInnerMother.withStatus(DistributionInnerStatus.CANCELED)
            .distributedQuantity(new DistributionInner.DistributedQuantity(BigDecimal.ZERO))
            .productOrderId(new DistributionInner.ProductOrderId(orderId))
            .build());

    doReturn(Optional.of(order)).when(this.orderRepository).find(productOrderId);
    doReturn(dis).when(this.distributionInnerRepository).findByProductOrderId(new DistributionInner.ProductOrderId(orderId));

    this.sut.execute(new UpdateDistributionInnerStatusCommand(orderId, "test"));

    verifyNoInteractions(this.eventBus);
    verify(this.distributionInnerRepository, never()).save(any(DistributionInner.class));
  }

  @Test
  void should_do_nothing_when_new_status_is_the_same() {
    final var orderId = UuidMother.fromInteger(101);

    final var now = OffsetDateTimeMother.fromInteger(2);
    this.clockUtils.setClock(Clock.fixed(now.toInstant(), ZoneId.of("UTC")));

    final var productOrderId = new OrderId(orderId.toString());
    final var order = new Order(productOrderId, OrderStatusKey.FORMALIZED, new OrderLines(new ArrayList<>()),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(1)),
        new OrderSupplierId(UUID.randomUUID().toString()), true, OrderVersion.firstVersion());

    final var dis = List.of(
        DistributionInnerMother.withStatus(DistributionInnerStatus.IN_PROGRESS)
            .distributedQuantity(new DistributionInner.DistributedQuantity(BigDecimal.TEN))
            .productOrderId(new DistributionInner.ProductOrderId(orderId))
            .build());

    doReturn(Optional.of(order)).when(this.orderRepository).find(productOrderId);
    doReturn(dis).when(this.distributionInnerRepository).findByProductOrderId(new DistributionInner.ProductOrderId(orderId));

    this.sut.execute(new UpdateDistributionInnerStatusCommand(orderId, "test"));

    verify(this.eventBus).send(this.eventsCaptor.capture());
    assertThat(this.eventsCaptor.getValue())
        .isEmpty();
    verify(this.distributionInnerRepository).save(this.saveCaptor.capture());
    assertThat(this.saveCaptor.getValue()).isEqualTo(dis.get(0));
  }

  @ParameterizedTest
  @MethodSource("provideStatusCases")
  void should_update_status(
      final OrderStatusKey orderStatusKey,
      final DistributionInnerStatus currentStatus,
      final DistributionInnerStatus expectedStatus,
      final String expectedEventType,
      final boolean isPublished,
      final BigDecimal distributedQuantity) {

    final var orderId = UuidMother.fromInteger(101);

    final var now = OffsetDateTimeMother.fromInteger(2);
    this.clockUtils.setClock(Clock.fixed(now.toInstant(), ZoneId.of("UTC")));

    final var productOrderId = new OrderId(orderId.toString());
    final var order = new Order(productOrderId, orderStatusKey, new OrderLines(new ArrayList<>()),
        BasicAudit.create(OffsetDateTimeMother.fromInteger(1)),
        new OrderSupplierId(UUID.randomUUID().toString()), isPublished, OrderVersion.firstVersion());

    final var distributionInner = DistributionInnerMother
        .withStatus(currentStatus)
        .distributedQuantity(new DistributionInner.DistributedQuantity(distributedQuantity))
        .productOrderId(new DistributionInner.ProductOrderId(orderId))
        .build();

    doReturn(Optional.of(order)).when(this.orderRepository).find(productOrderId);
    doReturn(List.of(distributionInner)).when(this.distributionInnerRepository)
        .findByProductOrderId(new DistributionInner.ProductOrderId(orderId));

    final List<DistributionInner> result = this.sut.execute(new UpdateDistributionInnerStatusCommand(orderId, "test"));

    verify(this.distributionInnerRepository).save(this.saveCaptor.capture());
    verify(this.eventBus).send(this.eventsCaptor.capture());

    assertThat(this.saveCaptor.getValue()).extracting(DistributionInner::status).isEqualTo(expectedStatus);
    assertThat(this.saveCaptor.getValue()).extracting(DistributionInner::audit).extracting(CompleteAudit::updatedAt)
        .isEqualTo(now);
    assertThat(this.saveCaptor.getValue()).extracting(DistributionInner::audit).extracting(CompleteAudit::updatedBy)
        .isEqualTo("test");
    assertThat(this.eventsCaptor.getValue())
        .isNotEmpty()
        .allMatch(domainEvent -> domainEvent instanceof DistributionInnerUnifiedEvent)
        .allMatch(domainEvent -> domainEvent.eventType().equals(expectedEventType));
    assertThat(result).containsExactlyInAnyOrderElementsOf(List.of(distributionInner));
  }

  public Stream<Arguments> provideStatusCases() {
    return Stream.of(
        Arguments.of(OrderStatusKey.CANCELLED, DistributionInnerStatus.IN_PROGRESS, DistributionInnerStatus.CLOSED,
            EventType.CLOSED.value, false, BigDecimal.ZERO),
        Arguments.of(OrderStatusKey.CANCELLED, DistributionInnerStatus.SENT, DistributionInnerStatus.CLOSED,
            EventType.CLOSED.value, false, BigDecimal.ZERO),
        Arguments.of(OrderStatusKey.CANCELLED, DistributionInnerStatus.PENDING, DistributionInnerStatus.PENDING,
            EventType.DELETED.value, false, BigDecimal.ZERO),

        Arguments.of(OrderStatusKey.CANCELLED, DistributionInnerStatus.NON_DISTRIBUTABLE, DistributionInnerStatus.NON_DISTRIBUTABLE,
            EventType.DELETED.value, false, BigDecimal.ZERO),
        Arguments.of(OrderStatusKey.CLOSED, DistributionInnerStatus.IN_PROGRESS, DistributionInnerStatus.CLOSED,
            EventType.CLOSED.value, false, BigDecimal.ZERO),

        Arguments.of(OrderStatusKey.CLOSED, DistributionInnerStatus.PENDING, DistributionInnerStatus.CLOSED,
            EventType.CLOSED.value, false, BigDecimal.ZERO),

        Arguments.of(OrderStatusKey.CLOSED, DistributionInnerStatus.NON_DISTRIBUTABLE, DistributionInnerStatus.CLOSED,
            EventType.CLOSED.value, false, BigDecimal.ZERO));

  }

  @Configuration
  @Import({ClockUtils.class, Transaction.class, UpdateDistributionInnerStatusCommandHandler.class})
  public static class Config {
  }
}
