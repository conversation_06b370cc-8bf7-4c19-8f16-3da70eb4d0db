package com.inditex.icdmdemg.application.distributionsummary.query;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery.DistributionConsumptionFactorRequest;
import com.inditex.icdmdemg.domain.distributions.DistributionConsumptionFactorDTO;
import com.inditex.icdmdemg.domain.distributions.DistributionConsumptionFactorDTO.DistributionType;
import com.inditex.icdmdemg.domain.distributions.DistributionConsumptionFactorDTO.ProductId;
import com.inditex.icdmdemg.domain.distributions.DistributionsRepository;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GetDistributionConsumptionFactorQueryHandlerTest {

  @Mock
  private DistributionsRepository distributionsRepository;

  @InjectMocks
  private GetDistributionConsumptionFactorQueryHandler sut;

  @Test
  void should_return_distribution_consumption_factor_summary() {
    final var productOrderId1 = new ProductId(UUID.randomUUID());
    final var productOrderId2 = new ProductId(UUID.randomUUID());

    final var distributionConsumptionFactorDTONominated = Instancio.of(DistributionConsumptionFactorDTO.class)
        .set(field("distributionType"), DistributionType.NOMINATED)
        .create();
    final var distributionConsumptionFactorDTOInner = Instancio.of(DistributionConsumptionFactorDTO.class)
        .set(field("distributionType"), DistributionType.INNER)
        .create();

    when(this.distributionsRepository.findDistributionConsumptionFactorByProductOrderIds(List.of(productOrderId1, productOrderId2)))
        .thenReturn(List.of(distributionConsumptionFactorDTONominated, distributionConsumptionFactorDTOInner));

    final var query =
        new GetDistributionConsumptionFactorQuery(
            new DistributionConsumptionFactorRequest(List.of(productOrderId1.value(), productOrderId2.value())));
    final var result = this.sut.ask(query);

    assertThat(result).isNotNull();
    assertThat(result.response()).isNotEmpty();
    assertThat(result.response().get().distributionsConsumptionFactor().getFirst().referenceId())
        .isEqualTo(distributionConsumptionFactorDTONominated.referenceId().value());
    assertThat(result.response().get().distributionsConsumptionFactor().getFirst().variantGroupId())
        .isEqualTo(distributionConsumptionFactorDTONominated.variantGroupId().value());
    assertThat(result.response().get().distributionsConsumptionFactor().getFirst().consumptionFactor())
        .isEqualTo(distributionConsumptionFactorDTONominated.consumptionFactor().value());
    assertThat(result.response().get().distributionsConsumptionFactor().getFirst().distributionType())
        .isEqualTo(distributionConsumptionFactorDTONominated.distributionType().toString());

    assertThat(result.response().get().distributionsConsumptionFactor().getLast().referenceId())
        .isEqualTo(distributionConsumptionFactorDTOInner.referenceId().value());
    assertThat(result.response().get().distributionsConsumptionFactor().getLast().variantGroupId())
        .isEqualTo(distributionConsumptionFactorDTOInner.variantGroupId().value());
    assertThat(result.response().get().distributionsConsumptionFactor().getLast().consumptionFactor())
        .isEqualTo(distributionConsumptionFactorDTOInner.consumptionFactor().value());
    assertThat(result.response().get().distributionsConsumptionFactor().getLast().distributionType())
        .isEqualTo(distributionConsumptionFactorDTOInner.distributionType().toString());
  }

  @Test
  void should_return_error_productOrderIds_empty() {
    final var query =
        new GetDistributionConsumptionFactorQuery(
            new DistributionConsumptionFactorRequest(List.of()));
    final var result = this.sut.ask(query);

    assertTrue(result.error().isPresent());
    verifyNoInteractions(this.distributionsRepository);

  }

}
