package com.inditex.icdmdemg.application.use.evaluation;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.application.use.mother.UsePurchasePurposeConditionMother;
import com.inditex.icdmdemg.application.use.mother.UsePurchasePurposeConditionMother.Builder;
import com.inditex.icdmdemg.application.use.service.PurchasePurposeParameter;
import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeCondition;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues;
import com.inditex.icdmdemg.domain.use.entity.UsePurchasePurposeCondition;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class PurchasePurposeEvaluateTest {

  @ParameterizedTest
  @MethodSource("provideTypes")
  void should_evaluate_purchase_purpose_conditions_correctly(final List<UsePurchasePurposeCondition> purchasePurposeConditions,
      final List<PurchasePurposeParameter> purchasePurposeParameterList,
      final PurchasePurposeEvaluationResult expected) {

    final var result =
        PurchasePurposeEvaluator.evaluate(new com.inditex.icdmdemg.domain.use.Use.Id(expected.useId().value()),
            purchasePurposeParameterList, purchasePurposeConditions);
    assertThat(result.coincidences()).isEqualTo(expected.coincidences());
    assertThat(result.oks()).isEqualTo(expected.oks());
    assertThat(result.kos()).isEqualTo(expected.kos());
    assertThat(result.paramsWithOk()).containsAll(expected.paramsWithOk()).hasSize(expected.paramsWithOk().size());
  }

  // coincidences in none in
  private static Stream<Arguments> provideTypes() {
    final var uuid = UUID.randomUUID();

    // cases
    // case 5 input 1
    final var case5input1 = List.of(
        new PurchasePurposeParameter(PurchasePurposeConditionName.BUYERGROUP, List.of("a")),
        new PurchasePurposeParameter(PurchasePurposeConditionName.SUPPLIER_PT, List.of("s2")));

    // case 6 input 1
    final var case6input1 = List.of(
        new PurchasePurposeParameter(PurchasePurposeConditionName.BUYERGROUP, List.of("a")),
        new PurchasePurposeParameter(PurchasePurposeConditionName.SUPPLIER_PT, List.of("s2", "s3")),
        new PurchasePurposeParameter(PurchasePurposeConditionName.FAMILY,
            List.of("1", "2", "3")));

    // case 6 input 2
    final var case6input2 = List.of(
        new PurchasePurposeParameter(PurchasePurposeConditionName.BUYERGROUP, List.of("b")),
        new PurchasePurposeParameter(PurchasePurposeConditionName.SUPPLIER_PT, List.of("s1")),
        new PurchasePurposeParameter(PurchasePurposeConditionName.FAMILY,
            List.of("2", "3")));

    // uses
    // u1a
    final Builder usePurchasePurposeConditionMotherBuilder = UsePurchasePurposeConditionMother.generateBuild();

    final var conditionsU1a = List
        .of(usePurchasePurposeConditionMotherBuilder.purchasePurposeCondition(PurchasePurposeCondition.EQUALS)
            .purchasePurposeConditionName(PurchasePurposeConditionName.BUYERGROUP)
            .purchasePurposeConditionValues(new PurchasePurposeConditionValues(
                List.of(new PurchasePurposeConditionValues.ConditionValue("a"))))
            .build());

    // u1b
    final var conditionsU1b = List
        .of(usePurchasePurposeConditionMotherBuilder.purchasePurposeCondition(PurchasePurposeCondition.EQUALS)
            .purchasePurposeConditionName(PurchasePurposeConditionName.BUYERGROUP)
            .purchasePurposeConditionValues(new PurchasePurposeConditionValues(
                List.of(new PurchasePurposeConditionValues.ConditionValue("b"))))
            .build());

    // u1aS1
    final var conditionsU1aS1 = List
        .of(usePurchasePurposeConditionMotherBuilder.purchasePurposeCondition(PurchasePurposeCondition.EQUALS)
            .purchasePurposeConditionName(PurchasePurposeConditionName.BUYERGROUP)
            .purchasePurposeConditionValues(new PurchasePurposeConditionValues(
                List.of(new PurchasePurposeConditionValues.ConditionValue("a"))))
            .build(),
            usePurchasePurposeConditionMotherBuilder.purchasePurposeCondition(PurchasePurposeCondition.EQUALS)
                .purchasePurposeConditionName(PurchasePurposeConditionName.SUPPLIER_PT)
                .purchasePurposeConditionValues(new PurchasePurposeConditionValues(
                    List.of(new PurchasePurposeConditionValues.ConditionValue("s1"))))
                .build());

    // u1aNoS1
    final var conditionsU1aNoS1 = List
        .of(usePurchasePurposeConditionMotherBuilder.purchasePurposeCondition(PurchasePurposeCondition.EQUALS)
            .purchasePurposeConditionName(PurchasePurposeConditionName.BUYERGROUP)
            .purchasePurposeConditionValues(new PurchasePurposeConditionValues(
                List.of(new PurchasePurposeConditionValues.ConditionValue("a"))))
            .build(),
            usePurchasePurposeConditionMotherBuilder.purchasePurposeCondition(PurchasePurposeCondition.NONE_IN)
                .purchasePurposeConditionName(PurchasePurposeConditionName.SUPPLIER_PT)
                .purchasePurposeConditionValues(new PurchasePurposeConditionValues(
                    List.of(new PurchasePurposeConditionValues.ConditionValue("s1"))))
                .build());
    // u1bF1
    final var conditionsU1bF1 = List.of(
        usePurchasePurposeConditionMotherBuilder.purchasePurposeCondition(PurchasePurposeCondition.EQUALS)
            .purchasePurposeConditionName(PurchasePurposeConditionName.BUYERGROUP)
            .purchasePurposeConditionValues(new PurchasePurposeConditionValues(
                List.of(new PurchasePurposeConditionValues.ConditionValue("b"))))
            .build(),
        usePurchasePurposeConditionMotherBuilder.purchasePurposeCondition(PurchasePurposeCondition.ANY_IN)
            .purchasePurposeConditionName(PurchasePurposeConditionName.FAMILY)
            .purchasePurposeConditionValues(new PurchasePurposeConditionValues(
                List.of(new PurchasePurposeConditionValues.ConditionValue("1"))))
            .build());

    // u1bF23
    final var conditionsU1bF23 = List.of(
        usePurchasePurposeConditionMotherBuilder.purchasePurposeCondition(PurchasePurposeCondition.EQUALS)
            .purchasePurposeConditionName(PurchasePurposeConditionName.BUYERGROUP)
            .purchasePurposeConditionValues(new PurchasePurposeConditionValues(
                List.of(new PurchasePurposeConditionValues.ConditionValue("b"))))
            .build(),
        usePurchasePurposeConditionMotherBuilder.purchasePurposeCondition(PurchasePurposeCondition.ALL_IN)
            .purchasePurposeConditionName(PurchasePurposeConditionName.FAMILY)
            .purchasePurposeConditionValues(new PurchasePurposeConditionValues(
                List.of(new PurchasePurposeConditionValues.ConditionValue("2"),
                    new PurchasePurposeConditionValues.ConditionValue("3"))))
            .build());
    // Expected results

    // case 5
    final var resultCase5u1a = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        0, List.of(PurchasePurposeConditionName.BUYERGROUP), 1);
    final var resultCase5u1aS1 = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        1, List.of(PurchasePurposeConditionName.BUYERGROUP), 1);
    final var resultCase5u1aNoS1 = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        0, List.of(PurchasePurposeConditionName.BUYERGROUP, PurchasePurposeConditionName.SUPPLIER_PT), 2);
    final var resultCase5u1b = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        1, List.of(), 0);
    final var resultCase5u1bF1 = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        1, List.of(), 0);
    final var resultCase5u1bF23 = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        1, List.of(), 0);

    // case 6 input 1
    final var resultCase6Input1U1a = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        0, List.of(PurchasePurposeConditionName.BUYERGROUP), 1);
    final var resultCase6Input1U1aS1 = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        1, List.of(PurchasePurposeConditionName.BUYERGROUP), 1);
    final var resultCase6Input1U1aNoS1 = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        0, List.of(PurchasePurposeConditionName.BUYERGROUP, PurchasePurposeConditionName.SUPPLIER_PT), 3);
    final var resultCase6Input1U1b = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        1, List.of(), 0);
    final var resultCase6Input1U1bF1 = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        1, List.of(PurchasePurposeConditionName.FAMILY), 1);
    final var resultCase6Input1U1bF23 = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        1, List.of(PurchasePurposeConditionName.FAMILY), 2);

    // case 6 input 2
    final var resultCase6Input2U1a = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        1, List.of(), 0);
    final var resultCase6Input2U1aS1 = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        1, List.of(PurchasePurposeConditionName.SUPPLIER_PT), 1);
    final var resultCase6Input2U1aNoS1 = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        2, List.of(), 0);
    final var resultCase6Input2U1b = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        0, List.of(PurchasePurposeConditionName.BUYERGROUP), 1);
    final var resultCase6Input2U1bF1 = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        1, List.of(PurchasePurposeConditionName.BUYERGROUP), 1);
    final var resultCase6Input2U1bF23 = new PurchasePurposeEvaluationResult(new Use.Id(uuid),
        0, List.of(PurchasePurposeConditionName.BUYERGROUP, PurchasePurposeConditionName.FAMILY), 3);

    return Stream.of(
        Arguments.of(conditionsU1a, case5input1, resultCase5u1a),
        Arguments.of(conditionsU1aS1, case5input1, resultCase5u1aS1),
        Arguments.of(conditionsU1aNoS1, case5input1, resultCase5u1aNoS1),
        Arguments.of(conditionsU1b, case5input1, resultCase5u1b),
        Arguments.of(conditionsU1bF1, case5input1, resultCase5u1bF1),
        Arguments.of(conditionsU1bF23, case5input1, resultCase5u1bF23),

        Arguments.of(conditionsU1a, case6input1, resultCase6Input1U1a),
        Arguments.of(conditionsU1aS1, case6input1, resultCase6Input1U1aS1),
        Arguments.of(conditionsU1aNoS1, case6input1, resultCase6Input1U1aNoS1),
        Arguments.of(conditionsU1b, case6input1, resultCase6Input1U1b),
        Arguments.of(conditionsU1bF1, case6input1, resultCase6Input1U1bF1),
        Arguments.of(conditionsU1bF23, case6input1, resultCase6Input1U1bF23),

        Arguments.of(conditionsU1a, case6input2, resultCase6Input2U1a),
        Arguments.of(conditionsU1aS1, case6input2, resultCase6Input2U1aS1),
        Arguments.of(conditionsU1aNoS1, case6input2, resultCase6Input2U1aNoS1),
        Arguments.of(conditionsU1b, case6input2, resultCase6Input2U1b),
        Arguments.of(conditionsU1bF1, case6input2, resultCase6Input2U1bF1),
        Arguments.of(conditionsU1bF23, case6input2, resultCase6Input2U1bF23));

  }
}
