package com.inditex.icdmdemg.application.distributionnominated.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.mockito.Mockito.doReturn;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.Set;

import com.inditex.icdmdemg.application.distributionnominated.command.PatchDistributionNominatedCommand.PatchNominatedRequest;
import com.inditex.icdmdemg.application.shared.validator.ConstraintValidatorConfiguration;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = {ConstraintValidatorConfiguration.class})
class PatchDistributionNominatedCommandTest {

  @MockitoBean
  private DistributionNominatedRepository nominatedRepository;

  @Autowired
  protected Validator validator;

  @Test
  void should_return_violations_when_quantities_not_positive() {
    final var patchNominatedRequest = Instancio.of(PatchNominatedRequest.class)
        .set(field("theoreticalQuantity"), BigDecimal.ZERO).set(field("requestedQuantity"), BigDecimal.ZERO)
        .set(field("consumptionFactor"), BigDecimal.ZERO).create();

    doReturn(Optional.of(Instancio.create(DistributionNominated.class))).when(this.nominatedRepository)
        .findById(new DistributionNominated.Id(patchNominatedRequest.distributionNominatedId()));

    final Set<ConstraintViolation<PatchNominatedRequest>> violations = this.validator
        .validate(patchNominatedRequest);

    assertThat(violations).hasSize(3);
  }

  @Test
  void should_return_violations_when_distribution_nominated_not_exist() {
    final var patchNominated = Instancio.create(PatchNominatedRequest.class);
    doReturn(Optional.empty()).when(this.nominatedRepository)
        .findById(new DistributionNominated.Id(patchNominated.distributionNominatedId()));

    final Set<ConstraintViolation<PatchNominatedRequest>> violations = this.validator.validate(patchNominated);

    assertThat(violations).hasSize(1);
  }

  @Test
  void should_return_empty_violations_when_distribution_nominated_exists() {
    final var patchNominatedRequest = Instancio.create(PatchNominatedRequest.class);

    doReturn(Optional.of(Instancio.create(DistributionNominated.class))).when(this.nominatedRepository)
        .findById(new DistributionNominated.Id(patchNominatedRequest.distributionNominatedId()));

    final Set<ConstraintViolation<PatchNominatedRequest>> violations = this.validator
        .validate(patchNominatedRequest);

    assertThat(violations).isEmpty();
  }
}
