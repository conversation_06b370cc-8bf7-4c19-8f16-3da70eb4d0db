package com.inditex.icdmdemg.application.distributionnominated.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.catchThrowable;
import static org.assertj.core.api.Assertions.tuple;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedDistributed;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributionStartDate;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.Id;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedStatus;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedLineMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderStatusKey;
import com.inditex.icdmdemg.domain.order.mother.OrderMother;
import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitmentRepository;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentDistributionNominatedLineId;
import com.inditex.icdmdemg.domain.shipmentcommitment.mother.ShipmentCommitmentMother;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.mockito.ArgumentCaptor;

@TestInstance(Lifecycle.PER_CLASS)
class UpdateDistributionNominatedFromShipmentCommandHandlerTest {
  private static final String TRIGGERED_BY = "Shipment Updated Event";

  private DistributionNominatedRepository distributionNominatedRepository;

  private ShipmentCommitmentRepository shipmentCommitmentRepository;

  private OrderRepository orderRepository;

  private ClockUtils clockUtils;

  private EventBus eventBus;

  private UpdateDistributionNominatedFromShipmentCommandHandler sut;

  @BeforeEach
  void setup() {
    final var transaction = new Transaction();
    this.distributionNominatedRepository = mock(DistributionNominatedRepository.class);
    this.shipmentCommitmentRepository = mock(ShipmentCommitmentRepository.class);
    this.orderRepository = mock(OrderRepository.class);
    this.clockUtils = mock(ClockUtils.class);
    this.eventBus = mock(EventBus.class);
    this.sut = new UpdateDistributionNominatedFromShipmentCommandHandler(this.distributionNominatedRepository,
        this.shipmentCommitmentRepository, this.orderRepository, transaction, this.clockUtils, this.eventBus);
  }

  @Test
  void should_save_nominated_distribution_when_one_shipment() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedLineId = UUID.randomUUID();
    final var id = new Id(distributionNominatedLineId);
    final var quantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(5000));
    final var distributionNominatedIdWithDateAndQuantity =
        new DistributionNominatedDistributed(distributionNominatedLineId.toString(), TRIGGERED_BY);
    final var distributionNominatedLine =
        DistributionNominatedLineMother.createdWithIdAndDistributionStartDate(id, new DistributionStartDate(now)).build();
    final var orderId = UuidMother.fromInteger(101);
    final var order = OrderMother.randomWith(OrderStatusKey.DRAFT, orderId, false);
    final var distributionNominatedInput = DistributionNominatedMother.pendingWithLines(List.of(distributionNominatedLine))
        .productOrderId(new ProductOrderId(orderId))
        .build();
    final var shipmentCommitmentStored = ShipmentCommitmentMother.with(UUID.randomUUID(), distributionNominatedLineId, now,
        now, quantity, UUID.randomUUID());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository.findByLineId(id)).thenReturn(Optional.of(distributionNominatedInput));
    when(this.orderRepository.find(order.getId())).thenReturn(Optional.of(order));
    when(this.shipmentCommitmentRepository.findByDistributionNominatedLineId(ShipmentCommitmentDistributionNominatedLineId.of(id.value())))
        .thenReturn(List.of(shipmentCommitmentStored));

    this.sut.execute(new UpdateDistributionNominatedFromShipmentCommand(distributionNominatedIdWithDateAndQuantity));

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor.forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    verify(this.eventBus).send(expectedEvent(nominatedCaptor));

    assertThat(nominatedCaptor.getValue().distributedQuantity().value()).isEqualTo(quantity);
    assertThat(nominatedCaptor.getValue().status()).isEqualTo(DistributionNominatedStatus.IN_PROGRESS);
    final DistributionNominated distributionNominated = nominatedCaptor.getValue();
    assertThat(distributionNominated.lines().value())
        .hasSize(1)
        .first()
        .returns(new DistributedQuantity(quantity), DistributionNominatedLine::distributedQuantity)
        .returns(new DistributionStartDate(now), DistributionNominatedLine::distributionStartDate)
        .returns(distributionNominatedLine.audit().update(now), DistributionNominatedLine::audit);
  }

  @Test
  void should_save_nominated_distribution_when_modify_shipment_existent() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedLineId = UUID.randomUUID();
    final var id = new Id(distributionNominatedLineId);
    final var quantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(5000));
    final var distributionNominatedIdWithDateAndQuantity =
        new DistributionNominatedDistributed(distributionNominatedLineId.toString(), TRIGGERED_BY);
    final var distributionNominatedLine =
        DistributionNominatedLineMother.createdWithIdAndDistributionStartDate(id, new DistributionStartDate(now)).build();
    final var orderId = UuidMother.fromInteger(101);
    final var order = OrderMother.randomWith(OrderStatusKey.DRAFT, orderId, false);
    final var distributionNominatedInput = DistributionNominatedMother.inProgressWithLines(List.of(distributionNominatedLine))
        .productOrderId(new ProductOrderId(orderId))
        .build();
    final var shipmentCommitmentStored = ShipmentCommitmentMother.with(UUID.randomUUID(), distributionNominatedLineId, now,
        now, quantity, UUID.randomUUID());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository.findByLineId(id)).thenReturn(Optional.of(distributionNominatedInput));
    when(this.orderRepository.find(order.getId())).thenReturn(Optional.of(order));
    when(this.shipmentCommitmentRepository.findByDistributionNominatedLineId(ShipmentCommitmentDistributionNominatedLineId.of(id.value())))
        .thenReturn(List.of(shipmentCommitmentStored));

    this.sut.execute(new UpdateDistributionNominatedFromShipmentCommand(distributionNominatedIdWithDateAndQuantity));

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor.forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    verify(this.eventBus).send(expectedEvent(nominatedCaptor));

    assertThat(nominatedCaptor.getValue().distributedQuantity().value()).isEqualTo(quantity);
    assertThat(nominatedCaptor.getValue().status()).isEqualTo(DistributionNominatedStatus.IN_PROGRESS);
    final DistributionNominated distributionNominated = nominatedCaptor.getValue();
    assertThat(distributionNominated.lines().value())
        .hasSize(1)
        .first()
        .returns(new DistributedQuantity(quantity), DistributionNominatedLine::distributedQuantity)
        .returns(new DistributionStartDate(now), DistributionNominatedLine::distributionStartDate)
        .returns(distributionNominatedLine.audit().update(now), DistributionNominatedLine::audit);
  }

  @Test
  void should_save_nominated_distribution_one_line_with_non_distributable_previous_state_when_one_shipment_cancelled() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedLineId = UUID.randomUUID();
    final var id = new Id(distributionNominatedLineId);
    final var distributionNominatedIdWithDateAndQuantity =
        new DistributionNominatedDistributed(distributionNominatedLineId.toString(), TRIGGERED_BY);
    final var distributionNominatedLine =
        DistributionNominatedLineMother.createdWithIdAndDistributionStartDate(id, new DistributionStartDate(now)).build();
    final var orderId = UuidMother.fromInteger(101);
    final var order = OrderMother.randomWith(OrderStatusKey.DRAFT, orderId, false);
    final var distributionNominatedInput = DistributionNominatedMother.inProgressWithLines(List.of(distributionNominatedLine))
        .productOrderId(new ProductOrderId(orderId))
        .build();
    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository.findByLineId(id)).thenReturn(Optional.of(distributionNominatedInput));
    when(this.orderRepository.find(order.getId())).thenReturn(Optional.of(order));
    when(this.shipmentCommitmentRepository.findByDistributionNominatedLineId(ShipmentCommitmentDistributionNominatedLineId.of(id.value())))
        .thenReturn(List.of());

    this.sut.execute(new UpdateDistributionNominatedFromShipmentCommand(distributionNominatedIdWithDateAndQuantity));

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor.forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    verify(this.eventBus).send(expectedEvent(nominatedCaptor));

    assertThat(nominatedCaptor.getValue().distributedQuantity().value()).isEqualTo(NumericUtils.roundUpScale2(BigDecimal.ZERO));
    assertThat(nominatedCaptor.getValue().status()).isEqualTo(DistributionNominatedStatus.NON_DISTRIBUTABLE);
    final DistributionNominated distributionNominated = nominatedCaptor.getValue();
    assertThat(distributionNominated.lines().value())
        .hasSize(1)
        .first()
        .returns(new DistributedQuantity(BigDecimal.ZERO), DistributionNominatedLine::distributedQuantity)
        .returns(new DistributionStartDate(null), DistributionNominatedLine::distributionStartDate)
        .returns(distributionNominatedLine.audit().update(now), DistributionNominatedLine::audit);
  }

  @Test
  void should_save_nominated_distribution_one_line_with_pending_previous_state_when_one_shipment_cancelled() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedLineId = UUID.randomUUID();
    final var id = new Id(distributionNominatedLineId);
    final var distributionNominatedIdWithDateAndQuantity =
        new DistributionNominatedDistributed(distributionNominatedLineId.toString(), TRIGGERED_BY);
    final var distributionNominatedLine =
        DistributionNominatedLineMother.createdWithIdAndDistributionStartDate(id, new DistributionStartDate(now)).build();
    final var orderId = UuidMother.fromInteger(101);
    final var order = OrderMother.randomWith(OrderStatusKey.FORMALIZED, orderId, true);
    final var distributionNominatedInput = DistributionNominatedMother.inProgressWithLines(List.of(distributionNominatedLine))
        .productOrderId(new ProductOrderId(orderId))
        .build();
    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository.findByLineId(id)).thenReturn(Optional.of(distributionNominatedInput));
    when(this.orderRepository.find(order.getId())).thenReturn(Optional.of(order));
    when(this.shipmentCommitmentRepository.findByDistributionNominatedLineId(ShipmentCommitmentDistributionNominatedLineId.of(id.value())))
        .thenReturn(List.of());

    this.sut.execute(new UpdateDistributionNominatedFromShipmentCommand(distributionNominatedIdWithDateAndQuantity));

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor.forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    verify(this.eventBus).send(expectedEvent(nominatedCaptor));

    assertThat(nominatedCaptor.getValue().distributedQuantity().value()).isEqualTo(NumericUtils.roundUpScale2(BigDecimal.ZERO));
    assertThat(nominatedCaptor.getValue().status()).isEqualTo(DistributionNominatedStatus.PENDING);
    final DistributionNominated distributionNominated = nominatedCaptor.getValue();
    assertThat(distributionNominated.lines().value())
        .hasSize(1)
        .first()
        .returns(new DistributedQuantity(BigDecimal.ZERO), DistributionNominatedLine::distributedQuantity)
        .returns(new DistributionStartDate(null), DistributionNominatedLine::distributionStartDate)
        .returns(distributionNominatedLine.audit().update(now), DistributionNominatedLine::audit);
  }

  @Test
  void should_save_nominated_distribution_with_several_lines_when_one_shipment_cancelled() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedLineId = UUID.randomUUID();
    final var id = new Id(distributionNominatedLineId);
    final var quantity2 = NumericUtils.roundUpScale2(BigDecimal.valueOf(2000));
    final var quantity3 = NumericUtils.roundUpScale2(BigDecimal.valueOf(3000));
    final var totalQuantityNominated = NumericUtils.roundUpScale2(BigDecimal.valueOf(5000));
    final var distributionNominatedIdWithDateAndQuantity =
        new DistributionNominatedDistributed(distributionNominatedLineId.toString(), TRIGGERED_BY);
    final var distributionNominatedLine2 =
        DistributionNominatedLineMother.createdWithIdAndDistributionStartDate(new Id(UUID.randomUUID()), new DistributionStartDate(now))
            .distributedQuantity(new DistributedQuantity(quantity2)).build();
    final var distributionNominatedLine3 =
        DistributionNominatedLineMother.createdWithIdAndDistributionStartDate(new Id(UUID.randomUUID()), new DistributionStartDate(now))
            .distributedQuantity(new DistributedQuantity(quantity3)).build();
    final var orderId = UuidMother.fromInteger(101);
    final var order = OrderMother.randomWith(OrderStatusKey.DRAFT, orderId, false);
    final var distributionNominatedInput = DistributionNominatedMother.inProgressWithLines(List.of(
        distributionNominatedLine2, distributionNominatedLine3))
        .productOrderId(new ProductOrderId(orderId))
        .build();

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository.findByLineId(id)).thenReturn(Optional.of(distributionNominatedInput));
    when(this.orderRepository.find(order.getId())).thenReturn(Optional.of(order));
    when(this.shipmentCommitmentRepository.findByDistributionNominatedLineId(ShipmentCommitmentDistributionNominatedLineId.of(id.value())))
        .thenReturn(List.of());

    this.sut.execute(new UpdateDistributionNominatedFromShipmentCommand(distributionNominatedIdWithDateAndQuantity));

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor.forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    verify(this.eventBus).send(expectedEvent(nominatedCaptor));

    assertThat(nominatedCaptor.getValue().distributedQuantity().value()).isEqualTo(totalQuantityNominated);
    final DistributionNominated distributionNominated1 = nominatedCaptor.getValue();
    assertThat(distributionNominated1.lines().value()).hasSize(2);
    assertThat(nominatedCaptor.getValue().status()).isEqualTo(DistributionNominatedStatus.IN_PROGRESS);
    final DistributionNominated distributionNominated = nominatedCaptor.getValue();
    final var lines =
        distributionNominated.lines().value().stream().filter(lineFound -> !lineFound.id().equals(id)).toList();
    assertThat(lines)
        .extracting(
            DistributionNominatedLine::distributedQuantity,
            DistributionNominatedLine::distributionStartDate)
        .contains(
            tuple(
                new DistributedQuantity(quantity3),
                new DistributionStartDate(now)),
            tuple(
                new DistributedQuantity(quantity2),
                new DistributionStartDate(now)));
  }

  @Test
  void should_save_nominated_distribution_with_one_line_when_several_shipments() {
    final var now = OffsetDateTime.now();
    final var nowMinus10Days = OffsetDateTime.now().minusDays(10);
    final var nowMinus5Days = OffsetDateTime.now().minusDays(5);
    final var distributionNominatedLineId = UUID.randomUUID();
    final var id = new Id(distributionNominatedLineId);
    final var quantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(10000));
    final var totalQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(60000));
    final var distributionNominatedIdWithDateAndQuantity =
        new DistributionNominatedDistributed(distributionNominatedLineId.toString(), TRIGGERED_BY);
    final var distributionNominatedLine =
        DistributionNominatedLineMother.createdWithIdAndDistributionStartDate(id, new DistributionStartDate(now)).build();

    final var orderId = UuidMother.fromInteger(101);
    final var order = OrderMother.randomWith(OrderStatusKey.DRAFT, orderId, false);
    final var distributionNominatedInput = DistributionNominatedMother.pendingWithLines(List.of(distributionNominatedLine))
        .productOrderId(new ProductOrderId(orderId))
        .build();
    final var shipmentCommitmentStored = ShipmentCommitmentMother.with(UUID.randomUUID(), distributionNominatedLineId, nowMinus10Days,
        now, quantity, UUID.randomUUID());
    final var shipmentCommitmentStored2 = ShipmentCommitmentMother.with(UUID.randomUUID(), distributionNominatedLineId, nowMinus5Days,
        now, quantity.add(quantity), UUID.randomUUID());
    final var shipmentCommitmentStored3 = ShipmentCommitmentMother.with(UUID.randomUUID(), distributionNominatedLineId, now,
        now, quantity.add(quantity).add(quantity), UUID.randomUUID());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository.findByLineId(id)).thenReturn(Optional.of(distributionNominatedInput));
    when(this.orderRepository.find(order.getId())).thenReturn(Optional.of(order));
    when(this.shipmentCommitmentRepository.findByDistributionNominatedLineId(ShipmentCommitmentDistributionNominatedLineId.of(id.value())))
        .thenReturn(List.of(shipmentCommitmentStored, shipmentCommitmentStored2, shipmentCommitmentStored3));

    this.sut.execute(new UpdateDistributionNominatedFromShipmentCommand(distributionNominatedIdWithDateAndQuantity));

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor.forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    verify(this.eventBus).send(expectedEvent(nominatedCaptor));

    assertThat(nominatedCaptor.getValue().distributedQuantity().value()).isEqualTo(totalQuantity);
    assertThat(nominatedCaptor.getValue().status()).isEqualTo(DistributionNominatedStatus.IN_PROGRESS);
    final DistributionNominated distributionNominated = nominatedCaptor.getValue();
    assertThat(distributionNominated.lines().value())
        .hasSize(1)
        .first()
        .returns(new DistributedQuantity(totalQuantity), DistributionNominatedLine::distributedQuantity)
        .returns(new DistributionStartDate(nowMinus10Days), DistributionNominatedLine::distributionStartDate)
        .returns(distributionNominatedLine.audit().update(now), DistributionNominatedLine::audit);
  }

  @Test
  void should_save_nominated_distribution_with_several_lines_when_one_shipment() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedLineId = UUID.randomUUID();
    final var id = new Id(distributionNominatedLineId);
    final var quantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(10000));
    final var quantity2 = NumericUtils.roundUpScale2(BigDecimal.valueOf(5000));
    final var quantity3 = NumericUtils.roundUpScale2(BigDecimal.valueOf(7000));
    final var totalQuantityNominated = NumericUtils.roundUpScale2(BigDecimal.valueOf(22000));

    final var orderId = UuidMother.fromInteger(101);
    final var order = OrderMother.randomWith(OrderStatusKey.DRAFT, orderId, false);
    final var distributionNominatedIdWithDateAndQuantity =
        new DistributionNominatedDistributed(distributionNominatedLineId.toString(), TRIGGERED_BY);
    final var distributionNominatedLine =
        DistributionNominatedLineMother.createdWithIdAndDistributionStartDate(id, new DistributionStartDate(now))
            .distributedQuantity(new DistributedQuantity(quantity)).build();
    final var distributionNominatedLine2 =
        DistributionNominatedLineMother.createdWithIdAndDistributionStartDate(new Id(UUID.randomUUID()), new DistributionStartDate(now))
            .distributedQuantity(new DistributedQuantity(quantity2)).build();
    final var distributionNominatedLine3 =
        DistributionNominatedLineMother.createdWithIdAndDistributionStartDate(new Id(UUID.randomUUID()), new DistributionStartDate(now))
            .distributedQuantity(new DistributedQuantity(quantity3)).build();
    final var distributionNominatedInput = DistributionNominatedMother.inProgressWithLines(List.of(distributionNominatedLine,
        distributionNominatedLine2, distributionNominatedLine3))
        .productOrderId(new ProductOrderId(orderId))
        .build();
    final var shipmentCommitmentStored = ShipmentCommitmentMother.with(UUID.randomUUID(), distributionNominatedLineId, now,
        now, quantity, UUID.randomUUID());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository.findByLineId(id)).thenReturn(Optional.of(distributionNominatedInput));
    when(this.orderRepository.find(order.getId())).thenReturn(Optional.of(order));
    when(this.shipmentCommitmentRepository.findByDistributionNominatedLineId(ShipmentCommitmentDistributionNominatedLineId.of(id.value())))
        .thenReturn(List.of(shipmentCommitmentStored));

    this.sut.execute(new UpdateDistributionNominatedFromShipmentCommand(distributionNominatedIdWithDateAndQuantity));

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor.forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    verify(this.eventBus).send(expectedEvent(nominatedCaptor));

    assertThat(nominatedCaptor.getValue().distributedQuantity().value()).isEqualTo(totalQuantityNominated);
    final DistributionNominated distributionNominated1 = nominatedCaptor.getValue();
    assertThat(distributionNominated1.lines().value()).hasSize(3);
    assertThat(nominatedCaptor.getValue().status()).isEqualTo(DistributionNominatedStatus.IN_PROGRESS);
    final DistributionNominated distributionNominated = nominatedCaptor.getValue();
    final var line =
        distributionNominated.lines().value().stream().filter(lineFound -> lineFound.id().equals(id)).findFirst().orElse(null);
    assertThat(line)
        .returns(new DistributedQuantity(quantity), DistributionNominatedLine::distributedQuantity)
        .returns(new DistributionStartDate(now), DistributionNominatedLine::distributionStartDate)
        .returns(distributionNominatedLine.audit().update(now), DistributionNominatedLine::audit);
  }

  @Test
  void should_throw_error_when_nominated_distribution_not_found() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedLineId = UUID.randomUUID();
    final var id = new Id(distributionNominatedLineId);
    final var distributionNominatedIdWithDateAndQuantity =
        new DistributionNominatedDistributed(distributionNominatedLineId.toString(), TRIGGERED_BY);

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository.findByLineId(id))
        .thenReturn(Optional.empty());

    final Throwable result = catchThrowable(
        () -> this.sut.execute(new UpdateDistributionNominatedFromShipmentCommand(distributionNominatedIdWithDateAndQuantity)));

    verify(this.distributionNominatedRepository, times(0)).save(any());
    verifyNoInteractions(this.eventBus);

    assertThat(result)
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining(String.format("Distribution Nominated with lineId %s not found", distributionNominatedLineId));
  }

  @Test
  void should_throw_error_when_order_not_found() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedLineId = UUID.randomUUID();
    final var id = new Id(distributionNominatedLineId);
    final var distributionNominatedIdWithDateAndQuantity =
        new DistributionNominatedDistributed(distributionNominatedLineId.toString(), TRIGGERED_BY);
    final var distributionNominatedLine =
        DistributionNominatedLineMother.createdWithIdAndDistributionStartDate(id, new DistributionStartDate(now)).build();
    final var distributionNominatedInput = DistributionNominatedMother.pendingWithLines(List.of(distributionNominatedLine))
        .build();

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository.findByLineId(id)).thenReturn(Optional.of(distributionNominatedInput));
    when(this.orderRepository.find(any())).thenReturn(Optional.empty());

    final Throwable result = catchThrowable(
        () -> this.sut.execute(new UpdateDistributionNominatedFromShipmentCommand(distributionNominatedIdWithDateAndQuantity)));

    verify(this.distributionNominatedRepository, times(0)).save(any());
    verifyNoInteractions(this.eventBus);

    assertThat(result)
        .isInstanceOf(IllegalArgumentException.class)
        .hasMessageContaining(String.format("Order with id %s not found", distributionNominatedInput.productOrderId().value().toString()));
  }

  private static List<DomainEvent<?>> expectedEvent(final ArgumentCaptor<DistributionNominated> nominatedCaptor) {
    return List.of(new DistributionNominatedUnifiedEvent(nominatedCaptor.getValue(), EventType.DISTRIBUTED));
  }

}
