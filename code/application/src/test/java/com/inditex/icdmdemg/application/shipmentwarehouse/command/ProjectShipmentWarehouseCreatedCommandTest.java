package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import com.inditex.icdmdemg.application.shared.validator.ConstraintValidatorConfiguration;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCreatedCommand.ShipmentWarehouseCreated;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerLineMother;
import com.inditex.icdmdemg.domain.distributioninner.mother.DistributionInnerMother;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = {ConstraintValidatorConfiguration.class})
class ProjectShipmentWarehouseCreatedCommandTest {
  @MockitoBean
  private DistributionInnerRepository distributionInnerRepository;

  @Autowired
  protected Validator validator;

  @Test
  void should_return_violations_with_null_ids_values() {
    final var object = new ShipmentWarehouseCreated(null, null, null);

    final Set<ConstraintViolation<ShipmentWarehouseCreated>> violations = this.validator.validate(object);

    assertThat(violations).hasSize(4);
  }

  @Test
  void should_return_violations_when_di_does_not_exist() {
    final var object = Instancio.create(ShipmentWarehouseCreated.class);
    doReturn(Optional.empty()).when(this.distributionInnerRepository)
        .findById(new Id(object.distributionInnerId()));

    final Set<ConstraintViolation<ShipmentWarehouseCreated>> violations = this.validator.validate(object);

    assertThat(violations).hasSize(1);
  }

  @Test
  void should_return_violations_when_diline_does_not_exist_in_di() {
    final var object = Instancio.create(ShipmentWarehouseCreated.class);
    final var di = DistributionInnerMother.pending()
        .lines(List.of(DistributionInnerLineMother.created().build())).build();
    doReturn(Optional.of(di)).when(this.distributionInnerRepository)
        .findById(new Id(object.distributionInnerId()));

    final Set<ConstraintViolation<ShipmentWarehouseCreated>> violations = this.validator.validate(object);

    assertThat(violations).hasSize(1);
  }

  @Test
  void should_return_violations_when_diline_exists_but_is_distributed() {
    final var object = Instancio.create(ShipmentWarehouseCreated.class);
    final var lineId = new DistributionInnerLine.Id(object.distributionInnerLineId());
    final var di = DistributionInnerMother.pending()
        .lines(List.of(DistributionInnerLineMother.distributed().id(lineId).build())).build();
    doReturn(Optional.of(di)).when(this.distributionInnerRepository)
        .findById(new Id(object.distributionInnerId()));

    final Set<ConstraintViolation<ShipmentWarehouseCreated>> violations = this.validator.validate(object);

    assertThat(violations).hasSize(1);
  }

}
