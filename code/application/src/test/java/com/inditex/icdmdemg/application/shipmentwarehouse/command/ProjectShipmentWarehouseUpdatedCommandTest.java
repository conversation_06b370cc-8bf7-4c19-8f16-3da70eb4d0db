package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verifyNoInteractions;

import java.util.Optional;
import java.util.Set;

import com.inditex.icdmdemg.application.shared.validator.ConstraintValidatorConfiguration;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseUpdatedCommand.ShipmentWarehouseUpdated;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouseRepository;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTrackingCode;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.bean.override.mockito.MockitoBean;

@TestInstance(Lifecycle.PER_CLASS)
@SpringBootTest(classes = {ConstraintValidatorConfiguration.class})
class ProjectShipmentWarehouseUpdatedCommandTest {
  @MockitoBean
  private ShipmentWarehouseRepository shipmentWarehouseRepository;

  @Autowired
  protected Validator validator;

  @Test
  void should_return_violations_with_null_values() {
    final var object = new ShipmentWarehouseUpdated(null, null, null);

    final Set<ConstraintViolation<ShipmentWarehouseUpdated>> violations = this.validator.validate(object);

    assertThat(violations).hasSize(3);
    verifyNoInteractions(this.shipmentWarehouseRepository);
  }

  @Test
  void should_return_violations_when_tracking_code_not_exist() {
    final var object = Instancio.create(ShipmentWarehouseUpdated.class);
    doReturn(Optional.empty()).when(this.shipmentWarehouseRepository)
        .findByTrackingCode(new ShipmentWarehouseTrackingCode(object.distributionTrackingCode()));

    final Set<ConstraintViolation<ShipmentWarehouseUpdated>> violations = this.validator.validate(object);

    assertThat(violations).hasSize(1);
  }

  @Test
  void should_return_empty_violations_when_tracking_code_exists() {
    final var object = Instancio.create(ShipmentWarehouseUpdated.class);
    doReturn(Optional.of(object)).when(this.shipmentWarehouseRepository)
        .findByTrackingCode(new ShipmentWarehouseTrackingCode(object.distributionTrackingCode()));

    final Set<ConstraintViolation<ShipmentWarehouseUpdated>> violations = this.validator.validate(object);

    assertThat(violations).isEmpty();
  }

}
