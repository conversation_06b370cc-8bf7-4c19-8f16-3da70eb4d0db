package com.inditex.icdmdemg.application.distributionnominated.command;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionnominated.command.PatchDistributionNominatedCommand.PatchNominatedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.AutoRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.PreselectedRequest;
import com.inditex.icdmdemg.application.distributionnominated.service.AlternativeSharedRawMaterialProvider;
import com.inditex.icdmdemg.application.distributionnominated.service.DistributionNominatedLinesIdentifier;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjustedProvider;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycleChangePendingQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedPlan;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedStatus;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedLineMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.lock.Lock;
import com.inditex.icdmdemg.domain.lock.LockRepository;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import org.instancio.Instancio;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.mockito.ArgumentCaptor;

@TestInstance(Lifecycle.PER_CLASS)
class PatchDistributionNominatedCommandHandlerTest {

  private ClockUtils clockUtils;

  private DistributionNominatedRepository distributionNominatedRepository;

  private LockRepository lockRepository;

  private CommitmentAdjustedProvider commitmentAdjustedProvider;

  private AlternativeSharedRawMaterialProvider alternativeSharedRawMaterialProvider;

  private DistributionNominatedLinesIdentifier linesIdentifier;

  private EventBus eventBus;

  PatchDistributionNominatedCommandHandler sut;

  @BeforeEach
  void setup() {
    final var transaction = new Transaction();
    this.clockUtils = mock(ClockUtils.class);
    this.distributionNominatedRepository = mock(DistributionNominatedRepository.class);
    this.lockRepository = mock(LockRepository.class);
    this.commitmentAdjustedProvider = mock(CommitmentAdjustedProvider.class);
    this.alternativeSharedRawMaterialProvider = mock(AlternativeSharedRawMaterialProvider.class);
    this.linesIdentifier = mock(DistributionNominatedLinesIdentifier.class);
    this.eventBus = mock(EventBus.class);
    this.sut = new PatchDistributionNominatedCommandHandler(transaction, this.clockUtils,
        this.distributionNominatedRepository, this.lockRepository, this.commitmentAdjustedProvider,
        this.alternativeSharedRawMaterialProvider, this.linesIdentifier, this.eventBus);
  }

  @AfterEach
  void resetMocks() {
    reset(this.clockUtils, this.distributionNominatedRepository, this.lockRepository,
        this.commitmentAdjustedProvider, this.linesIdentifier, this.eventBus);
  }

  @Test
  void should_throw_exception_when_empty_find_by_id() {
    final var command = Instancio.create(PatchDistributionNominatedCommand.class);
    when(this.distributionNominatedRepository
        .findById(new Id(command.patchNominatedRequest().distributionNominatedId())))
            .thenReturn(Optional.empty());

    assertThatThrownBy(() -> this.sut.execute(command)).isInstanceOf(ErrorException.class);
    verify(this.distributionNominatedRepository, never()).save(any());
  }

  @Test
  void should_not_update_when_no_modification() {
    final var now = OffsetDateTime.now();
    final var builder = DistributionNominatedMother
        .inProgressWithLines(List.of(DistributionNominatedLineMother.distributed().build()))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO));
    final var distributionNominated = builder.plan(DistributionNominatedPlan.AUTO).build();

    final var request = new PatchNominatedRequest(distributionNominated.getId().value(),
        distributionNominated.theoreticalQuantity().value(), distributionNominated.requestedQuantity().value(),
        distributionNominated.consumptionFactor().value(), "test");

    final var command = new PatchDistributionNominatedCommand(request, new AutoRequest());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    doReturn(Optional.of(distributionNominated)).when(this.distributionNominatedRepository)
        .findById(distributionNominated.getId());
    doReturn(List.of(distributionNominated)).when(this.distributionNominatedRepository)
        .findBySharedRawMaterial(distributionNominated.sharedRawMaterial());
    doReturn(distributionNominated.lines().value()).when(this.linesIdentifier).identifyUpdatedLines(
        eq(distributionNominated), eq(distributionNominated.requestedQuantity()),
        eq(distributionNominated.theoreticalQuantity()), anyList(), eq(now));

    this.sut.execute(command);
    verify(this.distributionNominatedRepository).save(distributionNominated);
    verify(this.lockRepository).lock(Lock.from(distributionNominated.sharedRawMaterial()));
    verify(this.eventBus).send(List.of());
  }

  @Test
  void should_update_consumptionFactor() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());

    final var requestedQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(2000));
    final var theoreticalQuantity = NumericUtils.roundUpScale2(BigDecimal.ONE);
    final var consumptionFactor = NumericUtils.roundUpScale2(BigDecimal.valueOf(500));

    final var payloadRequested = new DistributionNominated.RequestedQuantity(requestedQuantity);
    final var payloadTheoretical = new DistributionNominated.TheoreticalQuantity(theoreticalQuantity);

    final var request = new PatchNominatedRequest(distributionNominatedId, theoreticalQuantity, requestedQuantity,
        consumptionFactor, "test");

    final var distributionNominated = DistributionNominatedMother
        .pendingWithIdAndSharedRawMaterialAndLinesAndQuantities(distributionNominatedId,
            new SharedRawMaterialNominated(referenceId, useId, budgetCycle), requestedQuantity,
            theoreticalQuantity, BigDecimal.ONE,
            new ArrayList<>(List
                .of(new DistributionNominatedLine(new DistributionNominatedLine.Id(UUID.randomUUID()),
                    new CommitmentOrder(new CommitmentOrder.Id(orderId), new LineId(orderLineId),
                        new SupplierId(commitmentOrderSupplierId)),
                    new TheoreticalQuantity(BigDecimal.ONE),
                    new RequestedQuantity(BigDecimal.valueOf(2000)),
                    new DistributedQuantity(BigDecimal.ZERO), null, null,
                    new BasicAudit(now.plusDays(20), now.plusDays(20))))))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .referenceId(new ReferenceId(referenceId)).build();

    final var materialCommitmentUseSharedRawMaterial = MaterialCommitmentUseSharedRawMaterial
        .of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);

    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(orderId, orderLineId,
        budgetId, referenceId, useId, BigDecimal.valueOf(10000), commitmentOrderSupplierId);

    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, BigDecimal.valueOf(9000)));

    final var command = new PatchDistributionNominatedCommand(request, new AutoRequest());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository
        .findById(new Id(command.patchNominatedRequest().distributionNominatedId())))
            .thenReturn(Optional.of(distributionNominated));
    when(this.distributionNominatedRepository.findBySharedRawMaterial(distributionNominated.sharedRawMaterial()))
        .thenReturn(List.of(distributionNominated));
    when(this.commitmentAdjustedProvider
        .findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
            .thenReturn(partialQuantities);
    when(this.linesIdentifier.identifyUpdatedLines(eq(distributionNominated), eq(payloadRequested),
        eq(payloadTheoretical), anyList(), eq(now))).thenReturn(distributionNominated.lines().value());

    final var result = this.sut.execute(command);

    assertThat(result).isNotNull();
    assertThat(result.requestedQuantity().value()).isEqualTo(requestedQuantity);
    assertThat(result.consumptionFactor().value()).isEqualTo(consumptionFactor);
    assertThat(result.theoreticalQuantity().value()).isEqualTo(theoreticalQuantity);

    assertThat(result.lines().value()).hasSize(1);
    assertThat(result.lines().value()).first().returns(new RequestedQuantity(BigDecimal.valueOf(2000)),
        DistributionNominatedLine::requestedQuantity);

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor
        .forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    verify(this.lockRepository).lock(Lock.from(distributionNominated.sharedRawMaterial()));
    verify(this.eventBus).send(expectedEvent(nominatedCaptor, EventType.CORRECTED));
  }

  @Test
  void should_update_theoretical() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());

    final var requestedQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(2000));
    final var theoreticalQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(500));
    final var consumptionFactor = NumericUtils.roundUpScale2(BigDecimal.ONE);

    final var payloadRequested = new DistributionNominated.RequestedQuantity(requestedQuantity);
    final var payloadTheoretical = new DistributionNominated.TheoreticalQuantity(theoreticalQuantity);

    final var request = new PatchNominatedRequest(distributionNominatedId, theoreticalQuantity, requestedQuantity,
        consumptionFactor, "test");

    final var distributionNominated = DistributionNominatedMother
        .pendingWithIdAndSharedRawMaterialAndLinesAndQuantities(distributionNominatedId,
            new SharedRawMaterialNominated(referenceId, useId, budgetCycle), requestedQuantity,
            BigDecimal.ONE, consumptionFactor,
            new ArrayList<>(List
                .of(new DistributionNominatedLine(new DistributionNominatedLine.Id(UUID.randomUUID()),
                    new CommitmentOrder(new CommitmentOrder.Id(orderId), new LineId(orderLineId),
                        new SupplierId(commitmentOrderSupplierId)),
                    new TheoreticalQuantity(BigDecimal.ONE),
                    new RequestedQuantity(BigDecimal.valueOf(2000)),
                    new DistributedQuantity(BigDecimal.ZERO), null, null,
                    new BasicAudit(now.plusDays(20), now.plusDays(20))))))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .referenceId(new ReferenceId(referenceId)).build();

    final var materialCommitmentUseSharedRawMaterial = MaterialCommitmentUseSharedRawMaterial
        .of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);

    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(orderId, orderLineId,
        budgetId, referenceId, useId, BigDecimal.valueOf(10000), commitmentOrderSupplierId);

    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, BigDecimal.valueOf(9000)));

    final var command = new PatchDistributionNominatedCommand(request, new AutoRequest());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository
        .findById(new Id(command.patchNominatedRequest().distributionNominatedId())))
            .thenReturn(Optional.of(distributionNominated));
    when(this.distributionNominatedRepository.findBySharedRawMaterial(distributionNominated.sharedRawMaterial()))
        .thenReturn(List.of(distributionNominated));
    when(this.commitmentAdjustedProvider
        .findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
            .thenReturn(partialQuantities);
    when(this.linesIdentifier.identifyUpdatedLines(eq(distributionNominated), eq(payloadRequested),
        eq(payloadTheoretical), anyList(), eq(now))).thenReturn(distributionNominated.lines().value());

    final var result = this.sut.execute(command);

    assertThat(result).isNotNull();
    assertThat(result.requestedQuantity().value()).isEqualTo(requestedQuantity);
    assertThat(result.consumptionFactor().value()).isEqualTo(consumptionFactor);
    assertThat(result.theoreticalQuantity().value()).isEqualTo(theoreticalQuantity);

    assertThat(result.lines().value()).hasSize(1);
    assertThat(result.lines().value()).first().returns(new RequestedQuantity(BigDecimal.valueOf(2000)),
        DistributionNominatedLine::requestedQuantity);

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor
        .forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    verify(this.lockRepository).lock(Lock.from(distributionNominated.sharedRawMaterial()));
    verify(this.eventBus).send(expectedEvent(nominatedCaptor, EventType.CORRECTED));
  }

  @Test
  void should_update_requested_with_automatic_plan() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());

    final var requestedQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(2000));
    final var theoreticalQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(500));
    final var consumptionFactor = NumericUtils.roundUpScale2(BigDecimal.ONE);

    final var payloadRequested = new DistributionNominated.RequestedQuantity(requestedQuantity);
    final var payloadTheoretical = new DistributionNominated.TheoreticalQuantity(theoreticalQuantity);

    final var request = new PatchNominatedRequest(distributionNominatedId, theoreticalQuantity, requestedQuantity,
        consumptionFactor, "test");

    final var distributionNominated = DistributionNominatedMother
        .pendingWithIdAndSharedRawMaterialAndLinesAndQuantities(distributionNominatedId,
            new SharedRawMaterialNominated(referenceId, useId, budgetCycle), BigDecimal.ONE,
            consumptionFactor, theoreticalQuantity,
            new ArrayList<>(List
                .of(new DistributionNominatedLine(new DistributionNominatedLine.Id(UUID.randomUUID()),
                    new CommitmentOrder(new CommitmentOrder.Id(orderId), new LineId(orderLineId),
                        new SupplierId(commitmentOrderSupplierId)),
                    new TheoreticalQuantity(BigDecimal.ONE),
                    new RequestedQuantity(BigDecimal.valueOf(2000)),
                    new DistributedQuantity(BigDecimal.ZERO), null, null,
                    new BasicAudit(now.plusDays(20), now.plusDays(20))))))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .referenceId(new ReferenceId(referenceId)).build();

    final var materialCommitmentUseSharedRawMaterial = MaterialCommitmentUseSharedRawMaterial
        .of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);

    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(orderId, orderLineId,
        budgetId, referenceId, useId, BigDecimal.valueOf(10000), commitmentOrderSupplierId);

    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, BigDecimal.valueOf(9000)));

    final var command = new PatchDistributionNominatedCommand(request, new AutoRequest());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository
        .findById(new Id(command.patchNominatedRequest().distributionNominatedId())))
            .thenReturn(Optional.of(distributionNominated));
    when(this.distributionNominatedRepository.findBySharedRawMaterial(distributionNominated.sharedRawMaterial()))
        .thenReturn(List.of(distributionNominated));
    when(this.commitmentAdjustedProvider
        .findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
            .thenReturn(partialQuantities);
    when(this.linesIdentifier.identifyUpdatedLines(eq(distributionNominated), eq(payloadRequested),
        eq(payloadTheoretical), anyList(), eq(now))).thenReturn(distributionNominated.lines().value());

    final var result = this.sut.execute(command);

    assertThat(result).isNotNull();
    assertThat(result.requestedQuantity().value()).isEqualTo(requestedQuantity);
    assertThat(result.consumptionFactor().value()).isEqualTo(consumptionFactor);
    assertThat(result.theoreticalQuantity().value()).isEqualTo(theoreticalQuantity);

    assertThat(result.lines().value()).hasSize(1);
    assertThat(result.lines().value()).first().returns(new RequestedQuantity(BigDecimal.valueOf(2000)),
        DistributionNominatedLine::requestedQuantity);
    assertThat(result.plan()).isEqualTo(DistributionNominatedPlan.AUTO);

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor
        .forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    verify(this.lockRepository).lock(Lock.from(distributionNominated.sharedRawMaterial()));
    verify(this.eventBus).send(expectedEvent(nominatedCaptor, EventType.UPDATED));

    assertThat(nominatedCaptor.getValue().plan()).isEqualTo(DistributionNominatedPlan.AUTO);
  }

  @Test
  void should_throw_exception_when_update_but_budget_cycle_change_in_progress() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());

    final var requestedQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(2000));
    final var theoreticalQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(500));
    final var consumptionFactor = NumericUtils.roundUpScale2(BigDecimal.ONE);

    final var payloadRequested = new DistributionNominated.RequestedQuantity(requestedQuantity);
    final var payloadTheoretical = new DistributionNominated.TheoreticalQuantity(theoreticalQuantity);

    final var request = new PatchNominatedRequest(distributionNominatedId, theoreticalQuantity, requestedQuantity,
        consumptionFactor, "test");

    final var distributionNominated = DistributionNominatedMother
        .pendingWithIdAndSharedRawMaterialAndLinesAndQuantities(distributionNominatedId,
            new SharedRawMaterialNominated(referenceId, useId, budgetCycle), BigDecimal.ONE,
            consumptionFactor, theoreticalQuantity,
            new ArrayList<>(List
                .of(new DistributionNominatedLine(new DistributionNominatedLine.Id(UUID.randomUUID()),
                    new CommitmentOrder(new CommitmentOrder.Id(orderId), new LineId(orderLineId),
                        new SupplierId(commitmentOrderSupplierId)),
                    new TheoreticalQuantity(BigDecimal.ONE),
                    new RequestedQuantity(BigDecimal.valueOf(2000)),
                    new DistributedQuantity(BigDecimal.ZERO), null, null,
                    new BasicAudit(now.plusDays(20), now.plusDays(20))))))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ONE))
        .referenceId(new ReferenceId(referenceId))
        .build();

    final var materialCommitmentUseSharedRawMaterial = MaterialCommitmentUseSharedRawMaterial
        .of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);

    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(orderId, orderLineId,
        budgetId, referenceId, useId, BigDecimal.valueOf(10000), commitmentOrderSupplierId);

    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, BigDecimal.valueOf(9000)));

    final var command = new PatchDistributionNominatedCommand(request, new AutoRequest());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository
        .findById(new Id(command.patchNominatedRequest().distributionNominatedId())))
            .thenReturn(Optional.of(distributionNominated));
    when(this.distributionNominatedRepository.findBySharedRawMaterial(distributionNominated.sharedRawMaterial()))
        .thenReturn(List.of(distributionNominated));
    when(this.commitmentAdjustedProvider
        .findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
            .thenReturn(partialQuantities);
    when(this.linesIdentifier.identifyUpdatedLines(eq(distributionNominated), eq(payloadRequested),
        eq(payloadTheoretical), anyList(), eq(now))).thenReturn(distributionNominated.lines().value());

    assertThatThrownBy(() -> this.sut.execute(command))
        .isInstanceOf(ErrorException.class);
    verify(this.distributionNominatedRepository, never()).save(any());
    verify(this.eventBus, never()).send(any());
    verify(this.lockRepository, never()).lock(Lock.from(distributionNominated.sharedRawMaterial()));
  }

  @Test
  void should_update_requested_with_preselected_plan() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());

    final var requestedQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(3000));
    final var theoreticalQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(500));
    final var consumptionFactor = NumericUtils.roundUpScale2(BigDecimal.ONE);

    final var payloadRequested = new DistributionNominated.RequestedQuantity(requestedQuantity);
    final var payloadTheoretical = new DistributionNominated.TheoreticalQuantity(theoreticalQuantity);

    final var request = new PatchNominatedRequest(distributionNominatedId, theoreticalQuantity, requestedQuantity,
        consumptionFactor, "test");

    final var distributionNominated = DistributionNominatedMother
        .pendingWithIdAndSharedRawMaterialAndLinesAndQuantities(distributionNominatedId,
            new SharedRawMaterialNominated(referenceId, useId, budgetCycle), BigDecimal.ONE,
            consumptionFactor, theoreticalQuantity,
            new ArrayList<>(List
                .of(new DistributionNominatedLine(new DistributionNominatedLine.Id(UUID.randomUUID()),
                    new CommitmentOrder(new CommitmentOrder.Id(orderId), new LineId(orderLineId),
                        new SupplierId(commitmentOrderSupplierId)),
                    new TheoreticalQuantity(BigDecimal.ONE),
                    new RequestedQuantity(BigDecimal.valueOf(2000)),
                    new DistributedQuantity(BigDecimal.ZERO), null, null,
                    new BasicAudit(now.plusDays(20), now.plusDays(20))))))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .referenceId(new ReferenceId(referenceId)).build();

    final var materialCommitmentUseSharedRawMaterial = MaterialCommitmentUseSharedRawMaterial
        .of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);

    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(orderId, orderLineId,
        budgetId, referenceId, useId, BigDecimal.valueOf(10000), commitmentOrderSupplierId);

    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, BigDecimal.valueOf(9000)));

    final var preselection = new PreselectedRequest(
        List.of(new PreselectedRequest.LineSelectionRequest(orderId, orderLineId, requestedQuantity)));
    final var command = new PatchDistributionNominatedCommand(request, preselection);

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository
        .findById(new Id(command.patchNominatedRequest().distributionNominatedId())))
            .thenReturn(Optional.of(distributionNominated));
    when(this.distributionNominatedRepository.findBySharedRawMaterial(distributionNominated.sharedRawMaterial()))
        .thenReturn(List.of(distributionNominated));
    when(this.alternativeSharedRawMaterialProvider.alternatives(materialCommitmentUseSharedRawMaterial))
        .thenReturn(List.of(materialCommitmentUseSharedRawMaterial));
    when(this.commitmentAdjustedProvider
        .findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
            .thenReturn(partialQuantities);
    when(this.linesIdentifier.identifyUpdatedLines(eq(distributionNominated), eq(payloadRequested),
        eq(payloadTheoretical), anyList(), eq(now))).thenReturn(distributionNominated.lines().value());

    final var result = this.sut.execute(command);

    assertThat(result).isNotNull();
    assertThat(result.requestedQuantity().value()).isEqualTo(requestedQuantity);
    assertThat(result.consumptionFactor().value()).isEqualTo(consumptionFactor);
    assertThat(result.theoreticalQuantity().value()).isEqualTo(theoreticalQuantity);

    assertThat(result.lines().value()).hasSize(1);
    assertThat(result.lines().value()).first().returns(new RequestedQuantity(BigDecimal.valueOf(2000)),
        DistributionNominatedLine::requestedQuantity);

    assertThat(result.plan()).isEqualTo(DistributionNominatedPlan.PRESELECTED);

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor
        .forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    verify(this.lockRepository).lock(Lock.from(distributionNominated.sharedRawMaterial()));
    verify(this.eventBus).send(expectedEvent(nominatedCaptor, EventType.UPDATED));

    assertThat(nominatedCaptor.getValue().plan()).isEqualTo(DistributionNominatedPlan.PRESELECTED);
  }

  @Test
  void should_update_requested_closed() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());

    final var requestedQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(2000));
    final var theoreticalQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(500));
    final var consumptionFactor = NumericUtils.roundUpScale2(BigDecimal.ONE);

    final var payloadRequested = new DistributionNominated.RequestedQuantity(requestedQuantity);
    final var payloadTheoretical = new DistributionNominated.TheoreticalQuantity(theoreticalQuantity);

    final var request = new PatchNominatedRequest(distributionNominatedId, theoreticalQuantity, requestedQuantity,
        consumptionFactor, "test");

    final var distributionNominated = DistributionNominatedMother
        .pendingWithIdAndSharedRawMaterialAndLinesAndQuantities(distributionNominatedId,
            new SharedRawMaterialNominated(referenceId, useId, budgetCycle), BigDecimal.ONE,
            consumptionFactor, theoreticalQuantity,
            new ArrayList<>(List
                .of(new DistributionNominatedLine(new DistributionNominatedLine.Id(UUID.randomUUID()),
                    new CommitmentOrder(new CommitmentOrder.Id(orderId), new LineId(orderLineId),
                        new SupplierId(commitmentOrderSupplierId)),
                    new TheoreticalQuantity(BigDecimal.ONE),
                    new RequestedQuantity(BigDecimal.valueOf(2000)),
                    new DistributedQuantity(BigDecimal.ZERO), null, null,
                    new BasicAudit(now.plusDays(20), now.plusDays(20))))))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .referenceId(new ReferenceId(referenceId)).status(DistributionNominatedStatus.CLOSED).build();

    final var materialCommitmentUseSharedRawMaterial = MaterialCommitmentUseSharedRawMaterial
        .of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);

    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(orderId, orderLineId,
        budgetId, referenceId, useId, BigDecimal.valueOf(10000), commitmentOrderSupplierId);

    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, BigDecimal.valueOf(9000)));

    final var command = new PatchDistributionNominatedCommand(request, new AutoRequest());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository
        .findById(new Id(command.patchNominatedRequest().distributionNominatedId())))
            .thenReturn(Optional.of(distributionNominated));
    when(this.distributionNominatedRepository.findBySharedRawMaterial(distributionNominated.sharedRawMaterial()))
        .thenReturn(List.of(distributionNominated));
    when(this.commitmentAdjustedProvider
        .findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
            .thenReturn(partialQuantities);
    when(this.linesIdentifier.identifyUpdatedLines(eq(distributionNominated), eq(payloadRequested),
        eq(payloadTheoretical), anyList(), eq(now))).thenReturn(distributionNominated.lines().value());

    final var result = this.sut.execute(command);

    assertThat(result).isNotNull();
    assertThat(result.requestedQuantity().value()).isEqualTo(requestedQuantity);
    assertThat(result.consumptionFactor().value()).isEqualTo(consumptionFactor);
    assertThat(result.theoreticalQuantity().value()).isEqualTo(theoreticalQuantity);

    assertThat(result.lines().value()).hasSize(1);
    assertThat(result.lines().value()).first().returns(new RequestedQuantity(BigDecimal.valueOf(2000)),
        DistributionNominatedLine::requestedQuantity);

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor
        .forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    verify(this.lockRepository).lock(Lock.from(distributionNominated.sharedRawMaterial()));
    verify(this.eventBus).send(expectedEvent(nominatedCaptor, EventType.CORRECTED));
  }

  @Test
  void should_update_theoretical_requested_and_consumptionFactor() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());

    final var requestedQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(2000));
    final var theoreticalQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(500));
    final var consumptionFactor = NumericUtils.roundUpScale2(BigDecimal.ONE);

    final var payloadRequested = new DistributionNominated.RequestedQuantity(requestedQuantity);
    final var payloadTheoretical = new DistributionNominated.TheoreticalQuantity(theoreticalQuantity);

    final var request = new PatchNominatedRequest(distributionNominatedId, theoreticalQuantity, requestedQuantity,
        consumptionFactor, "test");

    final var distributionNominated = DistributionNominatedMother
        .pendingWithIdAndSharedRawMaterialAndLinesAndQuantities(distributionNominatedId,
            new SharedRawMaterialNominated(referenceId, useId, budgetCycle), BigDecimal.ONE, BigDecimal.ONE,
            BigDecimal.ONE,
            new ArrayList<>(List
                .of(new DistributionNominatedLine(new DistributionNominatedLine.Id(UUID.randomUUID()),
                    new CommitmentOrder(new CommitmentOrder.Id(orderId), new LineId(orderLineId),
                        new SupplierId(commitmentOrderSupplierId)),
                    new TheoreticalQuantity(BigDecimal.ONE),
                    new RequestedQuantity(BigDecimal.valueOf(2000)),
                    new DistributedQuantity(BigDecimal.ZERO), null, null,
                    new BasicAudit(now.plusDays(20), now.plusDays(20))))))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .referenceId(new ReferenceId(referenceId)).build();

    final var materialCommitmentUseSharedRawMaterial = MaterialCommitmentUseSharedRawMaterial
        .of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);

    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(orderId, orderLineId,
        budgetId, referenceId, useId, BigDecimal.valueOf(10000), commitmentOrderSupplierId);

    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, BigDecimal.valueOf(9000)));

    final var command = new PatchDistributionNominatedCommand(request, new AutoRequest());

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository
        .findById(new Id(command.patchNominatedRequest().distributionNominatedId())))
            .thenReturn(Optional.of(distributionNominated));
    when(this.distributionNominatedRepository.findBySharedRawMaterial(distributionNominated.sharedRawMaterial()))
        .thenReturn(List.of(distributionNominated));
    when(this.commitmentAdjustedProvider
        .findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
            .thenReturn(partialQuantities);
    when(this.linesIdentifier.identifyUpdatedLines(eq(distributionNominated), eq(payloadRequested),
        eq(payloadTheoretical), anyList(), eq(now))).thenReturn(distributionNominated.lines().value());

    final var result = this.sut.execute(command);

    assertThat(result).isNotNull();
    assertThat(result.requestedQuantity().value()).isEqualTo(requestedQuantity);
    assertThat(result.consumptionFactor().value()).isEqualTo(consumptionFactor);
    assertThat(result.theoreticalQuantity().value()).isEqualTo(theoreticalQuantity);

    assertThat(result.lines().value()).hasSize(1);
    assertThat(result.lines().value()).first().returns(new RequestedQuantity(BigDecimal.valueOf(2000)),
        DistributionNominatedLine::requestedQuantity);

    final ArgumentCaptor<DistributionNominated> nominatedCaptor = ArgumentCaptor
        .forClass(DistributionNominated.class);
    verify(this.distributionNominatedRepository).save(nominatedCaptor.capture());
    verify(this.lockRepository).lock(Lock.from(distributionNominated.sharedRawMaterial()));
    verify(this.eventBus).send(expectedEvent(nominatedCaptor, EventType.UPDATED));
  }

  @Test
  void should_throw_exception_when_preselection_commitments_does_not_exits() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());

    final var requestedQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(3000));
    final var theoreticalQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(500));
    final var consumptionFactor = NumericUtils.roundUpScale2(BigDecimal.ONE);

    final var payloadRequested = new DistributionNominated.RequestedQuantity(requestedQuantity);
    final var payloadTheoretical = new DistributionNominated.TheoreticalQuantity(theoreticalQuantity);

    final var request = new PatchNominatedRequest(distributionNominatedId, theoreticalQuantity, requestedQuantity,
        consumptionFactor, "test");

    final var distributionNominated = DistributionNominatedMother
        .pendingWithIdAndSharedRawMaterialAndLinesAndQuantities(distributionNominatedId,
            new SharedRawMaterialNominated(referenceId, useId, budgetCycle), BigDecimal.ONE,
            consumptionFactor, theoreticalQuantity,
            new ArrayList<>(List
                .of(new DistributionNominatedLine(new DistributionNominatedLine.Id(UUID.randomUUID()),
                    new CommitmentOrder(new CommitmentOrder.Id(orderId), new LineId(orderLineId),
                        new SupplierId(commitmentOrderSupplierId)),
                    new TheoreticalQuantity(BigDecimal.ONE),
                    new RequestedQuantity(BigDecimal.valueOf(2000)),
                    new DistributedQuantity(BigDecimal.ZERO), null, null,
                    new BasicAudit(now.plusDays(20), now.plusDays(20))))))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .referenceId(new ReferenceId(referenceId)).build();

    final var preselection = new PreselectedRequest(
        List.of(new PreselectedRequest.LineSelectionRequest(orderId, orderLineId, requestedQuantity)));
    final var command = new PatchDistributionNominatedCommand(request, preselection);

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository
        .findById(new Id(command.patchNominatedRequest().distributionNominatedId())))
            .thenReturn(Optional.of(distributionNominated));
    when(this.distributionNominatedRepository.findBySharedRawMaterial(distributionNominated.sharedRawMaterial()))
        .thenReturn(List.of(distributionNominated));
    when(this.linesIdentifier.identifyUpdatedLines(eq(distributionNominated), eq(payloadRequested),
        eq(payloadTheoretical), anyList(), eq(now))).thenReturn(distributionNominated.lines().value());

    final Throwable result = catchThrowable(() -> this.sut.execute(command));

    assertThat(result).isInstanceOf(ErrorException.class)
        .hasMessageContaining(String.format("Commitment %s with line %s not found", orderId, orderLineId));
    verify(this.distributionNominatedRepository, never()).save(any());
    verify(this.lockRepository).lock(Lock.from(distributionNominated.sharedRawMaterial()));
    verifyNoInteractions(this.eventBus);
  }

  @Test
  void should_throw_exception_when_reduce_preselection_and_distribution_already_starts() {
    final var now = OffsetDateTime.now();
    final var distributionNominatedId = UUID.randomUUID();
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId);
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var commitmentOrderSupplierId = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());

    final var requestedQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(1000));
    final var theoreticalQuantity = NumericUtils.roundUpScale2(BigDecimal.valueOf(500));
    final var consumptionFactor = NumericUtils.roundUpScale2(BigDecimal.ONE);

    final var payloadRequested = new DistributionNominated.RequestedQuantity(requestedQuantity);
    final var payloadTheoretical = new DistributionNominated.TheoreticalQuantity(theoreticalQuantity);

    final var request = new PatchNominatedRequest(distributionNominatedId, theoreticalQuantity, requestedQuantity,
        consumptionFactor, "test");

    final var distributionNominated = DistributionNominatedMother
        .pendingWithIdAndSharedRawMaterialAndLinesAndQuantities(distributionNominatedId,
            new SharedRawMaterialNominated(referenceId, useId, budgetCycle), BigDecimal.ONE,
            consumptionFactor, theoreticalQuantity,
            new ArrayList<>(List
                .of(new DistributionNominatedLine(new DistributionNominatedLine.Id(UUID.randomUUID()),
                    new CommitmentOrder(new CommitmentOrder.Id(orderId), new LineId(orderLineId),
                        new SupplierId(commitmentOrderSupplierId)),
                    new TheoreticalQuantity(BigDecimal.ONE),
                    new RequestedQuantity(BigDecimal.valueOf(2000)),
                    new DistributedQuantity(BigDecimal.valueOf(2000)), null, null,
                    new BasicAudit(now.plusDays(20), now.plusDays(20))))))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .referenceId(new ReferenceId(referenceId))
        .distributedQuantity(new DistributionNominated.DistributedQuantity(BigDecimal.valueOf(2000)))
        .build();

    final var materialCommitmentUseSharedRawMaterial = MaterialCommitmentUseSharedRawMaterial
        .of(String.valueOf(useId), String.valueOf(referenceId), budgetCycle);

    final var materialCommitmentUse = MaterialCommitmentUseMother.withCompositeIdAndQuantity(orderId, orderLineId,
        budgetId, referenceId, useId, BigDecimal.valueOf(10000), commitmentOrderSupplierId);

    final var partialQuantities = List.of(CommitmentAdjusted.of(materialCommitmentUse, BigDecimal.valueOf(9000)));

    final var preselection = new PreselectedRequest(
        List.of(new PreselectedRequest.LineSelectionRequest(orderId, orderLineId, requestedQuantity)));
    final var command = new PatchDistributionNominatedCommand(request, preselection);

    when(this.clockUtils.getCurrentOffsetDateTime()).thenReturn(now);
    when(this.distributionNominatedRepository
        .findById(new Id(command.patchNominatedRequest().distributionNominatedId())))
            .thenReturn(Optional.of(distributionNominated));
    when(this.distributionNominatedRepository.findBySharedRawMaterial(distributionNominated.sharedRawMaterial()))
        .thenReturn(List.of(distributionNominated));
    when(this.alternativeSharedRawMaterialProvider.alternatives(materialCommitmentUseSharedRawMaterial))
        .thenReturn(List.of(materialCommitmentUseSharedRawMaterial));
    when(this.commitmentAdjustedProvider
        .findByAnySharedRawMaterial(List.of(materialCommitmentUseSharedRawMaterial)))
            .thenReturn(partialQuantities);
    when(this.linesIdentifier.identifyUpdatedLines(eq(distributionNominated), eq(payloadRequested),
        eq(payloadTheoretical), anyList(), eq(now))).thenReturn(distributionNominated.lines().value());

    final Throwable result = catchThrowable(() -> this.sut.execute(command));

    assertThat(result).isInstanceOf(ErrorException.class)
        .hasMessageContaining("Cannot decrease requested quantity of a distributed line");
  }

  private static Collection<DomainEvent<?>> expectedEvent(final ArgumentCaptor<DistributionNominated> nominatedCaptor,
      final EventType... eventTypes) {
    final List<DomainEvent<?>> list = new ArrayList<>();
    for (final EventType eventType : eventTypes) {
      final DistributionNominatedUnifiedEvent distributionNominatedUnifiedEvent = new DistributionNominatedUnifiedEvent(
          nominatedCaptor.getValue(), eventType);
      list.add(distributionNominatedUnifiedEvent);
    }
    return list;
  }
}
