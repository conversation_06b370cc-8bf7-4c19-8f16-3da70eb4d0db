package com.inditex.icdmdemg.application.distributionnominated.query;

import static com.inditex.icdmdemg.shared.utils.NumericUtils.roundUpScale2;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.ParameterizedTest.INDEX_PLACEHOLDER;
import static org.mockito.Mockito.doReturn;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.stream.Stream;

import com.inditex.icdmdemg.application.distributionnominated.query.GetAvailableCommitmentsQuery.AvailableCommitment;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjustedProvider;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus.MaterialCommitmentUseStatusEnum;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedLineMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.shared.utils.CurrentMethodName;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.SliceImpl;

@ExtendWith(MockitoExtension.class)
class GetAvailableCommitmentsQueryHandlerTest {
  public static final String PARAMETERIZED_TEST_DESCRIPTION = "[" + INDEX_PLACEHOLDER + "] [{0}]";

  @Mock
  private DistributionNominatedRepository distributionNominatedRepository;

  @Mock
  private CommitmentAdjustedProvider commitmentAdjustedProvider;

  @InjectMocks
  private GetAvailableCommitmentsQueryHandler sut;

  @ParameterizedTest(name = PARAMETERIZED_TEST_DESCRIPTION)
  @MethodSource("provideCasesAndExpectedReturn")
  void should_return_correct_values(final String ignoredTestName, final List<CommitmentAdjusted> commitmentsAdjusted,
      final List<DistributionNominated> distributionNominateds,
      final List<AvailableCommitment> expectedAvailableCommitments, final PageRequest queryPageable) {
    final var query = new GetAvailableCommitmentsQuery(UuidMother.fromInteger(1),
        UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UuidMother.fromInteger(2)), UuidMother.fromInteger(3), queryPageable);

    doReturn(new SliceImpl<>(commitmentsAdjusted, queryPageable, false)).when(this.commitmentAdjustedProvider)
        .findBySharedRawMaterialPaged(
            MaterialCommitmentUseSharedRawMaterial.of(query.useId().toString(), query.referenceId().toString(), query.budgetCycle()),
            queryPageable);
    doReturn(distributionNominateds).when(this.distributionNominatedRepository)
        .findBySharedRawMaterial(new SharedRawMaterialNominated(query.referenceId(), query.useId(), query.budgetCycle()));

    final var expectedResult = new SliceImpl<>(expectedAvailableCommitments, queryPageable, false);

    final var result = this.sut.ask(query);

    assertThat(result.getContent()).isEqualTo(expectedAvailableCommitments);
    assertThat(result).isEqualTo(expectedResult);
  }

  private static Stream<Arguments> provideCasesAndExpectedReturn() {
    return Stream.of(
        whenEmptyShouldReturnEmpty(),
        whenOneDNAndClosedCommitmentShouldReturnCommitmentAdjustedZero(),
        whenNoDNsShouldReturnCommitmentAdjustedOutput(),
        whenNoDNsAndNoOpenCommitmentsShouldReturnZero(),
        whenOneDNShouldReturnCommitmentAdjustedMinusDN(),
        whenMoreThanOneDNShouldReturnOneCommitmentAdjustedMinusDNs(),
        whenMoreThanOneDNShouldReturnMoreThanOneCommitmentAdjustedMinusDNs());
  }

  private static Arguments whenMoreThanOneDNShouldReturnMoreThanOneCommitmentAdjustedMinusDNs() {
    final var pageable = PageRequest.of(0, 50);
    final var orderId = 100;
    final var orderLineId = 200;
    final var orderId2 = 102;
    final var orderLineId2 = 202;
    final var supplierId = 901;
    final var expectedDate = OffsetDateTime.now();

    final var dns = List.of(
        DistributionNominatedMother.pendingWithLines(List.of(
            DistributionNominatedLineMother
                .withCommitmentOrderAndQuantityAndWithoutAlternativeReference(UuidMother.fromInteger(orderId),
                    UuidMother.fromInteger(orderLineId), BigDecimal.valueOf(5))
                .build()))
            .build(),
        DistributionNominatedMother.pendingWithLines(List.of(
            DistributionNominatedLineMother
                .withCommitmentOrderAndQuantityAndWithoutAlternativeReference(UuidMother.fromInteger(orderId),
                    UuidMother.fromInteger(orderLineId), BigDecimal.valueOf(5))
                .build(),
            DistributionNominatedLineMother
                .withCommitmentOrderAndQuantityAndWithoutAlternativeReference(UuidMother.fromInteger(orderId2),
                    UuidMother.fromInteger(orderLineId2), BigDecimal.valueOf(5))
                .build()))
            .build());
    final var adjustedCommitments =
        List.of(new CommitmentAdjusted(
            MaterialCommitmentUseMother.with(001, BigDecimal.TEN, expectedDate, 300,
                400, 500, MaterialCommitmentUseStatusEnum.OPEN, orderId, orderLineId, supplierId),
            BigDecimal.valueOf(10)),
            new CommitmentAdjusted(
                MaterialCommitmentUseMother.with(002, BigDecimal.TEN, expectedDate, 300,
                    400, 500, MaterialCommitmentUseStatusEnum.OPEN, orderId2, orderLineId2, supplierId),
                BigDecimal.valueOf(15)));

    final var expectedResult =
        List.of(new AvailableCommitment(UuidMother.fromInteger(orderId), UuidMother.fromInteger(orderLineId),
            expectedDate, UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId)),
            roundUpScale2(BigDecimal.valueOf(0))),
            new AvailableCommitment(UuidMother.fromInteger(orderId2), UuidMother.fromInteger(orderLineId2),
                expectedDate, UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId)),
                roundUpScale2(BigDecimal.valueOf(10))));

    return Arguments.of(CurrentMethodName.get(), adjustedCommitments, dns, expectedResult, pageable);
  }

  private static Arguments whenMoreThanOneDNShouldReturnOneCommitmentAdjustedMinusDNs() {
    final var pageable = PageRequest.of(0, 50);
    final var orderId = 100;
    final var orderLineId = 200;
    final var supplierId = 901;
    final var expectedDate = OffsetDateTime.now();

    final var dns = List.of(
        DistributionNominatedMother.pendingWithLines(List.of(
            DistributionNominatedLineMother
                .withCommitmentOrderAndQuantityAndWithoutAlternativeReference(UuidMother.fromInteger(orderId),
                    UuidMother.fromInteger(orderLineId), BigDecimal.valueOf(5))
                .build()))
            .build(),
        DistributionNominatedMother.pendingWithLines(List.of(
            DistributionNominatedLineMother
                .withCommitmentOrderAndQuantityAndWithoutAlternativeReference(UuidMother.fromInteger(orderId),
                    UuidMother.fromInteger(orderLineId), BigDecimal.valueOf(5))
                .build()))
            .build());
    final var adjustedCommitments =
        List.of(new CommitmentAdjusted(
            MaterialCommitmentUseMother.with(001, BigDecimal.TEN, expectedDate, 300,
                400, 500, MaterialCommitmentUseStatusEnum.OPEN, orderId, orderLineId, supplierId),
            BigDecimal.valueOf(10)));

    final var expectedResult =
        List.of(new AvailableCommitment(UuidMother.fromInteger(orderId), UuidMother.fromInteger(orderLineId),
            expectedDate, UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId)),
            roundUpScale2(BigDecimal.valueOf(0))));

    return Arguments.of(CurrentMethodName.get(), adjustedCommitments, dns, expectedResult, pageable);
  }

  private static Arguments whenOneDNShouldReturnCommitmentAdjustedMinusDN() {
    final var pageable = PageRequest.of(0, 50);
    final var orderId = 100;
    final var orderLineId = 200;
    final var supplierId = 901;
    final var expectedDate = OffsetDateTime.now();

    final var dns = List.of(
        DistributionNominatedMother.pendingWithLines(List.of(
            DistributionNominatedLineMother
                .withCommitmentOrderAndQuantityAndWithoutAlternativeReference(UuidMother.fromInteger(orderId),
                    UuidMother.fromInteger(orderLineId), BigDecimal.valueOf(5))
                .build()))
            .build());
    final var adjustedCommitments =
        List.of(new CommitmentAdjusted(
            MaterialCommitmentUseMother.with(001, BigDecimal.TEN, expectedDate, 300,
                400, 500, MaterialCommitmentUseStatusEnum.OPEN, orderId, orderLineId, supplierId),
            BigDecimal.valueOf(10)));

    final var expectedResult =
        List.of(new AvailableCommitment(UuidMother.fromInteger(orderId), UuidMother.fromInteger(orderLineId),
            expectedDate, UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId)),
            roundUpScale2(BigDecimal.valueOf(5))));

    return Arguments.of(CurrentMethodName.get(), adjustedCommitments, dns, expectedResult, pageable);
  }

  private static Arguments whenOneDNAndClosedCommitmentShouldReturnCommitmentAdjustedZero() {
    final var pageable = PageRequest.of(0, 50);
    final var orderId = 100;
    final var orderLineId = 200;
    final var supplierId = 901;
    final var expectedDate = OffsetDateTime.now();

    final var dns = List.of(
        DistributionNominatedMother.pendingWithLines(List.of(
            DistributionNominatedLineMother
                .withCommitmentOrderAndQuantityAndWithoutAlternativeReference(UuidMother.fromInteger(orderId),
                    UuidMother.fromInteger(orderLineId), BigDecimal.valueOf(5))
                .build()))
            .build());
    final var adjustedCommitments =
        List.of(new CommitmentAdjusted(
            MaterialCommitmentUseMother.with(001, BigDecimal.TEN, expectedDate, 300,
                400, 500, MaterialCommitmentUseStatusEnum.CLOSED, orderId, orderLineId, supplierId),
            BigDecimal.valueOf(10)));

    final var expectedResult =
        List.of(new AvailableCommitment(UuidMother.fromInteger(orderId), UuidMother.fromInteger(orderLineId),
            expectedDate, UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId)),
            roundUpScale2(BigDecimal.ZERO)));

    return Arguments.of(CurrentMethodName.get(), adjustedCommitments, dns, expectedResult, pageable);
  }

  private static Arguments whenNoDNsAndNoOpenCommitmentsShouldReturnZero() {
    final var pageable = PageRequest.of(1, 1);
    final var orderId = 100;
    final var orderLineId = 200;
    final var supplierId = 500;
    final var expectedDate = OffsetDateTime.now();

    final var dns = List.of();
    final var adjustedCommitments =
        List.of(new CommitmentAdjusted(
            MaterialCommitmentUseMother.with(001, BigDecimal.TEN, expectedDate, 300,
                400, 500, MaterialCommitmentUseStatusEnum.CLOSED, orderId, orderLineId, supplierId),
            BigDecimal.valueOf(5)));
    final var expectedResult =
        List.of(new AvailableCommitment(UuidMother.fromInteger(orderId), UuidMother.fromInteger(orderLineId),
            expectedDate, UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId)),
            roundUpScale2(BigDecimal.ZERO)));

    return Arguments.of(CurrentMethodName.get(), adjustedCommitments, dns, expectedResult, pageable);
  }

  private static Arguments whenNoDNsShouldReturnCommitmentAdjustedOutput() {
    final var pageable = PageRequest.of(0, 50);
    final var orderId = 100;
    final var orderLineId = 200;
    final var supplierId = 500;
    final var expectedDate = OffsetDateTime.now();

    final var dns = List.of();
    final var adjustedCommitments =
        List.of(new CommitmentAdjusted(
            MaterialCommitmentUseMother.with(001, BigDecimal.TEN, expectedDate, 300,
                400, 500, MaterialCommitmentUseStatusEnum.OPEN, orderId, orderLineId, supplierId),
            BigDecimal.valueOf(5)));
    final var expectedResult =
        List.of(new AvailableCommitment(UuidMother.fromInteger(orderId), UuidMother.fromInteger(orderLineId),
            expectedDate, UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId)),
            roundUpScale2(BigDecimal.valueOf(5))));

    return Arguments.of(CurrentMethodName.get(), adjustedCommitments, dns, expectedResult, pageable);
  }

  private static Arguments whenEmptyShouldReturnEmpty() {
    final var pageable = PageRequest.of(0, 50);
    return Arguments.of(CurrentMethodName.get(), List.of(), List.of(), List.of(), pageable);
  }
}
