package com.inditex.icdmdemg.application.taxonomy;

import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;

import java.util.List;

import com.inditex.icdmdemg.domain.taxonomyuse.TaxonomyUse;
import com.inditex.icdmdemg.domain.taxonomyuse.TaxonomyUseRepository;
import com.inditex.icdmdemg.domain.taxonomyuse.entity.TaxonomyCode;
import com.inditex.icdmdemg.domain.taxonomyuse.entity.TaxonomyPath;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TaxonomyUpdaterProcessorTest {

  @Mock
  private TaxonomyUseRepository repository;

  @InjectMocks
  private TaxonomyUpdaterProcessor sut;

  @Test
  void should_save_taxonomies() {
    final var provided = List.of(
        new TaxonomyUse(new TaxonomyCode("ROOT"), new TaxonomyPath("/ROOT")),
        new TaxonomyUse(new TaxonomyCode("CLOTHES"), new TaxonomyPath("/ROOT/CLOTHES")),
        new TaxonomyUse(new TaxonomyCode("T_SHIRT"), new TaxonomyPath("/ROOT/CLOTHES/T_SHIRT")));

    doReturn(provided).when(this.repository).getTaxonomyFromProvider();

    this.sut.updateTaxonomies();

    verify(this.repository).saveAll(provided);
  }
}
