package com.inditex.icdmdemg.application.commitmentuse.service;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;
import static com.inditex.icdmdemg.shared.utils.RandomValue.randomString;

import static org.assertj.core.api.Assertions.assertThat;
import static org.instancio.Select.field;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.OrderUse;
import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.Use;
import com.inditex.icdmdemg.application.commitmentuse.service.conf.conf.McuBudgetCycleChangeExecutedProperties;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseCollection;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeExecutedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeRequestedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseMaterialReferenceId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderIdAndOrderLineId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLine;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLineId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseQuantity;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseServiceLocalizationId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseServiceLocalizationType;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus.MaterialCommitmentUseStatusEnum;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseTimestamp;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseUseId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseVersion;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MaterialCommitmentUseProjectorTest {

  @Mock
  private MaterialCommitmentUseRepository materialCommitmentUseRepository;

  @Mock
  private ClockUtils clockUtils;

  @Mock
  private McuBudgetCycleChangeExecutedProperties budgetCycleChangeExecutedProperties;

  @InjectMocks
  private MaterialCommitmentUseProjector sut;

  @ParameterizedTest
  @MethodSource("provideNotNullableParams")
  void should_not_create_material_commitment_if_input_contains_any_pk_null(final String budgetId, final String useId,
      final String materialReferenceId) {
    final var use = Instancio.of(Use.class)
        .set(field("materialReferenceId"), materialReferenceId)
        .set(field("useId"), useId)
        .create();
    final var orderUse = Instancio.of(OrderUse.class)
        .set(field("budgetId"), budgetId)
        .set(field("uses"), List.of(use))
        .create();
    doReturn(new MaterialCommitmentUseCollection(Collections.emptyList()))
        .when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(anyList());

    this.sut.projectFromUseToOrder(orderUse);

    verify(this.materialCommitmentUseRepository, never()).save(any(MaterialCommitmentUse.class));
    verify(this.materialCommitmentUseRepository, never()).delete(any());
  }

  private static Stream<Arguments> provideNotNullableParams() {
    return Stream.of(
        Arguments.of(null, randomString(), randomString()),
        Arguments.of(randomString(), null, randomString()),
        Arguments.of(randomString(), randomString(), null),
        Arguments.of(null, null, null));
  }

  @Test
  void should_create_material_commitment_if_not_exists_single() {
    final var now = OffsetDateTime.MIN;
    final var use = Instancio.create(Use.class);
    final var orderUse = Instancio.of(OrderUse.class)
        .set(field("uses"), List.of(use))
        .create();
    final var filter = MaterialCommitmentUseOrderIdAndOrderLineId.of(
        new MaterialCommitmentUseOrderId(orderUse.orderId()),
        new MaterialCommitmentUseOrderLineId(orderUse.orderLineId()));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(new MaterialCommitmentUseCollection(Collections.emptyList()))
        .when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final var expectedMaterialCommitmentCreated = MaterialCommitmentUse.create(
        new MaterialCommitmentUseId(orderUse.id()),
        new MaterialCommitmentUseMaterialReferenceId(use.materialReferenceId()),
        acceptNullElseMap(use.useId(), MaterialCommitmentUseUseId::new),
        acceptNullElseMap(use.quantity(), MaterialCommitmentUseQuantity::new),
        acceptNullElseMap(orderUse.expectedDate(), MaterialCommitmentUseTimestamp::new),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(orderUse.orderId()),
            acceptNullElseMap(orderUse.budgetId(), MaterialCommitmentUseBudgetId::new),
            acceptNullElseMap(orderUse.orderLineId(), MaterialCommitmentUseOrderLineId::new)),
        acceptNullElseMap(use.status(), MaterialCommitmentUseStatus::new),
        acceptNullElseMap(orderUse.serviceLocalizationId(), MaterialCommitmentUseServiceLocalizationId::new),
        acceptNullElseMap(orderUse.serviceLocalizationType(), MaterialCommitmentUseServiceLocalizationType::new),
        now,
        acceptNullElseMap(use.budgetCycleChangeDate(), MaterialCommitmentUseBudgetCycleChangeRequestedAt::new));

    verify(this.materialCommitmentUseRepository).save(expectedMaterialCommitmentCreated);
  }

  @Test
  void should_update_material_commitment_if_exists_and_it_changes_filter_single() {
    final var now = OffsetDateTime.MIN;

    final var use = Instancio.create(Use.class);
    final var orderUse = Instancio.of(OrderUse.class).set(field("budgetId"),
        UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())).set(field("uses"), List.of(use)).create();

    final var existingMaterialCommitment = new MaterialCommitmentUse(new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(use.materialReferenceId()), new MaterialCommitmentUseUseId(use.useId()),
        new MaterialCommitmentUseQuantity(BigDecimal.ONE), new MaterialCommitmentUseTimestamp(OffsetDateTime.now()),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderUse.orderId()),
            MaterialCommitmentUseBudgetId.of(orderUse.budgetId()), new MaterialCommitmentUseOrderLineId(orderUse.orderLineId())),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseServiceLocalizationType("LARACHA"),
        null, null, MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.MIN), null);

    final var filter = MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderUse.orderId()),
        new MaterialCommitmentUseOrderLineId(orderUse.orderLineId()));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(new MaterialCommitmentUseCollection(List.of(existingMaterialCommitment))).when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final var expectedMaterialCommitmentUpdated = new MaterialCommitmentUse(existingMaterialCommitment.getId(),
        existingMaterialCommitment.getMaterialReferenceId(), existingMaterialCommitment.getUseId(),
        new MaterialCommitmentUseQuantity(use.quantity()), new MaterialCommitmentUseTimestamp(orderUse.expectedDate()),
        new MaterialCommitmentUseOrderLine(existingMaterialCommitment.getOrderLine().orderId(), existingMaterialCommitment.getOrderLine()
            .budgetId(), existingMaterialCommitment.getOrderLine().orderLineId()),
        existingMaterialCommitment.getStatus(), new MaterialCommitmentUseServiceLocalizationId(orderUse.serviceLocalizationId()),
        new MaterialCommitmentUseServiceLocalizationType(orderUse.serviceLocalizationType()),
        new MaterialCommitmentUseBudgetCycleChangeRequestedAt(use.budgetCycleChangeDate()), null,
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.builder().createdAt(OffsetDateTime.MIN).updatedAt(now).build(), null);

    verify(this.materialCommitmentUseRepository).save(expectedMaterialCommitmentUpdated);
  }

  @Test
  void should_throw_when_null_date() {
    final OffsetDateTime now = null;

    final var use = Instancio.create(Use.class);
    final var orderUse = Instancio.of(OrderUse.class).set(field("budgetId"),
        UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())).set(field("uses"), List.of(use)).create();

    final var existingMaterialCommitment = new MaterialCommitmentUse(new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(use.materialReferenceId()), new MaterialCommitmentUseUseId(use.useId()),
        new MaterialCommitmentUseQuantity(BigDecimal.ONE), new MaterialCommitmentUseTimestamp(OffsetDateTime.now()),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderUse.orderId()),
            MaterialCommitmentUseBudgetId.of(orderUse.budgetId()), new MaterialCommitmentUseOrderLineId(orderUse.orderLineId())),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseServiceLocalizationType("LARACHA"),
        null, null, MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.MIN), null);

    final var filter = MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderUse.orderId()),
        new MaterialCommitmentUseOrderLineId(orderUse.orderLineId()));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(new MaterialCommitmentUseCollection(List.of(existingMaterialCommitment))).when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(List.of(filter));

    assertThrows(NullPointerException.class, () -> this.sut.projectFromUseToOrder(orderUse));
  }

  @Test
  void should_not_update_material_commitment_if_exists_and_no_changes_single() {
    final var id = UUID.randomUUID().toString();
    final var orderId = UUID.randomUUID().toString();
    final var orderLineId = UUID.randomUUID().toString();
    final var expectedDate =
        OffsetDateTime.MIN.plusDays(10);
    final var budgetId = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final var materialReferenceId = UUID.randomUUID().toString();
    final var quantity = BigDecimal.TEN;
    final var useId =
        UUID.randomUUID().toString();
    final var serviceLocalizationId = UUID.randomUUID().toString();
    final var serviceLocalizationType =
        "WAREHOUSE";
    final var now = OffsetDateTime.MIN;
    final var useStatus = MaterialCommitmentUseStatusEnum.OPEN.name();

    final var orderUse = new OrderUse(id, orderId, orderLineId, budgetId, serviceLocalizationId, serviceLocalizationType, expectedDate,
        List.of(new Use(materialReferenceId, useStatus, null, useId, quantity)));

    final var existingMaterialCommitment = new MaterialCommitmentUse(new MaterialCommitmentUseId(id),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId), new MaterialCommitmentUseUseId(useId),
        new MaterialCommitmentUseQuantity(quantity), new MaterialCommitmentUseTimestamp(expectedDate),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderId), new MaterialCommitmentUseBudgetId(budgetId),
            new MaterialCommitmentUseOrderLineId(orderLineId)),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
        new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType), null, null,
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.create(OffsetDateTime.MIN), null);
    final var filter = MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderId),
        new MaterialCommitmentUseOrderLineId(orderLineId));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(new MaterialCommitmentUseCollection(List.of(existingMaterialCommitment))).when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    verify(this.materialCommitmentUseRepository, never()).save(any(MaterialCommitmentUse.class));
  }

  @Test
  void should_update_material_commitment_if_exists_and_unmark_processed_single() {
    final var id = UUID.randomUUID().toString();
    final var orderId = UUID.randomUUID().toString();
    final var orderLineId = UUID.randomUUID().toString();
    final var expectedDate =
        OffsetDateTime.now().plusDays(10);
    final var budgetId = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final var materialReferenceId = UUID.randomUUID().toString();
    final var quantity = BigDecimal.TEN;
    final var useId =
        UUID.randomUUID().toString();
    final var serviceLocalizationId = UUID.randomUUID().toString();
    final var serviceLocalizationType =
        "WAREHOUSE";
    final var now = OffsetDateTime.now();
    final var useStatus = MaterialCommitmentUseStatusEnum.OPEN.name();

    final var orderUse = new OrderUse(id, orderId, orderLineId, budgetId, serviceLocalizationId, serviceLocalizationType, expectedDate,
        List.of(new Use(materialReferenceId, useStatus, null, useId, quantity)));

    final var existingMaterialCommitment = new MaterialCommitmentUse(new MaterialCommitmentUseId(id),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId), new MaterialCommitmentUseUseId(useId),
        new MaterialCommitmentUseQuantity(quantity), new MaterialCommitmentUseTimestamp(expectedDate.minusDays(20)),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderId), new MaterialCommitmentUseBudgetId(budgetId),
            new MaterialCommitmentUseOrderLineId(orderLineId)),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
        new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType),
        null, null,
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.create(OffsetDateTime.MIN),
        MaterialCommitmentUseTimestamp.of(OffsetDateTime.now()));
    final var filter =
        MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderId),
            new MaterialCommitmentUseOrderLineId(orderLineId));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(new MaterialCommitmentUseCollection(List.of(existingMaterialCommitment))).when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final var expectedMaterialCommitmentUpdated = new MaterialCommitmentUse(new MaterialCommitmentUseId(id),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId), new MaterialCommitmentUseUseId(useId),
        new MaterialCommitmentUseQuantity(quantity), new MaterialCommitmentUseTimestamp(expectedDate),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderId), new MaterialCommitmentUseBudgetId(budgetId),
            new MaterialCommitmentUseOrderLineId(orderLineId)),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
        new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType), null, null,
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.builder().createdAt(OffsetDateTime.MIN).updatedAt(now).build(), null);

    verify(this.materialCommitmentUseRepository).save(expectedMaterialCommitmentUpdated);
  }

  @Test
  void should_update_material_commitment_if_exists_but_not_unmark_before_processed_single() {
    final var id =
        UUID.randomUUID().toString();
    final var orderId = UUID.randomUUID().toString();
    final var orderLineId = UUID.randomUUID().toString();
    final var expectedDate = OffsetDateTime.now().minusDays(10);
    final var budgetId =
        UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final var materialReferenceId = UUID.randomUUID().toString();
    final var quantity = BigDecimal.TEN;
    final var useId = UUID.randomUUID().toString();
    final var serviceLocalizationId =
        UUID.randomUUID().toString();
    final var serviceLocalizationType = "WAREHOUSE";
    final var now = OffsetDateTime.now();
    final var useStatus = MaterialCommitmentUseStatusEnum.OPEN.name();

    final var orderUse = new OrderUse(id, orderId, orderLineId, budgetId, serviceLocalizationId, serviceLocalizationType, expectedDate,
        List.of(new Use(materialReferenceId, useStatus, null, useId, quantity)));

    final var existingMaterialCommitment = new MaterialCommitmentUse(new MaterialCommitmentUseId(id),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId), new MaterialCommitmentUseUseId(useId),
        new MaterialCommitmentUseQuantity(quantity), new MaterialCommitmentUseTimestamp(expectedDate.minusDays(20)),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderId), new MaterialCommitmentUseBudgetId(budgetId),
            new MaterialCommitmentUseOrderLineId(orderLineId)),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
        new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType), null, null,
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.create(OffsetDateTime.MIN), MaterialCommitmentUseTimestamp.of(now));
    final var filter =
        MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderId),
            new MaterialCommitmentUseOrderLineId(orderLineId));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(new MaterialCommitmentUseCollection(List.of(existingMaterialCommitment))).when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final var expectedMaterialCommitmentUpdated = new MaterialCommitmentUse(new MaterialCommitmentUseId(id),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId), new MaterialCommitmentUseUseId(useId),
        new MaterialCommitmentUseQuantity(quantity), new MaterialCommitmentUseTimestamp(expectedDate),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderId), new MaterialCommitmentUseBudgetId(budgetId),
            new MaterialCommitmentUseOrderLineId(orderLineId)),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
        new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType), null, null,
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.builder().createdAt(OffsetDateTime.MIN).updatedAt(now).build(),
        MaterialCommitmentUseTimestamp.of(now));

    verify(this.materialCommitmentUseRepository).save(expectedMaterialCommitmentUpdated);
  }

  @Test
  void should_update_material_commitment_if_exists_but_not_unmark_equal_processed_single() {
    final var id =
        UUID.randomUUID().toString();
    final var orderId = UUID.randomUUID().toString();
    final var orderLineId = UUID.randomUUID().toString();
    final var expectedDate = OffsetDateTime.now().minusDays(10);
    final var budgetId =
        UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final var materialReferenceId = UUID.randomUUID().toString();
    final var quantity = BigDecimal.TEN;
    final var useId = UUID.randomUUID().toString();
    final var serviceLocalizationId =
        UUID.randomUUID().toString();
    final var serviceLocalizationType = "WAREHOUSE";
    final var now = OffsetDateTime.now();
    final var useStatus = MaterialCommitmentUseStatusEnum.OPEN.name();

    final var orderUse = new OrderUse(id, orderId, orderLineId, budgetId, serviceLocalizationId, serviceLocalizationType, expectedDate,
        List.of(new Use(materialReferenceId, useStatus, null, useId, quantity)));

    final var existingMaterialCommitment =
        new MaterialCommitmentUse(new MaterialCommitmentUseId(id), new MaterialCommitmentUseMaterialReferenceId(materialReferenceId),
            new MaterialCommitmentUseUseId(useId), new MaterialCommitmentUseQuantity(BigDecimal.ONE),
            new MaterialCommitmentUseTimestamp(expectedDate), new MaterialCommitmentUseOrderLine(
                new MaterialCommitmentUseOrderId(orderId), new MaterialCommitmentUseBudgetId(budgetId),
                new MaterialCommitmentUseOrderLineId(orderLineId)),
            MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
            new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
            new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType), null, null,
            MaterialCommitmentUseVersion.firstVersion(), BasicAudit.create(OffsetDateTime.MIN),
            MaterialCommitmentUseTimestamp.of(now));
    final var filter =
        MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderId),
            new MaterialCommitmentUseOrderLineId(orderLineId));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(new MaterialCommitmentUseCollection(List.of(existingMaterialCommitment))).when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final var expectedMaterialCommitmentUpdated = new MaterialCommitmentUse(new MaterialCommitmentUseId(id),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId), new MaterialCommitmentUseUseId(useId),
        new MaterialCommitmentUseQuantity(quantity), new MaterialCommitmentUseTimestamp(expectedDate),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderId), new MaterialCommitmentUseBudgetId(budgetId),
            new MaterialCommitmentUseOrderLineId(orderLineId)),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
        new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType), null, null,
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.builder().createdAt(OffsetDateTime.MIN).updatedAt(now).build(),
        MaterialCommitmentUseTimestamp.of(now));

    verify(this.materialCommitmentUseRepository).save(expectedMaterialCommitmentUpdated);
  }

  @Test
  void should_update_material_commitment_if_exists_but_not_mark_processed_single() {
    final var id = UUID.randomUUID().toString();
    final var orderId = UUID.randomUUID().toString();
    final var orderLineId = UUID.randomUUID().toString();
    final var expectedDate =
        OffsetDateTime.now().plusDays(10);
    final var budgetId = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final var materialReferenceId = UUID.randomUUID().toString();
    final var quantity = BigDecimal.TEN;
    final var useId =
        UUID.randomUUID().toString();
    final var serviceLocalizationId = UUID.randomUUID().toString();
    final var serviceLocalizationType =
        "WAREHOUSE";
    final var now = OffsetDateTime.now();
    final var useStatus = MaterialCommitmentUseStatusEnum.OPEN.name();

    final var orderUse = new OrderUse(id, orderId, orderLineId, budgetId, serviceLocalizationId, serviceLocalizationType, expectedDate,
        List.of(new Use(materialReferenceId, useStatus, null, useId, quantity)));

    final var existingMaterialCommitment = new MaterialCommitmentUse(new MaterialCommitmentUseId(id),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId), new MaterialCommitmentUseUseId(useId),
        new MaterialCommitmentUseQuantity(quantity), new MaterialCommitmentUseTimestamp(expectedDate.plusDays(20)),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderId), new MaterialCommitmentUseBudgetId(budgetId),
            new MaterialCommitmentUseOrderLineId(orderLineId)),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
        new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType), null, null,
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.create(OffsetDateTime.MIN), null);
    final var filter = MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderId),
        new MaterialCommitmentUseOrderLineId(orderLineId));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(new MaterialCommitmentUseCollection(List.of(existingMaterialCommitment))).when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final var expectedMaterialCommitmentUpdated = new MaterialCommitmentUse(new MaterialCommitmentUseId(id),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId), new MaterialCommitmentUseUseId(useId),
        new MaterialCommitmentUseQuantity(quantity), new MaterialCommitmentUseTimestamp(expectedDate),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderId), new MaterialCommitmentUseBudgetId(budgetId),
            new MaterialCommitmentUseOrderLineId(orderLineId)),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
        new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType), null, null,
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.builder().createdAt(OffsetDateTime.MIN).updatedAt(now).build(), null);

    verify(this.materialCommitmentUseRepository).save(expectedMaterialCommitmentUpdated);
  }

  @Test
  void should_update_material_commitment_if_exists_but_not_unmark_nor_modify_processed_single() {
    final var id =
        UUID.randomUUID().toString();
    final var orderId = UUID.randomUUID().toString();
    final var orderLineId = UUID.randomUUID().toString();
    final var expectedDate = OffsetDateTime.now().minusDays(10);
    final var budgetId =
        UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final var materialReferenceId = UUID.randomUUID().toString();
    final var quantity = BigDecimal.TEN;
    final var useId = UUID.randomUUID().toString();
    final var serviceLocalizationId =
        UUID.randomUUID().toString();
    final var serviceLocalizationType = "WAREHOUSE";
    final var now = OffsetDateTime.now();
    final var useStatus = MaterialCommitmentUseStatusEnum.OPEN.name();

    final var orderUse = new OrderUse(id, orderId, orderLineId, budgetId, serviceLocalizationId, serviceLocalizationType, expectedDate,
        List.of(new Use(materialReferenceId, useStatus, null, useId, quantity)));

    final var existingMaterialCommitment =
        new MaterialCommitmentUse(new MaterialCommitmentUseId(id), new MaterialCommitmentUseMaterialReferenceId(materialReferenceId),
            new MaterialCommitmentUseUseId(useId), new MaterialCommitmentUseQuantity(BigDecimal.ONE),
            new MaterialCommitmentUseTimestamp(expectedDate), new MaterialCommitmentUseOrderLine(
                new MaterialCommitmentUseOrderId(orderId), new MaterialCommitmentUseBudgetId(budgetId),
                new MaterialCommitmentUseOrderLineId(orderLineId)),
            MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
            new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
            new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType), null, null,
            MaterialCommitmentUseVersion.firstVersion(), BasicAudit.create(OffsetDateTime.MIN),
            MaterialCommitmentUseTimestamp.of(now.minusDays(10)));
    final var filter =
        MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderId),
            new MaterialCommitmentUseOrderLineId(orderLineId));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(new MaterialCommitmentUseCollection(List.of(existingMaterialCommitment))).when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final var expectedMaterialCommitmentUpdated = new MaterialCommitmentUse(new MaterialCommitmentUseId(id),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId), new MaterialCommitmentUseUseId(useId),
        new MaterialCommitmentUseQuantity(quantity), new MaterialCommitmentUseTimestamp(expectedDate),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderId), new MaterialCommitmentUseBudgetId(budgetId),
            new MaterialCommitmentUseOrderLineId(orderLineId)),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
        new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType), null, null,
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.builder().createdAt(OffsetDateTime.MIN).updatedAt(now).build(),
        MaterialCommitmentUseTimestamp.of(now.minusDays(10)));

    verify(this.materialCommitmentUseRepository).save(expectedMaterialCommitmentUpdated);
  }

  @Test
  void should_update_one_create_one_delete_one() {
    final var orderId = UUID.randomUUID().toString();
    final var orderLineId =
        UUID.randomUUID().toString();

    final var serviceLocalizationId = UUID.randomUUID().toString();
    final var serviceLocalizationType = "WAREHOUSE";
    final var expectedDate = OffsetDateTime.MIN.plusDays(10);
    final var budgetId = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    final var now =
        OffsetDateTime.MIN;

    final var materialReferenceIdA = UUID.randomUUID().toString();
    final var quantityA = BigDecimal.TEN;
    final var useIdA =
        UUID.randomUUID().toString();
    final var useStatusA = MaterialCommitmentUseStatusEnum.OPEN.name();
    final var materialReferenceIdB =
        UUID.randomUUID().toString();
    final var quantityB = BigDecimal.TEN;
    final var useIdB = UUID.randomUUID().toString();
    final var useStatusB = MaterialCommitmentUseStatusEnum.OPEN.name();

    final Use useToUpdate = new Use(materialReferenceIdA, useStatusA, now, useIdA, quantityA);
    final Use useToCreate = new Use(materialReferenceIdB, useStatusB, null, useIdB, quantityB);
    final var orderUse = new OrderUse(UUID.randomUUID().toString(), orderId,
        orderLineId, budgetId, serviceLocalizationId, serviceLocalizationType, expectedDate, List.of(useToUpdate, useToCreate));

    final var existingMaterialCommitmentToUpdate = new MaterialCommitmentUse(new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceIdA), new MaterialCommitmentUseUseId(useIdA),
        new MaterialCommitmentUseQuantity(BigDecimal.ONE), new MaterialCommitmentUseTimestamp(OffsetDateTime.now().plusHours(3)),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderId), new MaterialCommitmentUseBudgetId(budgetId),
            new MaterialCommitmentUseOrderLineId(orderLineId)),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
        new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType), null, null,
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.create(OffsetDateTime.MIN), null);
    final var existingMaterialCommitmentUseToDelete = new MaterialCommitmentUse(new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseQuantity(BigDecimal.TEN), new MaterialCommitmentUseTimestamp(OffsetDateTime.now().plusHours(3)),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderId),
            new MaterialCommitmentUseBudgetId(UUID.randomUUID().toString()), new MaterialCommitmentUseOrderLineId(orderLineId)),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
        new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType), null, null,
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.create(OffsetDateTime.MIN), null);
    final var filter = MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderId),
        new MaterialCommitmentUseOrderLineId(orderLineId));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(new MaterialCommitmentUseCollection(List.of(existingMaterialCommitmentToUpdate, existingMaterialCommitmentUseToDelete)))
        .when(this.materialCommitmentUseRepository).findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final var expectedMaterialCommitmentCreated = MaterialCommitmentUse.create(new MaterialCommitmentUseId(orderUse.id()),
        new MaterialCommitmentUseMaterialReferenceId(useToCreate.materialReferenceId()), acceptNullElseMap(useToCreate.useId(),
            MaterialCommitmentUseUseId::new),
        acceptNullElseMap(useToCreate.quantity(), MaterialCommitmentUseQuantity::new),
        acceptNullElseMap(orderUse.expectedDate(), MaterialCommitmentUseTimestamp::new),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderUse.orderId()),
            acceptNullElseMap(orderUse.budgetId(), MaterialCommitmentUseBudgetId::new),
            acceptNullElseMap(orderUse.orderLineId(), MaterialCommitmentUseOrderLineId::new)),
        acceptNullElseMap(useStatusB,
            MaterialCommitmentUseStatus::new),
        acceptNullElseMap(orderUse.serviceLocalizationId(),
            MaterialCommitmentUseServiceLocalizationId::new),
        acceptNullElseMap(orderUse.serviceLocalizationType(),
            MaterialCommitmentUseServiceLocalizationType::new),
        now,
        acceptNullElseMap(useToCreate.budgetCycleChangeDate(),
            MaterialCommitmentUseBudgetCycleChangeRequestedAt::new));

    final var expectedMaterialCommitmentUpdated = new MaterialCommitmentUse(existingMaterialCommitmentToUpdate.getId(),
        existingMaterialCommitmentToUpdate.getMaterialReferenceId(), existingMaterialCommitmentToUpdate.getUseId(),
        new MaterialCommitmentUseQuantity(quantityA), new MaterialCommitmentUseTimestamp(expectedDate), new MaterialCommitmentUseOrderLine(
            existingMaterialCommitmentToUpdate.getOrderLine().orderId(), existingMaterialCommitmentToUpdate.getOrderLine().budgetId(),
            existingMaterialCommitmentToUpdate.getOrderLine().orderLineId()),
        existingMaterialCommitmentToUpdate.getStatus(), new MaterialCommitmentUseServiceLocalizationId(serviceLocalizationId),
        new MaterialCommitmentUseServiceLocalizationType(serviceLocalizationType),
        acceptNullElseMap(useToUpdate.budgetCycleChangeDate(),
            MaterialCommitmentUseBudgetCycleChangeRequestedAt::new),
        null,
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.builder().createdAt(OffsetDateTime.MIN).updatedAt(now).build(), null);

    verify(this.materialCommitmentUseRepository).save(expectedMaterialCommitmentUpdated);
    verify(this.materialCommitmentUseRepository).save(expectedMaterialCommitmentCreated);
    verify(this.materialCommitmentUseRepository).delete(existingMaterialCommitmentUseToDelete);
  }

  @Test
  void should_create_material_commitment_and_mark_as_budget_cycle_change_pending() {
    final var now = OffsetDateTime.MIN;
    final var use =
        Instancio.of(Use.class).set(field("budgetCycleChangeDate"), now).create();
    final var orderUse = Instancio.of(OrderUse.class)
        .set(field("uses"), List.of(use)).create();
    final var filter = MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderUse.orderId()),
        new MaterialCommitmentUseOrderLineId(orderUse.orderLineId()));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(new MaterialCommitmentUseCollection(Collections.emptyList()))
        .when(this.materialCommitmentUseRepository).findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final var expectedMaterialCommitmentCreated = MaterialCommitmentUse.create(new MaterialCommitmentUseId(orderUse.id()),
        new MaterialCommitmentUseMaterialReferenceId(use.materialReferenceId()),
        acceptNullElseMap(use.useId(), MaterialCommitmentUseUseId::new),
        acceptNullElseMap(use.quantity(), MaterialCommitmentUseQuantity::new), acceptNullElseMap(orderUse.expectedDate(),
            MaterialCommitmentUseTimestamp::new),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderUse.orderId()),
            acceptNullElseMap(orderUse.budgetId(), MaterialCommitmentUseBudgetId::new), acceptNullElseMap(orderUse.orderLineId(),
                MaterialCommitmentUseOrderLineId::new)),
        acceptNullElseMap(use.status(), MaterialCommitmentUseStatus::new),
        acceptNullElseMap(orderUse.serviceLocalizationId(), MaterialCommitmentUseServiceLocalizationId::new),
        acceptNullElseMap(orderUse.serviceLocalizationType(), MaterialCommitmentUseServiceLocalizationType::new),
        now,
        acceptNullElseMap(use.budgetCycleChangeDate(),
            MaterialCommitmentUseBudgetCycleChangeRequestedAt::new));

    final ArgumentCaptor<MaterialCommitmentUse> commitmentUseCaptor = ArgumentCaptor.forClass(MaterialCommitmentUse.class);
    verify(this.materialCommitmentUseRepository).save(commitmentUseCaptor.capture());
    final var savedMCU = commitmentUseCaptor.getValue();
    assertThat(savedMCU).isEqualTo(expectedMaterialCommitmentCreated);
  }

  @Test
  void should_update_material_commitment_and_overwrite_budget_cycle_change_date() {
    final var now = OffsetDateTime.MIN;
    final var use =
        Instancio.of(Use.class).set(field("budgetCycleChangeDate"), OffsetDateTime.MAX).create();
    final var orderUse = Instancio.of(OrderUse.class)
        .set(field("uses"), List.of(use)).create();

    final var existingMaterialCommitment = new MaterialCommitmentUse(new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(use.materialReferenceId()), new MaterialCommitmentUseUseId(use.useId()),
        new MaterialCommitmentUseQuantity(BigDecimal.ONE), new MaterialCommitmentUseTimestamp(OffsetDateTime.now()),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderUse.orderId()),
            MaterialCommitmentUseBudgetId.of(orderUse.budgetId()), new MaterialCommitmentUseOrderLineId(orderUse.orderLineId())),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseServiceLocalizationType("LARACHA"),
        MaterialCommitmentUseBudgetCycleChangeRequestedAt.of(now), MaterialCommitmentUseBudgetCycleChangeExecutedAt.of(now),
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.MIN), null);

    final var filter = MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderUse.orderId()),
        new MaterialCommitmentUseOrderLineId(orderUse.orderLineId()));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(new MaterialCommitmentUseCollection(List.of(existingMaterialCommitment))).when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final var expectedMaterialCommitmentUpdated = new MaterialCommitmentUse(existingMaterialCommitment.getId(),
        existingMaterialCommitment.getMaterialReferenceId(), existingMaterialCommitment.getUseId(),
        new MaterialCommitmentUseQuantity(use.quantity()), new MaterialCommitmentUseTimestamp(orderUse.expectedDate()),
        new MaterialCommitmentUseOrderLine(existingMaterialCommitment.getOrderLine().orderId(), existingMaterialCommitment.getOrderLine()
            .budgetId(), existingMaterialCommitment.getOrderLine().orderLineId()),
        existingMaterialCommitment.getStatus(), new MaterialCommitmentUseServiceLocalizationId(orderUse.serviceLocalizationId()),
        new MaterialCommitmentUseServiceLocalizationType(orderUse.serviceLocalizationType()),
        MaterialCommitmentUseBudgetCycleChangeRequestedAt.of(OffsetDateTime.MAX),
        existingMaterialCommitment.getBudgetCycleChangeExecutedAt(),
        MaterialCommitmentUseVersion.firstVersion(), BasicAudit.builder().createdAt(OffsetDateTime.MIN).updatedAt(now).build(), null);

    final ArgumentCaptor<MaterialCommitmentUse> commitmentUseCaptor = ArgumentCaptor.forClass(MaterialCommitmentUse.class);
    verify(this.materialCommitmentUseRepository).save(commitmentUseCaptor.capture());
    final var savedMCU = commitmentUseCaptor.getValue();
    assertThat(savedMCU).isEqualTo(expectedMaterialCommitmentUpdated);
  }

  @Test
  void should_create_mcu_and_mark_budget_cycle_change_executed_with_use_without_unit_at() {
    final var now = OffsetDateTime.MIN;
    final var use =
        Instancio.of(Use.class).set(field("budgetCycleChangeDate"), now).create();
    final var orderUse = Instancio.of(OrderUse.class)
        .set(field("uses"), List.of(use)).create();
    final var filter = MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderUse.orderId()),
        new MaterialCommitmentUseOrderLineId(orderUse.orderLineId()));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(List.of(use.useId())).when(this.budgetCycleChangeExecutedProperties).getUses();
    doReturn(new MaterialCommitmentUseCollection(Collections.emptyList()))
        .when(this.materialCommitmentUseRepository).findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final ArgumentCaptor<MaterialCommitmentUse> commitmentUseCaptor = ArgumentCaptor.forClass(MaterialCommitmentUse.class);
    verify(this.materialCommitmentUseRepository).save(commitmentUseCaptor.capture());
    final var savedMCU = commitmentUseCaptor.getValue();
    assertThat(savedMCU.getBudgetCycleChangeExecutedAt()).isNotNull();
  }

  @Test
  void should_update_mcu_and_mark_budget_cycle_change_executed_with_use_without_unit_at() {
    final var now = OffsetDateTime.MIN;
    final var use =
        Instancio.of(Use.class).set(field("budgetCycleChangeDate"), now).create();
    final var orderUse = Instancio.of(OrderUse.class)
        .set(field("uses"), List.of(use)).create();

    final var existingMaterialCommitment = new MaterialCommitmentUse(new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(use.materialReferenceId()), new MaterialCommitmentUseUseId(use.useId()),
        new MaterialCommitmentUseQuantity(BigDecimal.ONE), new MaterialCommitmentUseTimestamp(OffsetDateTime.now()),
        new MaterialCommitmentUseOrderLine(new MaterialCommitmentUseOrderId(orderUse.orderId()),
            MaterialCommitmentUseBudgetId.of(orderUse.budgetId()), new MaterialCommitmentUseOrderLineId(orderUse.orderLineId())),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseServiceLocalizationType("LARACHA"),
        MaterialCommitmentUseBudgetCycleChangeRequestedAt.of(now), null,
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.MIN), null);

    final var filter = MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderUse.orderId()),
        new MaterialCommitmentUseOrderLineId(orderUse.orderLineId()));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(List.of(use.useId())).when(this.budgetCycleChangeExecutedProperties).getUses();
    doReturn(new MaterialCommitmentUseCollection(List.of(existingMaterialCommitment))).when(this.materialCommitmentUseRepository)
        .findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final ArgumentCaptor<MaterialCommitmentUse> commitmentUseCaptor = ArgumentCaptor.forClass(MaterialCommitmentUse.class);
    verify(this.materialCommitmentUseRepository).save(commitmentUseCaptor.capture());
    final var savedMCU = commitmentUseCaptor.getValue();
    assertThat(savedMCU.getBudgetCycleChangeExecutedAt()).isNotNull();
  }

  @Test
  void should_create_mcu_and_mark_budget_cycle_change_executed_with_use_and_future_until_at() {
    final var now = OffsetDateTime.MIN;
    final var use =
        Instancio.of(Use.class).set(field("budgetCycleChangeDate"), now).create();
    final var orderUse = Instancio.of(OrderUse.class)
        .set(field("uses"), List.of(use)).create();
    final var filter = MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderUse.orderId()),
        new MaterialCommitmentUseOrderLineId(orderUse.orderLineId()));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(List.of(use.useId())).when(this.budgetCycleChangeExecutedProperties).getUses();
    doReturn(now.plusDays(1)).when(this.budgetCycleChangeExecutedProperties).getUntilAt();
    doReturn(new MaterialCommitmentUseCollection(Collections.emptyList()))
        .when(this.materialCommitmentUseRepository).findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final ArgumentCaptor<MaterialCommitmentUse> commitmentUseCaptor = ArgumentCaptor.forClass(MaterialCommitmentUse.class);
    verify(this.materialCommitmentUseRepository).save(commitmentUseCaptor.capture());
    final var savedMCU = commitmentUseCaptor.getValue();
    assertThat(savedMCU.getBudgetCycleChangeExecutedAt()).isNotNull();
  }

  @Test
  void should_create_mcu_and_not_mark_budget_cycle_change_executed_with_use_and_past_until_at() {
    final var now = OffsetDateTime.MIN.plusDays(1);
    final var use =
        Instancio.of(Use.class).set(field("budgetCycleChangeDate"), now).create();
    final var orderUse = Instancio.of(OrderUse.class)
        .set(field("uses"), List.of(use)).create();
    final var filter = MaterialCommitmentUseOrderIdAndOrderLineId.of(new MaterialCommitmentUseOrderId(orderUse.orderId()),
        new MaterialCommitmentUseOrderLineId(orderUse.orderLineId()));

    doReturn(now).when(this.clockUtils).getCurrentOffsetDateTime();
    doReturn(List.of(use.useId())).when(this.budgetCycleChangeExecutedProperties).getUses();
    doReturn(now.minusDays(1)).when(this.budgetCycleChangeExecutedProperties).getUntilAt();
    doReturn(new MaterialCommitmentUseCollection(Collections.emptyList()))
        .when(this.materialCommitmentUseRepository).findByAnyOrderIdAndOrderLineId(List.of(filter));

    this.sut.projectFromUseToOrder(orderUse);

    final ArgumentCaptor<MaterialCommitmentUse> commitmentUseCaptor = ArgumentCaptor.forClass(MaterialCommitmentUse.class);
    verify(this.materialCommitmentUseRepository).save(commitmentUseCaptor.capture());
    final var savedMCU = commitmentUseCaptor.getValue();
    assertThat(savedMCU.getBudgetCycleChangeExecutedAt()).isNull();
  }

}
