package com.inditex.icdmdemg.application.distributionnominated.query;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.shared.Error.NotFound;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)

class GetDistributionNominatedByIdQueryHandlerTest {

  @Mock
  private DistributionNominatedRepository distributionNominatedRepository;

  @InjectMocks
  private GetDistributionNominatedByIdQueryHandler sut;

  @Test
  void testDebtGetById() {
    final var distributionNominatedId = UUID.randomUUID();

    final var distributionNominated = Instancio.create(DistributionNominated.class);

    when(this.distributionNominatedRepository.findById(new Id(distributionNominatedId)))
        .thenReturn(Optional.of(distributionNominated));

    final var query = new GetDistributionNominatedByIdQuery(distributionNominatedId);
    final var output = this.sut.ask(query);

    assertThat(output).isNotNull();
    assertThat(output.response()).isNotEmpty();
    assertThat(output.error()).isEmpty();
  }

  @Test
  void testDebtGetByNonExistingId() {
    final var distributionNominatedId = UUID.randomUUID();

    when(this.distributionNominatedRepository.findById(new Id(distributionNominatedId)))
        .thenReturn(Optional.empty());

    final var query = new GetDistributionNominatedByIdQuery(distributionNominatedId);
    final var output = this.sut.ask(query);

    assertThat(output).isNotNull();
    assertThat(output.error()).isNotEmpty().hasValue(new NotFound(distributionNominatedId.toString()));
    assertThat(output.response()).isEmpty();
  }
}
