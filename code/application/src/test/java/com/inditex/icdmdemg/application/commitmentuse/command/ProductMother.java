package com.inditex.icdmdemg.application.commitmentuse.command;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Random;
import java.util.UUID;

import com.inditex.icdmdemg.domain.product.Product;
import com.inditex.icdmdemg.domain.product.entity.ProductCampaign;
import com.inditex.icdmdemg.domain.product.entity.ProductColor;
import com.inditex.icdmdemg.domain.product.entity.ProductFamilies;
import com.inditex.icdmdemg.domain.product.entity.ProductId;
import com.inditex.icdmdemg.domain.product.entity.ProductOriginMarket;
import com.inditex.icdmdemg.domain.product.entity.ProductOwners;
import com.inditex.icdmdemg.domain.product.entity.ProductQuality;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.domain.product.entity.ProductSupplier;
import com.inditex.icdmdemg.domain.product.entity.ProductVersion;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;

import org.jspecify.annotations.NonNull;

public interface ProductMother {

  static Builder created() {
    final var now = BasicAudit.create(OffsetDateTime.now());
    return new Builder()
        .productId(randomProductId())
        .referenceId(randomReferenceId())
        .colorId(randomColorId())
        .supplierId(randomSupplierId())
        .campaignId(randomCampaignId())
        .originMarketId(randomOriginMarketId())
        .quality(randomQuality())
        .families(emptyFamilies())
        .owners(emptyOwners())
        .version(new ProductVersion((short) 1))
        .audit(now);
  }

  private static @NonNull ProductId randomProductId() {
    return new ProductId(UUID.randomUUID().toString());
  }

  private static @NonNull ProductReferenceId randomReferenceId() {
    return new ProductReferenceId(UUID.randomUUID().toString());
  }

  private static @NonNull ProductColor randomColorId() {
    return new ProductColor(UUID.randomUUID());
  }

  private static @NonNull ProductSupplier randomSupplierId() {
    return new ProductSupplier(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
  }

  private static @NonNull ProductCampaign randomCampaignId() {
    return new ProductCampaign(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID()));
  }

  private static ProductOriginMarket randomOriginMarketId() {
    return new ProductOriginMarket(UUID.randomUUID());
  }

  private static ProductQuality randomQuality() {
    return new ProductQuality(new Random().nextInt(0, 9000));
  }

  private static ProductOwners emptyOwners() {
    return new ProductOwners(List.of());
  }

  private static ProductFamilies emptyFamilies() {
    return new ProductFamilies(List.of());
  }

  class Builder {
    private ProductId productId;

    private ProductReferenceId referenceId;

    private ProductColor colorId;

    private ProductSupplier supplierId;

    private ProductCampaign campaignId;

    private ProductOriginMarket originMarketId;

    private ProductQuality quality;

    private ProductOwners owners;

    private ProductFamilies families;

    private ProductVersion version;

    private BasicAudit audit;

    public Builder productId(final ProductId productId) {
      this.productId = productId;
      return this;
    }

    public Builder referenceId(final ProductReferenceId referenceId) {
      this.referenceId = referenceId;
      return this;
    }

    public Builder colorId(final ProductColor colorId) {
      this.colorId = colorId;
      return this;
    }

    public Builder supplierId(final ProductSupplier supplierId) {
      this.supplierId = supplierId;
      return this;
    }

    public Builder campaignId(final ProductCampaign campaignId) {
      this.campaignId = campaignId;
      return this;
    }

    public Builder originMarketId(final ProductOriginMarket originMarketId) {
      this.originMarketId = originMarketId;
      return this;
    }

    public Builder quality(final ProductQuality quality) {
      this.quality = quality;
      return this;
    }

    public Builder owners(final ProductOwners owners) {
      this.owners = owners;
      return this;
    }

    public Builder families(final ProductFamilies families) {
      this.families = families;
      return this;
    }

    public Builder version(final ProductVersion version) {
      this.version = version;
      return this;
    }

    public Builder audit(final BasicAudit audit) {
      this.audit = audit;
      return this;
    }

    public Product build() {
      return new Product(
          this.productId,
          this.referenceId,
          this.colorId,
          this.supplierId,
          this.campaignId,
          this.originMarketId,
          this.quality,
          this.families,
          this.owners,
          this.version,
          this.audit);
    }

  }
}
