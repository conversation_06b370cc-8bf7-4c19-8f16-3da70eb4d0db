package com.inditex.icdmdemg.application.use.query;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.Use.Id;
import com.inditex.icdmdemg.domain.use.UseRepository;
import com.inditex.icdmdemg.domain.use.mother.UseMother;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.SliceImpl;

@ExtendWith(MockitoExtension.class)

class GetUseByIdsHandlerTest {

  @Mock
  private UseRepository useRepository;

  @InjectMocks
  private GetUseByIdsQueryHandler sut;

  @Test
  void find_by_ids_return_all_values_if_ids_are_empty() {
    final var use = Instancio.create(Use.class);

    final List<UUID> useUuids = List.of();

    final var page = PageRequest.of(0, 10);
    when(this.useRepository.findByIdsPageable(this.toUseIds(useUuids), page))
        .thenReturn(new SliceImpl<>(List.of(use), page, false));

    final var query = new GetUseByIdsQuery(useUuids, 0, 10);
    final var output = this.sut.ask(query);

    assertThat(output).isNotNull();
    assertThat(output.getContent()).hasSize(1);

    verify(this.useRepository).findByIdsPageable(this.toUseIds(useUuids), page);
  }

  @Test
  void find_by_ids_should_return_values_in_same_order() {
    final var useId1 = UUID.randomUUID();
    final var useId2 = UUID.randomUUID();
    final var useId3 = UUID.randomUUID();
    final var useUuids = List.of(useId1, useId2, useId3);

    final var use1 = UseMother.generateBuild().id(new Use.Id(useId1)).build();
    final var use2 = UseMother.generateBuild().id(new Use.Id(useId2)).build();
    final var use3 = UseMother.generateBuild().id(new Use.Id(useId3)).build();

    final var page = PageRequest.of(0, 10);
    when(this.useRepository.findByIdsPageable(this.toUseIds(useUuids), page))
        .thenReturn(new SliceImpl<>(List.of(use3, use2, use1), page, false));
    final var query = new GetUseByIdsQuery(useUuids, 0, 10);
    final var output = this.sut.ask(query);

    assertThat(output).isNotNull();
    assertThat(output.getContent()).hasSize(3);
    for (int i = 0; i < 3; i++) {
      assertThat(useUuids.get(i)).isEqualTo(output.getContent().get(i).getId().value());
    }

    verify(this.useRepository).findByIdsPageable(this.toUseIds(useUuids), page);
  }

  @Test
  void find_by_ids_should_return_empty() {
    final var useId1 = UUID.randomUUID();
    final var useId2 = UUID.randomUUID();
    final var useUuids = List.of(useId1, useId2);

    final var page = PageRequest.of(0, 10);
    when(this.useRepository.findByIdsPageable(this.toUseIds(useUuids), page))
        .thenReturn(new SliceImpl<>(List.of(), page, false));

    final var query = new GetUseByIdsQuery(useUuids, 0, 10);
    final var output = this.sut.ask(query);

    assertThat(output).isNotNull();
    assertThat(output.getContent()).isEmpty();

    verify(this.useRepository).findByIdsPageable(this.toUseIds(useUuids), page);
  }

  private List<Id> toUseIds(final List<UUID> uuids) {
    return uuids.stream().map(Id::new).toList();
  }
}
