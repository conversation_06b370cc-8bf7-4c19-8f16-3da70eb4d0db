package com.inditex.icdmdemg.application.distributioninner.command;

import static java.text.MessageFormat.format;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ConsumptionFactor;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.UseId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.entity.CompositeKeyInner;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLines;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.domain.product.ProductRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CreateDistributionInnerCommandHandler
    implements ResultCommandHandler<CreateDistributionInnerCommand, CreateDistributionInnerResult> {

  private final DistributionInnerRepository distributionInnerRepository;

  private final ClockUtils clockUtils;

  private final UuidGenerator uuidGenerator;

  private final OrderRepository orderRepository;

  private final ProductRepository productRepository;

  private final Transaction transaction;

  private final EventBus eventBus;

  @Override
  public CreateDistributionInnerResult execute(final CreateDistributionInnerCommand command) {
    final var rootId = new Id(this.uuidGenerator.generate());
    final var referenceId = new ReferenceId(command.request().referenceId());
    final var useId = new UseId(command.request().useId());
    final var budgetCycle = new BudgetCycle(command.request().budgetCycle());
    final var productOrderId = new ProductOrderId(command.request().productOrderId());
    final var productVariantGroupId = new ProductVariantGroupId(command.request().productVariantGroupId());
    final var theoreticalQuantity = new TheoreticalQuantity(command.request().theoreticalQuantity());
    final var requestedQuantity = new RequestedQuantity(command.request().requestedQuantity());
    final var consumptionFactor = new ConsumptionFactor(command.request().consumptionFactor());
    final var triggeredBy = command.request().triggeredBy();
    final var now = this.clockUtils.getCurrentOffsetDateTime();

    final var orderId = new OrderId(command.request().productOrderId().toString());

    final var order = this.orderRepository.find(orderId)
        .orElseThrow(() -> new ErrorException(new BadRequest(format("Order {0} not found", orderId.value()))));

    final var product = this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.value().toString()))
        .orElseThrow(() -> new ErrorException(new BadRequest(format("Product of reference {0} not found", referenceId.value()))));
    final var referenceProductId = new ReferenceProductId(UUID.fromString(product.getProductId().value()));

    this.distributionInnerRepository
        .findByCompositeKey(new CompositeKeyInner(referenceId, useId, budgetCycle, productOrderId, productVariantGroupId))
        .ifPresent(di -> {
          throw new ErrorException(new BadRequest("DistributionInner already exists"));
        });

    final var distributionInner = DistributionInner.create(
        rootId,
        referenceId,
        useId,
        budgetCycle,
        referenceProductId,
        productOrderId,
        productVariantGroupId,
        ProductOrderStatusInfo.of(order),
        theoreticalQuantity,
        consumptionFactor,
        requestedQuantity,
        this.createLines(requestedQuantity.value(), now),
        now,
        triggeredBy,
        command.request().sendToDistribution());

    this.transaction.run(() -> {
      this.distributionInnerRepository.save(distributionInner);
      this.eventBus.send(distributionInner.domainEvents());
    });

    return new CreateDistributionInnerResult(distributionInner.getId());
  }

  private DistributionInnerLines createLines(
      final BigDecimal requestedQuantity,
      @NonNull final OffsetDateTime occurredAt) {
    final var lineId = this.uuidGenerator.generate();
    return new DistributionInnerLines(List.of(
        DistributionInnerLine.create(
            new DistributionInnerLine.Id(lineId),
            new DistributionInnerLine.TheoreticalQuantity(BigDecimal.valueOf(100)),
            new DistributionInnerLine.RequestedQuantity(requestedQuantity),
            occurredAt)));
  }
}
