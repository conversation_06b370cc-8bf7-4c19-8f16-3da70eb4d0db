package com.inditex.icdmdemg.application.distributionsummary.query;

import static com.inditex.icdmdemg.shared.utils.Nullables.safeListWithEmptyList;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.ProductProvisioningSummaryResponse;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.cqrs.core.Query;

import org.jspecify.annotations.NonNull;

public record GetProductProvisioningSummaryQuery(
    ProductProvisioningSummaryRequest productProvisioningSummaryRequest) implements Query<Response<ProductProvisioningSummaryResponse>> {

  public record ProductProvisioningSummaryRequest(
      @NonNull List<UUID> productIds,
      @NonNull List<UUID> referenceIds,
      @NonNull List<String> budgetCycles) {

    public static ProductProvisioningSummaryRequest of(final List<UUID> productIds, final List<UUID> referenceIds,
        final List<String> budgetCycles) {
      return new ProductProvisioningSummaryRequest(
          safeListWithEmptyList(productIds),
          safeListWithEmptyList(referenceIds),
          safeListWithEmptyList(budgetCycles));
    }
  }

  public record ProductProvisioningSummaryResponse(
      @NonNull List<Product> products) {
  }

  public record Product(
      @NonNull UUID productId,
      @NonNull List<Reference> references) {
  }

  public record Reference(
      @NonNull UUID referenceId,
      @NonNull UUID useId,
      @NonNull String budgetCycle,
      @NonNull Provision inner,
      @NonNull Provision nominated) {
  }

  public record Provision(
      @NonNull List<StockLocationQuantities> stockLocationQuantities,
      @NonNull OrderQuantity orderQuantity,
      @NonNull DistributionQuantity distributionQuantity) {
  }

  public record StockLocationQuantities(
      @NonNull String locationId,
      @NonNull String locationType,
      @NonNull BigDecimal stock) {
    public StockLocationQuantities(
        final String locationId,
        final String locationType,
        final BigDecimal stock) {
      this.locationId = locationId;
      this.locationType = locationType;
      this.stock = NumericUtils.roundUpScale2(stock);
    }
  }

  public record OrderQuantity(
      @NonNull BigDecimal ordered,
      @NonNull BigDecimal pending,
      @NonNull BigDecimal entered) {
    public OrderQuantity(
        final BigDecimal ordered,
        final BigDecimal pending,
        final BigDecimal entered) {
      this.ordered = NumericUtils.roundUpScale2(ordered);
      this.pending = NumericUtils.roundUpScale2(pending);
      this.entered = NumericUtils.roundUpScale2(entered);
    }
  }

  public record DistributionQuantity(
      @NonNull BigDecimal requested,
      @NonNull BigDecimal distributed,
      @NonNull BigDecimal pendingAssigned) {
    public DistributionQuantity(
        final BigDecimal requested,
        final BigDecimal distributed) {
      this(
          NumericUtils.roundUpScale2(requested),
          NumericUtils.roundUpScale2(distributed),
          NumericUtils.roundUpScale2(requested.subtract(distributed).max(BigDecimal.ZERO)));
    }
  }
}
