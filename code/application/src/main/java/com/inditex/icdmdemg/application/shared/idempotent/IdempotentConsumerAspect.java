package com.inditex.icdmdemg.application.shared.idempotent;

import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.consumermessage.ConsumerMessage;

import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Aspect
public class IdempotentConsumerAspect {
  private static final String BUILD_CONSUMER_MESSAGE_METHOD_NAME = "buildConsumerMessage";

  private final IdempotentConsumerService idempotentConsumerService;

  @Pointcut("within(@com.inditex.icdmdemg.application.shared.idempotent.EnableIdempotentConsuming *)")
  public void beanAnnotatedWith() {
    // Empty method just for AOP Pointcut
  }

  @Pointcut("execution(public void accept(..))")
  public void publicAcceptMethod() {
    // Empty method just for AOP Pointcut
  }

  @Around("publicAcceptMethod() && beanAnnotatedWith()")
  public void execute(final ProceedingJoinPoint joinPoint) {
    final var logger = LoggerFactory.getLogger(joinPoint.getTarget().getClass());
    this.logEvent(joinPoint, logger)
        .map(event -> this.buildConsumerMessage(joinPoint, event, logger))
        .ifPresent(message -> this.idempotentConsumerService.registerMessageAndProceed(message, joinPoint));
  }

  private Optional<? extends Message<?>> logEvent(final ProceedingJoinPoint joinPoint, final Logger logger) {
    return Stream.of(joinPoint.getArgs())
        .filter(event -> event instanceof Message<?>)
        .findFirst()
        .map(event -> ((Message<?>) event))
        .map(event -> {
          logger.info("Received event {} with payload {}", event.getClass().getSimpleName(), event);
          return event;
        });
  }

  private ConsumerMessage buildConsumerMessage(final ProceedingJoinPoint joinPoint, final Message<?> message, final Logger logger) {
    return this.getConsumerMessageFunction(joinPoint, logger).apply(message);
  }

  private Function<Message<?>, ConsumerMessage> getConsumerMessageFunction(final ProceedingJoinPoint joinPoint, final Logger logger) {
    final Function<Message<?>, ConsumerMessage> consumerMessageBuilder;
    try {
      consumerMessageBuilder =
          (Function) joinPoint.getTarget().getClass().getMethod(BUILD_CONSUMER_MESSAGE_METHOD_NAME).invoke(joinPoint.getTarget());
    } catch (final Exception e) {
      logger.error("Method {} not found in class {}", BUILD_CONSUMER_MESSAGE_METHOD_NAME, joinPoint.getTarget().getClass().getName());
      throw new IdempotentConsumerException(e);
    }
    return consumerMessageBuilder;
  }

}
