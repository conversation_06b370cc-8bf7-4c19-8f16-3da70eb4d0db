package com.inditex.icdmdemg.application.shared.bus;

import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@Primary
public class TransactionalCommandBus implements CommandBus {

  private final CommandBus delegate;

  private final Transaction transaction;

  public TransactionalCommandBus(@Qualifier("notTransactionalCommandBus") final CommandBus delegate, final Transaction transaction) {
    this.delegate = delegate;
    this.transaction = transaction;
  }

  @Override
  @Transactional
  public <T> T execute(final BaseCommand<T> command) {
    return this.transaction.run(() -> this.delegate.execute(command));
  }

}
