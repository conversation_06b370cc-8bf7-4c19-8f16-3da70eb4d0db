package com.inditex.icdmdemg.application.shipmentwarehouse.validator;

import java.util.Objects;

import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCreatedCommand.ShipmentWarehouseCreated;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DistributionInnerIdsValidatorImpl implements ConstraintValidator<DistributionInnerIdsValidator, ShipmentWarehouseCreated> {

  private final DistributionInnerRepository distributionInnerRepository;

  @Override
  public boolean isValid(final ShipmentWarehouseCreated distributionCreated, final ConstraintValidatorContext constraintValidatorContext) {

    return this.distributionInnerRepository.findById(new Id(distributionCreated.distributionInnerId()))
        .flatMap(distributionInner -> distributionInner.lines()
            .findLineById(new DistributionInnerLine.Id(distributionCreated.distributionInnerLineId())))
        .map(line -> Objects.isNull(line.trackingCode()))
        .orElse(false);
  }

}
