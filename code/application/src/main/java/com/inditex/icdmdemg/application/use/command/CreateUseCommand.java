package com.inditex.icdmdemg.application.use.command;

import java.time.OffsetDateTime;
import java.util.List;

import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeCondition;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues.ConditionValue;
import com.inditex.icdmdemg.domain.use.entity.UsePurchasePurposeCondition;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record CreateUseCommand(UseRequest useRequest) implements BaseCommand<CreateUseResult> {

  public record UseRequest(
      UseRequestNames names,
      List<String> assignable,
      String taxonomy,
      String customer,
      List<String> purchaseType,
      List<Condition> conditions,
      String triggeredBy) {

    public record Condition(
        String condition,
        String conditionName,
        List<String> conditionValues) {

      public UsePurchasePurposeCondition toDomain(final OffsetDateTime now) {
        return new UsePurchasePurposeCondition(
            PurchasePurposeCondition.valueOf(this.condition()),
            PurchasePurposeConditionName.valueOf(this.conditionName()),
            new PurchasePurposeConditionValues(this.conditionValues().stream().map(ConditionValue::new).toList()),
            new BasicAudit(now, now));
      }
    }
  }

}
