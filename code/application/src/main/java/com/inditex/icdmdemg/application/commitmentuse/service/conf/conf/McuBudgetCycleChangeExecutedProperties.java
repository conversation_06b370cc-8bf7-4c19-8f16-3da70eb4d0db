package com.inditex.icdmdemg.application.commitmentuse.service.conf.conf;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.annotation.DateTimeFormat;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "mcu.budget-cycle-change-executed-mark")
public class McuBudgetCycleChangeExecutedProperties {

  private List<String> uses = new ArrayList<>();

  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
  private OffsetDateTime untilAt;
}
