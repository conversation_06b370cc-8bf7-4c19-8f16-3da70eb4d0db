package com.inditex.icdmdemg.application.distributionnominated.query;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.shared.Error.NotFound;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetDistributionNominatedByIdQueryHandler
    implements QueryHandler<GetDistributionNominatedByIdQuery, Response<DistributionNominated>> {

  private final DistributionNominatedRepository distributionNominatedRepository;

  @Override
  public Response<DistributionNominated> ask(final GetDistributionNominatedByIdQuery query) {
    return this.distributionNominatedRepository.findById(new Id(query.distributionNominatedId()))
        .map(Response::ofResponse)
        .orElseGet(() -> Response.ofError(new NotFound(query.distributionNominatedId().toString())));
  }

}
