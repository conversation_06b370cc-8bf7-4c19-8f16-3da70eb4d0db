package com.inditex.icdmdemg.application.distributionsummary.query;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityV2Response;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.iopcmmnt.cqrs.core.Query;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record GetUsesAvailabilityV2Query(GetUsesAvailabilityV2Request request) implements Query<Response<UsesAvailabilityV2Response>> {

  public record GetUsesAvailabilityV2Request(
      UUID referenceId,
      UUID productReferenceId,
      String budgetCycle,
      UUID productOrderId,
      Boolean filterNoAvailability,
      List<String> purchaseType,
      String language) {
  }

  public static GetUsesAvailabilityV2Request of(
      final UUID referenceId,
      final UUID productReferenceId,
      final String budgetCycle,
      final UUID productOrderId, final Boolean filterNoAvailability, final List<String> purchaseType, final String language) {
    return new GetUsesAvailabilityV2Request(
        referenceId, productReferenceId, budgetCycle, productOrderId, filterNoAvailability, purchaseType, language);
  }

  public record UsesAvailabilityV2Response(
      List<UsesAvailabilityV2> data) {
  }

  public record UsesAvailabilityV2(
      UUID useId,
      String useName,
      List<String> purchaseTypes,
      List<String> assignable,
      Boolean matchPurchasePurpose,
      UsesAvailabilityTotalNominatedQuantityV2 nominated,
      UsesAvailabilityTotalInnerQuantityV2 inner) {
  }

  public record UsesAvailabilityTotalNominatedQuantityV2(
      BigDecimal totalQuantity) {

  }

  public record UsesAvailabilityTotalInnerQuantityV2(
      BigDecimal totalQuantity) {

  }
}
