package com.inditex.icdmdemg.application.commitmentuse.service;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.OrderUse;
import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.Use;
import com.inditex.icdmdemg.application.commitmentuse.service.conf.conf.McuBudgetCycleChangeExecutedProperties;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeRequestedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseMaterialReferenceId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderIdAndOrderLineId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLine;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLineId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseQuantity;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseServiceLocalizationId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseServiceLocalizationType;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseTimestamp;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseUniqueKey;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseUseId;
import com.inditex.icdmdemg.domain.shared.EntityAndActionResult;
import com.inditex.icdmdemg.domain.shared.EntityAndActionResults;
import com.inditex.icdmdemg.shared.utils.ClockUtils;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MaterialCommitmentUseProjector {

  private final MaterialCommitmentUseRepository materialCommitmentUseRepository;

  private final ClockUtils clockUtils;

  private final McuBudgetCycleChangeExecutedProperties mcuBudgetCycleChangeExecutedProperties;

  public List<EntityAndActionResult<MaterialCommitmentUse>> projectFromUseToOrder(final OrderUse orderUse) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();

    final var orderIdOrderLineIdPairs = List.of(MaterialCommitmentUseOrderIdAndOrderLineId.of(
        new MaterialCommitmentUseOrderId(orderUse.orderId()),
        new MaterialCommitmentUseOrderLineId(orderUse.orderLineId())));
    final var existingMaterialCommitmentWithPairs =
        this.materialCommitmentUseRepository.findByAnyOrderIdAndOrderLineId(orderIdOrderLineIdPairs);

    final var validUses = this.filterValidUses(orderUse);
    final var inputUseCommitmentsWithKey = validUses.stream()
        .collect(Collectors.toMap(
            MaterialCommitmentUseProjector::fromOrderUseUsePair,
            Function.identity()));
    final var existingMaterialCommitmentsWithKey = existingMaterialCommitmentWithPairs.materialCommitmentUses().stream()
        .collect(Collectors.toMap(
            MaterialCommitmentUseUniqueKey::fromMaterialCommitmentUse,
            Function.identity()));

    final var created = this.createOrUpdateProjections(inputUseCommitmentsWithKey, existingMaterialCommitmentsWithKey, now);
    final var deleted = this.deleteProjections(inputUseCommitmentsWithKey, existingMaterialCommitmentsWithKey);

    created.stream()
        .filter(EntityAndActionResult::isActionCreateOrUpdate)
        .map(EntityAndActionResult::entity)
        .forEach(this.materialCommitmentUseRepository::save);

    deleted.stream()
        .filter(EntityAndActionResult::isActionDelete)
        .map(EntityAndActionResult::entity)
        .forEach(this.materialCommitmentUseRepository::delete);

    return EntityAndActionResults.from(created, deleted);
  }

  private @NonNull List<Pair<OrderUse, Use>> filterValidUses(final OrderUse orderUse) {
    return orderUse.uses().stream()
        .map(use -> Pair.of(orderUse, use))
        .filter(pair -> this.isValidOrderUse(pair.getLeft(), pair.getRight()))
        .toList();
  }

  private boolean isValidOrderUse(final OrderUse orderUse, final Use use) {
    return !Objects.isNull(orderUse.budgetId())
        && !Objects.isNull(use.materialReferenceId()) && !Objects.isNull(use.useId());
  }

  private List<EntityAndActionResult<MaterialCommitmentUse>> createOrUpdateProjections(
      final Map<MaterialCommitmentUseUniqueKey, Pair<OrderUse, Use>> orderUseUsePairsWithKey,
      final Map<MaterialCommitmentUseUniqueKey, MaterialCommitmentUse> existingMaterialCommitmentsWithKey,
      final OffsetDateTime now) {

    return orderUseUsePairsWithKey.entrySet().stream()
        .map(entry -> {
          final MaterialCommitmentUse materialCommitment = existingMaterialCommitmentsWithKey.get(entry.getKey());
          final OrderUse orderUse = entry.getValue().getLeft();
          final Use use = entry.getValue().getRight();

          return Optional.ofNullable(materialCommitment)
              .map(matCom -> {
                if (matCom.willBeUpdatedWith(orderUse.expectedDate(), use.quantity(), use.status(), orderUse.serviceLocalizationId(),
                    orderUse.serviceLocalizationType(), use.budgetCycleChangeDate())) {
                  return this.updateProjection(matCom, orderUse, use, now);
                } else {
                  return EntityAndActionResult.kept(materialCommitment);
                }
              })
              .orElseGet(() -> this.createProjection(orderUse, use, now));
        })
        .toList();
  }

  private List<EntityAndActionResult<MaterialCommitmentUse>> deleteProjections(
      final Map<MaterialCommitmentUseUniqueKey, Pair<OrderUse, Use>> orderUseUsePairsWithKey,
      final Map<MaterialCommitmentUseUniqueKey, MaterialCommitmentUse> existingMaterialCommitmentsWithKey) {
    return existingMaterialCommitmentsWithKey.entrySet().stream().map(entry -> {
      if (!orderUseUsePairsWithKey.containsKey(entry.getKey())) {
        return EntityAndActionResult.deleted(entry.getValue());
      }
      return EntityAndActionResult.kept(existingMaterialCommitmentsWithKey.get(entry.getKey()));
    }).toList();
  }

  private EntityAndActionResult<MaterialCommitmentUse> createProjection(final OrderUse orderUse, final Use use,
      final OffsetDateTime now) {
    final var created = MaterialCommitmentUse.create(
        new MaterialCommitmentUseId(orderUse.id()),
        new MaterialCommitmentUseMaterialReferenceId(use.materialReferenceId()),
        new MaterialCommitmentUseUseId(use.useId()),
        acceptNullElseMap(use.quantity(), MaterialCommitmentUseQuantity::new),
        acceptNullElseMap(orderUse.expectedDate(), MaterialCommitmentUseTimestamp::new),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(orderUse.orderId()),
            new MaterialCommitmentUseBudgetId(orderUse.budgetId()),
            new MaterialCommitmentUseOrderLineId(orderUse.orderLineId())),
        acceptNullElseMap(use.status(), MaterialCommitmentUseStatus::new),
        acceptNullElseMap(orderUse.serviceLocalizationId(), MaterialCommitmentUseServiceLocalizationId::new),
        acceptNullElseMap(orderUse.serviceLocalizationType(), MaterialCommitmentUseServiceLocalizationType::new),
        now,
        acceptNullElseMap(use.budgetCycleChangeDate(), MaterialCommitmentUseBudgetCycleChangeRequestedAt::new));
    final var mcuBudgetCycleChanged = this.executeBudgetCycleChange(now, created);
    return EntityAndActionResult.created(mcuBudgetCycleChanged);
  }

  private EntityAndActionResult<MaterialCommitmentUse> updateProjection(final MaterialCommitmentUse materialCommitment,
      final OrderUse orderUse, final Use use, final OffsetDateTime now) {
    final var updated = materialCommitment.modifyWithUseCommitmentOrder(orderUse.expectedDate(),
        use.quantity(), use.status(), orderUse.serviceLocalizationId(),
        orderUse.serviceLocalizationType(), now, use.budgetCycleChangeDate());
    final var mcuBudgetCycleChanged = this.executeBudgetCycleChange(now, updated);
    return EntityAndActionResult.updated(mcuBudgetCycleChanged);
  }

  private static MaterialCommitmentUseUniqueKey fromOrderUseUsePair(final Pair<OrderUse, Use> orderUseUsePair) {
    return new MaterialCommitmentUseUniqueKey(orderUseUsePair.getLeft().orderId(), orderUseUsePair.getLeft().orderLineId(),
        orderUseUsePair.getRight().materialReferenceId(), orderUseUsePair.getLeft().budgetId(), orderUseUsePair.getRight().useId());
  }

  private MaterialCommitmentUse executeBudgetCycleChange(final OffsetDateTime now, final MaterialCommitmentUse mcu) {
    return mcu.isBudgetCycleChangeInProgress() && this.isBudgetCycleChangeConfiguredToExecute(mcu)
        ? mcu.executeBudgetCycleChange(now)
        : mcu;
  }

  private boolean isBudgetCycleChangeConfiguredToExecute(final MaterialCommitmentUse mcu) {
    return this.isBudgetCycleChangeUseConfigured(mcu) && this.isBudgetCycleChangeRequestedAtConfigured(mcu);
  }

  private boolean isBudgetCycleChangeUseConfigured(final MaterialCommitmentUse mcu) {
    return this.mcuBudgetCycleChangeExecutedProperties.getUses().contains(mcu.getUseId().value());
  }

  private boolean isBudgetCycleChangeRequestedAtConfigured(final MaterialCommitmentUse mcu) {
    return this.mcuBudgetCycleChangeExecutedProperties.getUntilAt() == null
        || this.mcuBudgetCycleChangeExecutedProperties.getUntilAt().isAfter(mcu.getBudgetCycleChangeRequestedAt().value());
  }

}
