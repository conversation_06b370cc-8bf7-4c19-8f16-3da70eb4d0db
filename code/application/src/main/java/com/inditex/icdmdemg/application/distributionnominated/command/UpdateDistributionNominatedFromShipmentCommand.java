package com.inditex.icdmdemg.application.distributionnominated.command;

import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedDistributed;
import com.inditex.iopcmmnt.cqrs.core.Command;

import org.jspecify.annotations.NonNull;

public record UpdateDistributionNominatedFromShipmentCommand(
    @NonNull DistributionNominatedDistributed distributionNominatedDistributed) implements Command {
}
