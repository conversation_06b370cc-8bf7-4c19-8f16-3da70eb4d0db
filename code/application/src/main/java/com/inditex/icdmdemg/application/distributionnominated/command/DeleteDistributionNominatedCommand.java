package com.inditex.icdmdemg.application.distributionnominated.command;

import java.util.UUID;

import com.inditex.icdmdemg.application.distributionnominated.validator.DistributionNominatedExistingByIdValidator;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import org.jspecify.annotations.NonNull;

public record DeleteDistributionNominatedCommand(DeleteNominatedRequest deleteNominatedRequest)
    implements
      BaseCommand<DistributionNominated> {

  @DistributionNominatedExistingByIdValidator
  public record DeleteNominatedRequest(
      @NonNull UUID distributionNominatedId,
      @NonNull String triggeredBy) {
  }

}
