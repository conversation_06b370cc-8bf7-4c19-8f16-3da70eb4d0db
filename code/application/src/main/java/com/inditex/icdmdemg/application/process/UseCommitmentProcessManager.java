package com.inditex.icdmdemg.application.process;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.commitmentuse.command.CalculateNominatedProvisionCommand;
import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand;
import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.OrderUse;
import com.inditex.icdmdemg.application.distributionnominated.command.AdjustDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.RegularizeColorAndProjectCommitmentCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.RegularizeColorAndProjectCommitmentCommand.LineToRegularize;
import com.inditex.icdmdemg.application.distributionnominated.command.RegularizeDistributionNominatedBudgetCycleChangeCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedLineCommitmentOrderSupplierCommand;
import com.inditex.icdmdemg.application.distributionnominated.query.GetDistributionNominatedByRegularizationCriteriaQuery;
import com.inditex.icdmdemg.application.distributionnominated.query.GetDistributionNominatedByRegularizationCriteriaQuery.RegularizationCriteria;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;
import com.inditex.iopcmmnt.cqrs.core.QueryBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UseCommitmentProcessManager {

  static final String TRIGGERED_BY = "CommitmentUpdatedEvent";

  private final CommandBus commandBus;

  private final QueryBus queryBus;

  public void execute(final UseCommitmentProcessManagerInput input) {
    final var lineRegularization =
        this.queryBus.ask(new GetDistributionNominatedByRegularizationCriteriaQuery(this.buildRegularizationCriteria(input)));

    final var sharedRawMaterials = lineRegularization.map(
        lineToRegularize -> {
          final var resultProjection = this.commandBus.execute(new RegularizeColorAndProjectCommitmentCommand(
              new LineToRegularize(lineToRegularize.distributionNominatedLineId()),
              input.orderUse()));
          return this.getSharedRawMaterialsFromProjectionResult(resultProjection.materialCommitmentUsesProjected());
        }).orElseGet(
            () -> {
              final var result = this.commandBus.execute(new ProjectMaterialCommitmentUseCommand(input.orderUse()));
              final var rawMaterials = this.getSharedRawMaterialsFromProjectionResult(result.materialCommitmentUses());
              this.commandBus.execute(new AdjustDistributionNominatedCommand(rawMaterials, TRIGGERED_BY));
              return rawMaterials;
            });

    this.commandBus.execute(this.getUpdateDistributionNominatedLineCommitmentOrderSupplierCommand(input));
    this.commandBus.execute(this.getRegularizeDistributionNominatedBudgetCycleChangeCommand(sharedRawMaterials));
    this.commandBus.execute(this.getCalculateNominatedProvisionCommand(sharedRawMaterials));
  }

  private List<SharedRawMaterialNominated> getSharedRawMaterialsFromProjectionResult(
      final List<MaterialCommitmentUse> materialCommitmentUses) {
    return materialCommitmentUses.stream()
        .map(use -> new SharedRawMaterialNominated(
            UUID.fromString(use.getMaterialReferenceId().value()),
            UUID.fromString(use.getUseId().value()),
            use.getOrderLine().budgetId().value()))
        .distinct()
        .toList();
  }

  private RegularizeDistributionNominatedBudgetCycleChangeCommand getRegularizeDistributionNominatedBudgetCycleChangeCommand(
      final List<SharedRawMaterialNominated> sharedRawMaterials) {
    return new RegularizeDistributionNominatedBudgetCycleChangeCommand(sharedRawMaterials, TRIGGERED_BY);
  }

  private CalculateNominatedProvisionCommand getCalculateNominatedProvisionCommand(
      final List<SharedRawMaterialNominated> sharedRawMaterials) {
    return new CalculateNominatedProvisionCommand(sharedRawMaterials, TRIGGERED_BY);
  }

  private UpdateDistributionNominatedLineCommitmentOrderSupplierCommand getUpdateDistributionNominatedLineCommitmentOrderSupplierCommand(
      final UseCommitmentProcessManagerInput input) {
    return new UpdateDistributionNominatedLineCommitmentOrderSupplierCommand(input.orderUse().orderId(),
        input.orderUse().orderLineId(), input.orderUse().serviceLocalizationId(), "UseAssignmentToOrder event");
  }

  private List<RegularizationCriteria> buildRegularizationCriteria(final UseCommitmentProcessManagerInput input) {
    return input.orderUse().uses().stream()
        .map(use -> new RegularizationCriteria(
            new CommitmentOrder(new CommitmentOrder.Id(UUID.fromString(input.orderUse().orderId())),
                new CommitmentOrder.LineId(UUID.fromString(input.orderUse().orderLineId())),
                new CommitmentOrder.SupplierId(input.orderUse.serviceLocalizationId())),
            new ReferenceId(UUID.fromString(use.materialReferenceId())),
            new DistributionNominatedLine.AlternativeReference.RequestedQuantity(use.quantity())))
        .toList();
  }

  public record UseCommitmentProcessManagerInput(OrderUse orderUse) {

  }

}
