package com.inditex.icdmdemg.application.distributionnominated.command;

import static com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.fromDistributions;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.AutoRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.PreselectedRequest;
import com.inditex.icdmdemg.application.distributionnominated.service.AlternativeSharedRawMaterialProvider;
import com.inditex.icdmdemg.application.distributionnominated.service.DistributionNominatedLinesIdentifier;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjustedProvider;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseUniqueKey;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ConsumptionFactor;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.UseId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.DistributionNominatedPlannerType;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan;
import com.inditex.icdmdemg.domain.lock.Lock;
import com.inditex.icdmdemg.domain.lock.LockRepository;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class PatchDistributionNominatedCommandHandler implements ResultCommandHandler<PatchDistributionNominatedCommand, DistributionNominated> {
  private final Transaction transaction;

  private final ClockUtils clockUtils;

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final LockRepository lockRepository;

  private final CommitmentAdjustedProvider commitmentAdjustedProvider;

  private final AlternativeSharedRawMaterialProvider alternativeSharedRawMaterialProvider;

  private final DistributionNominatedLinesIdentifier linesIdentifier;

  private final EventBus eventBus;

  @Override
  public DistributionNominated execute(final PatchDistributionNominatedCommand command) {
    final var patchNominatedRequest = command.patchNominatedRequest();
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var distributionNominated =
        this.distributionNominatedRepository.findById(new Id(patchNominatedRequest.distributionNominatedId()))
            .orElseThrow(() -> new ErrorException(new BadRequest(
                String.format("Distribution nominated not found for Id %s", patchNominatedRequest.distributionNominatedId()))));

    if (distributionNominated.isBudgetCycleChangeInProgress()) {
      throw new ErrorException(
          new BadRequest("Distribution nominated cannot be updated since its budgetCycle change has not been completed"));
    }

    final var sharedRawMaterial = distributionNominated.sharedRawMaterial();
    return this.transaction.run(() -> {
      this.lockRepository.lock(Lock.from(sharedRawMaterial));

      final var distributions = this.distributionNominatedRepository.findBySharedRawMaterial(sharedRawMaterial);

      final var commimentSharedRawMaterialList = this.getEquivalentAssignableSharedRawMaterialList(sharedRawMaterial);
      final var adjustedCommitments = this.commitmentAdjustedProvider.findByAnySharedRawMaterial(commimentSharedRawMaterialList);
      final var concretePlan = this.getConcretePlan(
          distributionNominated.getId(),
          distributionNominated.referenceId(),
          distributionNominated.budgetCycle(),
          distributionNominated.useId(),
          new RequestedQuantity(patchNominatedRequest.requestedQuantity()),
          adjustedCommitments,
          command.plan());
      final var planner = fromDistributions(
          sharedRawMaterial,
          distributions,
          adjustedCommitments,
          concretePlan,
          DistributionNominatedPlannerType.DEFAULT);
      final var linesResult = planner.executePlan().stream()
          .filter(plannedLine -> Objects.equals(plannedLine.entity().rootId(), distributionNominated.getId()))
          .toList();

      final var requestedQuantity = new RequestedQuantity(patchNominatedRequest.requestedQuantity());
      final var theoreticalQuantity = new TheoreticalQuantity(patchNominatedRequest.theoreticalQuantity());
      final var consumptionFactor = new ConsumptionFactor(patchNominatedRequest.consumptionFactor());

      final var updatedLines = this.linesIdentifier.identifyUpdatedLines(
          distributionNominated, requestedQuantity, theoreticalQuantity, linesResult, now);

      final var distributionNominatedPlan = command.plan().getDistributionNominatedPlan();

      final var distributionNominatedUpdated = distributionNominated.update(theoreticalQuantity, consumptionFactor, requestedQuantity,
          updatedLines, distributionNominatedPlan, now, patchNominatedRequest.triggeredBy());

      this.distributionNominatedRepository.save(distributionNominatedUpdated);
      this.eventBus.send(distributionNominatedUpdated.domainEvents());

      return distributionNominated;
    });
  }

  private Plan getConcretePlan(
      final Id rootId,
      final ReferenceId referenceId,
      final BudgetCycle budgetCycle,
      final UseId useId,
      final RequestedQuantity requestedQuantity,
      final List<CommitmentAdjusted> adjustedCommitments,
      final PlanRequest plan) {
    return switch (plan) {
      case AutoRequest() -> new Plan.Auto.Modify(rootId, requestedQuantity);
      case final PreselectedRequest preselected -> {
        final var commmitmentsMap = adjustedCommitments.stream().map(CommitmentAdjusted::materialCommitmentUse)
            .collect(Collectors.toMap(MaterialCommitmentUseUniqueKey::fromMaterialCommitmentUse, Function.identity()));
        final var preselection = preselected.toDomain(rootId, referenceId, budgetCycle, useId, commmitmentsMap);
        yield new Plan.Preselected.Modify(rootId, requestedQuantity, preselection);
      }
    };
  }

  private List<MaterialCommitmentUseSharedRawMaterial> getEquivalentAssignableSharedRawMaterialList(
      final SharedRawMaterialNominated sharedRawMaterial) {
    return this.alternativeSharedRawMaterialProvider.alternatives(MaterialCommitmentUseSharedRawMaterial.of(sharedRawMaterial));
  }

}
