package com.inditex.icdmdemg.application.distributionnominated.command;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedCloseConfigProvider;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateDistributionNominatedStatusCommandHandler implements
    ResultCommandHandler<UpdateDistributionNominatedStatusCommand, List<DistributionNominated>> {

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final OrderRepository orderRepository;

  private final DistributionNominatedCloseConfigProvider distributionNominatedCloseConfigProvider;

  private final Transaction transaction;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  @Override
  public List<DistributionNominated> execute(final UpdateDistributionNominatedStatusCommand command) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();

    final var orderOpt = this.orderRepository.find(new OrderId(command.orderId().toString()));
    if (orderOpt.isEmpty()) {
      return List.of();
    }

    final var orderInfo = ProductOrderStatusInfo.of(orderOpt.get());

    final var productOrderId = new ProductOrderId(UUID.fromString(orderOpt.get().getId().value()));
    final var distributions = this.distributionNominatedRepository.findByProductOrderId(productOrderId);

    for (final var distribution : distributions) {
      distribution.updateWithOrderState(orderInfo, now, command.triggeredBy(), this.distributionNominatedCloseConfigProvider);
      this.transaction.run(() -> {
        this.distributionNominatedRepository.save(distribution);
        this.eventBus.send(distribution.domainEvents());
      });
    }
    return distributions;
  }

}
