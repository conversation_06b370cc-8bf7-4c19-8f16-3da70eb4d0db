package com.inditex.icdmdemg.application.distributioninner.command;

import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateVariantGroupInput;
import com.inditex.iopcmmnt.cqrs.core.Command;

import org.jspecify.annotations.NonNull;

public record UpdateDistributionInnerProductVariantCommand(
    @NonNull UpdateVariantGroupInput variantGroup,
    @NonNull String triggeredBy) implements Command {
}
