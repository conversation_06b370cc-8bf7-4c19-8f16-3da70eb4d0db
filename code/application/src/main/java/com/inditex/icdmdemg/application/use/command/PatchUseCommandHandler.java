package com.inditex.icdmdemg.application.use.command;

import static java.lang.String.format;
import static java.util.Objects.isNull;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.Error.NotFound;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.Use.Id;
import com.inditex.icdmdemg.domain.use.UseRepository;
import com.inditex.icdmdemg.domain.use.entity.AssignableType;
import com.inditex.icdmdemg.domain.use.entity.UseNames;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PatchUseCommandHandler implements CommandHandler<PatchUseCommand> {

  private static final List<String> VALID_LOCALES = List.of("en", "es");

  private final UseRepository useRepository;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  private final Transaction transaction;

  @Override
  public void doHandle(final PatchUseCommand command) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var use = this.evaluateUse(command.useId());

    final var commandAssignable = command.assignable();
    final var assignableType = isNull(commandAssignable) ? use.assignable() : this.evaluateAssignable(commandAssignable);

    final var names = Optional.of(command.names())
        .filter(commandNames -> !commandNames.isEmpty())
        .map(useRequestNames -> this.evaluateNames(now, useRequestNames))
        .orElse(use.names());

    final var updatedUse = use.update(assignableType, names, now, command.triggeredBy());

    this.transaction.run(() -> {
      this.useRepository.save(updatedUse);
      this.eventBus.send(updatedUse.domainEvents());
    });

  }

  private Use evaluateUse(final UUID useId) {
    return this.useRepository.findById(new Id(useId))
        .orElseThrow(() -> new ErrorException(new NotFound(format("Use %s not found", useId))));
  }

  private AssignableType evaluateAssignable(final List<String> assignableValues) {
    try {
      return AssignableType.fromValues(assignableValues);
    } catch (final IllegalArgumentException e) {
      throw new ErrorException(new BadRequest(e.getMessage()));
    }
  }

  private UseNames evaluateNames(final OffsetDateTime now, final UseRequestNames useRequestNames) {
    return new UseNames(useRequestNames.toDomain(now));
  }

}
