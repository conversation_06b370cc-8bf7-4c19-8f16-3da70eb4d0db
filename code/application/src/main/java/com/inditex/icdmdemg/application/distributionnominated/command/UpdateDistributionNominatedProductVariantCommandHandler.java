package com.inditex.icdmdemg.application.distributionnominated.command;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateVariantGroupInput;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateDistributionNominatedProductVariantCommandHandler
    implements CommandHandler<UpdateDistributionNominatedProductVariantCommand> {

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final ClockUtils clockUtils;

  private final Transaction transaction;

  private final EventBus eventBus;

  @Override
  public void doHandle(final UpdateDistributionNominatedProductVariantCommand command) {
    this.processUpdateVariantGroup(command.variantGroup(), command.triggeredBy());
  }

  private void processUpdateVariantGroup(final UpdateVariantGroupInput updateVariantGroupInput, final String triggeredBy) {
    final var variantGroupIdsBySourceIds = updateVariantGroupInput.getVariantGroupIdsBySourceIds();

    if (variantGroupIdsBySourceIds.isEmpty()) {
      return;
    }
    final var distributionsNominatedToUpdate = this.findDistributionsNominatedToUpdate(variantGroupIdsBySourceIds);
    distributionsNominatedToUpdate
        .forEach(distribution -> this.updateDistributionsNominated(distribution, variantGroupIdsBySourceIds, triggeredBy));
  }

  private void updateDistributionsNominated(final DistributionNominated distribution, final Map<UUID, UUID> variantGroupIdsBySourceIds,
      final String triggeredBy) {
    log.info("Distribution Nominated '{}' has update product variant to '{}'", distribution.getId().value(),
        variantGroupIdsBySourceIds.get(distribution.productVariantGroupId().value()));
    final UUID newProductVariantGroupId = variantGroupIdsBySourceIds.get(distribution.productVariantGroupId().value());
    final var updatedDistribution = distribution.updateProductVariantGroupId(
        newProductVariantGroupId,
        this.clockUtils.getCurrentOffsetDateTime(),
        triggeredBy);

    this.transaction.run(() -> {
      this.distributionNominatedRepository.save(updatedDistribution);
      this.eventBus.send(distribution.domainEvents());
    });
  }

  private List<DistributionNominated> findDistributionsNominatedToUpdate(final Map<UUID, UUID> variantGroupIdsBySourceIds) {
    final List<ProductVariantGroupId> variantGroupSourceIds = variantGroupIdsBySourceIds.keySet().stream()
        .map(ProductVariantGroupId::new)
        .toList();

    return this.distributionNominatedRepository.findByProductVariantGroupIds(variantGroupSourceIds);
  }
}
