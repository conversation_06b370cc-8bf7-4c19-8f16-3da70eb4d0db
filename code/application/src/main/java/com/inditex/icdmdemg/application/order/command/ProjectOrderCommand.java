package com.inditex.icdmdemg.application.order.command;

import java.time.OffsetDateTime;
import java.util.List;

import com.inditex.iopcmmnt.cqrs.core.Command;

import org.jspecify.annotations.NonNull;

public record ProjectOrderCommand(ProductOrderCreatedOrUpdated productOrderEvent) implements Command {
  public record ProductOrderCreatedOrUpdated(@NonNull String id, @NonNull String statusKey, @NonNull String supplierId,
      @NonNull List<ProductOrderLine> orderLines) {

  }

  public record ProductOrderLine(
      @NonNull String id,
      String measuringUnitsId,
      @NonNull OffsetDateTime serviceDate,
      String budgetId,
      @NonNull List<ProductOrderQuantityDetail> productOrderQuantityDetails) {
  }

  public record ProductOrderQuantityDetail(@NonNull Integer quantity, @NonNull String referenceId) {
  }
}
