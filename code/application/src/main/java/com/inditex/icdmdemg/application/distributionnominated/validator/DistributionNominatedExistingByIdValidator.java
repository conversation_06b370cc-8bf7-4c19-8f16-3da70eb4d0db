package com.inditex.icdmdemg.application.distributionnominated.validator;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Target({TYPE, FIELD, PARAMETER})
@Retention(RUNTIME)
@Constraint(validatedBy = DistributionNominatedExistingByIdValidatorImpl.class)
public @interface DistributionNominatedExistingByIdValidator {

  String message() default "DistributionNominated doesn't exist by given UUID.";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};

}
