package com.inditex.icdmdemg.application.distributionnominated.query;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.application.distributionnominated.query.GetAvailableCommitmentsQuery.AvailableCommitment;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjustedProvider;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.Id;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetAvailableCommitmentsQueryHandler
    implements QueryHandler<GetAvailableCommitmentsQuery, Slice<AvailableCommitment>> {

  private final CommitmentAdjustedProvider commitmentAdjustedProvider;

  private final DistributionNominatedRepository distributionNominatedRepository;

  @Override
  public Slice<AvailableCommitment> ask(final GetAvailableCommitmentsQuery query) {
    final var adjustedCommitments = this.commitmentAdjustedProvider.findBySharedRawMaterialPaged(
        MaterialCommitmentUseSharedRawMaterial.of(query.useId().toString(), query.referenceId().toString(), query.budgetCycle()),
        query.pageable());

    final var dns = this.distributionNominatedRepository
        .findBySharedRawMaterial(new SharedRawMaterialNominated(query.referenceId(), query.useId(), query.budgetCycle()));

    final var requestedQuantityInLinesByCommitment = this.getRequestedQuantityInLinesGroupedByCommitment(dns);

    final var availableCommitmentsAdjustedWithLines = adjustedCommitments.stream()
        .map(adjustment -> this.adjustCommitmentIfPresentInLines(adjustment, requestedQuantityInLinesByCommitment))
        .map(this::getAvailableCommitmentFromAdjustedCommitment)
        .toList();

    return new SliceImpl<>(availableCommitmentsAdjustedWithLines, query.pageable(), adjustedCommitments.hasNext());
  }

  private Map<CommitmentOrder, RequestedQuantity> getRequestedQuantityInLinesGroupedByCommitment(final List<DistributionNominated> dns) {
    return dns.stream()
        .flatMap(nominated -> nominated.lines().value().stream())
        .collect(Collectors.groupingBy(
            DistributionNominatedLine::commitmentOrder,
            Collectors.reducing(
                new RequestedQuantity(BigDecimal.ZERO),
                DistributionNominatedLine::requestedQuantity,
                RequestedQuantity::add)));
  }

  private CommitmentAdjusted adjustCommitmentIfPresentInLines(
      final CommitmentAdjusted commitmentAdjusted,
      final Map<CommitmentOrder, RequestedQuantity> requestedQuantityInLinesByCommitment) {

    final BigDecimal initialQuantity = commitmentAdjusted.materialCommitmentUse().getStatus().isOpen()
        ? commitmentAdjusted.adjustedQuantity()
        : BigDecimal.ZERO;

    final CommitmentOrder commitmentOrder = this.buildCommitmentOrderFromMaterialCommitmentUse(
        commitmentAdjusted.materialCommitmentUse());

    final BigDecimal adjustedQuantity = requestedQuantityInLinesByCommitment.containsKey(commitmentOrder)
        ? initialQuantity.subtract(requestedQuantityInLinesByCommitment.get(commitmentOrder).value()).max(BigDecimal.ZERO)
        : initialQuantity;

    return CommitmentAdjusted.of(commitmentAdjusted.materialCommitmentUse(), adjustedQuantity);
  }

  private CommitmentOrder buildCommitmentOrderFromMaterialCommitmentUse(final MaterialCommitmentUse mcu) {
    return new CommitmentOrder(new Id(UUID.fromString(mcu.getOrderLine().orderId().value())),
        new LineId(UUID.fromString(mcu.getOrderLine().orderLineId().value())),
        new SupplierId(mcu.getServiceLocalizationId().value()));
  }

  private AvailableCommitment getAvailableCommitmentFromAdjustedCommitment(final CommitmentAdjusted commitmentAdjusted) {
    return new AvailableCommitment(
        UUID.fromString(commitmentAdjusted.materialCommitmentUse().getOrderLine().orderId().value()),
        UUID.fromString(commitmentAdjusted.materialCommitmentUse().getOrderLine().orderLineId().value()),
        commitmentAdjusted.materialCommitmentUse().getExpectedDate().value(),
        commitmentAdjusted.materialCommitmentUse().getServiceLocalizationId().value(),
        commitmentAdjusted.adjustedQuantity());
  }

}
