package com.inditex.icdmdemg.application.use.query;

import static com.inditex.icdmdemg.shared.utils.Nullables.safeListWithEmptyList;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.iopcmmnt.cqrs.core.Query;

import org.jspecify.annotations.NonNull;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;

public record GetUseByIdsQuery(
    @NonNull List<UUID> useIds,
    @NonNull PageRequest pageable)
    implements
      Query<Slice<Use>> {

  public GetUseByIdsQuery(final List<UUID> useIds, final Integer page, final Integer size) {
    this(
        safeListWithEmptyList(useIds),
        PageRequest.of(page, size));
  }
}
