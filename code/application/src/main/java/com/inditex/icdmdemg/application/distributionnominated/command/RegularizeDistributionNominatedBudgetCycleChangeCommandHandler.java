package com.inditex.icdmdemg.application.distributionnominated.command;

import static com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.fromDistributions;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.application.distributionnominated.service.DistributionNominatedLinesIdentifier;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjustedProvider;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse.MaterialCommitmentUseCompositeId;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLineId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.DistributionNominatedPlannerType;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan.Adjust;
import com.inditex.icdmdemg.domain.shared.EntityAndActionResult;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RegularizeDistributionNominatedBudgetCycleChangeCommandHandler
    implements CommandHandler<RegularizeDistributionNominatedBudgetCycleChangeCommand> {

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final CommitmentAdjustedProvider commitmentAdjustedProvider;

  private final DistributionNominatedLinesIdentifier linesIdentifier;

  private final MaterialCommitmentUseRepository materialCommitmentUseRepository;

  private final ClockUtils clockUtils;

  private final Transaction transaction;

  private final EventBus eventBus;

  @Override
  public void doHandle(final RegularizeDistributionNominatedBudgetCycleChangeCommand command) {
    final var sharedRawMaterialNominateds = command.sharedRawMaterialNominated();
    final var distributions = this.distributionNominatedRepository.findByAnySharedRawMaterial(sharedRawMaterialNominateds);
    final var adjustedCommitments = this.commitmentAdjustedProvider
        .findByAnySharedRawMaterial(sharedRawMaterialNominateds.stream().map(MaterialCommitmentUseSharedRawMaterial::of).toList());
    final var distributionsGroupedBySharedRawMaterial = distributions.stream()
        .collect(Collectors.groupingBy(DistributionNominated::sharedRawMaterial));
    final var adjustedCommitmentsGroupedBySharedRawMaterial = adjustedCommitments.stream()
        .collect(Collectors.groupingBy(CommitmentAdjusted::sharedRawMaterial));

    sharedRawMaterialNominateds.forEach(sharedRawMaterialNominated -> this.adjustSharedRawMaterial(
        command, sharedRawMaterialNominated, distributionsGroupedBySharedRawMaterial, adjustedCommitmentsGroupedBySharedRawMaterial));
  }

  private void adjustSharedRawMaterial(
      final RegularizeDistributionNominatedBudgetCycleChangeCommand command,
      final SharedRawMaterialNominated sharedRawMaterial,
      final Map<SharedRawMaterialNominated, List<DistributionNominated>> distributionsGroupedBySharedRawMaterial,
      final Map<MaterialCommitmentUseSharedRawMaterial, List<CommitmentAdjusted>> adjustedCommitmentsGroupedBySharedRawMaterial) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var distributions = distributionsGroupedBySharedRawMaterial.getOrDefault(sharedRawMaterial, List.of());
    final var adjustedCommitments = adjustedCommitmentsGroupedBySharedRawMaterial.getOrDefault(
        MaterialCommitmentUseSharedRawMaterial.of(sharedRawMaterial), List.of());

    final var planner =
        fromDistributions(sharedRawMaterial, distributions, adjustedCommitments, new Adjust(),
            DistributionNominatedPlannerType.BUDGET_CYCLE_CHANGE);
    final var adjustedLines = planner.executePlan();

    final var createdOrUpdatedAdjustedLines = adjustedLines.stream().filter(EntityAndActionResult::isActionCreateOrUpdate).toList();
    if (createdOrUpdatedAdjustedLines.isEmpty()) {
      return;
    }

    final var identifiedLines = this.linesIdentifier.identifyAdjustedLines(distributions, adjustedLines, now);

    this.updateDistributions(distributions, identifiedLines, now, command.triggeredBy())
        .forEach(
            distributionNominated -> this.transaction.run(() -> {
              this.distributionNominatedRepository.save(distributionNominated);
              this.eventBus.send(distributionNominated.domainEvents());
            }));

    final List<MaterialCommitmentUseCompositeId> usedCommitmentsCompositeIds = createdOrUpdatedAdjustedLines.stream()
        .map(EntityAndActionResult::entity)
        .map(line -> MaterialCommitmentUseCompositeId.of(sharedRawMaterial,
            new MaterialCommitmentUseOrderId(line.commitmentOrder().id().value().toString()),
            new MaterialCommitmentUseOrderLineId(line.commitmentOrder().lineId().value().toString())))
        .toList();
    final var mcusToExecuteBudgetCycleChange = this.materialCommitmentUseRepository.findByAnyCompositeId(usedCommitmentsCompositeIds);

    final var mcusExecutedBudgetCycleChange = mcusToExecuteBudgetCycleChange.materialCommitmentUses().stream()
        .map(mcu -> mcu.executeBudgetCycleChange(now))
        .toList();

    this.materialCommitmentUseRepository.saveAll(mcusExecutedBudgetCycleChange);
  }

  private List<DistributionNominated> updateDistributions(
      final List<DistributionNominated> distributions,
      final Map<Id, List<DistributionNominatedLine>> identifiedLines,
      final OffsetDateTime occurredOn, final String triggeredBy) {
    return distributions.stream()
        .map(distribution -> {
          final var lines = identifiedLines.getOrDefault(distribution.getId(), List.of());
          final var distributionAdjusted = distribution.adjust(lines, occurredOn, triggeredBy);
          return distributionAdjusted.recalculateBudgetCycleChangePendingQuantity(occurredOn, triggeredBy);
        })
        .toList();
  }

}
