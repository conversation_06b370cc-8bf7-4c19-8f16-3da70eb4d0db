package com.inditex.icdmdemg.application.distributionsummary.query;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery.DistributionSummaryResponse;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.iopcmmnt.cqrs.core.Query;

import org.jspecify.annotations.NonNull;

public record GetDistributionSummaryQuery(
    DistributionSummaryRequest DistributionSummaryRequest) implements Query<Response<DistributionSummaryResponse>> {

  public record DistributionSummaryRequest(
      @NonNull UUID referenceId,
      @NonNull UUID useId,
      @NonNull String budgetCycle) {
  }

  public record DistributionAllocated(
      UUID productOrderId,
      UUID productVariantGroupId,
      BigDecimal requestedQuantity,
      BigDecimal distributedQuantity) {
  }

  public record DistributionSummaryResponse(
      UUID referenceId,
      UUID useId,
      String budgetCycle,
      List<DistributionAllocated> distributionNominatesAllocated,
      List<DistributionAllocated> distributionInners) {
  }
}
