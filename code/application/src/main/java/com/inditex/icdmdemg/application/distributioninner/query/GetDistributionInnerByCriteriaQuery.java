package com.inditex.icdmdemg.application.distributioninner.query;

import static com.inditex.icdmdemg.shared.utils.Nullables.safeListWithEmptyList;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.iopcmmnt.cqrs.core.Query;

import org.jspecify.annotations.NonNull;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;

public record GetDistributionInnerByCriteriaQuery(
    @NonNull List<UUID> referenceIds,
    @NonNull List<UUID> productOrderIds,
    @NonNull List<String> budgetCycles,
    @NonNull List<String> status,
    @NonNull PageRequest pageable)
    implements
      Query<Slice<DistributionInner>> {

  public GetDistributionInnerByCriteriaQuery(final List<UUID> referenceIds, final List<UUID> productOrderIds,
      final List<String> budgetCycles, final List<String> status, final Integer page, final Integer size) {
    this(
        safeListWithEmptyList(referenceIds),
        safeListWithEmptyList(productOrderIds),
        safeListWithEmptyList(budgetCycles),
        safeListWithEmptyList(status),
        PageRequest.of(page, size));
  }
}
