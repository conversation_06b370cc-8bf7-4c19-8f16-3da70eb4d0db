package com.inditex.icdmdemg.application.shipmentcommitment.command;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCreatedCommand.ShipmentCreated;
import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitment;
import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitmentRepository;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentDistributedQuantity;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentDistributionNominatedLineId;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentId;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentMeasurementUnitId;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentQuantity;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentTimeStamp;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class ProjectCommitmentShipmentCreatedCommandHandler
    implements ResultCommandHandler<ProjectCommitmentShipmentCreatedCommand, ShipmentCommitment> {

  private final ClockUtils clock;

  private final ShipmentCommitmentRepository repository;

  private final Transaction transaction;

  @Override
  public ShipmentCommitment execute(final ProjectCommitmentShipmentCreatedCommand command) {
    final ShipmentCreated shipmentCommitmentCreated = command.shipmentCreated();

    return this.repository.findById(new ShipmentCommitmentId(shipmentCommitmentCreated.id()))
        .orElseGet(() -> this.create(shipmentCommitmentCreated));
  }

  private ShipmentCommitment create(final ShipmentCreated shipmentCreated) {
    final var shipmentCommitment = ShipmentCommitment.create(
        new ShipmentCommitmentId(shipmentCreated.id()),
        new ShipmentCommitmentDistributionNominatedLineId(shipmentCreated.distributionNominatedLineId()),
        new ShipmentCommitmentTimeStamp(shipmentCreated.sentDate()),
        new ShipmentCommitmentDistributedQuantity(new ShipmentCommitmentQuantity(shipmentCreated.quantity()),
            acceptNullElseMap(shipmentCreated.measurementUnitId(), ShipmentCommitmentMeasurementUnitId::new)),
        this.clock.getCurrentOffsetDateTime());
    return this.transaction.run(() -> this.repository.save(shipmentCommitment));
  }

}
