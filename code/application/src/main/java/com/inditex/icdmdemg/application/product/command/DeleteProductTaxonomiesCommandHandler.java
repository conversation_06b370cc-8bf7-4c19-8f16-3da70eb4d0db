package com.inditex.icdmdemg.application.product.command;

import com.inditex.icdmdemg.domain.product.ProductTaxonomyRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductTaxonomy.ProductId;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DeleteProductTaxonomiesCommandHandler implements CommandHandler<DeleteProductTaxonomiesCommand> {

  private final ProductTaxonomyRepository productTaxonomyRepository;

  private final ClockUtils clockUtils;

  @Override
  public void doHandle(final DeleteProductTaxonomiesCommand command) {
    this.productTaxonomyRepository.deleteAllByProductId(new ProductId(command.deletedProduct().deletedProductId()));
  }
}
