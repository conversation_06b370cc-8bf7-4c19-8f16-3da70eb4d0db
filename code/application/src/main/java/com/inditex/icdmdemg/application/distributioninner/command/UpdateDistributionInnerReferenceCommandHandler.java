package com.inditex.icdmdemg.application.distributioninner.command;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateReferenceInput;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateDistributionInnerReferenceCommandHandler
    implements CommandHandler<UpdateDistributionInnerReferenceCommand> {

  private final DistributionInnerRepository distributionInnerRepository;

  private final ClockUtils clockUtils;

  private final Transaction transaction;

  private final EventBus eventBus;

  @Override
  public void doHandle(final UpdateDistributionInnerReferenceCommand command) {
    this.processUpdateDistributionInnerReference(command.updateReferenceInput(), command.triggeredBy());
  }

  private void processUpdateDistributionInnerReference(final UpdateReferenceInput referenceInput, final String triggeredBy) {
    final var referenceIdsBySourceIds = referenceInput.getReferenceIdsBySourceIds();

    if (referenceIdsBySourceIds.isEmpty()) {
      return;
    }
    if (UpdateReferenceInput.EXCLUDE_TAXONOMY.equalsIgnoreCase(referenceInput.taxonomy())) {
      return;
    }
    final var distributionsInnerToUpdate = this.findDistributionsInnerToUpdate(referenceIdsBySourceIds);
    distributionsInnerToUpdate
        .forEach(distribution -> this.updateDistributionsInner(distribution, referenceIdsBySourceIds, referenceInput.productId(),
            triggeredBy));
  }

  private void updateDistributionsInner(final DistributionInner distribution, final Map<UUID, UUID> referenceIdsBySourceIds,
      final UUID productId, final String triggeredBy) {
    log.info("Distribution Inner '{}' has update reference to '{}' and productId to {}", distribution.getId().value(),
        referenceIdsBySourceIds.get(distribution.referenceId().value()), productId);
    final UUID newReferenceId = referenceIdsBySourceIds.get(distribution.referenceId().value());

    final var updatedDistribution = distribution.updateReferencesId(
        new ReferenceId(newReferenceId),
        new ReferenceProductId(productId),
        this.clockUtils.getCurrentOffsetDateTime(),
        triggeredBy);

    this.transaction.run(() -> {
      this.distributionInnerRepository.save(updatedDistribution);
      this.eventBus.send(distribution.domainEvents());
    });
  }

  private List<DistributionInner> findDistributionsInnerToUpdate(final Map<UUID, UUID> referenceIdsBySourceIds) {
    final List<ReferenceId> referenceSourceIds = referenceIdsBySourceIds.keySet().stream().map(ReferenceId::new).toList();

    return this.distributionInnerRepository.findByReferenceIds(referenceSourceIds);
  }

}
