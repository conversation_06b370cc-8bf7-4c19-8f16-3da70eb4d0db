package com.inditex.icdmdemg.application.distributionsummary.query;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.mapping;
import static java.util.stream.Collectors.toList;

import static com.inditex.icdmdemg.shared.utils.CollectionsUtils.mapToList;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.DistributionQuantity;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.OrderQuantity;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.Product;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.ProductProvisioningSummaryRequest;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.ProductProvisioningSummaryResponse;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.Reference;
import com.inditex.icdmdemg.application.distributionsummary.query.GetProductProvisioningSummaryQuery.StockLocationQuantities;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerSummaryDTO;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.LocalizationId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvisionRepository;
import com.inditex.icdmdemg.domain.shared.Error.MessageError;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseRepository;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.OrderUseType;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.ProductId;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.ProductReferenceId;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.StateEnum;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseRepository;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO.LocalizationType;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetProductProvisioningSummaryQueryHandler
    implements
    QueryHandler<GetProductProvisioningSummaryQuery, Response<ProductProvisioningSummaryResponse>> {

  private static final String ERROR_MESSAGE_FOR_INVALID_LISTS_PARAMS =
      "At least one materialReferenceId or productOrderId must be provided, but not at the same time.";

  private static final String COMMITMENT_SUPPLIER = "COMMITMENT_SUPPLIER";

  private final NominatedProvisionRepository nominatedProvisionRepository;

  private final DistributionInnerRepository distributionInnerRepository;

  private final OrderUseRepository orderUseRepository;

  private final ProductUseRepository productUseRepository;

  @Override
  public Response<ProductProvisioningSummaryResponse> ask(
      final GetProductProvisioningSummaryQuery summaryQuery) {

    final ProductProvisioningSummaryRequest request = summaryQuery.productProvisioningSummaryRequest();

    if (request.referenceIds().isEmpty() == request.productIds().isEmpty()) {
      return Response.ofError(new MessageError(ERROR_MESSAGE_FOR_INVALID_LISTS_PARAMS));
    }

    final var nominatedProvisions = this.getNominatedProvisions(request);
    final var innerSummaries = this.getInnerSummaries(request);
    final var orderUseSummaries = this.getOrderUseSummaries(request);
    final var productUseSummaries = this.getProductUseSummaries(request);

    final Map<ProductSharedRawMaterial, ProductProvisionSummaryDataProvider> summaryDataProviderMap =
        this.buildSummaryDataProvider(nominatedProvisions, innerSummaries, orderUseSummaries, productUseSummaries);

    final List<Product> productList = this.getProducts(summaryDataProviderMap);

    final var response = new ProductProvisioningSummaryResponse(productList);
    return Response.ofResponse(response);
  }

  private List<Product> getProducts(Map<ProductSharedRawMaterial, ProductProvisionSummaryDataProvider> summaryDataProviderMap) {
    final Map<UUID, List<Reference>> referencesByProduct =
        summaryDataProviderMap.entrySet().stream()
            .collect(groupingBy(
                e -> e.getKey().productId(),
                mapping(e -> this.buildReference(e.getKey(), e.getValue()), toList())));

    return referencesByProduct.entrySet().stream()
        .map(entry -> new Product(
            entry.getKey(),
            entry.getValue()))
        .toList();
  }

  private List<ProductUseSummaryDTO> getProductUseSummaries(ProductProvisioningSummaryRequest request) {
    return this.productUseRepository
        .findProductUseSummaries(
            mapToList(request.productIds(), ProductUseSummaryDTO.ProductId::new),
            mapToList(request.referenceIds(), ProductUseSummaryDTO.ReferenceId::new),
            mapToList(request.budgetCycles(), ProductUseSummaryDTO.BudgetCycle::new));
  }

  private List<OrderUseSummaryDTO> getOrderUseSummaries(ProductProvisioningSummaryRequest request) {
    return this.orderUseRepository
        .findOrderUseSummaries(
            mapToList(request.productIds(), ProductId::new),
            mapToList(request.referenceIds(), ProductReferenceId::new),
            mapToList(request.budgetCycles(), OrderUseSummaryDTO.BudgetCycle::new),
            List.of(StateEnum.CANCELED));
  }

  private List<DistributionInnerSummaryDTO> getInnerSummaries(ProductProvisioningSummaryRequest request) {
    return this.distributionInnerRepository
        .findDistributionInnerSummaries(
            mapToList(request.productIds(), ReferenceProductId::new),
            mapToList(request.referenceIds(), DistributionInner.ReferenceId::new),
            mapToList(request.budgetCycles(), BudgetCycle::new),
            List.of(DistributionInnerStatus.CANCELED));
  }

  private List<NominatedProvision> getNominatedProvisions(ProductProvisioningSummaryRequest request) {
    return this.nominatedProvisionRepository
        .findByProductsAndReferencesAndBudgets(
            mapToList(request.productIds(), uuid -> new NominatedProvision.ProductId(String.valueOf(uuid))),
            mapToList(request.referenceIds(), uuid -> new NominatedProvision.ReferenceId(String.valueOf(uuid))),
            mapToList(request.budgetCycles(), NominatedProvision.BudgetId::new))
        .nominatedProvisions();
  }

  private Map<ProductSharedRawMaterial, ProductProvisionSummaryDataProvider> buildSummaryDataProvider(
      List<NominatedProvision> nominatedProvisions, List<DistributionInnerSummaryDTO> innerSummaries,
      List<OrderUseSummaryDTO> orderUseSummaries, List<ProductUseSummaryDTO> productUseSummaries) {
    final Map<ProductSharedRawMaterial, ProductProvisionSummaryDataProvider> summaryDataProviderMap = new HashMap<>();

    for (final var nominatedProvision : nominatedProvisions) {
      final ProductSharedRawMaterial key = this.toKey(nominatedProvision);
      final ProductProvisionSummaryDataProvider data =
          summaryDataProviderMap.computeIfAbsent(key, k -> new ProductProvisionSummaryDataProvider());
      data.addNominatedSummary(nominatedProvision);
    }

    for (final var innerSummary : innerSummaries) {
      final ProductSharedRawMaterial key = this.toKey(innerSummary);
      final ProductProvisionSummaryDataProvider data =
          summaryDataProviderMap.computeIfAbsent(key, k -> new ProductProvisionSummaryDataProvider());
      data.setInnerSummary(innerSummary);
    }

    for (final var orderUseSummary : orderUseSummaries) {
      final ProductSharedRawMaterial key = this.toKey(orderUseSummary);
      final ProductProvisionSummaryDataProvider data =
          summaryDataProviderMap.computeIfAbsent(key, k -> new ProductProvisionSummaryDataProvider());
      if (orderUseSummary.orderType() == OrderUseType.ORDINARY) {
        data.setOrderUseInner(orderUseSummary);
      }
    }

    for (final var productUseSummary : productUseSummaries) {
      final ProductSharedRawMaterial key = this.toKey(productUseSummary);
      final ProductProvisionSummaryDataProvider data =
          summaryDataProviderMap.computeIfAbsent(key, k -> new ProductProvisionSummaryDataProvider());
      if (productUseSummary.localizationType() == LocalizationType.WAREHOUSE_DC) {
        data.addProductUseInner(productUseSummary);
      }
    }
    return summaryDataProviderMap;
  }

  private ProductSharedRawMaterial toKey(NominatedProvision np) {
    final UUID productId = UUID.fromString(np.productId().value());
    final UUID referenceId = UUID.fromString(np.referenceId().value());
    final UUID useId = UUID.fromString(np.useId().value());
    final String budgetCycle = np.budgetId().value();
    return new ProductSharedRawMaterial(productId, referenceId, useId, budgetCycle);
  }

  private ProductSharedRawMaterial toKey(DistributionInnerSummaryDTO is) {
    final UUID productId = is.referenceProductId().value();
    final UUID referenceId = is.referenceId().value();
    final UUID useId = is.useId().value();
    final String budgetCycle = is.budgetCycle().value();
    return new ProductSharedRawMaterial(productId, referenceId, useId, budgetCycle);
  }

  private ProductSharedRawMaterial toKey(OrderUseSummaryDTO ou) {
    final UUID productId = ou.productId().value();
    final UUID referenceId = ou.productReferenceId().value();
    final UUID useId = (ou.useId() != null) ? ou.useId().value() : null;
    final String budgetCycle = ou.budgetCycle().value();
    return new ProductSharedRawMaterial(productId, referenceId, useId, budgetCycle);
  }

  private ProductSharedRawMaterial toKey(ProductUseSummaryDTO pu) {
    final UUID productId = pu.productId().value();
    final UUID referenceId = pu.referenceId().value();
    final UUID useId = (pu.useId() != null) ? pu.useId().value() : null;
    final String budgetCycle = pu.budgetCycle().value();
    return new ProductSharedRawMaterial(productId, referenceId, useId, budgetCycle);
  }

  private Reference buildReference(ProductSharedRawMaterial key,
      ProductProvisionSummaryDataProvider data) {
    final var nominatedProvisions = data.getNominatedProvisions();
    BigDecimal nominatedTotalOrdered = BigDecimal.ZERO;
    BigDecimal nominatedTotalPending = BigDecimal.ZERO;
    BigDecimal nominatedTotalEntered = BigDecimal.ZERO;
    BigDecimal nominatedTotalRequested = BigDecimal.ZERO;
    final BigDecimal nominatedTotalDistributed;
    final Map<LocalizationId, BigDecimal> nominatedStocksMap = new HashMap<>();

    for (final var nominatedProvision : nominatedProvisions) {
      nominatedTotalOrdered = nominatedTotalOrdered.add(nominatedProvision.ordered().value());
      nominatedTotalPending = nominatedTotalPending.add(nominatedProvision.pending().value());
      nominatedTotalEntered = nominatedTotalEntered.add(nominatedProvision.entered().value());
      nominatedTotalRequested = nominatedTotalRequested.add(nominatedProvision.distributionRequested().value());
      this.addStockToLocalizationId(nominatedStocksMap, nominatedProvision);
    }
    nominatedTotalDistributed = nominatedTotalRequested;

    final OrderQuantity nominatedOrderQuantity =
        new OrderQuantity(
            nominatedTotalOrdered, nominatedTotalPending, nominatedTotalEntered);

    final DistributionQuantity nominatedDistQuantity =
        new DistributionQuantity(
            nominatedTotalRequested, nominatedTotalDistributed);

    final DistributionInnerSummaryDTO inner = data.getInnerSummary();
    final BigDecimal innerRequested = (inner != null)
        ? inner.requested().value()
        : BigDecimal.ZERO;
    final BigDecimal innerDistributed = (inner != null)
        ? inner.distributed().value()
        : BigDecimal.ZERO;

    final OrderUseSummaryDTO orderUseInner = data.getOrderUseInner();
    final BigDecimal ouInnerOrdered = (orderUseInner != null) ? orderUseInner.ordered().value() : BigDecimal.ZERO;
    final BigDecimal ouInnerPending = (orderUseInner != null) ? orderUseInner.pending().value() : BigDecimal.ZERO;
    final BigDecimal ouInnerEntered = (orderUseInner != null) ? orderUseInner.entered().value() : BigDecimal.ZERO;

    final List<StockLocationQuantities> innerStocks = new ArrayList<>();
    for (final var productUse : data.getProductUseInner()) {
      innerStocks.add(
          new StockLocationQuantities(
              productUse.localizationId().value(),
              productUse.localizationType().value(),
              productUse.stock().value()));
    }

    final List<StockLocationQuantities> nominatedStocks = nominatedStocksMap.entrySet().stream()
        .map(entry -> new StockLocationQuantities(
            entry.getKey().value(),
            COMMITMENT_SUPPLIER,
            entry.getValue()))
        .toList();

    final GetProductProvisioningSummaryQuery.Provision innerProvision =
        new GetProductProvisioningSummaryQuery.Provision(
            innerStocks,
            new OrderQuantity(
                ouInnerOrdered, ouInnerPending, ouInnerEntered),
            new DistributionQuantity(
                innerRequested, innerDistributed));

    final GetProductProvisioningSummaryQuery.Provision nominatedProvision =
        new GetProductProvisioningSummaryQuery.Provision(
            nominatedStocks,
            nominatedOrderQuantity,
            nominatedDistQuantity);

    return new Reference(
        key.referenceId(),
        key.useId(),
        key.budgetCycle(),
        innerProvision,
        nominatedProvision);
  }

  private void addStockToLocalizationId(final Map<LocalizationId, BigDecimal> stockMap,
      final NominatedProvision nominatedProvision) {
    final BigDecimal currentStock = Optional.ofNullable(stockMap.get(nominatedProvision.localizationId())).orElse(BigDecimal.ZERO);
    final BigDecimal newStock = currentStock.add(nominatedProvision.stock().value());
    stockMap.put(nominatedProvision.localizationId(), newStock);
  }

  private record ProductSharedRawMaterial(UUID productId, UUID referenceId, UUID useId, String budgetCycle) {

  }

  @Getter
  private static class ProductProvisionSummaryDataProvider {

    private final List<NominatedProvision> nominatedProvisions = new ArrayList<>();

    @Setter
    private DistributionInnerSummaryDTO innerSummary;

    @Setter
    private OrderUseSummaryDTO orderUseInner;

    private final List<ProductUseSummaryDTO> productUseInner = new ArrayList<>();

    public void addNominatedSummary(NominatedProvision dto) {
      this.nominatedProvisions.add(dto);
    }

    public void addProductUseInner(ProductUseSummaryDTO dto) {
      this.productUseInner.add(dto);
    }

  }
}
