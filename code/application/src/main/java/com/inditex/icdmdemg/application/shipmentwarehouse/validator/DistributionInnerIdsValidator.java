package com.inditex.icdmdemg.application.shipmentwarehouse.validator;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Target({TYPE, FIELD, PARAMETER})
@Retention(RUNTIME)
@Constraint(validatedBy = DistributionInnerIdsValidatorImpl.class)
public @interface DistributionInnerIdsValidator {

  String message() default "DistributionInnerId and DistributionInnerLineId should exist and not have any associated tracking code";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};

}
