package com.inditex.icdmdemg.application.product.command;

import com.inditex.icdmdemg.application.product.service.ProductProjector;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DeleteProductProjectionCommandHandler implements CommandHandler<DeleteProductProjectionCommand> {

  private final ProductProjector productProjector;

  @Override
  public void doHandle(final DeleteProductProjectionCommand command) {
    this.productProjector.deleteReference(command.referenceIdToDelete().id());
  }
}
