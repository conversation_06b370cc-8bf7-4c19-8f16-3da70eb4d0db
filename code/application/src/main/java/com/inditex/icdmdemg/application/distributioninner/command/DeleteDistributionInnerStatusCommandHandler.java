package com.inditex.icdmdemg.application.distributioninner.command;

import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DeleteDistributionInnerStatusCommandHandler implements
    CommandHandler<DeleteDistributionInnerStatusCommand> {

  private final DistributionInnerRepository distributionInnerRepository;

  private final Transaction transaction;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  @Override
  public void doHandle(final DeleteDistributionInnerStatusCommand command) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var orderInfo = ProductOrderStatusInfo.deleted();

    final var productOrderId = new ProductOrderId(UUID.fromString(command.orderId()));
    final var distributions = this.distributionInnerRepository.findByProductOrderId(productOrderId);

    for (final var distribution : distributions) {

      final var updatedDistribution =
          distribution.updateStatusFromOrder(orderInfo, now, command.triggeredBy());
      this.transaction.run(() -> {
        this.distributionInnerRepository.save(updatedDistribution);
        this.eventBus.send(updatedDistribution.domainEvents());
      });
    }
  }
}
