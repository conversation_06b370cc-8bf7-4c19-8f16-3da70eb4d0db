package com.inditex.icdmdemg.application.distributionnominated.command;

import static com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateReferenceInput;

import com.inditex.iopcmmnt.cqrs.core.Command;

import org.jspecify.annotations.NonNull;

public record UpdateDistributionNominatedReferenceCommand(
    @NonNull UpdateReferenceInput updateReferenceInput,
    @NonNull String triggeredBy) implements Command {

}
