package com.inditex.icdmdemg.application.distributionnominated.command;

import java.util.List;

import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.iopcmmnt.cqrs.core.Command;

import org.jspecify.annotations.NonNull;

public record AdjustDistributionNominatedCommand(
    @NonNull List<SharedRawMaterialNominated> sharedRawMaterialNominated,
    @NonNull String triggeredBy) implements Command {
}
