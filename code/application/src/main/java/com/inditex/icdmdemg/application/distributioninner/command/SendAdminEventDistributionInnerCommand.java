package com.inditex.icdmdemg.application.distributioninner.command;

import java.util.UUID;

import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import org.jspecify.annotations.NonNull;

public record SendAdminEventDistributionInnerCommand(
    SendEventRequest sendEventRequest) implements BaseCommand<SendAdminEventInnerResult> {

  public record SendEventRequest(
      @NonNull UUID distributionInnerId,
      @NonNull String eventType) {
  }

}
