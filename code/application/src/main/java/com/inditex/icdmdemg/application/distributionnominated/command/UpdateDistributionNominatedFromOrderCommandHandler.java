package com.inditex.icdmdemg.application.distributionnominated.command;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedFromOrderCommand.UpdateDistributionNominatedFromOrderCommandResult;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductSupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderBudgetId;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateDistributionNominatedFromOrderCommandHandler
    implements ResultCommandHandler<UpdateDistributionNominatedFromOrderCommand, UpdateDistributionNominatedFromOrderCommandResult> {

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final OrderRepository orderRepository;

  private final Transaction transaction;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  @Override
  public UpdateDistributionNominatedFromOrderCommandResult execute(final UpdateDistributionNominatedFromOrderCommand command) {

    final Optional<Order> orderOpt = this.orderRepository.find(new OrderId(command.orderId()));

    if (orderOpt.isEmpty()) {
      return UpdateDistributionNominatedFromOrderCommandResult.empty();
    }

    final Order order = orderOpt.get();
    final ProductSupplierId supplierId = new ProductSupplierId(order.supplierId().value());

    final ProductOrderId productOrderId = new ProductOrderId(UUID.fromString(order.getId().value()));
    final List<DistributionNominated> distributions = this.distributionNominatedRepository.findByProductOrderId(productOrderId);

    final Optional<OrderBudgetId> firstBudgetIdFromOrderOpt = order.firstBudgetId();

    final List<SharedRawMaterialNominated> sharedRawMaterialsFromUpdatedDistributionsNominated =
        this.updateAndSaveAllDistributionsNominatedFromOrder(
            command,
            supplierId,
            firstBudgetIdFromOrderOpt,
            distributions);

    return new UpdateDistributionNominatedFromOrderCommandResult(sharedRawMaterialsFromUpdatedDistributionsNominated);
  }

  private List<SharedRawMaterialNominated> updateAndSaveAllDistributionsNominatedFromOrder(
      final UpdateDistributionNominatedFromOrderCommand command,
      final ProductSupplierId supplierId,
      final Optional<OrderBudgetId> firstBudgetIdFromOrderOpt,
      final List<DistributionNominated> distributions) {

    final OffsetDateTime now = this.clockUtils.getCurrentOffsetDateTime();

    return distributions.stream()
        .filter(distribution -> distribution.isDistributionNominatedModified(supplierId,
            getBudgetCycleFromOrderOrDistribution(distribution, firstBudgetIdFromOrderOpt)))
        .map(distribution -> this.updateAndSaveDistributionNominated(distribution, firstBudgetIdFromOrderOpt, supplierId, now,
            command.triggeredBy()))
        .toList();
  }

  private SharedRawMaterialNominated updateAndSaveDistributionNominated(
      final DistributionNominated distributionNominated,
      final Optional<OrderBudgetId> firstBudgetIdOpt,
      final ProductSupplierId productSupplierId,
      final OffsetDateTime now,
      final String triggeredBy) {

    final BudgetCycle budgetCycle = getBudgetCycleFromOrderOrDistribution(distributionNominated, firstBudgetIdOpt);

    distributionNominated.updateProductSupplierId(productSupplierId, now, triggeredBy);
    distributionNominated.updateBudgetCycle(budgetCycle, now, triggeredBy);

    this.transaction.run(() -> {
      this.distributionNominatedRepository.save(distributionNominated);
      this.eventBus.send(distributionNominated.domainEvents());
    });

    return new SharedRawMaterialNominated(distributionNominated.referenceId(),
        distributionNominated.useId(),
        distributionNominated.budgetCycle());
  }

  private static BudgetCycle getBudgetCycleFromOrderOrDistribution(
      final DistributionNominated distribution,
      final Optional<OrderBudgetId> firstBudgetId) {
    return firstBudgetId
        .map(budgetId -> new BudgetCycle(budgetId.value()))
        .orElse(distribution.budgetCycle());
  }

}
