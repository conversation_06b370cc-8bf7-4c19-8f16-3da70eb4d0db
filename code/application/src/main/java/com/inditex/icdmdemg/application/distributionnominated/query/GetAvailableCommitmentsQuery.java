package com.inditex.icdmdemg.application.distributionnominated.query;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionnominated.query.GetAvailableCommitmentsQuery.AvailableCommitment;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.cqrs.core.Query;

import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.NullMarked;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;

@NullMarked
public record GetAvailableCommitmentsQuery(UUID referenceId, String budgetCycle, UUID useId, @NonNull PageRequest pageable)
    implements
      Query<Slice<AvailableCommitment>> {

  public GetAvailableCommitmentsQuery(final UUID referenceId, final String budgetCycle, final UUID useId, final Integer page,
      final Integer size) {
    this(referenceId, budgetCycle, useId, PageRequest.of(page, size));
  }

  public record AvailableCommitment(UUID commitmentOrderId, UUID commitmentOrderLineId, OffsetDateTime expectedDate, String locationId,
      BigDecimal availableQuantity) {
    public AvailableCommitment(final UUID commitmentOrderId, final UUID commitmentOrderLineId,
        final OffsetDateTime expectedDate, final String locationId, final BigDecimal availableQuantity) {
      this.commitmentOrderId = commitmentOrderId;
      this.commitmentOrderLineId = commitmentOrderLineId;
      this.expectedDate = expectedDate;
      this.locationId = locationId;
      this.availableQuantity = NumericUtils.roundUpScale2(availableQuantity);
    }
  }
}
