package com.inditex.icdmdemg.application.shipmentcommitment.command;

import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitmentRepository;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentId;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class ProjectCommitmentShipmentCancelledCommandHandler
    implements CommandHandler<ProjectCommitmentShipmentCancelledCommand> {

  private final ShipmentCommitmentRepository repository;

  private final Transaction transaction;

  @Override
  public void doHandle(final ProjectCommitmentShipmentCancelledCommand command) {
    final var cancelledShipmentCommitmentId = new ShipmentCommitmentId(command.shipmentCancelled().id());

    this.transaction.run(() -> this.repository.delete(cancelledShipmentCommitmentId));
  }

}
