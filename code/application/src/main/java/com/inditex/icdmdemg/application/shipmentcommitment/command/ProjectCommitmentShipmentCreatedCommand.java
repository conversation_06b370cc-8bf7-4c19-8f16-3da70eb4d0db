package com.inditex.icdmdemg.application.shipmentcommitment.command;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitment;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import org.jspecify.annotations.NonNull;

public record ProjectCommitmentShipmentCreatedCommand(
    ShipmentCreated shipmentCreated) implements BaseCommand<ShipmentCommitment> {

  public record ShipmentCreated(
      @NonNull String id,
      @NonNull String distributionNominatedLineId,
      BigDecimal quantity,
      String measurementUnitId,
      @NonNull OffsetDateTime sentDate) {

  }
}
