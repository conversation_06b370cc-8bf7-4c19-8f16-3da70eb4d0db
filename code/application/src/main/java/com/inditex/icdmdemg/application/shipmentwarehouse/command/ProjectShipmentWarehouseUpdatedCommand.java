package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

import com.inditex.icdmdemg.application.shipmentwarehouse.validator.ShipmentWarehouseTrackingCodeValidator;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import jakarta.validation.constraints.NotNull;

public record ProjectShipmentWarehouseUpdatedCommand(ShipmentWarehouseUpdated shipmentWarehouseUpdated)
    implements
      BaseCommand<ShipmentWarehouse> {

  public record ShipmentWarehouseUpdated(
      @ShipmentWarehouseTrackingCodeValidator String distributionTrackingCode,
      @NotNull BigDecimal quantity,
      @NotNull OffsetDateTime distributionLastUpdateDate) {

  }

}
