package com.inditex.icdmdemg.application.commitmentuse.command;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import org.jspecify.annotations.NonNull;

public record ProjectMaterialCommitmentUseCommand(OrderUse orderUse) implements BaseCommand<ProjectMaterialCommitmentUseCommandResult> {

  public record OrderUse(
      String id,
      @NonNull String orderId,
      @NonNull String orderLineId,
      String budgetId,
      String serviceLocalizationId,
      String serviceLocalizationType,
      OffsetDateTime expectedDate,
      List<Use> uses) {
  }

  public record Use(
      String materialReferenceId,
      String status,
      OffsetDateTime budgetCycleChangeDate,
      String useId,
      BigDecimal quantity) {
  }

}
