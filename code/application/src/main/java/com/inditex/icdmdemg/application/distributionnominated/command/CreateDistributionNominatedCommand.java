package com.inditex.icdmdemg.application.distributionnominated.command;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import jakarta.validation.constraints.Positive;
import org.jspecify.annotations.NonNull;

public record CreateDistributionNominatedCommand(
    NominatedRequest nominatedRequest,
    PlanRequest plan) implements BaseCommand<DistributionNominated> {

  public record NominatedRequest(
      @NonNull UUID referenceId,
      @NonNull UUID useId,
      @NonNull String budgetCycle,
      @NonNull UUID productOrderId,
      @NonNull UUID productVariantGroupId,
      @Positive @NonNull BigDecimal theoreticalQuantity,
      @Positive @NonNull BigDecimal requestedQuantity,
      @Positive @NonNull BigDecimal consumptionFactor,
      @NonNull String triggeredBy) {
  }

}
