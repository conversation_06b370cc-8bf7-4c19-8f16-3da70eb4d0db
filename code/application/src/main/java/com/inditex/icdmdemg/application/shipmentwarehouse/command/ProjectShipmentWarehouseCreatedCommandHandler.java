package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import java.util.UUID;

import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouseRepository;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseId;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseInner;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseInnerId;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseInnerLineId;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTimestamp;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTrackingCode;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ProjectShipmentWarehouseCreatedCommandHandler
    implements ResultCommandHandler<ProjectShipmentWarehouseCreatedCommand, ShipmentWarehouse> {

  private final ShipmentWarehouseRepository shipmentWarehouseRepository;

  private final ClockUtils clockUtils;

  private final Transaction transaction;

  private final UuidGenerator uuidGenerator;

  @Override
  public ShipmentWarehouse execute(final ProjectShipmentWarehouseCreatedCommand command) {
    final var shipmentWarehouseCreated = command.shipmentWarehouseCreated();
    final var shipmentWarehouseInner = this.createShipmentWarehouseInner(shipmentWarehouseCreated.distributionInnerId(),
        shipmentWarehouseCreated.distributionInnerLineId());
    final var shipmentWarehouse = ShipmentWarehouse.create(new ShipmentWarehouseId(this.uuidGenerator.generate().toString()),
        new ShipmentWarehouseTrackingCode(shipmentWarehouseCreated.trackingCode()), shipmentWarehouseInner,
        new ShipmentWarehouseTimestamp(this.clockUtils.getCurrentOffsetDateTime()));

    this.transaction.run(() -> this.shipmentWarehouseRepository.save(shipmentWarehouse));

    return shipmentWarehouse;
  }

  private ShipmentWarehouseInner createShipmentWarehouseInner(final UUID id, final UUID lineId) {
    return new ShipmentWarehouseInner(
        new ShipmentWarehouseInnerId(
            id.toString()),
        new ShipmentWarehouseInnerLineId(
            lineId.toString()));
  }

}
