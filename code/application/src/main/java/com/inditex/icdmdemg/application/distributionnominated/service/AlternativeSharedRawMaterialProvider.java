package com.inditex.icdmdemg.application.distributionnominated.service;

import java.util.List;
import java.util.stream.Stream;

import com.inditex.icdmdemg.application.product.service.ProductAlternativeProvider;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseMaterialReferenceId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AlternativeSharedRawMaterialProvider {

  private final ProductAlternativeProvider productAlternativeProvider;

  public List<MaterialCommitmentUseSharedRawMaterial> alternatives(MaterialCommitmentUseSharedRawMaterial sharedRawMaterial) {
    return this.alternatives(List.of(sharedRawMaterial));
  }

  public List<MaterialCommitmentUseSharedRawMaterial> alternatives(List<MaterialCommitmentUseSharedRawMaterial> sharedRawMaterialList) {
    final var productReferenceIds = sharedRawMaterialList.stream()
        .map(MaterialCommitmentUseSharedRawMaterial::materialCommitmentMaterialReferenceId)
        .map(MaterialCommitmentUseMaterialReferenceId::value)
        .map(ProductReferenceId::new)
        .toList();
    final var alternativeMap = this.productAlternativeProvider.alternativeProductReferences(productReferenceIds);
    return sharedRawMaterialList.stream()
        .flatMap(sharedRawMaterial -> {
          final var referenceId = new ProductReferenceId(sharedRawMaterial.materialCommitmentMaterialReferenceId().value());
          final var alternative = alternativeMap.get(referenceId)
              .map(alternativeProduct -> MaterialCommitmentUseSharedRawMaterial.of(
                  sharedRawMaterial.materialCommitmentUseId().value(),
                  alternativeProduct.getReferenceId().value(),
                  sharedRawMaterial.materialCommitmentBudgetId().value()));
          return Stream.concat(Stream.of(sharedRawMaterial), alternative.stream());
        })
        .distinct()
        .toList();
  }

}
