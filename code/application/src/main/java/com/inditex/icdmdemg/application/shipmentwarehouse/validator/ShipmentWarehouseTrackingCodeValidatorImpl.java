package com.inditex.icdmdemg.application.shipmentwarehouse.validator;

import java.util.Objects;

import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouseRepository;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTrackingCode;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ShipmentWarehouseTrackingCodeValidatorImpl
    implements ConstraintValidator<ShipmentWarehouseTrackingCodeValidator, String> {

  private final ShipmentWarehouseRepository shipmentWarehouseRepository;

  @Override
  public boolean isValid(final String trackingCode, final ConstraintValidatorContext constraintValidatorContext) {
    return Objects.nonNull(trackingCode)
        && this.shipmentWarehouseRepository
            .findByTrackingCode(new ShipmentWarehouseTrackingCode(trackingCode))
            .isPresent();
  }
}
