package com.inditex.icdmdemg.application.distributionnominated.service;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.AlternativeReference;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.AlternativeReference.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLines;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Line;
import com.inditex.icdmdemg.domain.shared.EntityAndActionResult;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DistributionNominatedLinesIdentifier {

  private final UuidGenerator uuidGenerator;

  public List<DistributionNominatedLine> identifyCreatedLines(
      DistributionNominated.Id rootId,
      DistributionNominated.RequestedQuantity requestedQuantity,
      DistributionNominated.TheoreticalQuantity theoreticalQuantity,
      List<EntityAndActionResult<Line>> lines,
      OffsetDateTime occurredOn) {
    final var identificationRequestMap = Map.of(
        rootId,
        new IdentificationRequest(new DistributionNominatedLines(List.of()), requestedQuantity, theoreticalQuantity));
    return this.identify(identificationRequestMap, lines, occurredOn).getOrDefault(rootId, List.of());
  }

  public List<DistributionNominatedLine> identifyUpdatedLines(
      DistributionNominated distributionNominated,
      DistributionNominated.RequestedQuantity requestedQuantity,
      DistributionNominated.TheoreticalQuantity theoreticalQuantity,
      List<EntityAndActionResult<Line>> lines,
      OffsetDateTime occurredOn) {
    final var identificationRequestMap = Map.of(
        distributionNominated.getId(),
        new IdentificationRequest(distributionNominated.lines(), requestedQuantity, theoreticalQuantity));
    return this.identify(identificationRequestMap, lines, occurredOn).getOrDefault(distributionNominated.getId(), List.of());
  }

  public Map<DistributionNominated.Id, List<DistributionNominatedLine>> identifyAdjustedLines(
      List<DistributionNominated> distributions,
      List<EntityAndActionResult<Line>> lines,
      OffsetDateTime occurredOn) {
    final var identificationRequestMap = distributions.stream()
        .map(distribution -> Pair.of(
            distribution.getId(),
            new IdentificationRequest(distribution.lines(), distribution.requestedQuantity(), distribution.theoreticalQuantity())))
        .collect(Collectors.toMap(Pair::getKey, Pair::getValue));
    return this.identify(identificationRequestMap, lines, occurredOn);
  }

  private Map<DistributionNominated.Id, List<DistributionNominatedLine>> identify(
      Map<DistributionNominated.Id, IdentificationRequest> identificationRequestMap,
      List<EntityAndActionResult<Line>> lines,
      OffsetDateTime occurredOn) {
    final var linesGroupedById = lines.stream()
        .filter(EntityAndActionResult::isActionCreateKeepOrUpdate)
        .map(EntityAndActionResult::entity)
        .collect(Collectors.groupingBy((Line line) -> line.compositeId().rootId()));

    return linesGroupedById.entrySet().stream()
        .filter(entry -> identificationRequestMap.containsKey(entry.getKey()))
        .map(entry -> {
          final var distributionId = entry.getKey();
          final var currentLines = entry.getValue();
          final var identificationRequest = identificationRequestMap.get(distributionId);
          final var distributionLines = identificationRequest.existingLines();
          final var requestedQuantity = identificationRequest.requestedQuantity();

          final var currentLinesGroupedByCommitmentOrder = currentLines.stream()
              .collect(Collectors.groupingBy(Line::commitmentOrder));

          final var updatedLines = currentLinesGroupedByCommitmentOrder.entrySet().stream()
              .map(commitmentOrderComputedEntry -> {
                final var commitmentOrder = commitmentOrderComputedEntry.getKey();
                final var computedLine = commitmentOrderComputedEntry.getValue();
                return distributionLines.getLineByCommitmentOrder(commitmentOrder)
                    .map(line -> this.updateLine(line, requestedQuantity, computedLine, occurredOn))
                    .orElseGet(() -> this.createLine(commitmentOrder, requestedQuantity, computedLine, occurredOn));
              })
              .toList();

          return Pair.of(distributionId, updatedLines);
        }).collect(Collectors.toMap(Pair::getKey, Pair::getValue));
  }

  private DistributionNominatedLine createLine(
      CommitmentOrder commitmentOrder,
      DistributionNominated.RequestedQuantity requestedQuantity,
      List<Line> computedLine,
      OffsetDateTime now) {
    final var id = new DistributionNominatedLine.Id(this.uuidGenerator.generate());
    final var created = DistributionNominatedLine.create(id, commitmentOrder, RequestedQuantity.zero(), requestedQuantity, now);
    return this.updateLine(created, requestedQuantity, computedLine, now);
  }

  private DistributionNominatedLine updateLine(
      DistributionNominatedLine line,
      DistributionNominated.RequestedQuantity requestedQuantity,
      List<Line> computedLine,
      OffsetDateTime now) {
    final var targetRequested = computedLine.stream()
        .filter(Predicate.not(Line::isAlternative))
        .map(Line::requested)
        .map(RequestedQuantity::new)
        .findFirst()
        .orElse(RequestedQuantity.zero());
    var updatedLine = line.updateRequested(targetRequested, requestedQuantity, now);

    final var adjustedAlternative = computedLine.stream().filter(Line::isAlternative).findFirst();

    if (adjustedAlternative.isPresent()) {
      final var alternativeReferenceId = new ReferenceId(adjustedAlternative.get().referenceId().value());
      final var alternativeRequestedQuantity = new AlternativeReference.RequestedQuantity(adjustedAlternative.get().requested());
      updatedLine = updatedLine.updateAlternative(alternativeReferenceId, alternativeRequestedQuantity, requestedQuantity,
          now);
    } else {
      updatedLine = updatedLine.removeAlternative(now);
    }

    return updatedLine;
  }

  private record IdentificationRequest(
      DistributionNominatedLines existingLines,
      DistributionNominated.RequestedQuantity requestedQuantity,
      DistributionNominated.TheoreticalQuantity theoreticalQuantity) {
  }

}
