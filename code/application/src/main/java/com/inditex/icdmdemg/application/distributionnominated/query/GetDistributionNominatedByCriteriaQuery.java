package com.inditex.icdmdemg.application.distributionnominated.query;

import static com.inditex.icdmdemg.shared.utils.Nullables.safeListWithEmptyList;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.iopcmmnt.cqrs.core.Query;

import org.jspecify.annotations.NonNull;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Slice;

public record GetDistributionNominatedByCriteriaQuery(
    @NonNull List<UUID> referenceIds,
    @NonNull List<UUID> productOrderIds,
    @NonNull List<UUID> lineIds,
    @NonNull List<UUID> commitmentOrderIds,
    @NonNull List<String> budgetCycles,
    @NonNull List<String> status,
    @NonNull PageRequest pageable)
    implements
      Query<Slice<DistributionNominated>> {

  public GetDistributionNominatedByCriteriaQuery(final List<UUID> referenceIds, final UUID lineId, final List<UUID> productOrderIds,
      final List<UUID> commitmentOrderIds, final List<String> budgetCycles, final List<String> status, final Integer page,
      final Integer size) {
    this(safeListWithEmptyList(referenceIds),
        safeListWithEmptyList(productOrderIds),
        Optional.ofNullable(lineId).stream().toList(),
        safeListWithEmptyList(commitmentOrderIds),
        safeListWithEmptyList(budgetCycles),
        safeListWithEmptyList(status),
        PageRequest.of(page, size));
  }
}
