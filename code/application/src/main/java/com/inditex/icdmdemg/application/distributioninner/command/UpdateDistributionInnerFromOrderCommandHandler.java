package com.inditex.icdmdemg.application.distributioninner.command;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateDistributionInnerFromOrderCommandHandler
    implements CommandHandler<UpdateDistributionInnerFromOrderCommand> {

  private final DistributionInnerRepository distributionInnerRepository;

  private final OrderRepository orderRepository;

  private final Transaction transaction;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  @Override
  public void doHandle(UpdateDistributionInnerFromOrderCommand command) {

    final Optional<Order> orderOpt = this.orderRepository.find(new OrderId(command.orderId()));

    if (orderOpt.isPresent()) {
      final Order order = orderOpt.get();

      final var productOrderId =
          new com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId(UUID.fromString(order.getId().value()));
      final var optionalBudgetCycle = order.firstBudgetId();

      if (optionalBudgetCycle.isPresent()) {
        final List<DistributionInner> distributions = this.distributionInnerRepository.findByProductOrderId(productOrderId);
        final BudgetCycle budgetCycle = new BudgetCycle(optionalBudgetCycle.get().value());

        distributions.forEach(
            distribution -> {
              if (distribution.isBudgetCycleModified(budgetCycle)) {
                distribution.updateBudgetCycle(budgetCycle, this.clockUtils.getCurrentOffsetDateTime(), command.triggeredBy());
                this.transaction.run(() -> {
                  this.distributionInnerRepository.save(distribution);
                  this.eventBus.send(distribution.domainEvents());
                });
              }
            });
      }
    }
  }
}
