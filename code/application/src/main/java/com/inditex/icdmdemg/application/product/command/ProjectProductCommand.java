package com.inditex.icdmdemg.application.product.command;

import java.util.List;
import java.util.UUID;

import com.inditex.iopcmmnt.cqrs.core.Command;

import org.jspecify.annotations.NonNull;

public record ProjectProductCommand(ProductReference productReference) implements Command {

  public record ProductReference(@NonNull String productId, @NonNull String referenceId,
      String supplierId, String campaignId, UUID originMarketId, List<String> families, Attributes attributes) {

  }

  public record Attributes(UUID color, Integer quality, List<String> owners) {
  }

}
