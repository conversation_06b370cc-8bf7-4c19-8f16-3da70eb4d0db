package com.inditex.icdmdemg.application.use.evaluation;

import java.util.List;

import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;

import org.jspecify.annotations.NonNull;

public record PurchasePurposeEvaluationResult(Use.Id useId, int kos,
    @NonNull List<PurchasePurposeConditionName> paramsWithOk, int coincidences) {

  public int oks() {
    return this.paramsWithOk.size();
  }

  public boolean isOk() {
    return this.kos == 0;
  }
}
