package com.inditex.icdmdemg.application.distributionsummary.query;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailability;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityResponse;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityTotalInnerQuantity;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityTotalNominatedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUseQuantitiesDTO;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.BudgetId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvisionRepository;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedTotalQuantityByUseDTO;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseInnerPendingByUseDTO;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseRepository;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.BudgetCycle;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.Pending;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.ProductReferenceId;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseInnerStockByUseDTO;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseRepository;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO.ReferenceId;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO.Stock;
import com.inditex.icdmdemg.provis.domain.use.UseDTO;
import com.inditex.icdmdemg.provis.domain.use.UseDTO.Id;
import com.inditex.icdmdemg.provis.domain.use.UseDTO.UsePurchaseType;
import com.inditex.icdmdemg.provis.domain.use.UsesRepository;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetUsesAvailabilityQueryHandler implements QueryHandler<GetUsesAvailabilityQuery, Response<UsesAvailabilityResponse>> {

  private final UsesRepository usesRepository;

  private final OrderUseRepository orderUseRepository;

  private final ProductUseRepository productUseRepository;

  private final NominatedProvisionRepository nominatedProvisionRepository;

  private final DistributionInnerRepository distributionInnerRepository;

  @Override
  public Response<UsesAvailabilityResponse> ask(GetUsesAvailabilityQuery query) {
    final var referenceId = query.request().referenceId();
    final var useId = query.request().useId();
    final var budgetCycle = query.request().budgetCycle();

    final var innerCalculationsHashMap = new HashMap<UseDTO.Id, InnerCalculations>();
    final var nominatedTotalQuantityByUseHashMap = new HashMap<UseDTO.Id, NominatedTotalQuantityByUseDTO>();
    final var usesAvailabilityDataList = new ArrayList<UsesAvailability>();

    final List<UseDTO> relatedUses = this.usesRepository.findRelatedUsesByUseId(new UseDTO.Id(useId));
    final List<UUID> relatedUsesIds = relatedUses.stream().map(UseDTO::id).map(Id::value).toList();

    this.calculateInnerByUsePending(referenceId, relatedUsesIds, budgetCycle, innerCalculationsHashMap);
    this.calculateInnerByUseRequestedAndDistributed(relatedUsesIds, referenceId, budgetCycle, innerCalculationsHashMap);
    this.calculateInnerByUseStock(referenceId, relatedUsesIds, budgetCycle, innerCalculationsHashMap);

    this.calculateNominatedByUseTotalQuantity(referenceId, relatedUsesIds, budgetCycle, nominatedTotalQuantityByUseHashMap);

    calculateUsesAvailability(relatedUses, innerCalculationsHashMap, usesAvailabilityDataList, nominatedTotalQuantityByUseHashMap);

    return Response.ofResponse(new UsesAvailabilityResponse(usesAvailabilityDataList));
  }

  private static void calculateUsesAvailability(List<UseDTO> relatedUses, HashMap<Id, InnerCalculations> innerCalculationsHashMap,
      ArrayList<UsesAvailability> usesAvailabilityList,
      HashMap<Id, NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseHashMap) {
    relatedUses.forEach(use -> {
      final Id useId = new Id(use.id().value());
      final InnerCalculations innerCalculations =
          innerCalculationsHashMap.containsKey(useId) ? innerCalculationsHashMap.get(useId)
              : InnerCalculations.create(useId);

      usesAvailabilityList.add(
          new UsesAvailability(
              use.id().value(),
              use.name().value(),
              use.getPurchaseTypesList().stream().map(UsePurchaseType::value).toList(),
              use.assignable().value(),
              new UsesAvailabilityTotalNominatedQuantity(
                  nominatedTotalQuantityByUseHashMap.containsKey(useId)
                      ? nominatedTotalQuantityByUseHashMap.get(useId).totalQuantity().value()
                      : NumericUtils.roundUpScale2(BigDecimal.ZERO)),
              new UsesAvailabilityTotalInnerQuantity(
                  (innerCalculations.pending.value().add(innerCalculations.stock.value())).subtract(
                      innerCalculations.requested.value().subtract(innerCalculations.distributed.value())))));
    });
  }

  private void calculateNominatedByUseTotalQuantity(UUID referenceId, List<UUID> relatedUsesIds, String budgetCycle,
      HashMap<Id, NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseHashMap) {
    final List<NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseDTOList =
        this.nominatedProvisionRepository.findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            relatedUsesIds.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    nominatedTotalQuantityByUseDTOList.forEach(nominatedTotalQuantityByUseDTO -> nominatedTotalQuantityByUseHashMap
        .put(new Id(nominatedTotalQuantityByUseDTO.useId().value()), nominatedTotalQuantityByUseDTO));
  }

  private void calculateInnerByUseStock(UUID referenceId, List<UUID> relatedUsesIds, String budgetCycle,
      HashMap<Id, InnerCalculations> innerCalculationsHashMap) {
    final List<ProductUseInnerStockByUseDTO> innerProductUseSummaries =
        this.productUseRepository.findProductUseInnerStockByUseDTO(new ReferenceId(referenceId),
            relatedUsesIds.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
            new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    innerProductUseSummaries.forEach(
        productUse -> {
          final var useId = new UseDTO.Id(productUse.useId().value());
          final var stock = new Stock(productUse.stock().value());
          innerCalculationsHashMap.merge(
              useId,
              InnerCalculations.create(useId, stock),
              InnerCalculations::add);
        });
  }

  private void calculateInnerByUseRequestedAndDistributed(List<UUID> relatedUsesIds, UUID referenceId, String budgetCycle,
      HashMap<Id, InnerCalculations> innerCalculationsHashMap) {
    final List<DistributionInnerUseQuantitiesDTO> distributionsInner =
        this.distributionInnerRepository.findDistributionInnerUseQuantities(
            relatedUsesIds.stream().map(DistributionInner.UseId::new).toList(),
            new DistributionInner.ReferenceId(referenceId),
            new DistributionInner.BudgetCycle(budgetCycle));

    distributionsInner.forEach(distribution -> {
      final var useId = new UseDTO.Id(distribution.useId().value());
      innerCalculationsHashMap.merge(
          useId,
          InnerCalculations.create(useId, distribution.requested(), distribution.distributed()),
          InnerCalculations::add);
    });
  }

  private void calculateInnerByUsePending(UUID referenceId, List<UUID> relatedUsesIds, String budgetCycle,
      HashMap<Id, InnerCalculations> innerCalculationsHashMap) {
    final var orderUseInnerPendingByUsesDTOList =
        this.orderUseRepository.findOrderUseInnerPendingBySharedRawMaterialUses(
            new ProductReferenceId(referenceId),
            relatedUsesIds.stream().map(OrderUseInnerPendingByUseDTO.UseId::new).toList(),
            new BudgetCycle(budgetCycle));

    orderUseInnerPendingByUsesDTOList.forEach(
        orderUseInnerPendingByUsesDTO -> {
          final var useId = new UseDTO.Id(orderUseInnerPendingByUsesDTO.useId().value());
          final var pending = new Pending(orderUseInnerPendingByUsesDTO.pending().value());
          innerCalculationsHashMap.merge(
              useId,
              InnerCalculations.create(useId, pending),
              InnerCalculations::add);
        });
  }

  public record InnerCalculations(
      UseDTO.Id useId,
      RequestedQuantity requested,
      DistributedQuantity distributed,
      Stock stock,
      Pending pending) {

    public static InnerCalculations create(UseDTO.Id useId) {
      return new InnerCalculations(useId, new RequestedQuantity(BigDecimal.ZERO), new DistributedQuantity(BigDecimal.ZERO),
          new Stock(BigDecimal.ZERO), new Pending(BigDecimal.ZERO));
    }

    public static InnerCalculations create(UseDTO.Id useId, Pending pending) {
      return create(useId).addPending(pending);
    }

    public static InnerCalculations create(UseDTO.Id useId, RequestedQuantity requested, DistributedQuantity distributed) {
      return create(useId).addRequestedAndDistributed(requested, distributed);
    }

    public static InnerCalculations create(UseDTO.Id useId, Stock stock) {
      return create(useId).addStock(stock);
    }

    public InnerCalculations add(InnerCalculations innerCalculations) {
      return new InnerCalculations(
          this.useId,
          new RequestedQuantity(this.requested.value().add(innerCalculations.requested.value())),
          new DistributedQuantity(this.distributed.value().add(innerCalculations.distributed.value())),
          new Stock(this.stock.value().add(innerCalculations.stock.value())),
          new Pending(this.pending.value().add(innerCalculations.pending.value())));
    }

    private InnerCalculations addRequestedAndDistributed(RequestedQuantity requested, DistributedQuantity distributed) {
      return new InnerCalculations(
          this.useId,
          new RequestedQuantity(this.requested.value().add(requested.value())),
          new DistributedQuantity(this.distributed.value().add(distributed.value())),
          this.stock,
          this.pending);
    }

    private InnerCalculations addStock(Stock stock) {
      return new InnerCalculations(
          this.useId,
          this.requested,
          this.distributed,
          new Stock(this.stock.value().add(stock.value())),
          this.pending);
    }

    private InnerCalculations addPending(Pending pending) {
      return new InnerCalculations(
          this.useId,
          this.requested,
          this.distributed,
          this.stock,
          new Pending(this.pending.value().add(pending.value())));
    }
  }
}
