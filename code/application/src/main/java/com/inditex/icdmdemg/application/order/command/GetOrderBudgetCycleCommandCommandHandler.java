package com.inditex.icdmdemg.application.order.command;

import java.util.Optional;

import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderBudgetId;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class GetOrderBudgetCycleCommandCommandHandler implements ResultCommandHandler<GetOrderBudgetCycleCommand, Optional<OrderBudgetId>> {

  private final OrderRepository orderRepository;

  @Override
  public Optional<OrderBudgetId> execute(final GetOrderBudgetCycleCommand command) {
    return this.orderRepository.find(new OrderId(command.orderId()))
        .flatMap(Order::firstBudgetId);
  }
}
