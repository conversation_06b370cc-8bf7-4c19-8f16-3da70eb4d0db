package com.inditex.icdmdemg.application.order.command;

import com.inditex.icdmdemg.application.order.service.OrderProjector;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class PublishedOrderCommandHandler implements CommandHandler<PublishedOrderCommand> {

  private final OrderProjector orderProjector;

  @Override
  public void doHandle(final PublishedOrderCommand command) {
    this.orderProjector.publishOrder(command.publishedOrder());
  }

}
