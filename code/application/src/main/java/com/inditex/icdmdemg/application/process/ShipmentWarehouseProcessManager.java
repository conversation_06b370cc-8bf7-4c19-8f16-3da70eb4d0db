package com.inditex.icdmdemg.application.process;

import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCompletedCommand;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCompletedCommand.ShipmentWarehouseCompleted;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCreatedCommand;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCreatedCommand.ShipmentWarehouseCreated;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseStartedCommand;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseStartedCommand.ShipmentWarehouseStarted;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseUpdatedCommand;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseUpdatedCommand.ShipmentWarehouseUpdated;
import com.inditex.icdmdemg.application.shipmentwarehouse.command.UpdateDistributionInnerFromShipmentWarehouseCommand;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class ShipmentWarehouseProcessManager {
  private final Validator validator;

  private final CommandBus commandBus;

  public void execute(final ShipmentWarehouseCreated shipmentWarehouseCreated) {
    if (!this.isValid(shipmentWarehouseCreated)) {
      return;
    }

    final var shipmentWarehouseResult = this.commandBus.execute(new ProjectShipmentWarehouseCreatedCommand(shipmentWarehouseCreated));
    this.commandBus.execute(new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouseResult));
  }

  public void execute(final ShipmentWarehouseUpdated shipmentWarehouseUpdated) {
    if (!this.isValid(shipmentWarehouseUpdated)) {
      return;
    }
    final var shipmentWarehouseResult = this.commandBus.execute(new ProjectShipmentWarehouseUpdatedCommand(shipmentWarehouseUpdated));
    this.commandBus.execute(new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouseResult));
  }

  public void execute(final ShipmentWarehouseStarted shipmentWarehouseStarted) {
    if (!this.isValid(shipmentWarehouseStarted)) {
      return;
    }
    final var shipmentWarehouseResult = this.commandBus.execute(new ProjectShipmentWarehouseStartedCommand(shipmentWarehouseStarted));
    this.commandBus.execute(new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouseResult));
  }

  public void execute(final ShipmentWarehouseCompleted shipmentWarehouseCompleted) {
    if (!this.isValid(shipmentWarehouseCompleted)) {
      return;
    }
    final var shipmentWarehouseResult = this.commandBus.execute(new ProjectShipmentWarehouseCompletedCommand(shipmentWarehouseCompleted));
    this.commandBus.execute(new UpdateDistributionInnerFromShipmentWarehouseCommand(shipmentWarehouseResult));
  }

  public <T> boolean isValid(final T object) {
    final var violations = this.validator.validate(object);
    if (violations.isEmpty()) {
      return true;
    }
    violations.forEach(v -> log.warn("Validation error: {} | Invalid value: {}", v.getMessage(), v.getInvalidValue()));
    return false;
  }
}
