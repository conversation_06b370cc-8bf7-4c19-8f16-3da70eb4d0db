package com.inditex.icdmdemg.application.distributionnominated.command;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedCloseConfigProvider;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DeleteDistributionNominatedStatusCommandHandler implements
    ResultCommandHandler<DeleteDistributionNominatedStatusCommand, List<DistributionNominated>> {

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final DistributionNominatedCloseConfigProvider distributionNominatedCloseConfigProvider;

  private final Transaction transaction;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  @Override
  public List<DistributionNominated> execute(final DeleteDistributionNominatedStatusCommand command) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var orderInfo = ProductOrderStatusInfo.deleted();
    final var productOrderId = new ProductOrderId(UUID.fromString(command.orderId()));
    final var distributions = this.distributionNominatedRepository.findByProductOrderId(productOrderId);

    for (final var distribution : distributions) {
      final var updatedDistribution =
          distribution.updateWithOrderState(orderInfo, now, command.triggeredBy(), this.distributionNominatedCloseConfigProvider);
      this.transaction.run(() -> {
        this.distributionNominatedRepository.save(updatedDistribution);
        this.eventBus.send(updatedDistribution.domainEvents());
      });
    }
    return distributions;
  }

}
