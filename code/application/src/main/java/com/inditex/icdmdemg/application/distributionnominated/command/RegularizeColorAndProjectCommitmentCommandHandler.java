package com.inditex.icdmdemg.application.distributionnominated.command;

import com.inditex.icdmdemg.application.commitmentuse.command.RegularizeColorAndProjectCommitmentCommandResult;
import com.inditex.icdmdemg.application.commitmentuse.service.MaterialCommitmentUseProjector;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.Id;
import com.inditex.icdmdemg.domain.shared.EntityAndActionResult;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RegularizeColorAndProjectCommitmentCommandHandler
    implements ResultCommandHandler<RegularizeColorAndProjectCommitmentCommand, RegularizeColorAndProjectCommitmentCommandResult> {

  public static final String REGULARIZATION_USER = "REGULARIZATION";

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  private final Transaction transaction;

  private final MaterialCommitmentUseProjector materialCommitmentUseProjector;

  @Override
  public RegularizeColorAndProjectCommitmentCommandResult execute(final RegularizeColorAndProjectCommitmentCommand command) {
    final var lineIdToRegularize = command.lineToRegularize().distributionNominatedLineId();
    return this.distributionNominatedRepository.findByLineId(lineIdToRegularize)
        .map(dnWithLineToRegularize -> this.transaction.run(() -> {
          this.regularizeDistributionNominated(dnWithLineToRegularize, lineIdToRegularize);
          final var entityAndActionResults = this.materialCommitmentUseProjector.projectFromUseToOrder(command.orderUse());
          return new RegularizeColorAndProjectCommitmentCommandResult(
              entityAndActionResults.stream().map(EntityAndActionResult::entity).toList());
        }))
        .orElse(RegularizeColorAndProjectCommitmentCommandResult.empty());
  }

  private void regularizeDistributionNominated(final DistributionNominated dn, final Id lineIdToRegularize) {
    final var regularizedDistributionNominated =
        dn.regularize(lineIdToRegularize, this.clockUtils.getCurrentOffsetDateTime(), REGULARIZATION_USER);
    this.distributionNominatedRepository.save(regularizedDistributionNominated);
    this.eventBus.send(regularizedDistributionNominated.domainEvents());
  }
}
