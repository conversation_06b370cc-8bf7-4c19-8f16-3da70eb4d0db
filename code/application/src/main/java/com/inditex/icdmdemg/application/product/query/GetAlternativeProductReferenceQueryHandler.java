package com.inditex.icdmdemg.application.product.query;

import java.util.UUID;

import com.inditex.icdmdemg.application.product.query.GetAlternativeProductReferenceQuery.GetAlternativeProductReferenceResponse;
import com.inditex.icdmdemg.application.product.service.ProductAlternativeProvider;
import com.inditex.icdmdemg.domain.product.Product;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class GetAlternativeProductReferenceQueryHandler
    implements QueryHandler<GetAlternativeProductReferenceQuery, Response<GetAlternativeProductReferenceResponse>> {
  private final ProductAlternativeProvider productAlternativeProvider;

  @Override
  public Response<GetAlternativeProductReferenceResponse> ask(final GetAlternativeProductReferenceQuery query) {
    return this.productAlternativeProvider.alternativeProductReference(new ProductReferenceId(query.referenceId().toString()))
        .map(Product::getReferenceId)
        .map(ProductReferenceId::value)
        .map(UUID::fromString)
        .map(GetAlternativeProductReferenceResponse::new)
        .map(Response::ofResponse)
        .orElseGet(() -> {
          log.info("Alternative product reference not found for referenceId={}.", query.referenceId());
          return Response.ofResponse(new GetAlternativeProductReferenceResponse(null));
        });
  }
}
