package com.inditex.icdmdemg.application.distributioninner.command;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.iopcmmnt.cqrs.core.Command;

import jakarta.validation.constraints.Positive;
import org.jspecify.annotations.NullMarked;

@NullMarked
public record PatchDistributionInnerCommand(
    UUID distributionInnerId,
    @Positive BigDecimal theoreticalQuantity,
    @Positive BigDecimal requestedQuantity,
    @Positive BigDecimal consumptionFactor,
    String triggeredBy) implements Command {

}
