package com.inditex.icdmdemg.application.distributioninner.command;

import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import org.jspecify.annotations.NonNull;

public record DeleteDistributionInnerCommand(DeleteInnerRequest deleteInnerRequest) implements BaseCommand<DistributionInner> {

  public record DeleteInnerRequest(
      @NonNull UUID distributionInnerId,
      @NonNull String triggeredBy) {
  }
}
