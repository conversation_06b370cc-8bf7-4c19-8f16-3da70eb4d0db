package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import java.time.OffsetDateTime;

import com.inditex.icdmdemg.application.shipmentwarehouse.validator.ShipmentWarehouseTrackingCodeValidator;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import jakarta.validation.constraints.NotNull;

public record ProjectShipmentWarehouseStartedCommand(ShipmentWarehouseStarted shipmentWarehouseStarted)
    implements
      BaseCommand<ShipmentWarehouse> {

  public record ShipmentWarehouseStarted(
      @ShipmentWarehouseTrackingCodeValidator String distributionTrackingCode,
      @NotNull OffsetDateTime distributionStartDate) {
  }

}
