package com.inditex.icdmdemg.application.distributionnominated.command;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseUniqueKey;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.UseId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedPlan;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Line;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;

import org.jspecify.annotations.NullMarked;

@NullMarked
public sealed interface PlanRequest {

  DistributionNominatedPlan getDistributionNominatedPlan();

  record AutoRequest() implements PlanRequest {

    @Override
    public DistributionNominatedPlan getDistributionNominatedPlan() {
      return DistributionNominatedPlan.AUTO;
    }
  }

  record PreselectedRequest(List<LineSelectionRequest> lineSelections) implements PlanRequest {

    public List<Line> toDomain(
        final Id rootId,
        final ReferenceId referenceId,
        final BudgetCycle budgetCycle,
        final UseId useId,
        final Map<MaterialCommitmentUseUniqueKey, MaterialCommitmentUse> commmitmentsMap) {
      return this.lineSelections().stream()
          .map(lineSelection -> {
            final var commitmentUseUniqueKey = new MaterialCommitmentUseUniqueKey(
                lineSelection.commitmentOrderId().toString(),
                lineSelection.commitmentOrderLineId().toString(),
                referenceId.value().toString(),
                budgetCycle.value(),
                useId.value().toString());
            if (!commmitmentsMap.containsKey(commitmentUseUniqueKey)) {
              throw new ErrorException(new BadRequest(
                  String.format("Commitment %s with line %s not found", commitmentUseUniqueKey.orderId(),
                      commitmentUseUniqueKey.orderLineId())));
            }
            final var supplierId = commmitmentsMap.get(commitmentUseUniqueKey).getServiceLocalizationId().value();
            final var commitmentOrder = new CommitmentOrder(
                new CommitmentOrder.Id(lineSelection.commitmentOrderId()),
                new LineId(lineSelection.commitmentOrderLineId()),
                new SupplierId(supplierId));
            return new Line(
                rootId,
                commitmentOrder,
                referenceId,
                referenceId,
                lineSelection.lineRequested(),
                BigDecimal.ZERO);
          })
          .toList();
    }

    @Override
    public DistributionNominatedPlan getDistributionNominatedPlan() {
      return DistributionNominatedPlan.PRESELECTED;
    }

    public record LineSelectionRequest(
        UUID commitmentOrderId,
        UUID commitmentOrderLineId,
        BigDecimal lineRequested) {

    }
  }
}
