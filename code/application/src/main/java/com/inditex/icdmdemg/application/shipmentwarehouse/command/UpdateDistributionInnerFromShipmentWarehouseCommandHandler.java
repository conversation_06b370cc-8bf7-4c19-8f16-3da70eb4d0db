package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import java.time.OffsetDateTime;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.DistributionEndDate;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.DistributionStartDate;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.TrackingCode;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
@Slf4j
public class UpdateDistributionInnerFromShipmentWarehouseCommandHandler
    implements CommandHandler<UpdateDistributionInnerFromShipmentWarehouseCommand> {

  public static final String SHIPMENT_WAREHOUSE_EVENT = "ShipmentWarehouse event";

  public static final String DISTRIBUTION_INNER_NOT_FOUND = "DistributionInner not found for id %s and lineId %s";

  public static final String UPDATE_DISTRIBUTION_FOR_NOT_SUPPOSE_TO_UPDATE_DI_WARN =
      "Updating distribution for DistributionInner with id %s and status %s";

  public static final String END_DISTRIBUTION_NOT_IN_PROGRESS_WARN = "Updating distribution end date for di %s whose status is %s";

  private final DistributionInnerRepository distributionInnerRepository;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  private final Transaction transaction;

  @Override
  public void doHandle(final UpdateDistributionInnerFromShipmentWarehouseCommand command) {
    final var shipmentWarehouse = command.shipmentWarehouse();
    final var now = this.clockUtils.getCurrentOffsetDateTime();

    final var distributionInner =
        this.distributionInnerRepository.findById(new Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerId().value())))
            .map(distInner -> this.updateLineWithDistribution(shipmentWarehouse, distInner, now))
            .orElseThrow(() -> new IllegalArgumentException(String.format(DISTRIBUTION_INNER_NOT_FOUND,
                shipmentWarehouse.getDistributionInner().innerId().value(),
                shipmentWarehouse.getDistributionInner().innerLineId().value())));

    this.transaction.run(() -> {
      this.distributionInnerRepository.save(distributionInner);
      this.eventBus.send(distributionInner.domainEvents());
    });
  }

  private DistributionInner updateLineWithDistribution(final ShipmentWarehouse shipmentWarehouse,
      final DistributionInner distributionInner, final OffsetDateTime now) {
    if (distributionInner.status().isClosed()
        || distributionInner.status().isNonDistributable() || distributionInner.status().isCanceled()) {
      final String warnMessage = String.format(UPDATE_DISTRIBUTION_FOR_NOT_SUPPOSE_TO_UPDATE_DI_WARN, distributionInner.getId().value(),
          distributionInner.status().value());
      log.warn(warnMessage);
    }

    if (isDINotInProgressAndShipmentHasEnded(shipmentWarehouse, distributionInner)) {
      final String alertMessage = String.format(END_DISTRIBUTION_NOT_IN_PROGRESS_WARN, distributionInner.getId().value(),
          distributionInner.status().value());
      log.warn(alertMessage);
    }

    return distributionInner.updateDistributionInnerLineWithDistribution(
        new DistributionInnerLine.Id(UUID.fromString(shipmentWarehouse.getDistributionInner().innerLineId().value())),
        new TrackingCode(shipmentWarehouse.getTrackingCode().value()),
        new DistributionInnerLine.DistributedQuantity(shipmentWarehouse.getQuantity().value()),
        acceptNullElseMap(shipmentWarehouse.getStartDate(), time -> new DistributionStartDate(time.value())),
        acceptNullElseMap(shipmentWarehouse.getEndDate(), time -> new DistributionEndDate(time.value())),
        SHIPMENT_WAREHOUSE_EVENT,
        now);
  }

  private static boolean isDINotInProgressAndShipmentHasEnded(ShipmentWarehouse shipmentWarehouse,
      DistributionInner distributionInner) {
    return !distributionInner.status().isInProgress() && shipmentWarehouse.getEndDate() != null;
  }
}
