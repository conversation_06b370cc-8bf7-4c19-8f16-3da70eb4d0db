package com.inditex.icdmdemg.application.distributionnominated.query;

import java.util.List;
import java.util.Optional;

import com.inditex.icdmdemg.application.distributionnominated.query.GetDistributionNominatedByRegularizationCriteriaQuery.LineRegularization;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.AlternativeReference.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.iopcmmnt.cqrs.core.Query;

import org.jspecify.annotations.NonNull;

public record GetDistributionNominatedByRegularizationCriteriaQuery(
    List<RegularizationCriteria> criteriaList) implements Query<Optional<LineRegularization>> {

  public record RegularizationCriteria(
      @NonNull CommitmentOrder commitmentOrder,
      DistributionNominated.@NonNull ReferenceId referenceId,
      @NonNull RequestedQuantity requestedQuantity) {
  }

  public record LineRegularization(DistributionNominated.@NonNull Id distributionNominatedId,
      DistributionNominatedLine.@NonNull Id distributionNominatedLineId) {
  }
}
