package com.inditex.icdmdemg.application.shipmentcommitment.command;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import java.util.Optional;

import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentModifiedCommand.ShipmentModified;
import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitment;
import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitmentRepository;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentDistributedQuantity;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentId;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentMeasurementUnitId;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentQuantity;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
class ProjectCommitmentShipmentModifiedCommandHandler
    implements ResultCommandHandler<ProjectCommitmentShipmentModifiedCommand, Optional<ShipmentCommitment>> {

  private final ClockUtils clock;

  private final ShipmentCommitmentRepository repository;

  private final Transaction transaction;

  @Override
  public Optional<ShipmentCommitment> execute(final ProjectCommitmentShipmentModifiedCommand command) {
    final ShipmentModified shipmentCommitmentModified = command.shipmentModified();
    return this.update(shipmentCommitmentModified);
  }

  private Optional<ShipmentCommitment> update(final ShipmentModified shipmentModified) {
    return this.repository.findById(new ShipmentCommitmentId(shipmentModified.id()))
        .map(shipmentCommitment -> {
          final var updatedShipmentCommitment = shipmentCommitment.update(
              new ShipmentCommitmentDistributedQuantity(
                  new ShipmentCommitmentQuantity(shipmentModified.quantity()),
                  acceptNullElseMap(shipmentModified.measurementUnitId(), ShipmentCommitmentMeasurementUnitId::new)),
              this.clock.getCurrentOffsetDateTime());

          return this.transaction.run(() -> this.repository.save(updatedShipmentCommitment));
        }).or(() -> {
          log.info("Shipment commitment not found with id: {}", shipmentModified.id());
          return Optional.empty();
        });
  }

}
