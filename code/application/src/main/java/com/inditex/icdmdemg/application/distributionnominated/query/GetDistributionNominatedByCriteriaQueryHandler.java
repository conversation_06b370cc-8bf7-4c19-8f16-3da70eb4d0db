package com.inditex.icdmdemg.application.distributionnominated.query;

import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.Id;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedStatus;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetDistributionNominatedByCriteriaQueryHandler
    implements QueryHandler<GetDistributionNominatedByCriteriaQuery, Slice<DistributionNominated>> {

  private final DistributionNominatedRepository distributionNominatedRepository;

  @Override
  public Slice<DistributionNominated> ask(final GetDistributionNominatedByCriteriaQuery query) {
    final var referenceIds = query.referenceIds().stream().filter(Objects::nonNull).map(ReferenceId::new).toList();
    final var productOrderIds = query.productOrderIds().stream().filter(Objects::nonNull).map(ProductOrderId::new).toList();
    final var lineIds = query.lineIds().stream().filter(Objects::nonNull).map(Id::new).toList();
    final var commitmentOrderIds = query.commitmentOrderIds().stream().filter(Objects::nonNull).map(CommitmentOrder.Id::new).toList();
    final var budgetCycles = query.budgetCycles().stream().filter(Objects::nonNull).map(BudgetCycle::new).toList();
    final var statusList = query.status().stream().filter(Objects::nonNull).map(DistributionNominatedStatus::valueOf).toList();

    if (Stream.of(referenceIds, productOrderIds, lineIds, commitmentOrderIds).allMatch(List::isEmpty)) {
      throw new ErrorException(new BadRequest("Some filter is required"));
    }

    return this.distributionNominatedRepository.findByCriteria(
        referenceIds, productOrderIds, lineIds, commitmentOrderIds, budgetCycles, statusList, query.pageable());
  }
}
