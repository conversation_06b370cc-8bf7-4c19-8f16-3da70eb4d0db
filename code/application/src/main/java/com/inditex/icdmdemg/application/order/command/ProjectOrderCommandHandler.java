package com.inditex.icdmdemg.application.order.command;

import com.inditex.icdmdemg.application.order.service.OrderProjector;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class ProjectOrderCommandHandler implements CommandHandler<ProjectOrderCommand> {

  private final OrderProjector orderProjector;

  @Override
  public void doHandle(final ProjectOrderCommand command) {
    this.orderProjector.projectFromProductOrder(command.productOrderEvent());
  }

}
