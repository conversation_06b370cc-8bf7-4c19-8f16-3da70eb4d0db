package com.inditex.icdmdemg.application.distributioninner.command;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.shared.Error.NotFound;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CloseDistributionInnerCommandHandler implements CommandHandler<CloseDistributionInnerCommand> {

  private final DistributionInnerRepository distributionInnerRepository;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  private final Transaction transaction;

  @Override
  public void doHandle(final CloseDistributionInnerCommand command) {
    final var distributionInner = this.distributionInnerRepository.findById(new Id(command.distributionInnerId()))
        .orElseThrow(
            () -> new ErrorException(new NotFound(String.format("Distribution Inner %s not found", command.distributionInnerId()))));

    distributionInner.close(this.clockUtils.getCurrentOffsetDateTime(), command.triggeredBy());

    this.transaction.run(() -> {
      this.distributionInnerRepository.save(distributionInner);
      this.eventBus.send(distributionInner.domainEvents());
    });
  }

}
