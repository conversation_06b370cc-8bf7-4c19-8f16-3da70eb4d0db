package com.inditex.icdmdemg.application.use.command;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.Locale;

import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.domain.use.entity.UseName;
import com.inditex.icdmdemg.domain.use.entity.UseName.Lang;
import com.inditex.icdmdemg.domain.use.entity.UseName.Name;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record UseRequestNames(List<UseRequestName> values) {

  public UseRequestNames(List<UseRequestName> values) {
    this.values = List.copyOf(values);
  }

  public List<UseName> toDomain(final OffsetDateTime now) {
    return this.values().stream().map(it -> it.toDomain(now)).toList();
  }

  public boolean isEmpty() {
    return this.values().isEmpty();
  }

  public record UseRequestName(
      Locale locale,
      String translation) {

    public UseName toDomain(final OffsetDateTime now) {
      return new UseName(
          new Name(this.translation()),
          new Lang(this.locale().getLanguage()),
          new BasicAudit(now, now));
    }

  }
}
