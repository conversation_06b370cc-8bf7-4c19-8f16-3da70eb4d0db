package com.inditex.icdmdemg.application.distributionnominated.command;

import static com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.fromDistributions;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.application.distributionnominated.service.AlternativeSharedRawMaterialProvider;
import com.inditex.icdmdemg.application.distributionnominated.service.DistributionNominatedLinesIdentifier;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjustedProvider;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.DistributionNominatedPlannerType;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan.Adjust;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AdjustDistributionNominatedCommandHandler implements CommandHandler<AdjustDistributionNominatedCommand> {

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final CommitmentAdjustedProvider commitmentAdjustedProvider;

  private final DistributionNominatedLinesIdentifier linesIdentifier;

  private final AlternativeSharedRawMaterialProvider alternativeSharedRawMaterialProvider;

  private final ClockUtils clockUtils;

  private final Transaction transaction;

  private final EventBus eventBus;

  @Override
  public void doHandle(AdjustDistributionNominatedCommand command) {

    final var sharedRawMaterialNominateds = command.sharedRawMaterialNominated();

    if (sharedRawMaterialNominateds.isEmpty()) {
      return;
    }

    final var distributions = this.distributionNominatedRepository.findByAnySharedRawMaterial(sharedRawMaterialNominateds);
    final var commitmentSharedRawMaterialList = this.getEquivalentAssignableSharedRawMaterialList(distributions);
    final var adjustedCommitments = this.commitmentAdjustedProvider.findByAnySharedRawMaterial(commitmentSharedRawMaterialList);

    final var distributionsGroupedBySharedRawMaterial = distributions.stream()
        .collect(Collectors.groupingBy(DistributionNominated::sharedRawMaterial));
    final var adjustedCommitmentsGroupedBySharedRawMaterial = adjustedCommitments.stream()
        .collect(Collectors.groupingBy(CommitmentAdjusted::sharedRawMaterial));

    sharedRawMaterialNominateds.forEach(sharedRawMaterialNominated -> this.adjustSharedRawMaterial(
        command, sharedRawMaterialNominated, distributionsGroupedBySharedRawMaterial, adjustedCommitmentsGroupedBySharedRawMaterial));

  }

  private void adjustSharedRawMaterial(
      final AdjustDistributionNominatedCommand command,
      final SharedRawMaterialNominated sharedRawMaterial,
      final Map<SharedRawMaterialNominated, List<DistributionNominated>> distributionsGroupedBySharedRawMaterial,
      final Map<MaterialCommitmentUseSharedRawMaterial, List<CommitmentAdjusted>> adjustedCommitmentsGroupedBySharedRawMaterial) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var distributions = distributionsGroupedBySharedRawMaterial.getOrDefault(sharedRawMaterial, List.of());
    final var commitmentSharedRawMaterialList = this.getEquivalentAssignableSharedRawMaterialList(distributions);
    final var adjustedCommitments = commitmentSharedRawMaterialList.stream()
        .map(srm -> adjustedCommitmentsGroupedBySharedRawMaterial.getOrDefault(srm, List.of()))
        .flatMap(Collection::stream).distinct()
        .toList();

    final var planner =
        fromDistributions(sharedRawMaterial, distributions, adjustedCommitments, new Adjust(), DistributionNominatedPlannerType.DEFAULT);
    final var adjustedLines = planner.executePlan();

    final var identifiedLines = this.linesIdentifier.identifyAdjustedLines(distributions, adjustedLines, now);
    final var updatedDistributions = this.updateDistributions(distributions, identifiedLines, now, command.triggeredBy());

    updatedDistributions.forEach(
        distributionNominated -> this.transaction.run(() -> {
          this.distributionNominatedRepository.save(distributionNominated);
          this.eventBus.send(distributionNominated.domainEvents());
        }));
  }

  private List<MaterialCommitmentUseSharedRawMaterial> getEquivalentAssignableSharedRawMaterialList(
      final List<DistributionNominated> distributions) {
    final var sharedRawMaterialList = distributions.stream()
        .map(DistributionNominated::sharedRawMaterial)
        .map(MaterialCommitmentUseSharedRawMaterial::of)
        .distinct()
        .toList();
    return this.alternativeSharedRawMaterialProvider.alternatives(sharedRawMaterialList);
  }

  private List<DistributionNominated> updateDistributions(
      List<DistributionNominated> distributions,
      Map<Id, List<DistributionNominatedLine>> identifiedLines,
      OffsetDateTime occurredOn, String triggeredBy) {
    return distributions.stream()
        .map(distribution -> {
          final var lines = identifiedLines.getOrDefault(distribution.getId(), List.of());
          return distribution.adjust(lines, occurredOn, triggeredBy);
        })
        .toList();
  }

}
