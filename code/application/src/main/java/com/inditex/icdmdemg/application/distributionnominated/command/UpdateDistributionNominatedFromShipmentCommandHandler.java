package com.inditex.icdmdemg.application.distributionnominated.command;

import static java.util.Comparator.comparing;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributionStartDate;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.Id;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitmentRepository;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentDistributionNominatedLineId;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UpdateDistributionNominatedFromShipmentCommandHandler
    implements CommandHandler<UpdateDistributionNominatedFromShipmentCommand> {

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final ShipmentCommitmentRepository shipmentCommitmentRepository;

  private final OrderRepository orderRepository;

  private final Transaction transaction;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  @Override
  public void doHandle(final UpdateDistributionNominatedFromShipmentCommand command) {
    final var distributionNominatedLineId = command.distributionNominatedDistributed().lineId();
    final var distributionNominated =
        this.distributionNominatedRepository.findByLineId(new Id(UUID.fromString(distributionNominatedLineId)))
            .orElseThrow(() -> new IllegalArgumentException(
                String.format("Distribution Nominated with lineId %s not found", distributionNominatedLineId)));

    final var order = this.orderRepository.find(new OrderId(distributionNominated.productOrderId().value().toString()))
        .orElseThrow(() -> new IllegalArgumentException(
            String.format("Order with id %s not found", distributionNominated.productOrderId().value().toString())));
    final var orderInfo = ProductOrderStatusInfo.of(order);

    final var shipments = this.shipmentCommitmentRepository
        .findByDistributionNominatedLineId(
            new ShipmentCommitmentDistributionNominatedLineId(distributionNominatedLineId));
    final var totalLineQuantity = shipments
        .stream().map(shipment -> shipment.getDistributedQuantity().quantity().value())
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    final var oldestSentDateShipment = shipments
        .stream()
        .min(comparing(shipment -> shipment.getSentDate().value()))
        .map(shipment -> shipment.getSentDate().value());

    final var distributionStartDate =
        oldestSentDateShipment.isPresent() && totalLineQuantity.compareTo(BigDecimal.ZERO) > 0 ? oldestSentDateShipment.get() : null;

    distributionNominated.updateDistribution(
        new Id(UUID.fromString(distributionNominatedLineId)),
        new DistributedQuantity(totalLineQuantity),
        new DistributionStartDate(distributionStartDate),
        orderInfo,
        this.clockUtils.getCurrentOffsetDateTime(),
        command.distributionNominatedDistributed().triggeredBy());

    this.transaction.run(() -> {
      this.distributionNominatedRepository.save(distributionNominated);
      this.eventBus.send(distributionNominated.domainEvents());
    });
  }
}
