package com.inditex.icdmdemg.application.distributionsummary.query;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery.DistributionAllocated;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery.DistributionSummaryRequest;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionSummaryQuery.DistributionSummaryResponse;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLine;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.entity.SharedRawMaterialInner;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.Id;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetDistributionSummaryQueryHandler
    implements QueryHandler<GetDistributionSummaryQuery, Response<DistributionSummaryResponse>> {

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final DistributionInnerRepository distributionInnerRepository;

  private final MaterialCommitmentUseRepository materialCommitmentUseRepository;

  @Override
  public Response<DistributionSummaryResponse> ask(final GetDistributionSummaryQuery summaryQuery) {
    final var request = summaryQuery.DistributionSummaryRequest();
    final var commitmentOrders = this.findOpenCommitmentOrders(request);
    final var groupedDistributionNominates = this.distributionNominatedRepository
        .findBySharedRawMaterial(this.buildSharedMaterial(request)).stream()
        .collect(Collectors.toMap(
            CommonDistributionOrderInfo::of,
            dn -> this.convertToAllocatedSummary(dn, commitmentOrders),
            this::mergeAllocated))
        .values()
        .stream()
        .toList();

    final var groupedDistributionInners =
        this.distributionInnerRepository.findBySharedRawMaterial(this.buildSharedMaterialInner(request)).stream()
            .collect(Collectors.toMap(
                CommonDistributionOrderInfo::of,
                this::convertToAllocatedInnerSummary,
                this::mergeAllocated))
            .values()
            .stream()
            .toList();

    return Response.ofResponse(new DistributionSummaryResponse(
        request.referenceId(),
        request.useId(),
        request.budgetCycle(),
        groupedDistributionNominates,
        groupedDistributionInners));
  }

  private List<CommitmentOrder> findOpenCommitmentOrders(final DistributionSummaryRequest request) {
    return this.materialCommitmentUseRepository
        .findByAnySharedRawMaterial(List.of(
            MaterialCommitmentUseSharedRawMaterial.of(request.useId().toString(), request.referenceId().toString(), request.budgetCycle())))
        .open()
        .stream()
        .map(mcu -> this.toCommitmentOrder(mcu.getOrderLine(), mcu))
        .toList();
  }

  private CommitmentOrder toCommitmentOrder(final MaterialCommitmentUseOrderLine line, final MaterialCommitmentUse materialCommitmentUse) {
    return new CommitmentOrder(new Id(UUID.fromString(line.orderId().value())), new LineId(UUID.fromString(line.orderLineId().value())),
        new SupplierId(materialCommitmentUse.getServiceLocalizationId().value()));
  }

  private DistributionAllocated convertToAllocatedSummary(final DistributionNominated dn,
      final List<CommitmentOrder> commitmentOrders) {
    final var quantities = dn.quantitiesFromCommitmentOrders(commitmentOrders);
    return new DistributionAllocated(
        dn.productOrderId().value(),
        dn.productVariantGroupId().value(),
        quantities.requested().value(),
        quantities.distributed().value());
  }

  private DistributionAllocated convertToAllocatedInnerSummary(final DistributionInner di) {
    return new DistributionAllocated(
        di.productOrderId().value(),
        di.productVariantGroupId().value(),
        di.requestedQuantity().value(),
        di.distributedQuantity().value());
  }

  private DistributionAllocated mergeAllocated(final DistributionAllocated dn1,
      final DistributionAllocated dn2) {
    final var mergedRequestedQuantity = dn1.requestedQuantity().add(dn2.requestedQuantity());
    final var mergedDistributedQuantity = dn1.distributedQuantity().add(dn2.distributedQuantity());

    return new DistributionAllocated(
        dn1.productOrderId(),
        dn1.productVariantGroupId(),
        mergedRequestedQuantity,
        mergedDistributedQuantity);
  }

  private SharedRawMaterialNominated buildSharedMaterial(final DistributionSummaryRequest distributionSummaryRequest) {
    return new SharedRawMaterialNominated(
        distributionSummaryRequest.referenceId(),
        distributionSummaryRequest.useId(),
        distributionSummaryRequest.budgetCycle());
  }

  private SharedRawMaterialInner buildSharedMaterialInner(final DistributionSummaryRequest distributionSummaryRequest) {
    return new SharedRawMaterialInner(
        distributionSummaryRequest.referenceId(),
        distributionSummaryRequest.useId(),
        distributionSummaryRequest.budgetCycle());
  }

  private record CommonDistributionOrderInfo(UUID productOrderId, UUID productVariantGroupId) {

    static CommonDistributionOrderInfo of(final DistributionNominated distributionNominated) {
      return new CommonDistributionOrderInfo(distributionNominated.productOrderId().value(),
          distributionNominated.productVariantGroupId().value());
    }

    static CommonDistributionOrderInfo of(final DistributionInner distributionInner) {
      return new CommonDistributionOrderInfo(distributionInner.productOrderId().value(),
          distributionInner.productVariantGroupId().value());
    }

  }
}
