package com.inditex.icdmdemg.application.product.command;

import java.time.OffsetDateTime;
import java.util.UUID;

import com.inditex.icdmdemg.domain.product.ProductTaxonomyRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductTaxonomy;
import com.inditex.icdmdemg.domain.product.entity.ProductTaxonomy.ProductId;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ProjectProductTaxonomiesCommandHandler implements CommandHandler<ProjectProductTaxonomiesCommand> {

  private final ProductTaxonomyRepository productTaxonomyRepository;

  private final ClockUtils clockUtils;

  @Override
  public void doHandle(final ProjectProductTaxonomiesCommand command) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var newTaxonomy = command.productTaxonomy().taxonomy();
    final var productId = new ProductId(command.productTaxonomy().productId());
    final var taxonomy = this.productTaxonomyRepository.findByProductId(productId);
    taxonomy.ifPresentOrElse(existingTaxonomy -> this.updateTaxonomyIfChanged(existingTaxonomy, newTaxonomy, now),
        () -> this.createNewProductTaxonomy(productId.value(), newTaxonomy, now));
  }

  private void createNewProductTaxonomy(UUID productId, String newTaxonomy, OffsetDateTime now) {
    this.productTaxonomyRepository.save(
        ProductTaxonomy.create(productId, newTaxonomy, now, now));
  }

  private void updateTaxonomyIfChanged(ProductTaxonomy existingTaxonomy, String newTaxonomy, OffsetDateTime now) {
    if (existingTaxonomy.willBeUpdatedWith(newTaxonomy)) {
      this.productTaxonomyRepository.save(
          existingTaxonomy.updateTaxonomyValue(newTaxonomy,
              now));
    }
  }
}
