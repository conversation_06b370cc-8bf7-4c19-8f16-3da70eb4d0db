package com.inditex.icdmdemg.application.distributioninner.command;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DeleteDistributionInnerCommandHandler implements ResultCommandHandler<DeleteDistributionInnerCommand, DistributionInner> {
  private final Transaction transaction;

  private final ClockUtils clockUtils;

  private final DistributionInnerRepository distributionInnerRepository;

  private final EventBus eventBus;

  @Override
  public DistributionInner execute(final DeleteDistributionInnerCommand command) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var distributionInner = this.distributionInnerRepository
        .findById(new Id(command.deleteInnerRequest().distributionInnerId()))
        .orElseThrow(() -> new ErrorException(new BadRequest(
            String.format("Distribution inner not found for Id %s", command.deleteInnerRequest().distributionInnerId()))));

    final DistributionInner deleteDistributionInner = distributionInner.delete(now, command.deleteInnerRequest().triggeredBy());

    this.transaction.run(() -> {
      this.distributionInnerRepository.save(deleteDistributionInner);
      this.eventBus.send(deleteDistributionInner.domainEvents());
    });

    return deleteDistributionInner;
  }
}
