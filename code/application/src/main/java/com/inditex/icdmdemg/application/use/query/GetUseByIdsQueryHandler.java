package com.inditex.icdmdemg.application.use.query;

import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.Use.Id;
import com.inditex.icdmdemg.domain.use.UseRepository;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetUseByIdsQueryHandler
    implements QueryHandler<GetUseByIdsQuery, Slice<Use>> {

  private final UseRepository useRepository;

  @Override
  public Slice<Use> ask(final GetUseByIdsQuery query) {
    final var useIds = query.useIds().stream().filter(Objects::nonNull).map(Use.Id::new).toList();
    final var sliceData = this.useRepository.findByIdsPageable(useIds, query.pageable());

    if (useIds.isEmpty()) {
      return sliceData;
    } else {
      // To maintain same order than parameter
      final Map<Id, Use> mapData = sliceData.getContent().stream().collect(Collectors.toMap(Use::getId, item -> item));
      final var useOrderedAsCall = useIds.stream().filter(mapData::containsKey).map(mapData::get).toList();
      return new SliceImpl<>(useOrderedAsCall, sliceData.getPageable(), sliceData.hasNext());
    }
  }
}
