package com.inditex.icdmdemg.application.commitmentuse.service;

import java.util.UUID;

import com.inditex.icdmdemg.application.commitmentuse.command.CalculateNominatedProvisionCommand;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseTimestamp;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class MaterialCommitmentUseExpiredProcessor {

  private static final String TRIGGERED_BY = "MaterialCommitmentUseProcessor";

  private final CommandBus commandBus;

  private final ClockUtils clockUtils;

  private final MaterialCommitmentUseRepository materialCommitmentUseRepository;

  public void processExpiredExpectedDate() {
    final var now = MaterialCommitmentUseTimestamp.of(this.clockUtils.getCurrentOffsetDateTime());

    final var materialCommitmentUses =
        this.materialCommitmentUseRepository.findNotProcessedAndExpectedDateIsLessThan(now.value()).materialCommitmentUses();

    if (materialCommitmentUses.isEmpty()) {
      return;
    }

    final var materialCommitmentUsesProcessed = materialCommitmentUses.stream()
        .map(materialCommitmentUse -> materialCommitmentUse.setAsProcessed(now)).toList();

    final var sharedRawMaterialList = materialCommitmentUses.stream()
        .map(MaterialCommitmentUse::sharedRawMaterial)
        .map(this::mapToNominated).distinct().toList();

    this.commandBus.execute(new CalculateNominatedProvisionCommand(sharedRawMaterialList, TRIGGERED_BY));
    this.materialCommitmentUseRepository.saveAll(materialCommitmentUsesProcessed);
  }

  private SharedRawMaterialNominated mapToNominated(final MaterialCommitmentUseSharedRawMaterial commitmentUseSharedRawMaterial) {
    return new SharedRawMaterialNominated(
        UUID.fromString(commitmentUseSharedRawMaterial.materialCommitmentMaterialReferenceId().value()),
        UUID.fromString(commitmentUseSharedRawMaterial.materialCommitmentUseId().value()),
        commitmentUseSharedRawMaterial.materialCommitmentBudgetId().value());
  }
}
