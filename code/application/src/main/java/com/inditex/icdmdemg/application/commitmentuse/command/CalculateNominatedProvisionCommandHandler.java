package com.inditex.icdmdemg.application.commitmentuse.command;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.CalculateNominatedProvisionRequest;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.Commitment;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.CommitmentOrderLine;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.CommitmentOrderLineStatus;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.Distribution;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.DistributionQuantities;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.OrderLine;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.Provision;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.SharedRawMaterial;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.BudgetId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.DistributionDistributed;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.DistributionRequested;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Entered;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.LocalizationId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Ordered;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Pending;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.ProductId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.ReferenceId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Stock;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.UseId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvisionRepository;
import com.inditex.icdmdemg.domain.nominatedprovision.entity.NominatedProvisionSharedRawMaterial;
import com.inditex.icdmdemg.domain.product.Product;
import com.inditex.icdmdemg.domain.product.ProductRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CalculateNominatedProvisionCommandHandler
    implements CommandHandler<CalculateNominatedProvisionCommand> {

  private final MaterialCommitmentUseRepository materialCommitmentUseRepository;

  private final NominatedProvisionRepository nominatedProvisionRepository;

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final ProductRepository productRepository;

  private final ClockUtils clockUtils;

  @Override
  public void doHandle(final CalculateNominatedProvisionCommand command) {
    final var sharedRawMaterials = command.sharedRawMaterials().stream()
        .map(MaterialCommitmentUseSharedRawMaterial::of)
        .toList();
    final var commitments = this.materialCommitmentUseRepository.findByAnySharedRawMaterial(sharedRawMaterials)
        .materialCommitmentUses().stream()
        .map(this::getCommitment)
        .toList();
    final var distributionNominated = this.distributionNominatedRepository.findByAnySharedRawMaterial(command.sharedRawMaterials())
        .stream()
        .map(this::getDistributions)
        .flatMap(List::stream)
        .toList();

    final var calculateNominatedProvisions = CalculateNominatedProvision
        .calculate(
            new CalculateNominatedProvisionRequest(commitments, distributionNominated));
    final var referenceIds = calculateNominatedProvisions.provisions()
        .stream()
        .map(provision -> new ProductReferenceId(provision.provisionKey().sharedRawMaterial().referenceId().toString()))
        .collect(Collectors.toUnmodifiableSet());
    final var productInfoForRawMaterials = this.productRepository.findByReferenceIds(referenceIds.stream().toList()).mappedByReferenceId();

    this.nominatedProvisionRepository
        .deleteAllByAnySharedRawMaterial(sharedRawMaterials.stream().map(this::toNominatedProvisionSharedRawMaterial).toList());

    calculateNominatedProvisions.provisions()
        .forEach(provision -> {
          final var referenceId = provision.provisionKey().sharedRawMaterial().referenceId().toString();
          final var optionalProduct = Optional.ofNullable(productInfoForRawMaterials
              .get(new ProductReferenceId(referenceId)));
          optionalProduct.ifPresentOrElse(
              product -> this.nominatedProvisionRepository.save(this.nominatedProvisionFrom(provision, product, command.triggeredBy())),
              () -> log.error(
                  "NominatedProvision [referenceId: {}, useId: {}, budgetId: {}] "
                      + " could not be created because product with referenceId does not exist",
                  provision.provisionKey().sharedRawMaterial().referenceId(),
                  provision.provisionKey().sharedRawMaterial().useId(),
                  provision.provisionKey().sharedRawMaterial().budgetId()));
        });
  }

  private Commitment getCommitment(final MaterialCommitmentUse materialCommitmentUse) {
    return new Commitment(new OrderLine(
        UUID.fromString(materialCommitmentUse.getOrderLine().orderId().value()),
        UUID.fromString(materialCommitmentUse.getOrderLine().orderLineId().value()),
        new SharedRawMaterial(
            UUID.fromString(materialCommitmentUse.getMaterialReferenceId().value()),
            UUID.fromString(materialCommitmentUse.getUseId().value()),
            materialCommitmentUse.getOrderLine().budgetId().value())),
        materialCommitmentUse.getQuantity().value(),
        new CommitmentOrderLine(
            CommitmentOrderLineStatus.valueOf(materialCommitmentUse.getStatus().value()),
            materialCommitmentUse.getServiceLocalizationId().value(),
            materialCommitmentUse.getExpectedDate().value()));
  }

  private List<Distribution> getDistributions(final DistributionNominated distributionNominated) {
    return distributionNominated.lines().value().stream()
        .map(line -> new Distribution(
            new OrderLine(line.commitmentOrder().id().value(),
                line.commitmentOrder().lineId().value(),
                new SharedRawMaterial(distributionNominated.sharedRawMaterial().referenceId().value(),
                    distributionNominated.sharedRawMaterial().useId().value(),
                    distributionNominated.sharedRawMaterial().budgetCycle().value())),
            new DistributionQuantities(line.requestedQuantity().value(), line.distributedQuantity().value())))
        .toList();
  }

  private NominatedProvisionSharedRawMaterial toNominatedProvisionSharedRawMaterial(
      MaterialCommitmentUseSharedRawMaterial sharedRawMaterial) {
    return new NominatedProvisionSharedRawMaterial(
        new UseId(sharedRawMaterial.materialCommitmentUseId().value()),
        new ReferenceId(sharedRawMaterial.materialCommitmentMaterialReferenceId().value()),
        new BudgetId(sharedRawMaterial.materialCommitmentBudgetId().value()));
  }

  private NominatedProvision nominatedProvisionFrom(
      final Provision provision,
      final Product product,
      final String triggeredBy) {
    return NominatedProvision.create(
        new ProductId(product.getProductId().value()),
        new ReferenceId(provision.provisionKey().sharedRawMaterial().referenceId().toString()),
        new UseId(provision.provisionKey().sharedRawMaterial().useId().toString()),
        new BudgetId(provision.provisionKey().sharedRawMaterial().budgetId()),
        new LocalizationId(provision.provisionKey().localization()),
        new Ordered(provision.ordered()),
        new Entered(provision.entered()),
        new Pending(provision.pending()),
        new Stock(provision.stock()),
        new DistributionDistributed(provision.distribution().distributed()),
        new DistributionRequested(provision.distribution().requested()),
        this.clockUtils.getCurrentOffsetDateTime(),
        triggeredBy);
  }
}
