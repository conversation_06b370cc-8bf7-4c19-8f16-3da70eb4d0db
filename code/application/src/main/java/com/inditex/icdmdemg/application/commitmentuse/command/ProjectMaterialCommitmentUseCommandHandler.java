package com.inditex.icdmdemg.application.commitmentuse.command;

import java.util.List;

import com.inditex.icdmdemg.application.commitmentuse.service.MaterialCommitmentUseProjector;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.shared.EntityAndActionResult;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class ProjectMaterialCommitmentUseCommandHandler
    implements ResultCommandHandler<ProjectMaterialCommitmentUseCommand, ProjectMaterialCommitmentUseCommandResult> {

  private final MaterialCommitmentUseProjector materialCommitmentUseProjector;

  private final Transaction transaction;

  @Override
  public ProjectMaterialCommitmentUseCommandResult execute(final ProjectMaterialCommitmentUseCommand command) {
    final List<EntityAndActionResult<MaterialCommitmentUse>> entityAndActionResults =
        this.transaction.run(() -> this.materialCommitmentUseProjector.projectFromUseToOrder(command.orderUse()));
    return new ProjectMaterialCommitmentUseCommandResult(entityAndActionResults.stream().map(EntityAndActionResult::entity).toList());
  }
}
