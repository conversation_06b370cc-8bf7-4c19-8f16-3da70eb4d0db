package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import java.util.UUID;

import com.inditex.icdmdemg.application.shipmentwarehouse.validator.DistributionInnerIdsValidator;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import jakarta.validation.constraints.NotNull;

public record ProjectShipmentWarehouseCreatedCommand(ShipmentWarehouseCreated shipmentWarehouseCreated)
    implements
      BaseCommand<ShipmentWarehouse> {
  @DistributionInnerIdsValidator
  public record ShipmentWarehouseCreated(
      @NotNull UUID distributionInnerId,
      @NotNull UUID distributionInnerLineId,
      @NotNull String trackingCode) {
  }

}
