package com.inditex.icdmdemg.application.use.command;

import static java.lang.String.format;

import java.time.OffsetDateTime;
import java.util.List;

import com.inditex.icdmdemg.application.use.command.CreateUseCommand.UseRequest;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.Error.MessageError;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.domain.taxonomyuse.TaxonomyUseRepository;
import com.inditex.icdmdemg.domain.taxonomyuse.entity.TaxonomyCode;
import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.Use.Customer;
import com.inditex.icdmdemg.domain.use.Use.Id;
import com.inditex.icdmdemg.domain.use.Use.Taxonomy;
import com.inditex.icdmdemg.domain.use.UseRepository;
import com.inditex.icdmdemg.domain.use.entity.AssignableType;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeCondition;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;
import com.inditex.icdmdemg.domain.use.entity.PurchaseType;
import com.inditex.icdmdemg.domain.use.entity.UseConditions;
import com.inditex.icdmdemg.domain.use.entity.UseNames;
import com.inditex.icdmdemg.domain.use.entity.UsePurchasePurposeCondition;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.CollectionsUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CreateUseCommandHandler implements ResultCommandHandler<CreateUseCommand, CreateUseResult> {

  private static final List<String> VALID_LOCALE_LANGUAGES = List.of("en", "es");

  private final UuidGenerator uuidGenerator;

  private final ClockUtils clockUtils;

  private final UseRepository useRepository;

  private final TaxonomyUseRepository taxonomyUseRepository;

  private final Transaction transaction;

  private final EventBus eventBus;

  @Override
  public CreateUseResult execute(final CreateUseCommand command) {

    final var rootId = new Id(this.uuidGenerator.generate());
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var useRequest = command.useRequest();
    final var assignable = this.getAssignableValue(useRequest.assignable());
    final var customer = this.evaluateCustomer(useRequest.customer());
    final var purchaseType = this.evaluatePurchaseType(useRequest.purchaseType());
    final var useNames = this.getUseNames(now, useRequest.names());
    final var useConditions = this.generateUseConditionsAndValidate(now, useRequest, purchaseType);
    final var aggregateAudit = CompleteAudit.create(useRequest.triggeredBy(), now);
    final var taxonomy = this.evaluateTaxonomy(useRequest.taxonomy());

    final var use = Use.create(
        rootId,
        assignable,
        taxonomy,
        customer,
        purchaseType,
        useNames,
        useConditions,
        aggregateAudit);

    this.transaction.run(() -> {
      this.useRepository.save(use);
      this.eventBus.send(use.domainEvents());
    });

    return new CreateUseResult(use.getId());
  }

  private AssignableType getAssignableValue(final List<String> assignableValues) {
    try {
      return AssignableType.fromValues(assignableValues);
    } catch (final IllegalArgumentException e) {
      throw new ErrorException(new BadRequest(e.getMessage()));
    }
  }

  private Customer evaluateCustomer(final String customer) {
    if (!UrnConstantsEnum.BUYER.isValidUrn(customer)) {
      throw new ErrorException(new BadRequest(format("Customer %s is not valid format", customer)));
    }
    return new Customer(customer);
  }

  private PurchaseType evaluatePurchaseType(final List<String> purchaseTypeValues) {
    try {
      return PurchaseType.fromValues(purchaseTypeValues);
    } catch (final IllegalArgumentException e) {
      throw new ErrorException(new BadRequest(e.getMessage()));
    }
  }

  private UseNames getUseNames(final OffsetDateTime now, final UseRequestNames useRequestNames) {
    return new UseNames(useRequestNames.toDomain(now));
  }

  private UseConditions generateUseConditionsAndValidate(final OffsetDateTime now,
      final UseRequest useRequest,
      final PurchaseType purchaseType) {
    final var uses = this.useRepository.findBySingleTaxonomyCustomerAndPurchaseType(new Taxonomy(useRequest.taxonomy()),
        new Customer(useRequest.customer()), purchaseType);

    final var domainConditionsFromRequest = useRequest.conditions().stream()
        .map(condition -> {
          this.evaluateUseCondition(condition.condition());
          this.evaluateUseConditionName(condition.conditionName());
          return condition.toDomain(now);
        })
        .toList();
    this.evaluateUsePurchasePurposeConditions(useRequest, uses, domainConditionsFromRequest);

    return new UseConditions(domainConditionsFromRequest);
  }

  private void evaluateUsePurchasePurposeConditions(final UseRequest useRequest, final List<Use> uses,
      final List<UsePurchasePurposeCondition> purchasePurposeConditionsFromRequest) {
    final boolean useExists = uses.stream()
        .anyMatch(use -> CollectionsUtils.haveSameElements(use.conditions().value(), purchasePurposeConditionsFromRequest,
            UsePurchasePurposeCondition.comparator()));
    if (useExists) {
      throw new ErrorException(new MessageError(
          format("Use already exists for taxonomy %s, customer %s, purchaseType %s and conditions %s",
              useRequest.taxonomy(), useRequest.customer(), useRequest.purchaseType(), useRequest.conditions())));
    }
  }

  private void evaluateUseCondition(final String condition) {
    if (!PurchasePurposeCondition.contains(condition)) {
      throw new ErrorException(new BadRequest(format("Purchase condition type %s not valid", condition)));
    }
  }

  private void evaluateUseConditionName(final String name) {
    if (!PurchasePurposeConditionName.contains(name)) {
      throw new ErrorException(new BadRequest(format("Purchase condition name %s not valid", name)));
    }
  }

  private Taxonomy evaluateTaxonomy(final String taxonomy) {
    return this.taxonomyUseRepository.findByCode(new TaxonomyCode(taxonomy))
        .map(taxonomyUse -> new Taxonomy(taxonomyUse.getCode().value()))
        .orElseThrow(() -> new ErrorException(new BadRequest(format("Taxonomy %s not found", taxonomy))));
  }
}
