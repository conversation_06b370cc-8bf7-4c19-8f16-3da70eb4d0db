package com.inditex.icdmdemg.application.product.service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.domain.product.Product;
import com.inditex.icdmdemg.domain.product.Product.EquivalentReference;
import com.inditex.icdmdemg.domain.product.ProductRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductColor;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;

import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class ProductAlternativeProvider {

  private final ProductRepository productRepository;

  private final String uncolouredId;

  public ProductAlternativeProvider(ProductRepository productRepository, @Value("${product-reference.uncoloured.id}") String uncolouredId) {
    this.productRepository = productRepository;
    this.uncolouredId = uncolouredId;
  }

  public Optional<Product> alternativeProduct(Product product) {
    return this.alternativeProducts(List.of(product)).get(product);
  }

  public Optional<Product> alternativeProductReference(ProductReferenceId productReferenceId) {
    return this.productRepository.findByReferenceId(productReferenceId)
        .flatMap(product -> this.alternativeProducts(List.of(product)).get(product));
  }

  public Map<ProductReferenceId, Optional<Product>> alternativeProductReferences(List<ProductReferenceId> productReferenceIds) {
    final var products = this.productRepository.findByReferenceIds(productReferenceIds);
    final var alternatives = this.alternativeProducts(products.products()).entrySet().stream()
        .map(entry -> Pair.of(entry.getKey().getReferenceId(), entry.getValue()))
        .collect(Collectors.toMap(Pair::getKey, Pair::getValue));
    return productReferenceIds.stream()
        .collect(Collectors.toMap(
            Function.identity(),
            productReferenceId -> alternatives.containsKey(productReferenceId)
                ? alternatives.get(productReferenceId)
                : Optional.empty()));
  }

  public Map<Product, Optional<Product>> alternativeProducts(List<Product> products) {
    final var productColor = new ProductColor(UUID.fromString(this.uncolouredId));
    final var equivalentReferencesColorFilled = products.stream()
        .map(Product::equivalentReference)
        .map(reference -> reference.withColorId(productColor))
        .filter(EquivalentReference::allNonNull)
        .distinct()
        .toList();
    final var equivalentProducts = this.productRepository.findByAnyEquivalentReference(equivalentReferencesColorFilled);
    final var groupedByEquivalentReference = equivalentProducts.products().stream()
        .collect(Collectors.groupingBy(Product::equivalentReference));
    return products.stream()
        .distinct()
        .map(product -> {
          final var colorFilled = product.equivalentReference().withColorId(productColor);
          final var firstEquivalent = groupedByEquivalentReference.getOrDefault(colorFilled, List.of()).stream().findFirst();
          return Pair.of(product, firstEquivalent);
        })
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
  }
}
