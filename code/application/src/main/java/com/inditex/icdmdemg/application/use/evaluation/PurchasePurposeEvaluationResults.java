package com.inditex.icdmdemg.application.use.evaluation;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record PurchasePurposeEvaluationResults(List<PurchasePurposeEvaluationResult> results) {

  private static final Comparator<PurchasePurposeEvaluationResult> INSTANCE = Comparator
      .comparingInt(PurchasePurposeEvaluationResult::kos)
      .thenComparing(Comparator.comparingInt(PurchasePurposeEvaluationResult::oks).reversed())
      .thenComparing(result -> result.paramsWithOk().stream().map(PurchasePurposeConditionName::getPriority)
          .min(Integer::compareTo).orElse(Integer.MAX_VALUE))
      .thenComparing(Comparator.comparingInt(PurchasePurposeEvaluationResult::coincidences).reversed());

  public PurchasePurposeEvaluationResults(List<PurchasePurposeEvaluationResult> results) {
    this.results = results.stream().sorted(INSTANCE).toList();
  }

  public static PurchasePurposeEvaluationResults empty() {
    return new PurchasePurposeEvaluationResults(List.of());
  }

  public List<Use.Id> useIds() {
    return this.results.stream().map(PurchasePurposeEvaluationResult::useId).toList();
  }

  public Map<Use.Id, Boolean> compiledUseIds() {
    return this.results.stream().collect(Collectors.toMap(PurchasePurposeEvaluationResult::useId, PurchasePurposeEvaluationResult::isOk));
  }
}
