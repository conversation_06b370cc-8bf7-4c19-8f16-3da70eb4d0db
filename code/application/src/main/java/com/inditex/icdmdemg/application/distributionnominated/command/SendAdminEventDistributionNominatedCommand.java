package com.inditex.icdmdemg.application.distributionnominated.command;

import java.util.UUID;

import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import org.jspecify.annotations.NonNull;

public record SendAdminEventDistributionNominatedCommand(
    SendEventRequest sendEventRequest) implements BaseCommand<SendAdminEventNominatedResult> {

  public record SendEventRequest(
      @NonNull UUID distributionNominatedId,
      @NonNull String eventType) {
  }

}
