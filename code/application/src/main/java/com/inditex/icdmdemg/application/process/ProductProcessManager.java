package com.inditex.icdmdemg.application.process;

import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.application.distributioninner.command.UpdateDistributionInnerProductVariantCommand;
import com.inditex.icdmdemg.application.distributioninner.command.UpdateDistributionInnerReferenceCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedProductVariantCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedReferenceCommand;
import com.inditex.icdmdemg.application.product.command.DeleteProductProjectionCommand;
import com.inditex.icdmdemg.application.product.command.DeleteProductProjectionCommand.ProductReferenceIdDeleted;
import com.inditex.icdmdemg.application.product.command.DeleteProductTaxonomiesCommand;
import com.inditex.icdmdemg.application.product.command.DeleteProductTaxonomiesCommand.DeletedProduct;
import com.inditex.icdmdemg.application.product.command.ProjectProductCommand;
import com.inditex.icdmdemg.application.product.command.ProjectProductCommand.ProductReference;
import com.inditex.icdmdemg.application.product.command.ProjectProductTaxonomiesCommand;
import com.inditex.icdmdemg.application.product.command.ProjectProductTaxonomiesCommand.ProductTaxonomy;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NonNull;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ProductProcessManager {

  private final CommandBus commandBus;

  public void execute(final ProductReferenceIdDeleted productReferenceIdDeleted) {
    this.commandBus.execute(new DeleteProductProjectionCommand(productReferenceIdDeleted));
  }

  public void execute(final ProductReference productReference) {
    this.commandBus.execute(new ProjectProductCommand(productReference));
  }

  public void execute(final ProductTaxonomy productTaxonomy) {
    this.commandBus.execute(new ProjectProductTaxonomiesCommand(productTaxonomy));
  }

  public void execute(final DeletedProduct deletedProduct) {
    this.commandBus.execute(new DeleteProductTaxonomiesCommand(deletedProduct));
  }

  public void execute(final MergeSplitSummary mergeSplitSummary) {
    this.commandBus
        .execute(new UpdateDistributionNominatedProductVariantCommand(
            mergeSplitSummary.updateVariantGroupInput(),
            mergeSplitSummary.triggeredBy()));
    this.commandBus
        .execute(new UpdateDistributionNominatedReferenceCommand(
            mergeSplitSummary.updateReferenceInput(),
            mergeSplitSummary.triggeredBy()));

    this.commandBus.execute(new UpdateDistributionInnerProductVariantCommand(
        mergeSplitSummary.updateVariantGroupInput(),
        mergeSplitSummary.triggeredBy()));

    this.commandBus
        .execute(new UpdateDistributionInnerReferenceCommand(
            mergeSplitSummary.updateReferenceInput(),
            mergeSplitSummary.triggeredBy()));
  }

  public record MergeSplitSummary(UpdateReferenceInput updateReferenceInput, UpdateVariantGroupInput updateVariantGroupInput,
      @NonNull String triggeredBy) {

    public record UpdateReferenceInput(@NonNull UUID productId, @NonNull String taxonomy, List<Variant> variants) {

      public static final String EXCLUDE_TAXONOMY = "CLOTHES";

      public Map<UUID, UUID> getReferenceIdsBySourceIds() {
        return this.variants.stream().collect(Collectors.toMap(Variant::referenceSourceId, Variant::referenceId));
      }

      public record Variant(@NonNull UUID referenceId, @NonNull UUID referenceSourceId) {
      }
    }

    public record UpdateVariantGroupInput(List<VariantGroup> variantGroups) {
      private static final String COLOR_GROUP = "COLOR_GROUP";

      public Map<UUID, UUID> getVariantGroupIdsBySourceIds() {
        return this.variantGroups.stream()
            .filter(variantGroup -> COLOR_GROUP.equals(variantGroup.name()))
            .collect(Collectors.toMap(VariantGroup::variantGroupSourceId, VariantGroup::variantGroupId));
      }

      public record VariantGroup(@NonNull UUID variantGroupId, @NonNull UUID variantGroupSourceId, @NonNull String name) {
      }
    }
  }
}
