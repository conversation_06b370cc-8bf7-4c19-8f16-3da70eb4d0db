package com.inditex.icdmdemg.application.distributionnominated.command;

import java.util.EnumSet;
import java.util.Set;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class SendAdminEventDistributionNominatedCommandHandler
    implements ResultCommandHandler<SendAdminEventDistributionNominatedCommand, SendAdminEventNominatedResult> {
  private static final Set<EventType> ADMITTED_EVENT_TYPES = EnumSet.of(
      EventType.UPDATED,
      EventType.CREATED,
      EventType.CORRECTED,
      EventType.CLOSED);

  private final Transaction transaction;

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final EventBus eventBus;

  @Override
  public SendAdminEventNominatedResult execute(final SendAdminEventDistributionNominatedCommand command) {
    final var eventTypeToSend = EventType.fromValue(command.sendEventRequest().eventType());
    if (!this.isAdmittedEventType(eventTypeToSend)) {
      throw new ErrorException(new BadRequest(
          "Event type not allowed. Allowed events are: "
              + "DistributionRequested OR DistributionCorrected "
              + "OR DistributionClosed OR DistributionCanceled"));
    }
    final var distribution = this.distributionNominatedRepository.findById(new Id(command.sendEventRequest().distributionNominatedId()))
        .orElseThrow(() -> new ErrorException(new BadRequest("Distribution not found with given ID")))
        .registerEvent(eventTypeToSend);
    this.eventBus.send(distribution.domainEvents());

    return new SendAdminEventNominatedResult(distribution.getId());
  }

  private boolean isAdmittedEventType(final EventType eventTypeToSend) {
    return ADMITTED_EVENT_TYPES.contains(eventTypeToSend);
  }

}
