package com.inditex.icdmdemg.application.process;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.commitmentuse.command.CalculateNominatedProvisionCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.AdjustDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.CreateDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.DeleteDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.DeleteDistributionNominatedCommand.DeleteNominatedRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PatchDistributionNominatedCommand;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DistributionNominatedProcessManager {
  private final CommandBus commandBus;

  public UUID createDistributionNominated(final CreateDistributionNominatedCommand command) {
    final var distributionNominated = this.commandBus.execute(command);
    this.commandBus
        .execute(new CalculateNominatedProvisionCommand(List.of(distributionNominated.sharedRawMaterial()),
            command.nominatedRequest().triggeredBy()));
    return distributionNominated.getId().value();
  }

  public void deleteDistributionNominated(final DeleteNominatedRequest request) {
    final var distributionNominated = this.commandBus.execute(new DeleteDistributionNominatedCommand(request));
    this.commandBus
        .execute(new AdjustDistributionNominatedCommand(List.of(distributionNominated.sharedRawMaterial()), request.triggeredBy()));
    this.commandBus
        .execute(new CalculateNominatedProvisionCommand(List.of(distributionNominated.sharedRawMaterial()), request.triggeredBy()));
  }

  public void patchDistributionNominated(final PatchDistributionNominatedCommand command) {
    final var triggeredBy = command.patchNominatedRequest().triggeredBy();
    final var distributionNominated = this.commandBus.execute(command);
    this.commandBus
        .execute(new AdjustDistributionNominatedCommand(List.of(distributionNominated.sharedRawMaterial()), triggeredBy));
    this.commandBus
        .execute(new CalculateNominatedProvisionCommand(List.of(distributionNominated.sharedRawMaterial()), triggeredBy));
  }
}
