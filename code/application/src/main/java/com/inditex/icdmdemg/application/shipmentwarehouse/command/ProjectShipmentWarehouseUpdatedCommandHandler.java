package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseUpdatedCommand.ShipmentWarehouseUpdated;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouseRepository;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseQuantity;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTimestamp;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTrackingCode;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ProjectShipmentWarehouseUpdatedCommandHandler implements
    ResultCommandHandler<ProjectShipmentWarehouseUpdatedCommand, ShipmentWarehouse> {

  private static final String SHIPMENT_WAREHOUSE_WITH_TRACKING_CODE_NOT_FOUND = "ShipmentWarehouse with tracking code %s not found";

  private final ShipmentWarehouseRepository shipmentWarehouseRepository;

  private final ClockUtils clockUtils;

  private final Transaction transaction;

  @Override
  public ShipmentWarehouse execute(final ProjectShipmentWarehouseUpdatedCommand command) {
    final var shipmentWarehouseUpdated = command.shipmentWarehouseUpdated();

    return this.shipmentWarehouseRepository.findByTrackingCode(
        new ShipmentWarehouseTrackingCode(shipmentWarehouseUpdated.distributionTrackingCode()))
        .map(shipmentWarehouseFound -> {
          this.updateDistributionWarehouseDelivery(shipmentWarehouseFound, shipmentWarehouseUpdated);
          this.transaction.run(() -> this.shipmentWarehouseRepository.save(shipmentWarehouseFound));
          return shipmentWarehouseFound;
        }).orElseThrow(() -> new IllegalArgumentException(String.format(SHIPMENT_WAREHOUSE_WITH_TRACKING_CODE_NOT_FOUND,
            shipmentWarehouseUpdated.distributionTrackingCode())));
  }

  private void updateDistributionWarehouseDelivery(final ShipmentWarehouse shipmentWarehouse,
      final ShipmentWarehouseUpdated shipmentWarehouseUpdated) {
    shipmentWarehouse.updateWithDelivery(
        new ShipmentWarehouseQuantity(shipmentWarehouseUpdated.quantity()),
        ShipmentWarehouseTimestamp.of(shipmentWarehouseUpdated.distributionLastUpdateDate()),
        ShipmentWarehouseTimestamp.of(this.clockUtils.getCurrentOffsetDateTime()));
  }
}
