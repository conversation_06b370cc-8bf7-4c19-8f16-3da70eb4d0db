package com.inditex.icdmdemg.application.distributioninner.command;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import jakarta.validation.constraints.Positive;
import org.jspecify.annotations.NonNull;

public record CreateDistributionInnerCommand(InnerRequest request) implements BaseCommand<CreateDistributionInnerResult> {

  public record InnerRequest(
      @NonNull UUID referenceId,
      @NonNull UUID useId,
      @NonNull String budgetCycle,
      @NonNull UUID productOrderId,
      @NonNull UUID productVariantGroupId,
      @Positive @NonNull BigDecimal theoreticalQuantity,
      @Positive @NonNull BigDecimal requestedQuantity,
      @Positive @NonNull BigDecimal consumptionFactor,
      @NonNull boolean sendToDistribution,
      @NonNull String triggeredBy) {

  }
}
