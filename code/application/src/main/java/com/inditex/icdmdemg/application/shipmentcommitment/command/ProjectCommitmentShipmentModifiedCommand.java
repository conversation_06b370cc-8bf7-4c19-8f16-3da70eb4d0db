package com.inditex.icdmdemg.application.shipmentcommitment.command;

import java.math.BigDecimal;
import java.util.Optional;

import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitment;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import org.jspecify.annotations.NonNull;

public record ProjectCommitmentShipmentModifiedCommand(
    ShipmentModified shipmentModified) implements BaseCommand<Optional<ShipmentCommitment>> {

  public record ShipmentModified(
      @NonNull String id,
      @NonNull String distributionNominatedLineId,
      BigDecimal quantity,
      String measurementUnitId) {

  }
}
