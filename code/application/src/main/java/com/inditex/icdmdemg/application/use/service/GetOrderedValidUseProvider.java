package com.inditex.icdmdemg.application.use.service;

import java.util.List;

import com.inditex.icdmdemg.application.use.evaluation.PurchasePurposeEvaluationResult;
import com.inditex.icdmdemg.application.use.evaluation.PurchasePurposeEvaluationResults;
import com.inditex.icdmdemg.application.use.evaluation.PurchasePurposeEvaluator;
import com.inditex.icdmdemg.domain.taxonomyuse.TaxonomyUse;
import com.inditex.icdmdemg.domain.taxonomyuse.TaxonomyUseRepository;
import com.inditex.icdmdemg.domain.taxonomyuse.entity.TaxonomyCode;
import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.Use.Customer;
import com.inditex.icdmdemg.domain.use.Use.Taxonomy;
import com.inditex.icdmdemg.domain.use.UseRepository;
import com.inditex.icdmdemg.domain.use.entity.PurchaseType;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetOrderedValidUseProvider {

  private final UseRepository useRepository;

  private final TaxonomyUseRepository taxonomyUseRepository;

  public PurchasePurposeEvaluationResults getOrderedValidUse(
      final TaxonomyCode code,
      final Customer customer,
      final PurchaseType purchaseType,
      final List<PurchasePurposeParameter> parameters) {

    return this.taxonomyUseRepository.findByCode(code)
        .map(taxonomyUse -> this.processValidUses(taxonomyUse, customer, purchaseType, parameters))
        .orElse(PurchasePurposeEvaluationResults.empty());
  }

  private PurchasePurposeEvaluationResults processValidUses(
      final TaxonomyUse taxonomyUse,
      final Customer customer,
      final PurchaseType purchaseType,
      final List<PurchasePurposeParameter> parameters) {

    final var purchaseTypes = this.determinePurchaseTypesByPurchaseType(purchaseType);
    final var uses = this.findUses(taxonomyUse, customer, purchaseTypes);
    return this.evaluateUses(uses, parameters);
  }

  private List<PurchaseType> determinePurchaseTypesByPurchaseType(final PurchaseType purchaseType) {
    return PurchaseType.NOMINATED_INNER.equals(purchaseType)
        ? List.of(PurchaseType.INNER, PurchaseType.NOMINATED, PurchaseType.NOMINATED_INNER)
        : List.of(purchaseType, PurchaseType.NOMINATED_INNER);
  }

  private List<Use> findUses(
      final TaxonomyUse taxonomyUse,
      final Customer customer,
      final List<PurchaseType> purchaseTypes) {
    return this.useRepository.findByTaxonomiesAndCustomerAndPurchaseTypes(
        taxonomyUse.mapToTaxonomies().stream().map(Taxonomy::new).toList(),
        customer,
        purchaseTypes);
  }

  private PurchasePurposeEvaluationResults evaluateUses(
      final List<Use> uses,
      final List<PurchasePurposeParameter> parameters) {
    return new PurchasePurposeEvaluationResults(
        uses.stream()
            .map(use -> this.evaluateUse(use, parameters))
            .toList());
  }

  private PurchasePurposeEvaluationResult evaluateUse(
      final Use use,
      final List<PurchasePurposeParameter> parameters) {
    return PurchasePurposeEvaluator.evaluate(
        use.getId(),
        parameters,
        use.conditions().value());
  }

}
