package com.inditex.icdmdemg.application.distributionnominated.command;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class DeleteDistributionNominatedCommandHandler
    implements ResultCommandHandler<DeleteDistributionNominatedCommand, DistributionNominated> {
  private final Transaction transaction;

  private final ClockUtils clockUtils;

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final EventBus eventBus;

  @Override
  public DistributionNominated execute(final DeleteDistributionNominatedCommand command) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var distributionNominated =
        this.distributionNominatedRepository.findById(new Id(command.deleteNominatedRequest().distributionNominatedId()))
            .orElseThrow(() -> new ErrorException(new BadRequest(
                String.format("Distribution nominated not found for Id %s", command.deleteNominatedRequest().distributionNominatedId()))));

    final DistributionNominated deletedDistributionNominated =
        distributionNominated.delete(now, command.deleteNominatedRequest().triggeredBy());

    this.transaction.run(() -> {
      this.distributionNominatedRepository.save(deletedDistributionNominated);
      this.eventBus.send(deletedDistributionNominated.domainEvents());
    });

    return deletedDistributionNominated;
  }

}
