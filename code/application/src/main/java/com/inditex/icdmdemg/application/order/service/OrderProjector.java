package com.inditex.icdmdemg.application.order.service;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderCreatedOrUpdated;
import com.inditex.icdmdemg.application.order.command.PublishedOrderCommand.PublishedOrder;
import com.inditex.icdmdemg.application.order.command.UpdateOrderStatusCommand.StatusOrderUpdated;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderBudgetId;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.OrderLine;
import com.inditex.icdmdemg.domain.order.entity.OrderLineId;
import com.inditex.icdmdemg.domain.order.entity.OrderLineMeasuringUnitsId;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantity;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetail;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetailReferenceId;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetails;
import com.inditex.icdmdemg.domain.order.entity.OrderLineServiceDate;
import com.inditex.icdmdemg.domain.order.entity.OrderLines;
import com.inditex.icdmdemg.domain.order.entity.OrderStatusKey;
import com.inditex.icdmdemg.domain.order.entity.OrderSupplierId;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class OrderProjector {

  private final OrderRepository orderRepository;

  private final ClockUtils clockUtils;

  private final Transaction transaction;

  public void projectFromProductOrder(final ProductOrderCreatedOrUpdated productOrderCreatedOrUpdated) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var orderId = new OrderId(productOrderCreatedOrUpdated.id());
    final var orderStatusKey = OrderStatusKey.valueOf(productOrderCreatedOrUpdated.statusKey());
    final var orderLines = this.getOrderLines(productOrderCreatedOrUpdated);
    final var orderSupplierId = new OrderSupplierId(productOrderCreatedOrUpdated.supplierId());
    this.orderRepository.find(orderId)
        .ifPresentOrElse(productOrderFound -> {
          if (productOrderFound.willBeUpdatedWith(orderLines, orderSupplierId)) {
            final var updated = productOrderFound.update(orderLines, orderSupplierId, now);
            this.transaction.run(() -> this.orderRepository.save(updated));
          }
        }, () -> {
          final var created = Order.create(orderId, orderStatusKey, orderLines, now, orderSupplierId);
          this.transaction.run(() -> this.orderRepository.save(created));
        });
  }

  private OrderLines getOrderLines(final ProductOrderCreatedOrUpdated productOrderCreatedOrUpdated) {
    return new OrderLines(productOrderCreatedOrUpdated.orderLines().stream()
        .map(productOrderLine -> new OrderLine(
            new OrderLineId(productOrderLine.id()),
            new OrderLineServiceDate(productOrderLine.serviceDate()),
            acceptNullElseMap(productOrderLine.measuringUnitsId(), OrderLineMeasuringUnitsId::new),
            acceptNullElseMap(productOrderLine.budgetId(), OrderBudgetId::new),
            new OrderLineQuantityDetails(productOrderLine.productOrderQuantityDetails().stream()
                .map(productOrderQuantityDetail -> new OrderLineQuantityDetail(
                    new OrderLineQuantity(productOrderQuantityDetail.quantity()),
                    new OrderLineQuantityDetailReferenceId(productOrderQuantityDetail.referenceId())))
                .toList())))
        .toList());
  }

  public void updateStatus(final StatusOrderUpdated statusOrderUpdated) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var orderId = new OrderId(statusOrderUpdated.id());
    final var newStatus = OrderStatusKey.valueOf(statusOrderUpdated.statusKey());
    this.orderRepository.find(orderId)
        .ifPresent(productOrderFound -> {
          if (productOrderFound.willBeUpdatedWith(newStatus)) {
            final var updated = productOrderFound.updateStatus(newStatus, now);
            this.transaction.run(() -> this.orderRepository.save(updated));
          }
        });
  }

  public void publishOrder(final PublishedOrder publishedOrder) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var orderId = new OrderId(publishedOrder.id());
    this.orderRepository.find(orderId)
        .ifPresent(productOrderFound -> {
          if (productOrderFound.willBePublished()) {
            final var updated = productOrderFound.publish(now);
            this.transaction.run(() -> this.orderRepository.save(updated));
          }
        });
  }
}
