package com.inditex.icdmdemg.application.distributionnominated.command;

import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateReferenceInput;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateDistributionNominatedReferenceCommandHandler
    implements CommandHandler<UpdateDistributionNominatedReferenceCommand> {

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final ClockUtils clockUtils;

  private final Transaction transaction;

  private final EventBus eventBus;

  @Override
  public void doHandle(final UpdateDistributionNominatedReferenceCommand command) {
    this.processUpdateDistributionNominatedReference(command.updateReferenceInput(), command.triggeredBy());
  }

  private void processUpdateDistributionNominatedReference(final UpdateReferenceInput referenceInput, final String triggeredBy) {
    final var referenceIdsBySourceIds = referenceInput.getReferenceIdsBySourceIds();

    if (referenceIdsBySourceIds.isEmpty()) {
      return;
    }
    if (referenceInput.taxonomy().equalsIgnoreCase(UpdateReferenceInput.EXCLUDE_TAXONOMY)) {
      return;
    }
    final var distributionsNominatedToUpdate = this.findDistributionsNominatedToUpdate(referenceIdsBySourceIds);
    distributionsNominatedToUpdate
        .forEach(distribution -> this.updateDistributionsNominated(distribution, referenceIdsBySourceIds, referenceInput.productId(),
            triggeredBy));
  }

  private void updateDistributionsNominated(final DistributionNominated distribution, final Map<UUID, UUID> referenceIdsBySourceIds,
      final UUID productId, final String triggeredBy) {
    log.info("Distribution Nominated '{}' has update reference to '{}' and productId to {}", distribution.getId().value(),
        referenceIdsBySourceIds.get(distribution.referenceId().value()), productId);
    final UUID newReferenceId = referenceIdsBySourceIds.get(distribution.referenceId().value());

    final var updatedDistribution = distribution.updateReferencesId(
        new ReferenceId(newReferenceId),
        new ReferenceProductId(productId),
        this.clockUtils.getCurrentOffsetDateTime(),
        triggeredBy);

    this.transaction.run(() -> {
      this.distributionNominatedRepository.save(updatedDistribution);
      this.eventBus.send(distribution.domainEvents());
    });
  }

  private List<DistributionNominated> findDistributionsNominatedToUpdate(final Map<UUID, UUID> referenceIdsBySourceIds) {
    final List<ReferenceId> referenceSourceIds = referenceIdsBySourceIds.keySet().stream().map(ReferenceId::new).toList();

    return this.distributionNominatedRepository.findByReferenceIds(referenceSourceIds);
  }

}
