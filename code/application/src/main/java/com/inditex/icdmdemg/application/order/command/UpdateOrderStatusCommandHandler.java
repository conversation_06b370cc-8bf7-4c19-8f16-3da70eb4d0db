package com.inditex.icdmdemg.application.order.command;

import com.inditex.icdmdemg.application.order.service.OrderProjector;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class UpdateOrderStatusCommandHandler implements CommandHandler<UpdateOrderStatusCommand> {

  private final OrderProjector orderProjector;

  @Override
  public void doHandle(final UpdateOrderStatusCommand command) {
    this.orderProjector.updateStatus(command.orderStatusUpdated());
  }

}
