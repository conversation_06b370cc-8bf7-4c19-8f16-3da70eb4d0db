package com.inditex.icdmdemg.application.distributionnominated.command;

import com.inditex.icdmdemg.application.process.ProductProcessManager.MergeSplitSummary.UpdateVariantGroupInput;
import com.inditex.iopcmmnt.cqrs.core.Command;

import org.jspecify.annotations.NonNull;

public record UpdateDistributionNominatedProductVariantCommand(
    @NonNull UpdateVariantGroupInput variantGroup,
    @NonNull String triggeredBy) implements Command {

}
