package com.inditex.icdmdemg.application.use.evaluation;

import java.util.ArrayList;
import java.util.List;

import com.inditex.icdmdemg.application.use.service.PurchasePurposeParameter;
import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues.ConditionValue;
import com.inditex.icdmdemg.domain.use.entity.UsePurchasePurposeCondition;
import com.inditex.icdmdemg.domain.use.entity.ranker.PurchasePurposeRankResult;

public interface PurchasePurposeEvaluator {

  static PurchasePurposeEvaluationResult evaluate(final Use.Id useId,
      final List<PurchasePurposeParameter> parameters,
      final List<UsePurchasePurposeCondition> conditions) {
    var kos = 0;
    var coincidences = 0;
    final var paramsWithOk = new ArrayList<PurchasePurposeConditionName>();

    for (final var condition : conditions) {
      final var matchingParam = parameters.stream()
          .filter(p -> p.name().equals(condition.name())).findFirst();

      if (matchingParam.isPresent()) {
        final var result = evaluateParameterCondition(matchingParam.get(), condition);
        if (result.isComplied()) {
          paramsWithOk.add(condition.name());
          coincidences += result.coincidences();
        } else {
          kos++;
        }
      }
    }

    return new PurchasePurposeEvaluationResult(useId, kos, paramsWithOk, coincidences);
  }

  private static PurchasePurposeRankResult evaluateParameterCondition(
      final PurchasePurposeParameter param, final UsePurchasePurposeCondition condition) {

    final var purchasePurposeParameterValues = param.values();
    final var usePurchasePurposeConditionValues = condition.values().value().stream()
        .map(ConditionValue::value)
        .toList();

    return condition.condition().getEvaluator().apply(purchasePurposeParameterValues, usePurchasePurposeConditionValues);
  }
}
