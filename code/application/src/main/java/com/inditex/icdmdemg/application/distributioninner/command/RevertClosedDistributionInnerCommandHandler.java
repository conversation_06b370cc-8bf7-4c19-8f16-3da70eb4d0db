package com.inditex.icdmdemg.application.distributioninner.command;

import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.Error.NotFound;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class RevertClosedDistributionInnerCommandHandler implements CommandHandler<RevertClosedDistributionInnerCommand> {

  private final DistributionInnerRepository distributionInnerRepository;

  private final OrderRepository orderRepository;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  @Transactional
  @Override
  public void doHandle(final RevertClosedDistributionInnerCommand command) {
    final var distributionInner = this.findDistributionInner(command.distributionInnerId());

    this.guardProductOrderIsNotClosed(distributionInner);

    distributionInner.revertClose(command.triggeredBy(), this.clockUtils.getCurrentOffsetDateTime());

    this.distributionInnerRepository.save(distributionInner);
    this.eventBus.send(distributionInner.domainEvents());
  }

  private void guardProductOrderIsNotClosed(final DistributionInner distributionInner) {
    final var productOrder = this.findProductOrder(distributionInner.productOrderId());
    if (productOrder.isClosed() || productOrder.isCancelled()) {
      throw new ErrorException(
          new BadRequest(
              String.format("No reopen allowed in DI with id %s because finish product order is %s",
                  distributionInner.getId().value(), productOrder.orderStatusKey().value())));
    }
  }

  private Order findProductOrder(final ProductOrderId productOrderId) {
    return this.orderRepository.find(new OrderId(productOrderId.value().toString()))
        .orElseThrow(
            () -> new ErrorException(
                new NotFound(String.format("Product Order %s not found", productOrderId.value()))));
  }

  private DistributionInner findDistributionInner(final UUID distributionInnerId) {
    return this.distributionInnerRepository.findById(new Id(distributionInnerId))
        .orElseThrow(
            () -> new ErrorException(new NotFound(String.format("Distribution Inner %s not found", distributionInnerId))));
  }

}
