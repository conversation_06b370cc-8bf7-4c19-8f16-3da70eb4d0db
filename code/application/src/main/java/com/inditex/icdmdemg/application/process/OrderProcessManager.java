package com.inditex.icdmdemg.application.process;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.application.commitmentuse.command.CalculateNominatedProvisionCommand;
import com.inditex.icdmdemg.application.distributioninner.command.DeleteDistributionInnerStatusCommand;
import com.inditex.icdmdemg.application.distributioninner.command.UpdateDistributionInnerFromOrderCommand;
import com.inditex.icdmdemg.application.distributioninner.command.UpdateDistributionInnerStatusCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.AdjustDistributionNominatedCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.DeleteDistributionNominatedStatusCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.RegularizeDistributionNominatedBudgetCycleChangeCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedFromOrderCommand;
import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedStatusCommand;
import com.inditex.icdmdemg.application.order.command.GetOrderBudgetCycleCommand;
import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand;
import com.inditex.icdmdemg.application.order.command.ProjectOrderCommand.ProductOrderCreatedOrUpdated;
import com.inditex.icdmdemg.application.order.command.ProjectOrderDeletedCommand;
import com.inditex.icdmdemg.application.order.command.ProjectOrderDeletedCommand.ProductOrderDeleted;
import com.inditex.icdmdemg.application.order.command.PublishedOrderCommand;
import com.inditex.icdmdemg.application.order.command.PublishedOrderCommand.PublishedOrder;
import com.inditex.icdmdemg.application.order.command.UpdateOrderStatusCommand;
import com.inditex.icdmdemg.application.order.command.UpdateOrderStatusCommand.StatusOrderUpdated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.order.entity.OrderBudgetId;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class OrderProcessManager {

  public static final String ORDER_UPDATED_EVENT = "Order Updated Event";

  private final CommandBus commandBus;

  public void executeCreate(final ProductOrderCreatedOrUpdated productOrderEvent) {
    this.commandBus.execute(new ProjectOrderCommand(productOrderEvent));
  }

  public void executeUpdate(final ProductOrderCreatedOrUpdated productOrderEvent) {
    final Optional<OrderBudgetId> originalBudgetCycleOpt = this.commandBus.execute(new GetOrderBudgetCycleCommand(productOrderEvent.id()));
    this.commandBus.execute(new ProjectOrderCommand(productOrderEvent));

    this.commandBus.execute(new UpdateDistributionInnerFromOrderCommand(
        productOrderEvent.id(),
        ORDER_UPDATED_EVENT));

    final List<SharedRawMaterialNominated> sharedRawMaterialsUpdated =
        this.commandBus.execute(
            new UpdateDistributionNominatedFromOrderCommand(
                productOrderEvent.id(),
                ORDER_UPDATED_EVENT))
            .sharedRawMaterialUpdated();

    final var sharedRawMaterialFromBudgetCycleChange = this.sharedRawMaterialNominatedFromResult(
        originalBudgetCycleOpt,
        sharedRawMaterialsUpdated);
    this.commandBus
        .execute(new RegularizeDistributionNominatedBudgetCycleChangeCommand(sharedRawMaterialFromBudgetCycleChange, ORDER_UPDATED_EVENT));
    this.commandBus.execute(new CalculateNominatedProvisionCommand(sharedRawMaterialFromBudgetCycleChange, ORDER_UPDATED_EVENT));
  }

  public void execute(final ProductOrderDeleted productOrderEvent) {
    final var orderDeletedTriggeredBy = "OrderDeleted";
    this.commandBus.execute(new ProjectOrderDeletedCommand(productOrderEvent));
    final var distributionsNominated =
        this.commandBus.execute(new DeleteDistributionNominatedStatusCommand(productOrderEvent.id(), orderDeletedTriggeredBy));
    this.commandBus.execute(new DeleteDistributionInnerStatusCommand(productOrderEvent.id(), orderDeletedTriggeredBy));
    final var sharedRawMaterialNominated = distributionsNominated.stream().map(DistributionNominated::sharedRawMaterial).toList();

    this.commandBus.execute(new AdjustDistributionNominatedCommand(
        sharedRawMaterialNominated, orderDeletedTriggeredBy));
    this.commandBus.execute(new CalculateNominatedProvisionCommand(sharedRawMaterialNominated, orderDeletedTriggeredBy));
  }

  public void execute(final StatusOrderUpdated statusUpdated) {
    final var orderStatusUpdatedTriggeredBy = "OrderStatusUpdated";
    this.commandBus.execute(new UpdateOrderStatusCommand(statusUpdated));
    final var distributionsNominated = this.commandBus
        .execute(new UpdateDistributionNominatedStatusCommand(UUID.fromString(statusUpdated.id()), orderStatusUpdatedTriggeredBy));
    this.commandBus
        .execute(new UpdateDistributionInnerStatusCommand(UUID.fromString(statusUpdated.id()), orderStatusUpdatedTriggeredBy));
    final var sharedRawMaterialNominated =
        distributionsNominated.stream().map(DistributionNominated::sharedRawMaterial).toList();
    this.commandBus.execute(new AdjustDistributionNominatedCommand(
        sharedRawMaterialNominated, orderStatusUpdatedTriggeredBy));
    this.commandBus.execute(new CalculateNominatedProvisionCommand(sharedRawMaterialNominated, orderStatusUpdatedTriggeredBy));
  }

  public void execute(final PublishedOrder publishedOrder) {
    this.commandBus.execute(new PublishedOrderCommand(publishedOrder));
    this.commandBus
        .execute(new UpdateDistributionNominatedStatusCommand(UUID.fromString(publishedOrder.id()), "Order Published Event"));
  }

  private List<SharedRawMaterialNominated> sharedRawMaterialNominatedFromResult(final Optional<OrderBudgetId> originalOrderBudgetCycle,
      final List<SharedRawMaterialNominated> sharedRawMaterials) {
    return originalOrderBudgetCycle.map(orderBudgetId -> new BudgetCycle(orderBudgetId.value()))
        .map(oldBudgetCycle -> sharedRawMaterials.stream()
            .filter(sharedRawMaterialNominated -> !Objects.equals(sharedRawMaterialNominated.budgetCycle(), oldBudgetCycle))
            .flatMap(sharedRawMaterialNominated -> Stream.of(
                sharedRawMaterialNominated, sharedRawMaterialNominated.doBudgetCycleChange(oldBudgetCycle)))
            .toList())
        .orElseGet(List::of);
  }
}
