package com.inditex.icdmdemg.application.shared.idempotent;

import com.inditex.icdmdemg.domain.consumermessage.ConsumerMessage;
import com.inditex.icdmdemg.domain.consumermessage.ConsumerMessageRepository;
import com.inditex.icdmdemg.domain.consumermessage.DuplicatedMessageException;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Component
@RequiredArgsConstructor
@Slf4j
@Transactional
public class IdempotentConsumerService {

  private final ConsumerMessageRepository messageRepository;

  public void registerMessageAndProceed(final ConsumerMessage message, final ProceedingJoinPoint joinPoint) {
    try {
      this.messageRepository.create(message);
    } catch (final DuplicatedMessageException e) {
      log.warn("Message with id={} is already processed", message.id());
      return;
    }
    this.proceedJoinPoint(joinPoint);
  }

  private void proceedJoinPoint(final ProceedingJoinPoint joinPoint) {
    try {
      joinPoint.proceed();
    } catch (final Throwable e) {
      log.error("Error processing message", e);
      throw new IdempotentConsumerException(e);
    }
  }

}
