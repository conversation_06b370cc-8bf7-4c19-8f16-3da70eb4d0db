package com.inditex.icdmdemg.application.distributioninner.command;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateDistributionInnerStatusCommandHandler implements
    ResultCommandHandler<UpdateDistributionInnerStatusCommand, List<DistributionInner>> {

  private final DistributionInnerRepository distributionInnerRepository;

  private final OrderRepository orderRepository;

  private final Transaction transaction;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  @Override
  public List<DistributionInner> execute(final UpdateDistributionInnerStatusCommand command) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();

    final var orderOpt = this.orderRepository.find(new OrderId(command.orderId().toString()));
    if (orderOpt.isEmpty()) {
      return List.of();
    }

    final var orderInfo = ProductOrderStatusInfo.of(orderOpt.get());

    final var productOrderId = new DistributionInner.ProductOrderId(UUID.fromString(orderOpt.get().getId().value()));
    final var distributions = this.distributionInnerRepository.findByProductOrderId(productOrderId);

    for (final var distribution : distributions) {

      if (distribution.status().isClosed() || distribution.status().isCanceled()) {
        log.error(String.format("Distribution Inner '%s' is %s, its status will not be updated", distribution.getId().value(),
            distribution.status().value()));
      } else {
        final var updatedDistribution =
            distribution.updateStatusFromOrder(orderInfo, now, command.triggeredBy());
        this.transaction.run(() -> {
          this.distributionInnerRepository.save(updatedDistribution);
          this.eventBus.send(updatedDistribution.domainEvents());
        });
      }
    }
    return distributions;
  }

}
