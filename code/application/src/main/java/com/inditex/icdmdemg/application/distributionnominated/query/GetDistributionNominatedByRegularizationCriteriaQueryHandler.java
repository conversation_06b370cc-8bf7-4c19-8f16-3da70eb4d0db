package com.inditex.icdmdemg.application.distributionnominated.query;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import com.inditex.icdmdemg.application.distributionnominated.query.GetDistributionNominatedByRegularizationCriteriaQuery.LineRegularization;
import com.inditex.icdmdemg.application.distributionnominated.query.GetDistributionNominatedByRegularizationCriteriaQuery.RegularizationCriteria;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetDistributionNominatedByRegularizationCriteriaQueryHand<PERSON> implements
    QueryHandler<GetDistributionNominatedByRegularizationCriteriaQuery, Optional<LineRegularization>> {

  private final DistributionNominatedRepository distributionNominatedRepository;

  @Override
  public Optional<LineRegularization> ask(GetDistributionNominatedByRegularizationCriteriaQuery query) {
    return query.criteriaList().stream()
        .flatMap(criteria -> {
          final var distributions = this.distributionNominatedRepository
              .findByCommitmentOrderAndReferenceId(criteria.commitmentOrder(), criteria.referenceId());

          return distributions.stream()
              .sorted(Comparator.comparing(dn -> dn.audit().createdAt()))
              .flatMap(distributionNominated -> distributionNominated.lines().getLineByCommitmentOrder(criteria.commitmentOrder()).stream()
                  .filter(line -> line.alternativeReference() != null)
                  .filter(
                      line -> line.alternativeReference().requestedQuantity().value()
                          .compareTo(this.calculateFreeCommitmentQuantity(distributions, criteria)) == 0)
                  .map(line -> new LineRegularization(distributionNominated.getId(), line.id())));
        }).findFirst();
  }

  private BigDecimal calculateFreeCommitmentQuantity(final List<DistributionNominated> dns, final RegularizationCriteria criteria) {
    final var totalAmountAssignedToCriteriaReference = dns.stream()
        .flatMap(distributionNominated -> distributionNominated.lines().value().stream())
        .filter(line -> line.commitmentOrder().equals(criteria.commitmentOrder()))
        .map(line -> line.requestedQuantity().value())
        .reduce(BigDecimal.ZERO, BigDecimal::add);

    return criteria.requestedQuantity().value().subtract(totalAmountAssignedToCriteriaReference);
  }
}
