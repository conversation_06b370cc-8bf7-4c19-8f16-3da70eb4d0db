package com.inditex.icdmdemg.application.distributionnominated.command;

import static java.text.MessageFormat.format;

import java.util.List;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.AutoRequest;
import com.inditex.icdmdemg.application.distributionnominated.command.PlanRequest.PreselectedRequest;
import com.inditex.icdmdemg.application.distributionnominated.service.AlternativeSharedRawMaterialProvider;
import com.inditex.icdmdemg.application.distributionnominated.service.DistributionNominatedLinesIdentifier;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjustedProvider;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseUniqueKey;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ConsumptionFactor;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductSupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.UseId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLines;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.DistributionNominatedPlannerType;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan;
import com.inditex.icdmdemg.domain.lock.Lock;
import com.inditex.icdmdemg.domain.lock.LockRepository;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.domain.product.ProductRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class CreateDistributionNominatedCommandHandler
    implements ResultCommandHandler<CreateDistributionNominatedCommand, DistributionNominated> {
  private final Transaction transaction;

  private final ClockUtils clockUtils;

  private final UuidGenerator uuidGenerator;

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final LockRepository lockRepository;

  private final CommitmentAdjustedProvider commitmentAdjustedProvider;

  private final AlternativeSharedRawMaterialProvider alternativeSharedRawMaterialProvider;

  private final DistributionNominatedLinesIdentifier linesIdentifier;

  private final OrderRepository orderRepository;

  private final ProductRepository productRepository;

  private final EventBus eventBus;

  @Override
  public DistributionNominated execute(final CreateDistributionNominatedCommand command) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();
    final var payloadRequest = command.nominatedRequest();
    final var referenceId = new ReferenceId(payloadRequest.referenceId());
    final var useId = new UseId(payloadRequest.useId());
    final var budgetCycle = new BudgetCycle(payloadRequest.budgetCycle());

    final var sharedRawMaterial = new SharedRawMaterialNominated(referenceId, useId, budgetCycle);
    return this.transaction.run(() -> {
      this.lockRepository.lock(Lock.from(sharedRawMaterial));

      final var distributions = this.distributionNominatedRepository.findBySharedRawMaterial(sharedRawMaterial);
      final var commimentSharedRawMaterialList = this.getEquivalentAssignableSharedRawMaterialList(sharedRawMaterial);
      final var adjustedCommitments = this.commitmentAdjustedProvider.findByAnySharedRawMaterial(commimentSharedRawMaterialList);

      final var orderId = new OrderId(command.nominatedRequest().productOrderId().toString());
      final var order = this.orderRepository.find(orderId)
          .orElseThrow(() -> new ErrorException(new BadRequest(format("Order {0} not found", orderId.value()))));

      final var product = this.productRepository.findByReferenceId(new ProductReferenceId(referenceId.value().toString()))
          .orElseThrow(() -> new ErrorException(new BadRequest(format("Product of reference {0} not found", referenceId.value()))));

      final var rootId = new Id(this.uuidGenerator.generate());
      final var requestedQuantity = new RequestedQuantity(payloadRequest.requestedQuantity());
      final var theoreticalQuantity = new TheoreticalQuantity(payloadRequest.theoreticalQuantity());
      final var consumptionFactor = new ConsumptionFactor(payloadRequest.consumptionFactor());
      final var referenceProductId = new ReferenceProductId(UUID.fromString(product.getProductId().value()));
      final var productOrderId = new ProductOrderId(payloadRequest.productOrderId());
      final var productOrderSupplierId = new ProductSupplierId(order.supplierId().value());
      final var productVariantGroupId = new ProductVariantGroupId(payloadRequest.productVariantGroupId());
      final var productOrderInfo = ProductOrderStatusInfo.of(order);

      final var concretePlan = this.getConcretePlan(
          rootId,
          referenceId,
          budgetCycle,
          useId,
          requestedQuantity,
          adjustedCommitments,
          command.plan());
      final var planner = DistributionNominatedPlanner.fromDistributions(
          sharedRawMaterial,
          distributions,
          adjustedCommitments,
          concretePlan,
          DistributionNominatedPlannerType.DEFAULT);

      final var linesResult = planner.executePlan();

      final var createdLines = this.linesIdentifier.identifyCreatedLines(rootId, requestedQuantity, theoreticalQuantity, linesResult, now);
      final var lines = new DistributionNominatedLines(createdLines);
      final var distributionNominatedPlan = command.plan().getDistributionNominatedPlan();

      final var distribution = DistributionNominated.create(
          rootId,
          referenceId,
          useId,
          budgetCycle,
          referenceProductId,
          productOrderId,
          productOrderSupplierId,
          productVariantGroupId,
          productOrderInfo,
          theoreticalQuantity,
          consumptionFactor,
          requestedQuantity,
          lines,
          distributionNominatedPlan,
          now,
          payloadRequest.triggeredBy());

      this.distributionNominatedRepository.save(distribution);
      this.eventBus.send(distribution.domainEvents());

      return distribution;
    });
  }

  private Plan getConcretePlan(
      final Id rootId,
      final ReferenceId referenceId,
      final BudgetCycle budgetCycle,
      final UseId useId,
      final RequestedQuantity requestedQuantity,
      final List<CommitmentAdjusted> adjustedCommitments,
      final PlanRequest plan) {
    return switch (plan) {
      case AutoRequest() -> new Plan.Auto.Create(rootId, requestedQuantity);
      case PreselectedRequest preselected -> {
        final var commmitmentsMap = adjustedCommitments.stream().map(CommitmentAdjusted::materialCommitmentUse)
            .collect(Collectors.toMap(MaterialCommitmentUseUniqueKey::fromMaterialCommitmentUse, Function.identity()));
        final var preselection = preselected.toDomain(rootId, referenceId, budgetCycle, useId, commmitmentsMap);
        yield new Plan.Preselected.Create(rootId, requestedQuantity, preselection);
      }
    };
  }

  private List<MaterialCommitmentUseSharedRawMaterial> getEquivalentAssignableSharedRawMaterialList(
      final SharedRawMaterialNominated sharedRawMaterial) {
    return this.alternativeSharedRawMaterialProvider.alternatives(MaterialCommitmentUseSharedRawMaterial.of(sharedRawMaterial));
  }

}
