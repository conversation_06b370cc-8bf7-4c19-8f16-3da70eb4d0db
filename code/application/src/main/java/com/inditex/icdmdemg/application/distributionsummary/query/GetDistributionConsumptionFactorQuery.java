package com.inditex.icdmdemg.application.distributionsummary.query;

import static com.inditex.icdmdemg.shared.utils.Nullables.safeListWithEmptyList;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery.DistributionConsumptionFactorResponse;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.iopcmmnt.cqrs.core.Query;

import org.jspecify.annotations.NonNull;

public record GetDistributionConsumptionFactorQuery(
    DistributionConsumptionFactorRequest distributionConsumptionFactorRequest)
    implements
      Query<Response<DistributionConsumptionFactorResponse>> {

  public record DistributionConsumptionFactorRequest(@NonNull List<UUID> productOrderIds) {
    public static DistributionConsumptionFactorRequest of(final List<UUID> productOrderIds) {
      return new DistributionConsumptionFactorRequest(
          safeListWithEmptyList(productOrderIds));
    }
  }

  public record DistributionConsumptionFactorResponse(
      @NonNull List<DistributionConsumptionFactor> distributionsConsumptionFactor) {
  }

  public record DistributionConsumptionFactor(
      UUID referenceId,
      UUID variantGroupId,
      BigDecimal consumptionFactor,
      String distributionType) {
  }

}
