package com.inditex.icdmdemg.application.distributionnominated.validator;

import com.inditex.icdmdemg.application.distributionnominated.command.PatchDistributionNominatedCommand.PatchNominatedRequest;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class DistributionNominatedExistingByIdValidatorImpl
    implements ConstraintValidator<DistributionNominatedExistingByIdValidator, PatchNominatedRequest> {

  private final DistributionNominatedRepository nominatedRepository;

  @Override
  public boolean isValid(final PatchNominatedRequest nominatedRequest,
      final ConstraintValidatorContext constraintValidatorContext) {
    return this.nominatedRepository.findById(new Id(nominatedRequest.distributionNominatedId())).isPresent();
  }

}
