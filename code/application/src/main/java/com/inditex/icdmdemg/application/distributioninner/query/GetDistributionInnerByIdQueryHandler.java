package com.inditex.icdmdemg.application.distributioninner.query;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.shared.Error.NotFound;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetDistributionInnerByIdQueryHandler
    implements QueryHandler<GetDistributionInnerByIdQuery, Response<DistributionInner>> {

  private final DistributionInnerRepository distributionInnerRepository;

  @Override
  public Response<DistributionInner> ask(final GetDistributionInnerByIdQuery query) {
    return this.distributionInnerRepository.findById(new Id(query.distributionInnerId()))
        .map(Response::ofResponse)
        .orElseGet(() -> Response.ofError(new NotFound(query.distributionInnerId().toString())));
  }

}
