package com.inditex.icdmdemg.application.distributionnominated.query;

import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.iopcmmnt.cqrs.core.Query;

public record GetDistributionNominatedByIdQuery(UUID distributionNominatedId) implements Query<Response<DistributionNominated>> {

}
