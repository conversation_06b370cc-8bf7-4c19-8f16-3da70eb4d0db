package com.inditex.icdmdemg.application.shipmentwarehouse.validator;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.ElementType.PARAMETER;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Target({FIELD, PARAMETER})
@Retention(RUNTIME)
@Constraint(validatedBy = ShipmentWarehouseTrackingCodeValidatorImpl.class)
public @interface ShipmentWarehouseTrackingCodeValidator {

  String message() default "DistributionTrackingCode is mandatory and should exist.";

  Class<?>[] groups() default {};

  Class<? extends Payload>[] payload() default {};

}
