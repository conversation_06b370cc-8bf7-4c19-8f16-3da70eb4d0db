package com.inditex.icdmdemg.application.distributionnominated.command;

import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderIdAndOrderLineId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedRepository;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.Id;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class UpdateDistributionNominatedLineCommitmentOrderSupplierCommandHandler
    implements CommandHandler<UpdateDistributionNominatedLineCommitmentOrderSupplierCommand> {

  private final DistributionNominatedRepository distributionNominatedRepository;

  private final MaterialCommitmentUseRepository materialCommitmentUseRepository;

  private final Transaction transaction;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  @Override
  public void doHandle(final UpdateDistributionNominatedLineCommitmentOrderSupplierCommand command) {
    final var now = this.clockUtils.getCurrentOffsetDateTime();

    final var mcuOpt = this.materialCommitmentUseRepository.findByAnyOrderIdAndOrderLineId(List.of(
        MaterialCommitmentUseOrderIdAndOrderLineId.of(command.orderId(), command.orderLineId())))
        .materialCommitmentUses().stream()
        .findFirst();
    if (mcuOpt.isEmpty()) {
      return;
    }

    final var supplierId = new CommitmentOrder.SupplierId(mcuOpt.get().getServiceLocalizationId().value());
    final var commitmentOrderId = new Id(this.uuidFromString(command.orderId()));
    final var commitmentOrderLineId = new LineId(this.uuidFromString(command.orderLineId()));
    final var distributions = this.distributionNominatedRepository
        .findByCommitmentOrderIdAndOrderLineId(commitmentOrderId,
            commitmentOrderLineId);

    for (final var distribution : distributions) {
      final var updatedDistribution =
          distribution.updateLineSupplier(commitmentOrderId, commitmentOrderLineId, supplierId, now, command.triggeredBy());
      this.transaction.run(() -> {
        this.distributionNominatedRepository.save(updatedDistribution);
        this.eventBus.send(updatedDistribution.domainEvents());
      });
    }
  }

  private UUID uuidFromString(final String uuidString) {
    return UUID.fromString(uuidString);
  }
}
