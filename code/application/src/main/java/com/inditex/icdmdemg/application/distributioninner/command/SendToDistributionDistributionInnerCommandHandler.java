package com.inditex.icdmdemg.application.distributioninner.command;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.Error.NotFound;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class SendToDistributionDistributionInnerCommandHandler implements CommandHandler<SendToDistributionDistributionInnerCommand> {

  private final DistributionInnerRepository distributionInnerRepository;

  private final ClockUtils clockUtils;

  private final EventBus eventBus;

  private final Transaction transaction;

  @Override
  public void doHandle(final SendToDistributionDistributionInnerCommand command) {
    final var distributionInner = this.distributionInnerRepository.findById(new Id(command.distributionInnerId()))
        .orElseThrow(
            () -> new ErrorException(new NotFound(String.format("Distribution Inner %s not found", command.distributionInnerId()))));

    if (!distributionInner.status().isNonDistributable()) {
      throw new ErrorException(new BadRequest(
          String.format("Distribution inner cannot be set as ready to distribute since its status %s is different from NON_DISTRIBUTABLE",
              distributionInner.status().value())));
    }

    final var updatedAt = this.clockUtils.getCurrentOffsetDateTime();

    distributionInner.updateStatusToPending(updatedAt, command.triggeredBy());

    this.transaction.run(() -> {
      this.distributionInnerRepository.save(distributionInner);
      this.eventBus.send(distributionInner.domainEvents());
    });
  }

}
