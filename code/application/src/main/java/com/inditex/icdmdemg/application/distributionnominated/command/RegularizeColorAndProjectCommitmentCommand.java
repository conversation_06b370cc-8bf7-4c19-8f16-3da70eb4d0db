package com.inditex.icdmdemg.application.distributionnominated.command;

import com.inditex.icdmdemg.application.commitmentuse.command.ProjectMaterialCommitmentUseCommand.OrderUse;
import com.inditex.icdmdemg.application.commitmentuse.command.RegularizeColorAndProjectCommitmentCommandResult;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import org.jspecify.annotations.NonNull;

public record RegularizeColorAndProjectCommitmentCommand(LineToRegularize lineToRegularize, OrderUse orderUse)
    implements
      BaseCommand<RegularizeColorAndProjectCommitmentCommandResult> {
  public record LineToRegularize(DistributionNominatedLine.@NonNull Id distributionNominatedLineId) {
  }
}
