package com.inditex.icdmdemg.application.order.command;

import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class ProjectOrderDeletedCommandHandler implements CommandHandler<ProjectOrderDeletedCommand> {

  private final OrderRepository orderRepository;

  private final Transaction transaction;

  @Override
  public void doHandle(final ProjectOrderDeletedCommand command) {
    final var orderIdDeleted = new OrderId(command.productOrderEvent().id());

    this.transaction.run(() -> this.orderRepository.deleteById(orderIdDeleted));
  }
}
