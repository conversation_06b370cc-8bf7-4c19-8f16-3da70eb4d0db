package com.inditex.icdmdemg.application.taxonomy;

import com.inditex.icdmdemg.domain.taxonomyuse.TaxonomyUseRepository;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class TaxonomyUpdaterProcessor {

  private final TaxonomyUseRepository repository;

  public void updateTaxonomies() {
    final var taxonomyFromProvider = this.repository.getTaxonomyFromProvider();
    this.repository.saveAll(taxonomyFromProvider);
  }
}
