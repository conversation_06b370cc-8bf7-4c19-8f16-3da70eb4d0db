package com.inditex.icdmdemg.application.distributionnominated.command;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionnominated.validator.DistributionNominatedExistingByIdValidator;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import jakarta.validation.constraints.Positive;
import org.jspecify.annotations.NonNull;

public record PatchDistributionNominatedCommand(
    @NonNull PatchNominatedRequest patchNominatedRequest,
    @NonNull PlanRequest plan) implements BaseCommand<DistributionNominated> {

  @DistributionNominatedExistingByIdValidator
  public record PatchNominatedRequest(
      @NonNull UUID distributionNominatedId,
      @Positive @NonNull BigDecimal theoreticalQuantity,
      @Positive @NonNull BigDecimal requestedQuantity,
      @Positive @NonNull BigDecimal consumptionFactor,
      @NonNull String triggeredBy) {
  }

}
