package com.inditex.icdmdemg.application.process;

import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedFromShipmentCommand;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCancelledCommand;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCancelledCommand.ShipmentCancelled;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCreatedCommand;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentCreatedCommand.ShipmentCreated;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentModifiedCommand;
import com.inditex.icdmdemg.application.shipmentcommitment.command.ProjectCommitmentShipmentModifiedCommand.ShipmentModified;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedDistributed;
import com.inditex.iopcmmnt.cqrs.core.CommandBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CommitmentShipmentProcessManager {
  private static final String TRIGGERED_BY = "Shipment Updated Event";

  private final CommandBus commandBus;

  public void execute(final ShipmentCreated input) {
    this.commandBus.execute(new ProjectCommitmentShipmentCreatedCommand(input));
    this.commandBus.execute(
        new UpdateDistributionNominatedFromShipmentCommand(
            new DistributionNominatedDistributed(input.distributionNominatedLineId(), TRIGGERED_BY)));
  }

  public void execute(final ShipmentModified input) {
    this.commandBus.execute(new ProjectCommitmentShipmentModifiedCommand(input))
        .ifPresent(shipmentCommitment -> this.commandBus.execute(
            new UpdateDistributionNominatedFromShipmentCommand(
                new DistributionNominatedDistributed(input.distributionNominatedLineId(), TRIGGERED_BY))));
  }

  public void execute(final ShipmentCancelled input) {
    this.commandBus.execute(new ProjectCommitmentShipmentCancelledCommand(input));
    this.commandBus.execute(
        new UpdateDistributionNominatedFromShipmentCommand(
            new DistributionNominatedDistributed(input.distributionNominatedLineId(), TRIGGERED_BY)));
  }

}
