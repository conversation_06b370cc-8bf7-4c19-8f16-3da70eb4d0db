package com.inditex.icdmdemg.application.distributionsummary.query;

import static com.inditex.icdmdemg.shared.utils.Nullables.safeCollectionToEmptyList;
import static com.inditex.icdmdemg.shared.utils.UrnConstantsEnum.BUYER;
import static com.inditex.icdmdemg.shared.utils.UrnConstantsEnum.BUYER_CODE;
import static com.inditex.icdmdemg.shared.utils.UrnConstantsEnum.BUYER_GROUP;
import static com.inditex.icdmdemg.shared.utils.UrnConstantsEnum.BUYER_SUBGROUP;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityTotalInnerQuantityV2;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityTotalNominatedQuantityV2;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityV2;
import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityV2Query.UsesAvailabilityV2Response;
import com.inditex.icdmdemg.application.use.evaluation.PurchasePurposeEvaluationResults;
import com.inditex.icdmdemg.application.use.service.GetOrderedValidUseProvider;
import com.inditex.icdmdemg.application.use.service.PurchasePurposeParameter;
import com.inditex.icdmdemg.domain.category.CategoryRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUseQuantitiesDTO;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.BudgetId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvisionRepository;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedTotalQuantityByUseDTO;
import com.inditex.icdmdemg.domain.order.OrderRepository;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.product.Product;
import com.inditex.icdmdemg.domain.product.ProductRepository;
import com.inditex.icdmdemg.domain.product.ProductTaxonomyRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductOwner;
import com.inditex.icdmdemg.domain.product.entity.ProductOwner.OwnerId;
import com.inditex.icdmdemg.domain.product.entity.ProductTaxonomy;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.domain.taxonomyuse.entity.TaxonomyCode;
import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.Use.Customer;
import com.inditex.icdmdemg.domain.use.UseRepository;
import com.inditex.icdmdemg.domain.use.entity.AssignableType;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;
import com.inditex.icdmdemg.domain.use.entity.PurchaseType;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseInnerPendingByUseDTO;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseRepository;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.BudgetCycle;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.Pending;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.ProductReferenceId;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseInnerStockByUseDTO;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseRepository;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO.ReferenceId;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO.Stock;
import com.inditex.icdmdemg.provis.domain.use.UseDTO.Id;
import com.inditex.icdmdemg.shared.utils.LocaleUtils;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetUsesAvailabilityV2QueryHandler implements QueryHandler<GetUsesAvailabilityV2Query, Response<UsesAvailabilityV2Response>> {

  private final UseRepository useRepository;

  private final OrderRepository orderRepository;

  private final ProductRepository productRepository;

  private final OrderUseRepository orderUseRepository;

  private final CategoryRepository categoryRepository;

  private final GetOrderedValidUseProvider useProvider;

  private final ProductUseRepository productUseRepository;

  private final ProductTaxonomyRepository productTaxonomyRepository;

  private final DistributionInnerRepository distributionInnerRepository;

  private final NominatedProvisionRepository nominatedProvisionRepository;

  @Override
  public Response<UsesAvailabilityV2Response> ask(final GetUsesAvailabilityV2Query query) {
    final var referenceId = query.request().referenceId();
    final var productReferenceId = query.request().productReferenceId();
    final var budgetCycle = query.request().budgetCycle();
    final var productOrderId = query.request().productOrderId();
    final var locale = LocaleUtils.createLocale(query.request().language());
    final var filterNoAvailability = query.request().filterNoAvailability();

    final var innerCalculationsHashMap = new HashMap<Id, InnerCalculations>();
    final var nominatedTotalQuantityByUseHashMap = new HashMap<Id, NominatedTotalQuantityByUseDTO>();
    final var usesAvailabilityDataList = new ArrayList<UsesAvailabilityV2>();

    final var orderedValidUse = this.findValidUses(query, referenceId, productReferenceId, productOrderId, budgetCycle);
    final var orderedValidUseMap = orderedValidUse.compiledUseIds();
    final var uses = this.useRepository.findByIds(orderedValidUse.useIds());
    final var relatedUsesIds = uses.stream().map(use -> use.getId().value()).toList();

    this.calculateInnerByUsePending(referenceId, relatedUsesIds, budgetCycle, innerCalculationsHashMap);
    this.calculateInnerByUseRequestedAndDistributed(relatedUsesIds, referenceId, budgetCycle, innerCalculationsHashMap);
    this.calculateInnerByUseStock(referenceId, relatedUsesIds, budgetCycle, innerCalculationsHashMap);
    this.calculateNominatedByUseTotalQuantity(referenceId, relatedUsesIds, budgetCycle, nominatedTotalQuantityByUseHashMap);

    calculateUsesAvailability(uses, innerCalculationsHashMap, usesAvailabilityDataList, nominatedTotalQuantityByUseHashMap,
        orderedValidUseMap,
        locale);

    return Response.ofResponse(new UsesAvailabilityV2Response(
        filterNoAvailability ? this.filterEmptyAvailability(usesAvailabilityDataList) : usesAvailabilityDataList));
  }

  private static void calculateUsesAvailability(
      final List<Use> uses,
      final Map<Id, InnerCalculations> innerCalculationsHashMap,
      final List<UsesAvailabilityV2> usesAvailabilityList,
      final Map<Id, NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseHashMap,
      final Map<Use.Id, Boolean> orderedValidUse,
      final Locale locale) {

    uses.forEach(use -> {
      final Id useId = new Id(use.getId().value());
      final InnerCalculations innerCalculations =
          innerCalculationsHashMap.containsKey(useId) ? innerCalculationsHashMap.get(useId)
              : InnerCalculations.create(useId);

      usesAvailabilityList.add(
          new UsesAvailabilityV2(
              use.getId().value(),
              use.names().getNameTranslated(locale),
              use.purchaseType().getStringList(),
              safeCollectionToEmptyList(use.assignable(), AssignableType::getStringList),
              orderedValidUse.get(use.getId()),
              new UsesAvailabilityTotalNominatedQuantityV2(
                  nominatedTotalQuantityByUseHashMap.containsKey(useId)
                      ? nominatedTotalQuantityByUseHashMap.get(useId).totalQuantity().value()
                      : NumericUtils.roundUpScale2(BigDecimal.ZERO)),
              new UsesAvailabilityTotalInnerQuantityV2(
                  (innerCalculations.pending.value().add(innerCalculations.stock.value())).subtract(
                      innerCalculations.requested.value().subtract(innerCalculations.distributed.value()).max(BigDecimal.ZERO)))));
    });
  }

  private PurchasePurposeEvaluationResults findValidUses(final GetUsesAvailabilityV2Query query, final UUID referenceId,
      final UUID productReferenceId,
      final UUID productOrderId, final String budgetCycle) {
    final var taxonomyMP = this.productTaxonomyRepository.findByReferenceId(referenceId).orElseThrow(
        () -> new ErrorException(new BadRequest(String.format("Taxonomy not found with given Reference ID: %s", referenceId))));
    final var referencePT = this.productRepository
        .findByReferenceId(new com.inditex.icdmdemg.domain.product.entity.ProductReferenceId(productReferenceId.toString()))
        .orElseThrow(() -> new ErrorException(
            new BadRequest(String.format("Reference not found with given Product Reference ID: %s", productReferenceId))));
    final var order = this.orderRepository.find(new OrderId(productOrderId.toString())).orElseThrow(
        () -> new ErrorException(new BadRequest(String.format("Order not found with given Product Order ID: %s", productOrderId))));

    final var supplierId = order.supplierId().value();
    final var hierarchy = this.getHierarchy(referencePT);
    final var customer = hierarchy.get(BUYER).stream().findFirst().get();
    final var families = this.getFamilies(budgetCycle, referencePT);

    return this.getOrderedValidUses(query, hierarchy, families, supplierId, taxonomyMP, customer);
  }

  private PurchasePurposeEvaluationResults getOrderedValidUses(final GetUsesAvailabilityV2Query query,
      final Map<UrnConstantsEnum, List<String>> hierarchy, final List<String> families, final String supplierId,
      final ProductTaxonomy taxonomyMP, final String customer) {

    final var params = Stream.of(
        new PurchasePurposeParameter(PurchasePurposeConditionName.BUYERGROUP, hierarchy.getOrDefault(BUYER_GROUP, List.of())),
        new PurchasePurposeParameter(PurchasePurposeConditionName.BUYERSUBGROUP, hierarchy.getOrDefault(BUYER_SUBGROUP, List.of())),
        new PurchasePurposeParameter(PurchasePurposeConditionName.BUYERCODE, hierarchy.getOrDefault(BUYER_CODE, List.of())),
        new PurchasePurposeParameter(PurchasePurposeConditionName.FAMILY, families),
        new PurchasePurposeParameter(PurchasePurposeConditionName.SUPPLIER_PT, List.of(supplierId)))
        .filter(purchasePurposeParameter -> !purchasePurposeParameter.values().isEmpty())
        .toList();

    return this.useProvider.getOrderedValidUse(
        new TaxonomyCode(taxonomyMP.taxonomyValue().value()),
        new Customer(customer),
        PurchaseType.fromValues(query.request().purchaseType()),
        params);
  }

  private List<String> getFamilies(final String budgetCycle, final Product referencePT) {
    final var campaignsBudgetCycle = this.categoryRepository.getEquivalent(List.of(budgetCycle));
    final var families = referencePT.getFamilies().value();
    return campaignsBudgetCycle.stream()
        .flatMap(campaign -> families.stream()
            .filter(productFamily -> Objects.equals(campaign, productFamily.campaignId().value()))
            .map(productFamily -> productFamily.familyId().value()))
        .toList();
  }

  private Map<UrnConstantsEnum, List<String>> getHierarchy(final Product referencePT) {
    final var parents =
        this.categoryRepository
            .getParents(referencePT.getOwners().value().stream().map(ProductOwner::ownerId).map(OwnerId::value).toList());

    final var parentsGroupedByUrn = parents.stream()
        .flatMap(parent -> Stream.of(BUYER_CODE, BUYER_GROUP, BUYER_SUBGROUP, BUYER)
            .filter(urn -> parent.startsWith(urn.getPrefix()))
            .map(urn -> Map.entry(urn, parent)))
        .collect(Collectors.groupingBy(
            Map.Entry::getKey, Collectors.collectingAndThen(Collectors.mapping(Map.Entry::getValue, Collectors.toSet()), List::copyOf)));

    if (!parentsGroupedByUrn.containsKey(BUYER)) {
      throw new ErrorException(new BadRequest("Product Reference dont have a BUYER configured"));
    }
    if (parentsGroupedByUrn.get(BUYER).size() > 1) {
      throw new ErrorException(new BadRequest("Product Reference has more than one BUYER configured"));
    }
    return parentsGroupedByUrn;
  }

  private ArrayList<UsesAvailabilityV2> filterEmptyAvailability(final ArrayList<UsesAvailabilityV2> usesAvailabilityDataList) {
    final ArrayList<UsesAvailabilityV2> filteredList = new ArrayList<>();
    if (!usesAvailabilityDataList.isEmpty()) {
      filteredList.add(usesAvailabilityDataList.getFirst());
      filteredList.addAll(
          usesAvailabilityDataList.subList(1, usesAvailabilityDataList.size()).stream()
              .filter(usesAvailability -> usesAvailability.nominated().totalQuantity().compareTo(BigDecimal.ZERO) != 0
                  || usesAvailability.inner().totalQuantity().compareTo(BigDecimal.ZERO) != 0)
              .toList());
    }
    return filteredList;
  }

  private void calculateNominatedByUseTotalQuantity(final UUID referenceId, final List<UUID> relatedUsesIds, final String budgetCycle,
      final HashMap<Id, NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseHashMap) {
    final List<NominatedTotalQuantityByUseDTO> nominatedTotalQuantityByUseDTOList =
        this.nominatedProvisionRepository.findNominatedTotalQuantityByUseDTO(
            new NominatedProvision.ReferenceId(referenceId.toString()),
            relatedUsesIds.stream().map(NominatedTotalQuantityByUseDTO.UseId::new).toList(), new BudgetId(budgetCycle));

    nominatedTotalQuantityByUseDTOList.forEach(nominatedTotalQuantityByUseDTO -> nominatedTotalQuantityByUseHashMap
        .put(new Id(nominatedTotalQuantityByUseDTO.useId().value()), nominatedTotalQuantityByUseDTO));
  }

  private void calculateInnerByUseStock(final UUID referenceId, final List<UUID> relatedUsesIds, final String budgetCycle,
      final HashMap<Id, InnerCalculations> innerCalculationsHashMap) {
    final List<ProductUseInnerStockByUseDTO> innerProductUseSummaries =
        this.productUseRepository.findProductUseInnerStockByUseDTO(new ReferenceId(referenceId),
            relatedUsesIds.stream().map(ProductUseInnerStockByUseDTO.UseId::new).toList(),
            new ProductUseSummaryDTO.BudgetCycle(budgetCycle));

    innerProductUseSummaries.forEach(
        productUse -> {
          final var useId = new Id(productUse.useId().value());
          final var stock = new Stock(productUse.stock().value());
          innerCalculationsHashMap.merge(
              useId,
              InnerCalculations.create(useId, stock),
              InnerCalculations::add);
        });
  }

  private void calculateInnerByUseRequestedAndDistributed(final List<UUID> relatedUsesIds, final UUID referenceId, final String budgetCycle,
      final HashMap<Id, InnerCalculations> innerCalculationsHashMap) {
    final List<DistributionInnerUseQuantitiesDTO> distributionsInner =
        this.distributionInnerRepository.findDistributionInnerUseQuantities(
            relatedUsesIds.stream().map(DistributionInner.UseId::new).toList(),
            new DistributionInner.ReferenceId(referenceId),
            new DistributionInner.BudgetCycle(budgetCycle));

    distributionsInner.forEach(distribution -> {
      final var useId = new Id(distribution.useId().value());
      innerCalculationsHashMap.merge(
          useId,
          InnerCalculations.create(useId, distribution.requested(), distribution.distributed()),
          InnerCalculations::add);
    });
  }

  private void calculateInnerByUsePending(final UUID referenceId, final List<UUID> relatedUsesIds, final String budgetCycle,
      final HashMap<Id, InnerCalculations> innerCalculationsHashMap) {
    final var orderUseInnerPendingByUsesDTOList =
        this.orderUseRepository.findOrderUseInnerPendingBySharedRawMaterialUses(
            new ProductReferenceId(referenceId),
            relatedUsesIds.stream().map(OrderUseInnerPendingByUseDTO.UseId::new).toList(),
            new BudgetCycle(budgetCycle));

    orderUseInnerPendingByUsesDTOList.forEach(
        orderUseInnerPendingByUsesDTO -> {
          final var useId = new Id(orderUseInnerPendingByUsesDTO.useId().value());
          final var pending = new Pending(orderUseInnerPendingByUsesDTO.pending().value());
          innerCalculationsHashMap.merge(
              useId,
              InnerCalculations.create(useId, pending),
              InnerCalculations::add);
        });
  }

  public record InnerCalculations(
      Id useId,
      RequestedQuantity requested,
      DistributedQuantity distributed,
      Stock stock,
      Pending pending) {

    public static InnerCalculations create(final Id useId) {
      return new InnerCalculations(useId, new RequestedQuantity(BigDecimal.ZERO), new DistributedQuantity(BigDecimal.ZERO),
          new Stock(BigDecimal.ZERO), new Pending(BigDecimal.ZERO));
    }

    public static InnerCalculations create(final Id useId, final Pending pending) {
      return create(useId).addPending(pending);
    }

    public static InnerCalculations create(final Id useId, final RequestedQuantity requested, final DistributedQuantity distributed) {
      return create(useId).addRequestedAndDistributed(requested, distributed);
    }

    public static InnerCalculations create(final Id useId, final Stock stock) {
      return create(useId).addStock(stock);
    }

    public InnerCalculations add(final InnerCalculations innerCalculations) {
      return new InnerCalculations(
          this.useId,
          new RequestedQuantity(this.requested.value().add(innerCalculations.requested.value())),
          new DistributedQuantity(this.distributed.value().add(innerCalculations.distributed.value())),
          new Stock(this.stock.value().add(innerCalculations.stock.value())),
          new Pending(this.pending.value().add(innerCalculations.pending.value())));
    }

    private InnerCalculations addRequestedAndDistributed(final RequestedQuantity requested, final DistributedQuantity distributed) {
      return new InnerCalculations(
          this.useId,
          new RequestedQuantity(this.requested.value().add(requested.value())),
          new DistributedQuantity(this.distributed.value().add(distributed.value())),
          this.stock,
          this.pending);
    }

    private InnerCalculations addStock(final Stock stock) {
      return new InnerCalculations(
          this.useId,
          this.requested,
          this.distributed,
          new Stock(this.stock.value().add(stock.value())),
          this.pending);
    }

    private InnerCalculations addPending(final Pending pending) {
      return new InnerCalculations(
          this.useId,
          this.requested,
          this.distributed,
          this.stock,
          new Pending(this.pending.value().add(pending.value())));
    }
  }
}
