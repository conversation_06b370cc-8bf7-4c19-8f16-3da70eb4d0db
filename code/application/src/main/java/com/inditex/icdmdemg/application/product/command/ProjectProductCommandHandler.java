package com.inditex.icdmdemg.application.product.command;

import com.inditex.icdmdemg.application.product.service.ProductProjector;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ProjectProductCommandHandler implements CommandHandler<ProjectProductCommand> {

  private final ProductProjector productProjector;

  @Override
  public void doHandle(final ProjectProductCommand command) {
    this.productProjector.fromProductCreatedOrUpdated(command.productReference());
  }
}
