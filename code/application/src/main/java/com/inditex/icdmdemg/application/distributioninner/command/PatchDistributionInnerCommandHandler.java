package com.inditex.icdmdemg.application.distributioninner.command;

import java.time.OffsetDateTime;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ConsumptionFactor;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLines;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.Error.NotFound;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.UuidGenerator;
import com.inditex.iopcmmnt.cqrs.core.CommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PatchDistributionInnerCommandHandler implements CommandHandler<PatchDistributionInnerCommand> {

  private final DistributionInnerRepository distributionInnerRepository;

  private final ClockUtils clockUtils;

  private final UuidGenerator uuidGenerator;

  private final EventBus eventBus;

  @Override
  public void doHandle(final PatchDistributionInnerCommand command) {
    final var distributionInner = this.distributionInnerRepository.findById(new Id(command.distributionInnerId()))
        .orElseThrow(
            () -> new ErrorException(new NotFound(String.format("Distribution Inner %s not found", command.distributionInnerId()))));

    if (DistributionInnerStatus.CLOSED.equals(distributionInner.status())
        || DistributionInnerStatus.CANCELED.equals(distributionInner.status())) {
      throw new ErrorException(new BadRequest(
          String.format("Distribution inner cannot be modified since its status is %s", distributionInner.status().value())));
    }

    final var updatedAt = this.clockUtils.getCurrentOffsetDateTime();
    final var consumptionFactor = new ConsumptionFactor(command.consumptionFactor());
    final var theoreticalQuantity = new TheoreticalQuantity(command.theoreticalQuantity());
    final var requestedQuantity = new RequestedQuantity(command.requestedQuantity());
    final var lines = this.updateDistributionInnerLines(requestedQuantity, distributionInner, updatedAt);

    distributionInner.update(consumptionFactor, theoreticalQuantity, requestedQuantity, lines, command.triggeredBy(), updatedAt);

    this.distributionInnerRepository.save(distributionInner);
    this.eventBus.send(distributionInner.domainEvents());
  }

  private DistributionInnerLines updateDistributionInnerLines(
      final RequestedQuantity requestedQuantity,
      final DistributionInner distributionInner,
      final OffsetDateTime updatedAt) {
    if (!distributionInner.isRequestedQuantityModified(requestedQuantity)) {
      return distributionInner.lines();
    }

    if (distributionInner.isRequestedQuantityIncreased(requestedQuantity)) {
      return this.increaseRequestedQuantity(requestedQuantity, distributionInner, updatedAt);
    } else {
      return this.decreaseRequestedQuantity(requestedQuantity, distributionInner, updatedAt);
    }
  }

  private DistributionInnerLines increaseRequestedQuantity(
      final RequestedQuantity requestedQuantity,
      final DistributionInner distributionInner,
      final OffsetDateTime updatedAt) {
    final var increasedQuantity = new DistributionInnerLine.RequestedQuantity(
        requestedQuantity.value().subtract(distributionInner.requestedQuantity().value()));
    final var aliveLine = distributionInner.lines().getAliveLine();
    if (aliveLine.isPresent()) {
      final var updatedLine = aliveLine.get().incrementRequestedQuantity(increasedQuantity, updatedAt);
      return distributionInner.lines().updateLine(updatedLine, requestedQuantity.value());
    } else {
      final var newLine = DistributionInnerLine.create(
          new DistributionInnerLine.Id(this.uuidGenerator.generate()),
          DistributionInnerLine.TheoreticalQuantity.computeLineTheoretical(increasedQuantity,
              distributionInner.requestedQuantity().value()),
          new DistributionInnerLine.RequestedQuantity(increasedQuantity.value()),
          updatedAt);
      return distributionInner.lines().addLine(newLine, requestedQuantity.value());
    }
  }

  private DistributionInnerLines decreaseRequestedQuantity(
      final RequestedQuantity requestedQuantity,
      final DistributionInner distributionInner,
      final OffsetDateTime updatedAt) {
    return distributionInner.decreaseRequestedQuantity(requestedQuantity, updatedAt).lines();
  }
}
