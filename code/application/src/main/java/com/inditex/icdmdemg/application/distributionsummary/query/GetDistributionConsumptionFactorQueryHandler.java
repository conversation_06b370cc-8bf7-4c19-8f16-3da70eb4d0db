package com.inditex.icdmdemg.application.distributionsummary.query;

import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery.DistributionConsumptionFactor;
import com.inditex.icdmdemg.application.distributionsummary.query.GetDistributionConsumptionFactorQuery.DistributionConsumptionFactorResponse;
import com.inditex.icdmdemg.domain.distributions.DistributionConsumptionFactorDTO;
import com.inditex.icdmdemg.domain.distributions.DistributionConsumptionFactorDTO.ProductId;
import com.inditex.icdmdemg.domain.distributions.DistributionsRepository;
import com.inditex.icdmdemg.domain.shared.Error.MessageError;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetDistributionConsumptionFactorQueryHandler
    implements QueryHandler<GetDistributionConsumptionFactorQuery, Response<DistributionConsumptionFactorResponse>> {

  private static final String ERROR_MESSAGE_FOR_INVALID_LISTS_PARAMS = "At least one productOrderId must be provided";

  private final DistributionsRepository distributionsRepository;

  @Override
  public Response<DistributionConsumptionFactorResponse> ask(final GetDistributionConsumptionFactorQuery query) {
    final var request = query.distributionConsumptionFactorRequest();

    if (request.productOrderIds().isEmpty()) {
      return Response.ofError(new MessageError(ERROR_MESSAGE_FOR_INVALID_LISTS_PARAMS));
    }

    final var result = this.distributionsRepository
        .findDistributionConsumptionFactorByProductOrderIds(
            query.distributionConsumptionFactorRequest().productOrderIds().stream().map(ProductId::new)
                .toList())
        .stream().map(this::mapToResponse)
        .toList();

    return Response.ofResponse(new DistributionConsumptionFactorResponse(result));
  }

  private DistributionConsumptionFactor mapToResponse(final DistributionConsumptionFactorDTO distributionConsumptionFactorDTO) {
    return new DistributionConsumptionFactor(
        distributionConsumptionFactorDTO.referenceId().value(),
        distributionConsumptionFactorDTO.variantGroupId().value(),
        distributionConsumptionFactorDTO.consumptionFactor().value(),
        distributionConsumptionFactorDTO.distributionType().toString());
  }
}
