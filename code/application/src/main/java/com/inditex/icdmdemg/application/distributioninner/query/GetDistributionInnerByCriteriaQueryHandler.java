package com.inditex.icdmdemg.application.distributioninner.query;

import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.iopcmmnt.cqrs.core.QueryHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Slice;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class GetDistributionInnerByCriteriaQueryHandler
    implements QueryHandler<GetDistributionInnerByCriteriaQuery, Slice<DistributionInner>> {

  private final DistributionInnerRepository distributionInnerRepository;

  @Override
  public Slice<DistributionInner> ask(final GetDistributionInnerByCriteriaQuery query) {
    final var referenceIds = query.referenceIds().stream().filter(Objects::nonNull).map(ReferenceId::new).toList();
    final var productOrderIds = query.productOrderIds().stream().filter(Objects::nonNull).map(ProductOrderId::new).toList();
    final var budgetCycles = query.budgetCycles().stream().filter(Objects::nonNull).map(BudgetCycle::new).toList();
    final var statusList = query.status().stream().filter(Objects::nonNull).map(DistributionInnerStatus::valueOf).toList();

    if (Stream.of(referenceIds, productOrderIds).allMatch(List::isEmpty)) {
      throw new ErrorException(new BadRequest("Some filter is required"));
    }

    return this.distributionInnerRepository.findByCriteria(referenceIds, productOrderIds, budgetCycles, statusList, query.pageable());
  }
}
