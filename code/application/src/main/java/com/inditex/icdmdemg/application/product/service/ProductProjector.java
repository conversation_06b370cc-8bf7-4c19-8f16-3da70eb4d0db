package com.inditex.icdmdemg.application.product.service;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;
import static com.inditex.icdmdemg.shared.utils.Nullables.safeListWithEmptyList;

import java.util.UUID;

import com.inditex.icdmdemg.application.product.command.ProjectProductCommand.ProductReference;
import com.inditex.icdmdemg.domain.product.Product;
import com.inditex.icdmdemg.domain.product.ProductRepository;
import com.inditex.icdmdemg.domain.product.entity.ProductCampaign;
import com.inditex.icdmdemg.domain.product.entity.ProductColor;
import com.inditex.icdmdemg.domain.product.entity.ProductFamilies;
import com.inditex.icdmdemg.domain.product.entity.ProductFamily;
import com.inditex.icdmdemg.domain.product.entity.ProductFamily.CampaignId;
import com.inditex.icdmdemg.domain.product.entity.ProductFamily.FamilyId;
import com.inditex.icdmdemg.domain.product.entity.ProductId;
import com.inditex.icdmdemg.domain.product.entity.ProductOriginMarket;
import com.inditex.icdmdemg.domain.product.entity.ProductOwner;
import com.inditex.icdmdemg.domain.product.entity.ProductOwner.OwnerId;
import com.inditex.icdmdemg.domain.product.entity.ProductOwners;
import com.inditex.icdmdemg.domain.product.entity.ProductQuality;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.domain.product.entity.ProductSupplier;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ProductProjector {

  private final ProductRepository productRepository;

  private final ClockUtils clockUtils;

  private final Transaction transaction;

  public Product fromProductCreatedOrUpdated(final ProductReference productReference) {

    final var now = this.clockUtils.getCurrentOffsetDateTime();

    final var productId = new ProductId(productReference.productId());
    final var referenceId = new ProductReferenceId(productReference.referenceId());
    final var supplier = new ProductSupplier(productReference.supplierId());
    final var color = acceptNullElseMap(productReference.attributes().color(), ProductColor::new);
    final var campaign = acceptNullElseMap(productReference.campaignId(), ProductCampaign::new);
    final var originMarket = acceptNullElseMap(productReference.originMarketId(), ProductOriginMarket::new);
    final var quality = acceptNullElseMap(productReference.attributes().quality(), ProductQuality::new);
    final var families = new ProductFamilies(safeListWithEmptyList(productReference.families()).stream()
        .map(s -> ProductFamily.create(new CampaignId(productReference.campaignId()), new FamilyId(s), now)).toList());
    final var owners = new ProductOwners(safeListWithEmptyList(productReference.attributes().owners()).stream()
        .map(s -> ProductOwner.create(new OwnerId(s), now)).toList());

    return this.productRepository.findByReferenceId(referenceId)
        .map(productFound -> {
          if (productFound.willBeUpdatedWith(color, supplier,
              campaign, originMarket, quality, families, owners)) {

            final var productUpdated =
                productFound.modify(color, supplier, campaign, originMarket, quality, families.value(), owners.value(),
                    now);

            this.transaction.run(() -> this.productRepository.save(productUpdated));

            return productUpdated;
          }

          return productFound;
        }).orElseGet(() -> {
          final var product = Product.create(productId, referenceId, color, supplier, campaign, originMarket, quality,
              families, owners, now);

          return this.transaction.run(() -> this.productRepository.save(product));
        });

  }

  public void deleteReference(final UUID referenceIdToDelete) {
    this.productRepository.findByReferenceId(new ProductReferenceId(referenceIdToDelete.toString()))
        .ifPresent(productReferenceFound -> this.transaction
            .run(() -> this.productRepository.delete(productReferenceFound)));
  }
}
