package com.inditex.icdmdemg.application.distributioninner.command;

import java.util.EnumSet;
import java.util.Set;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerRepository;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;
import com.inditex.iopcmmnt.ddd.core.EventBus;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
class SendAdminEventDistributionInnerCommandHandler
    implements ResultCommandHandler<SendAdminEventDistributionInnerCommand, SendAdminEventInnerResult> {
  private static final Set<EventType> ADMITTED_EVENT_TYPES = EnumSet.of(
      EventType.UPDATED_PENDING,
      EventType.CREATED_PENDING,
      EventType.UPDATED_NON_DISTRIBUTABLE,
      EventType.CREATED_NON_DISTRIBUTABLE,
      EventType.CORRECTED,
      EventType.CLOSED,
      EventType.CANCELED,
      EventType.DELETED);

  private final DistributionInnerRepository distributionInnerRepository;

  private final EventBus eventBus;

  @Override
  public SendAdminEventInnerResult execute(final SendAdminEventDistributionInnerCommand command) {
    final var eventTypeToSend = EventType.fromValue(command.sendEventRequest().eventType());
    if (!this.isAdmittedEventType(eventTypeToSend)) {
      throw new ErrorException(new BadRequest(
          "Event type not allowed. Allowed events are: "
              + "DistributionRequested OR DistributionCorrected "
              + "OR DistributionClosed OR DistributionCanceled"));
    }
    final var distribution = this.distributionInnerRepository.findById(new Id(command.sendEventRequest().distributionInnerId()))
        .orElseThrow(() -> new ErrorException(new BadRequest("Distribution not found with given ID")))
        .registerEvent(eventTypeToSend);
    this.eventBus.send(distribution.domainEvents());

    return new SendAdminEventInnerResult(distribution.getId());
  }

  private boolean isAdmittedEventType(final EventType eventTypeToSend) {
    return ADMITTED_EVENT_TYPES.contains(eventTypeToSend);
  }

}
