package com.inditex.icdmdemg.application.distributionsummary.query;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.application.distributionsummary.query.GetUsesAvailabilityQuery.UsesAvailabilityResponse;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.iopcmmnt.cqrs.core.Query;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record GetUsesAvailabilityQuery(GetUsesAvailabilityRequest request) implements Query<Response<UsesAvailabilityResponse>> {

  public record GetUsesAvailabilityRequest(
      UUID referenceId,
      UUID useId,
      String budgetCycle) {
  }

  public static GetUsesAvailabilityRequest of(
      UUID referenceId,
      UUID useId,
      String budgetCycle) {
    return new GetUsesAvailabilityRequest(
        referenceId,
        useId,
        budgetCycle);
  }

  public record UsesAvailabilityResponse(
      List<UsesAvailability> data) {
  }

  public record UsesAvailability(
      UUID useId,
      String useName,
      List<String> purchaseTypes,
      boolean assignable,
      UsesAvailabilityTotalNominatedQuantity nominated,
      UsesAvailabilityTotalInnerQuantity inner) {
  }

  public record UsesAvailabilityTotalNominatedQuantity(
      BigDecimal totalQuantity) {

  }

  public record UsesAvailabilityTotalInnerQuantity(
      BigDecimal totalQuantity) {

  }
}
