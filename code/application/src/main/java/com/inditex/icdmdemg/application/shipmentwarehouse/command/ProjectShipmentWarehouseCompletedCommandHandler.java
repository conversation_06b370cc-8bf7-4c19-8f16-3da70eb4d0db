package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import com.inditex.icdmdemg.application.shipmentwarehouse.command.ProjectShipmentWarehouseCompletedCommand.ShipmentWarehouseCompleted;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouseRepository;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTimestamp;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTrackingCode;
import com.inditex.icdmdemg.shared.utils.ClockUtils;
import com.inditex.icdmdemg.shared.utils.Transaction;
import com.inditex.iopcmmnt.cqrs.core.ResultCommandHandler;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class ProjectShipmentWarehouseCompletedCommandHandler
    implements ResultCommandHandler<ProjectShipmentWarehouseCompletedCommand, ShipmentWarehouse> {

  private static final String SHIPMENT_WAREHOUSE_WITH_TRACKING_CODE_NOT_FOUND = "ShipmentWarehouse with tracking code %s not found";

  private final ShipmentWarehouseRepository shipmentWarehouseRepository;

  private final ClockUtils clockUtils;

  private final Transaction transaction;

  @Override
  public ShipmentWarehouse execute(final ProjectShipmentWarehouseCompletedCommand command) {
    final ShipmentWarehouseCompleted shipmentWarehouseCompleted = command.shipmentWarehouseCompleted();

    return this.shipmentWarehouseRepository.findByTrackingCode(
        new ShipmentWarehouseTrackingCode(shipmentWarehouseCompleted.distributionTrackingCode()))
        .map(shipmentWarehouseFound -> {
          shipmentWarehouseFound.endShipment(new ShipmentWarehouseTimestamp(shipmentWarehouseCompleted.distributionEndDate()),
              new ShipmentWarehouseTimestamp(this.clockUtils.getCurrentOffsetDateTime()));
          this.transaction.run(() -> this.shipmentWarehouseRepository.save(shipmentWarehouseFound));
          return shipmentWarehouseFound;
        }).orElseThrow(() -> new IllegalArgumentException(String.format(SHIPMENT_WAREHOUSE_WITH_TRACKING_CODE_NOT_FOUND,
            shipmentWarehouseCompleted.distributionTrackingCode())));
  }
}
