package com.inditex.icdmdemg.application.product.query;

import java.util.UUID;

import com.inditex.icdmdemg.application.product.query.GetAlternativeProductReferenceQuery.GetAlternativeProductReferenceResponse;
import com.inditex.icdmdemg.domain.shared.Response;
import com.inditex.iopcmmnt.cqrs.core.Query;

public record GetAlternativeProductReferenceQuery(UUID referenceId) implements Query<Response<GetAlternativeProductReferenceResponse>> {

  public record GetAlternativeProductReferenceResponse(UUID alternativeReferenceId) {
  }
}
