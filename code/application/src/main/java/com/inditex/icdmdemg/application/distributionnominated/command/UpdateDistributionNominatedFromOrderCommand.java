package com.inditex.icdmdemg.application.distributionnominated.command;

import java.util.List;

import com.inditex.icdmdemg.application.distributionnominated.command.UpdateDistributionNominatedFromOrderCommand.UpdateDistributionNominatedFromOrderCommandResult;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

public record UpdateDistributionNominatedFromOrderCommand(
    String orderId,
    String triggeredBy) implements BaseCommand<UpdateDistributionNominatedFromOrderCommandResult> {

  public record UpdateDistributionNominatedFromOrderCommandResult(
      List<SharedRawMaterialNominated> sharedRawMaterialUpdated) {

    public static UpdateDistributionNominatedFromOrderCommandResult empty() {
      return new UpdateDistributionNominatedFromOrderCommandResult(List.of());
    }

  }

}
