package com.inditex.icdmdemg.application.commitmentuse.command;

import java.util.List;

import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.iopcmmnt.cqrs.core.Command;

import org.jspecify.annotations.NonNull;

public record CalculateNominatedProvisionCommand(
    @NonNull List<SharedRawMaterialNominated> sharedRawMaterials,
    @NonNull String triggeredBy)
    implements
      Command {
}
