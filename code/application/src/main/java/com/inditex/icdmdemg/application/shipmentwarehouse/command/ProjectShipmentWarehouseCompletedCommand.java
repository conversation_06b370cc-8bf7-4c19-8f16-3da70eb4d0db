package com.inditex.icdmdemg.application.shipmentwarehouse.command;

import java.time.OffsetDateTime;

import com.inditex.icdmdemg.application.shipmentwarehouse.validator.ShipmentWarehouseTrackingCodeValidator;
import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.iopcmmnt.cqrs.core.BaseCommand;

import jakarta.validation.constraints.NotNull;

public record ProjectShipmentWarehouseCompletedCommand(ShipmentWarehouseCompleted shipmentWarehouseCompleted)
    implements
      BaseCommand<ShipmentWarehouse> {

  public record ShipmentWarehouseCompleted(
      @ShipmentWarehouseTrackingCodeValidator String distributionTrackingCode,
      @NotNull OffsetDateTime distributionEndDate) {
  }

}
