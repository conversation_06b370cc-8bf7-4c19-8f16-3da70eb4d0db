package com.inditex.icdmdemg.application.use.command;

import java.util.List;
import java.util.UUID;

import com.inditex.iopcmmnt.cqrs.core.Command;

import lombok.Getter;
import org.jspecify.annotations.NullMarked;
import org.jspecify.annotations.Nullable;

@NullMarked
public record PatchUseCommand(
    UUID useId,
    UseRequestNames names,
    @Nullable List<String> assignable,
    String triggeredBy) implements Command {

  public record UsePatchRequestName(
      String locale,
      String translation) {
  }

  @Getter
  public enum AssignablePatchRequest {
    NOMINATED(List.of(AssignablePatchRequest.NOMINATED_VALUE)),
    INNER(List.of(AssignablePatchRequest.INNER_VALUE)),
    NOMINATED_INNER(List.of(AssignablePatchRequest.NOMINATED_VALUE, AssignablePatchRequest.INNER_VALUE)),
    NO_ASSIGNABLE(List.of());

    private static final String NOMINATED_VALUE = "NOMINATED";

    private static final String INNER_VALUE = "INNER";

    private final List<String> stringList;

    AssignablePatchRequest(final List<String> stringList) {
      this.stringList = stringList;
    }
  }
}
