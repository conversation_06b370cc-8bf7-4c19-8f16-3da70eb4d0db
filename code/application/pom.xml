<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.inditex.icdmdemg</groupId>
    <artifactId>icdmdebtmgmt</artifactId>
    <version>1.91.0-SNAPSHOT</version>
    <relativePath>..</relativePath>
  </parent>

  <artifactId>icdmdebtmgmt-application</artifactId>
  <packaging>jar</packaging>

  <name>${project.groupId}:${project.artifactId}</name>
  <description>Implementation of the domain layer (primary ports).</description>

  <dependencies>
    <dependency>
      <groupId>com.inditex.iopcmmntddd</groupId>
      <artifactId>lib-iopcmmntddd-core</artifactId>
    </dependency>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>icdmdebtmgmt-domain</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-messaging</artifactId>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <scope>provided</scope>
    </dependency>

    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-tx</artifactId>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-starter-retry</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.inditex.amigafwk.test</groupId>
      <artifactId>test-starter-instancio</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.test</groupId>
      <artifactId>test-starter-logcaptor</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.inditex.iopcmmntddd</groupId>
      <artifactId>lib-iopcmmntddd-starter-spring-glue</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.data</groupId>
      <artifactId>data-starter-scs-outbox-jdbc</artifactId>
    </dependency>
  </dependencies>

  <build>
    <finalName>${project.artifactId}-${project.version}</finalName>
    <plugins />
  </build>

  <reporting />
</project>
