package com.inditex.icdmdemg.provis.domain.orderuse;

import java.util.List;

import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.BudgetCycle;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.ProductId;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.ProductReferenceId;
import com.inditex.icdmdemg.provis.domain.orderuse.OrderUseSummaryDTO.StateEnum;

import org.jspecify.annotations.NullMarked;

@NullMarked
public interface OrderUseRepository {

  List<OrderUseSummaryDTO> findOrderUseSummaries(
      final List<ProductId> productIds,
      final List<ProductReferenceId> productReferenceIds,
      final List<BudgetCycle> budgetCycles,
      final List<StateEnum> excludedStatus);

  List<OrderUseInnerPendingByUseDTO> findOrderUseInnerPendingBySharedRawMaterialUses(
      final ProductReferenceId referenceId,
      final List<OrderUseInnerPendingByUseDTO.UseId> uses,
      final BudgetCycle budgetCycle);
}
