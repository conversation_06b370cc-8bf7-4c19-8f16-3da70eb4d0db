package com.inditex.icdmdemg.domain.distributions;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record DistributionConsumptionFactorDTO(
    ReferenceId referenceId,
    ProductVariantGroupId variantGroupId,
    ConsumptionFactor consumptionFactor,
    DistributionType distributionType) {

  public record ProductId(UUID value) implements ValueObject<UUID> {
  }

  public record ReferenceId(UUID value) implements ValueObject<UUID> {
  }

  public record ProductVariantGroupId(UUID value) implements ValueObject<UUID> {
  }

  public record ConsumptionFactor(BigDecimal value) implements ValueObject<BigDecimal> {
    public ConsumptionFactor(@NonNull final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }
  }

  public enum DistributionType {
    NOMINATED,
    INNER
  }
}
