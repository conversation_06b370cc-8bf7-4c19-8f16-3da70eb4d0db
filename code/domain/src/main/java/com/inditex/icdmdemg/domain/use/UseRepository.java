package com.inditex.icdmdemg.domain.use;

import static com.inditex.icdmdemg.domain.use.Use.Id;

import java.util.List;
import java.util.Optional;

import com.inditex.icdmdemg.domain.use.Use.Customer;
import com.inditex.icdmdemg.domain.use.Use.Taxonomy;
import com.inditex.icdmdemg.domain.use.entity.PurchaseType;

import org.jspecify.annotations.NullMarked;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;

@NullMarked
public interface UseRepository {

  void save(final Use use);

  Optional<Use> findById(Id id);

  List<Use> findByIds(List<Id> ids);

  List<Use> findByTaxonomiesAndCustomerAndPurchaseTypes(final List<Taxonomy> taxonomies, final Customer customer,
      final List<PurchaseType> purchaseTypes);

  List<Use> findBySingleTaxonomyCustomerAndPurchaseType(final Taxonomy taxonomy, final Customer customer,
      final PurchaseType purchaseType);

  Slice<Use> findByIdsPageable(final List<Id> useIds, final Pageable pageable);
}
