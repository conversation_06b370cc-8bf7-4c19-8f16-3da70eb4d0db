package com.inditex.icdmdemg.domain.use.entity;

import java.util.Arrays;
import java.util.List;
import java.util.function.BiFunction;

import com.inditex.icdmdemg.domain.use.entity.ranker.PurchasePurposeAllInRanker;
import com.inditex.icdmdemg.domain.use.entity.ranker.PurchasePurposeAnyInRanker;
import com.inditex.icdmdemg.domain.use.entity.ranker.PurchasePurposeEqualsRanker;
import com.inditex.icdmdemg.domain.use.entity.ranker.PurchasePurposeNoneInRanker;
import com.inditex.icdmdemg.domain.use.entity.ranker.PurchasePurposeRankResult;

import lombok.Getter;

@Getter
public enum PurchasePurposeCondition {
  EQUALS(PurchasePurposeEqualsRanker::evaluate),
  ALL_IN(PurchasePurposeAllInRanker::evaluate),
  ANY_IN(PurchasePurposeAnyInRanker::evaluate),
  NONE_IN(PurchasePurposeNoneInRanker::evaluate);

  private final BiFunction<List<String>, List<String>, PurchasePurposeRankResult> evaluator;

  PurchasePurposeCondition(BiFunction<List<String>, List<String>, PurchasePurposeRankResult> evaluator) {
    this.evaluator = evaluator;
  }

  public static boolean contains(final String purposeConditionValue) {
    return Arrays.stream(PurchasePurposeCondition.values())
        .anyMatch(purchasePurposeCondition -> purchasePurposeCondition.name().equals(purposeConditionValue));
  }

}
