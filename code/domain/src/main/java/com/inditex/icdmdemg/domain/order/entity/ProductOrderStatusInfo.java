package com.inditex.icdmdemg.domain.order.entity;

import com.inditex.icdmdemg.domain.order.Order;

public record ProductOrderStatusInfo(
    Status status,
    Boolean published) {

  public static ProductOrderStatusInfo of(final Order order) {
    final var orderStatus = switch (order.orderStatusKey()) {
      case DRAFT -> Status.DRAFT;
      case FORMALIZED -> Status.FORMALIZED;
      case CANCELLED -> Status.CANCELLED;
      case CLOSED -> Status.CLOSED;
    };
    return new ProductOrderStatusInfo(orderStatus, order.isPublished());
  }

  public static ProductOrderStatusInfo deleted() {
    return new ProductOrderStatusInfo(Status.DELETED, false);
  }

  public boolean isPublished() {
    return this.published;
  }

  public boolean isCancelledOrDeleted() {
    return this.isDeleted() || this.isCancelled();
  }

  public boolean isCancelled() {
    return this.status == Status.CANCELLED;
  }

  public boolean isDeleted() {
    return this.status == Status.DELETED;
  }

  public enum Status {
    DRAFT, FORMALIZED, CANCELLED, CLOSED, DELETED
  }
}
