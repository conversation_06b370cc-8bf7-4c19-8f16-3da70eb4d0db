package com.inditex.icdmdemg.provis.domain.orderuse;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

public record OrderUseSummaryDTO(
    ProductId productId,
    ProductReferenceId productReferenceId,
    UseId useId,
    BudgetCycle budgetCycle,
    Ordered ordered,
    Pending pending,
    Entered entered,
    OrderUseType orderType) {

  public record BudgetCycle(String value) implements ValueObject<String> {

  }

  public enum OrderUseType implements ValueObject<String> {
    ORDINARY(),
    COMMITMENT(),
    SHIPMENT(),
    TRANSFORMATION();

    @Override
    public String value() {
      return this.name();
    }
  }

  public enum StateEnum implements ValueObject<String> {
    DRAFT(),
    IN_PROGRESS(),
    CANCELED(),
    CLOSED();

    @Override
    public String value() {
      return this.name();
    }
  }

  public record ProductId(UUID value) implements ValueObject<UUID> {

  }

  public record ProductReferenceId(UUID value) implements ValueObject<UUID> {

  }

  public record UseId(UUID value) implements ValueObject<UUID> {

  }

  public record Ordered(BigDecimal value) implements ValueObject<BigDecimal> {

    public Ordered(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }

  }

  public record Pending(BigDecimal value) implements ValueObject<BigDecimal> {

    public Pending(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }
  }

  public record Entered(BigDecimal value) implements ValueObject<BigDecimal> {

    public Entered(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }
  }
}
