package com.inditex.icdmdemg.domain.product;

import java.util.List;
import java.util.Optional;

import com.inditex.icdmdemg.domain.product.Product.EquivalentReference;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;

import org.jspecify.annotations.NullMarked;

@NullMarked
public interface ProductRepository {
  Optional<Product> findByReferenceId(ProductReferenceId referenceId);

  ProductCollection findByReferenceIds(List<ProductReferenceId> referenceIds);

  ProductCollection findByAnyEquivalentReference(List<EquivalentReference> equivalentReferences);

  Product save(Product product);

  void delete(Product productToDelete);
}
