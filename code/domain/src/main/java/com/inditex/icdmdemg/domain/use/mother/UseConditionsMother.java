package com.inditex.icdmdemg.domain.use.mother;

import java.time.OffsetDateTime;
import java.util.List;

import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeCondition;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues.ConditionValue;
import com.inditex.icdmdemg.domain.use.entity.UseConditions;
import com.inditex.icdmdemg.domain.use.entity.UsePurchasePurposeCondition;
import com.inditex.icdmdemg.shared.utils.RandomValue;

public interface UseConditionsMother {

  static UseConditions generateCondition(final OffsetDateTime now) {
    return new UseConditions(List.of(new UsePurchasePurposeCondition(
        PurchasePurposeCondition.EQUALS,
        PurchasePurposeConditionName.BUYERGROUP,
        new PurchasePurposeConditionValues(List.of(new ConditionValue("urn:BUYERGROUP:123e4567-e89b-12d3-a456-526655440002"))),
        new BasicAudit(now, now))));
  }

  static UseConditions generateConditions(final OffsetDateTime now) {
    return new UseConditions(List.of(new UsePurchasePurposeCondition(
        PurchasePurposeCondition.ALL_IN,
        PurchasePurposeConditionName.BUYERSUBGROUP,
        new PurchasePurposeConditionValues(List.of(
            new ConditionValue("urn:BUYERSUBGROUP:123e4567-e89b-12d3-a456-526655440002"),
            new ConditionValue("urn:BUYERSUBGROUP:542c8136-b788b-12d3-a456-526655444159"))),
        new BasicAudit(now, now)),
        new UsePurchasePurposeCondition(
            PurchasePurposeCondition.EQUALS,
            PurchasePurposeConditionName.BUYERGROUP,
            new PurchasePurposeConditionValues(
                List.of(new ConditionValue("urn:BUYERGROUP:123e4567-e89b-12d3-a456-526655440002"))),
            new BasicAudit(now, now))));
  }

  static UseConditions randomConditions() {
    final var randomCondition =
        PurchasePurposeCondition.values()[RandomValue.randomPositiveInteger(PurchasePurposeCondition.values().length)];
    final var randomConditionName =
        PurchasePurposeConditionName.values()[RandomValue.randomPositiveInteger(PurchasePurposeConditionName.values().length)];
    final var conditionValues =
        List.of("urn:BUYERGROUP:eeb1b180-6941-414e-b508-004cbe32c554", "urn:BUYERGROUP:8f731e72-e62a-41a5-91bf-2b84dbe70ead",
            "urn:product-supplier:2c9a306d-eaf5-43a4-ba9a-57473cf0e8c5", "urn:product-supplier:542864cf-2841-4db8-82e1-4afedbfe842b");
    final var conditionValue = conditionValues.get(RandomValue.randomPositiveInteger(conditionValues.size()));
    return new UseConditions(List.of(new UsePurchasePurposeCondition(randomCondition,
        randomConditionName, new PurchasePurposeConditionValues(List.of(new ConditionValue(conditionValue))),
        new BasicAudit(OffsetDateTime.now(), OffsetDateTime.now()))));
  }
}
