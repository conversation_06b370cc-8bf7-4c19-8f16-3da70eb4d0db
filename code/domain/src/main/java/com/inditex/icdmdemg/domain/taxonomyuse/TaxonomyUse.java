package com.inditex.icdmdemg.domain.taxonomyuse;

import java.util.Arrays;
import java.util.List;

import com.inditex.icdmdemg.domain.taxonomyuse.entity.TaxonomyCode;
import com.inditex.icdmdemg.domain.taxonomyuse.entity.TaxonomyPath;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.jspecify.annotations.NonNull;

@EqualsAndHashCode
@ToString(callSuper = true)
@Getter
public class TaxonomyUse {

  private final TaxonomyCode code;

  private final TaxonomyPath path;

  public TaxonomyUse(
      @NonNull final TaxonomyCode code,
      @NonNull final TaxonomyPath path) {
    this.code = code;
    this.path = path;
  }

  public List<String> mapToTaxonomies() {
    return Arrays.stream(this.path.value().split("/")).filter(s -> !s.isEmpty()).toList();
  }
}
