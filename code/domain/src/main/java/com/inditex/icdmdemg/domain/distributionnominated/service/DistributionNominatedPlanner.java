package com.inditex.icdmdemg.domain.distributionnominated.service;

import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeExecutedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeRequestedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus;
import com.inditex.icdmdemg.domain.commitmentuse.entity.PartialQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Commitment.CompositeCommitmentId;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Line.CompositeLineId;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan.Adjust;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan.Auto;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan.Preselected;
import com.inditex.icdmdemg.domain.shared.EntityAndActionResult;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import lombok.Getter;
import org.apache.commons.lang3.tuple.Pair;

public abstract class DistributionNominatedPlanner {

  protected static final BigDecimal ZERO = NumericUtils.roundUpScale2(BigDecimal.ZERO);

  protected final Map<Id, Request> requests;

  protected final Map<CompositeLineId, Line> lines;

  protected final Map<CompositeCommitmentId, Commitment> commitments;

  protected final Adjustments adjustments;

  protected final DistributionNominatedPlannerType plannerType;

  protected final List<Line> linesDiscardedFromPlanning;

  protected DistributionNominatedPlanner(final List<Request> requests, final List<Line> lines, final List<Commitment> commitments,
      final List<Line> linesDiscardedFromPlanning,
      final DistributionNominatedPlannerType plannerType) {
    this.requests = requests.stream().collect(Collectors.toMap(Request::id, Function.identity()));
    this.lines = lines.stream().collect(Collectors.toMap(Line::compositeId, Function.identity()));
    this.commitments = commitments.stream().collect(Collectors.toMap(Commitment::compositeId, Function.identity()));
    this.adjustments = new Adjustments(lines.stream().map(Line::compositeId).collect(Collectors.toSet()));
    this.linesDiscardedFromPlanning = linesDiscardedFromPlanning;
    this.plannerType = plannerType;
    this.validate();
  }

  public static DistributionNominatedPlanner fromDistributions(
      final SharedRawMaterialNominated sharedRawMaterial,
      final List<DistributionNominated> distributions,
      final List<CommitmentAdjusted> partialCommitments,
      final Plan plan,
      final DistributionNominatedPlannerType plannerType) {

    final var distributionsToPlan = distributions.stream().filter(
        distribution -> plannerType.getIsDistributionNominatedUsedForPlanning().apply(distribution))
        .toList();
    final var distributionsDiscarded = distributions.stream().filter(
        distribution -> !plannerType.getIsDistributionNominatedUsedForPlanning().apply(distribution))
        .toList();

    final var requests = distributionsToPlan.stream()
        .map(distribution -> new Request(distribution.getId(), distribution.requestedQuantity().value(), distribution.audit().createdAt()))
        .toList();

    final var commitments = getPlannerCommitmentsFromAdjustedCommitments(sharedRawMaterial, partialCommitments);

    final var lines = getPlannerLinesFromDistributions(distributionsToPlan);
    final var distributionsDiscardedForPlanning = getPlannerLinesFromDistributions(distributionsDiscarded);

    return switch (plan) {
      case final Auto auto ->
        new AutomaticDistributionNominatedPlanner(requests, lines, commitments, distributionsDiscardedForPlanning, auto, plannerType);
      case final Preselected preselected ->
        new PreselectedDistributionNominatedPlanner(requests, lines, commitments, distributionsDiscardedForPlanning, preselected,
            plannerType);
      case final Adjust ignored ->
        new AdjustDistributionNominatedPlanner(requests, lines, commitments, distributionsDiscardedForPlanning, plannerType);
    };
  }

  private static List<Commitment> getPlannerCommitmentsFromAdjustedCommitments(SharedRawMaterialNominated sharedRawMaterial,
      List<CommitmentAdjusted> partialCommitments) {
    return partialCommitments.stream()
        .map(adjustedCommitment -> {
          final var commitmentOrder = new CommitmentOrder(new CommitmentOrder.Id(
              UUID.fromString(adjustedCommitment.materialCommitmentUse().getOrderLine().orderId().value())),
              new LineId(UUID.fromString(adjustedCommitment.materialCommitmentUse().getOrderLine().orderLineId().value())),
              new SupplierId(adjustedCommitment.materialCommitmentUse().getServiceLocalizationId().value()));
          final var referenceId =
              new ReferenceId(UUID.fromString(adjustedCommitment.materialCommitmentUse().getMaterialReferenceId().value()));
          final var commitmentStatus = adjustedCommitment.materialCommitmentUse().getStatus();
          return new Commitment(commitmentOrder, referenceId, sharedRawMaterial.referenceId(), commitmentStatus,
              adjustedCommitment.adjustedQuantity(),
              adjustedCommitment.materialCommitmentUse().getBudgetCycleChangeRequestedAt(),
              adjustedCommitment.materialCommitmentUse().getBudgetCycleChangeExecutedAt(),
              adjustedCommitment.materialCommitmentUse().getExpectedDate().value());
        }).toList();
  }

  private static List<Line> getPlannerLinesFromDistributions(List<DistributionNominated> distributionsFiltered) {
    return distributionsFiltered.stream()
        .flatMap(distribution -> distribution.lines().value().stream()
            .flatMap(line -> {
              final var mainLine = Stream.of(new Line(distribution.getId(), line.commitmentOrder(), distribution.referenceId(),
                  distribution.referenceId(), line.requestedQuantity().value(), line.distributedQuantity().value(),
                  Optional.of(line.audit().createdAt())));
              final var altLine = Stream.ofNullable(line.alternativeReference())
                  .map(altRef -> new Line(distribution.getId(), line.commitmentOrder(), new ReferenceId(altRef.referenceId().value()),
                      distribution.referenceId(), altRef.requestedQuantity().value(), line.distributedQuantity().value(),
                      Optional.of(line.audit().createdAt())));
              return Stream.concat(mainLine, altLine);
            }))
        .toList();
  }

  public List<EntityAndActionResult<Line>> executePlan() {
    this.execute();
    return this.identifyAdjustments();
  }

  protected abstract void execute();

  protected void allocate(final Id id, final BigDecimal requested) {
    var allocated = this.linesRequestedForIdAdjusted(id);
    final var availableCommitments = this.availableCommitmentsForAllocateAdjusted(id);
    int commitmentsPointer = 0;
    while (commitmentsPointer < availableCommitments.size()
        && requested.subtract(allocated).compareTo(ZERO) > 0) {
      final var availableCommitment = availableCommitments.get(commitmentsPointer);
      final var remaining = requested.subtract(allocated).max(ZERO);
      final var allocation = remaining.min(availableCommitment.partialQuantity());
      allocated = allocated.add(allocation);
      final var compositeLineId =
          new CompositeLineId(id, availableCommitment.entity().commitmentOrder(), availableCommitment.entity().referenceId(),
              availableCommitment.entity().targetReferenceId());
      this.adjustments.add(compositeLineId, allocation);
      commitmentsPointer++;
    }
  }

  protected List<PartialQuantity<Commitment>> availableCommitmentsForAllocateAdjusted(final Id id) {
    final var commitmentsUsed = this.commitmentsAdjusted();
    return this.availableCommitmentsFor(id).stream()
        .map(commitment -> PartialQuantity.of(
            commitment,
            commitment.quantity().subtract(commitmentsUsed.getOrDefault(commitment.compositeId(), ZERO)).max(ZERO)))
        .filter(partial -> partial.partialQuantity().compareTo(ZERO) > 0)
        .toList();
  }

  protected Optional<Request> lookUpRequest(final Id id) {
    return Optional.ofNullable(this.requests.get(id));
  }

  protected Optional<Commitment> lookUpCommitment(final CompositeCommitmentId compositeCommitmentId) {
    return Optional.ofNullable(this.commitments.get(compositeCommitmentId))
        .filter(commitment -> !commitment.isBudgetCycleChangeInProgress());
  }

  protected List<Line> lookUpRequestLines(final Id id) {
    return this.lines.values().stream().filter(line -> line.rootId().equals(id)).toList();
  }

  protected Map<CompositeCommitmentId, BigDecimal> requestedByCommitment() {
    return this.lines.values().stream()
        .collect(Collectors.toMap(Line::compositeCommitmentId, Line::requested, BigDecimal::add));
  }

  protected BigDecimal linesRequestedForIdAdjusted(final Id id) {
    return this.linesRequestedForId(id).add(this.adjustments.adjustedForId(id));
  }

  protected Map<CompositeCommitmentId, BigDecimal> commitmentsAdjusted() {
    final var adjustmentsByCommitment = this.adjustments.adjustmentsByCommitment();
    final var requestedByCommitment = this.requestedByCommitment();
    return Stream.of(adjustmentsByCommitment.entrySet(), requestedByCommitment.entrySet())
        .flatMap(Collection::stream)
        .collect(Collectors.toMap(Entry::getKey, Entry::getValue, BigDecimal::add));
  }

  protected List<Commitment> availableCommitmentsFor(final Id id) {
    return this.commitments.values().stream()
        .filter(commitment -> this.plannerType.getIsAvailableCommitment().apply(commitment))
        .sorted(this.allocationOrder(id))
        .toList();
  }

  protected Comparator<Line> deallocationOrder() {
    final Function<Line, OffsetDateTime> byRequestDate = (Line line) -> Optional.ofNullable(this.requests.get(line.rootId()))
        .map(Request::creationDate).orElse(OffsetDateTime.MAX);
    final Function<Line, OffsetDateTime> byCommitmentDate =
        (Line line) -> Optional.ofNullable(this.commitments.get(line.compositeCommitmentId()))
            .map(Commitment::expectedDate).orElse(OffsetDateTime.MIN);
    final Function<Line, OffsetDateTime> byLineDate = (Line line) -> line.creationDate().orElse(OffsetDateTime.MAX);
    final Function<Line, Boolean> byAlternative = Line::isAlternative;
    return Comparator.comparing(byRequestDate).thenComparing(byCommitmentDate).thenComparing(byLineDate).thenComparing(byAlternative)
        .reversed();
  }

  private List<EntityAndActionResult<Line>> identifyAdjustments() {
    final var adjustedLines = this.adjustments.adjustments().entrySet().stream()
        .map(entry -> {
          final var key = entry.getKey();
          final var adjustment = entry.getValue();
          final var existingLine = this.lines.get(key);

          if (existingLine != null) {
            final var newRequested = existingLine.requested().add(adjustment).max(ZERO);
            return newRequested.compareTo(existingLine.requested()) == 0
                ? EntityAndActionResult.kept(existingLine)
                : EntityAndActionResult.updated(existingLine.withRequested(newRequested));
          } else {
            final var newLine = new Line(key.rootId(), key.commitmentOrder(), key.referenceId(), key.targetReferenceId(), adjustment, ZERO);
            return EntityAndActionResult.created(newLine);
          }
        });

    final var initialLines = this.linesDiscardedFromPlanning.stream()
        .map(EntityAndActionResult::kept);

    return Stream.concat(adjustedLines, initialLines)
        .toList();
  }

  private BigDecimal linesRequestedForId(final Id id) {
    return this.lookUpRequestLines(id).stream().map(Line::requested).reduce(ZERO, BigDecimal::add);
  }

  private Set<CommitmentOrder> commitmentUsedIds(final Id id) {
    return this.lookUpRequestLines(id).stream()
        .map(Line::compositeCommitmentId)
        .map(CompositeCommitmentId::commitmentOrder)
        .collect(Collectors.toSet());
  }

  private Comparator<Commitment> allocationOrder(final Id id) {
    final var commitmentUsedIds = this.commitmentUsedIds(id);
    return Comparator.comparing((Commitment commitment) -> !commitmentUsedIds.contains(commitment.compositeId().commitmentOrder()))
        .thenComparing(Commitment::expectedDate)
        .thenComparing(Commitment::isAlternative);
  }

  private void validate() {
    if (this.lines.values().stream().map(Line::rootId).anyMatch(Predicate.not(this.requests::containsKey))) {
      throw new IllegalArgumentException(MessageFormat.format("Requests does not contain any of these request {0}",
          this.lines.values().stream().map(Line::rootId).map(Id::value).toList()));
    }
    if (this.lines.values().stream()
        .anyMatch(line -> line.creationDate()
            .orElseThrow(() -> new IllegalArgumentException("Line " + line.compositeId() + " should have creationDate"))
            .isBefore(this.requests.get(line.rootId).creationDate()))) {
      throw new IllegalArgumentException("No line can have a creation date older than its parent request");
    }
  }

  public sealed interface Plan {

    sealed interface Auto extends Plan {

      record Create(Id id, RequestedQuantity requestedQuantity) implements Auto {
      }

      record Modify(Id id, RequestedQuantity requestedQuantity) implements Auto {
      }

    }

    sealed interface Preselected extends Plan {

      record Create(Id id, RequestedQuantity requestedQuantity, List<Line> lines) implements Preselected {
      }

      record Modify(Id id, RequestedQuantity requestedQuantity, List<Line> lines) implements Preselected {
      }
    }

    record Adjust() implements Plan {
    }
  }

  public record Request(Id id, BigDecimal requested, OffsetDateTime creationDate) {
  }

  public record Commitment(
      CommitmentOrder commitmentOrder,
      ReferenceId referenceId,
      ReferenceId targetReferenceId,
      MaterialCommitmentUseStatus status,
      BigDecimal quantity,
      MaterialCommitmentUseBudgetCycleChangeRequestedAt budgetCycleChangeRequestedDate,
      MaterialCommitmentUseBudgetCycleChangeExecutedAt budgetCycleChangeExecutedDate,
      OffsetDateTime expectedDate) {

    public CompositeCommitmentId compositeId() {
      return new CompositeCommitmentId(this.commitmentOrder, this.referenceId, this.targetReferenceId);
    }

    public boolean isAlternative() {
      return !this.referenceId.equals(this.targetReferenceId);
    }

    public boolean isBudgetCycleChangeInProgress() {
      return MaterialCommitmentUse.isBudgetCycleChangeInProgress(this.budgetCycleChangeRequestedDate, this.budgetCycleChangeExecutedDate);
    }

    public record CompositeCommitmentId(CommitmentOrder commitmentOrder, ReferenceId referenceId, ReferenceId targetReferenceId) {
    }
  }

  public record Line(
      Id rootId,
      CommitmentOrder commitmentOrder,
      ReferenceId referenceId,
      ReferenceId targetReferenceId,
      BigDecimal requested,
      BigDecimal distributed,
      Optional<OffsetDateTime> creationDate) {

    public Line(
        final Id rootId,
        final CommitmentOrder commitmentOrder,
        final ReferenceId referenceId,
        final ReferenceId targetReferenceId,
        final BigDecimal requested,
        final BigDecimal distributed) {
      this(rootId, commitmentOrder, referenceId, targetReferenceId, requested, distributed, Optional.empty());
    }

    public boolean isNotDistributed() {
      return !this.isDistributed();
    }

    public boolean isDistributed() {
      return this.distributed.compareTo(ZERO) > 0;
    }

    public boolean isAlternative() {
      return !this.referenceId.equals(this.targetReferenceId);
    }

    public CompositeLineId compositeId() {
      return new CompositeLineId(this.rootId, this.commitmentOrder, this.referenceId, this.targetReferenceId);
    }

    public CompositeCommitmentId compositeCommitmentId() {
      return new CompositeCommitmentId(this.commitmentOrder, this.referenceId, this.targetReferenceId);
    }

    public Line withRequested(final BigDecimal requested) {
      return new Line(this.rootId, this.commitmentOrder, this.referenceId, this.targetReferenceId, requested, this.distributed,
          this.creationDate);
    }

    public record CompositeLineId(Id rootId, CommitmentOrder commitmentOrder, ReferenceId referenceId, ReferenceId targetReferenceId) {
    }
  }

  public record Adjustments(HashMap<CompositeLineId, BigDecimal> adjustments) {

    public Adjustments(final Set<CompositeLineId> lineIds) {
      this(new HashMap<>(lineIds.stream().collect(Collectors.toMap(Function.identity(), lineId -> ZERO))));
    }

    public BigDecimal get(final CompositeLineId lineId) {
      return this.adjustments.getOrDefault(lineId, ZERO);
    }

    public void add(final CompositeLineId lineId, final BigDecimal adjustment) {
      this.adjustments.compute(lineId, (idCommitmentOrderPair, bigDecimal) -> Optional.ofNullable(bigDecimal).orElse(ZERO).add(adjustment));
    }

    public void subtract(final CompositeLineId lineId, final BigDecimal adjustment) {
      this.add(lineId, adjustment.negate());
    }

    public Map<CompositeCommitmentId, BigDecimal> adjustmentsByCommitment() {
      return this.adjustments.entrySet().stream()
          .map(entry -> {
            final var commitmentId = new CompositeCommitmentId(entry.getKey().commitmentOrder(), entry.getKey().referenceId(),
                entry.getKey().targetReferenceId());
            return Pair.of(commitmentId, entry.getValue());
          })
          .collect(Collectors.toMap(Entry::getKey, Entry::getValue, BigDecimal::add));
    }

    public BigDecimal adjustedForId(final Id id) {
      return this.adjustments.entrySet().stream()
          .filter(lineId -> lineId.getKey().rootId().equals(id))
          .map(Entry::getValue)
          .reduce(ZERO, BigDecimal::add);
    }

  }

  @Getter
  public enum DistributionNominatedPlannerType implements ValueObject<String> {
    DEFAULT(FilterDistributionNominated::plannerTypeIsDefault, IsAvailableCommitment::plannerTypeIsDefault),
    BUDGET_CYCLE_CHANGE(FilterDistributionNominated::plannerTypeIsBudgetCycleChange, IsAvailableCommitment::plannerTypeIsBudgetCycleChange);

    private final Function<DistributionNominated, Boolean> isDistributionNominatedUsedForPlanning;

    private final Function<Commitment, Boolean> isAvailableCommitment;

    DistributionNominatedPlannerType(final Function<DistributionNominated, Boolean> isDistributionNominatedUsedForPlanning,
        final Function<Commitment, Boolean> isAvailableCommitment) {
      this.isDistributionNominatedUsedForPlanning = isDistributionNominatedUsedForPlanning;
      this.isAvailableCommitment = isAvailableCommitment;
    }

    interface FilterDistributionNominated {
      static boolean plannerTypeIsDefault(final DistributionNominated distributionNominated) {
        return !distributionNominated.isBudgetCycleChangeInProgress();
      }

      static boolean plannerTypeIsBudgetCycleChange(final DistributionNominated distributionNominated) {
        return distributionNominated.isBudgetCycleChangeInProgress();
      }
    }

    interface IsAvailableCommitment {
      static boolean plannerTypeIsDefault(final Commitment commitment) {
        return !commitment.status().isClosed() && !plannerTypeIsBudgetCycleChange(commitment);
      }

      static boolean plannerTypeIsBudgetCycleChange(final Commitment commitment) {
        return commitment.isBudgetCycleChangeInProgress();
      }
    }

    @Override
    public String value() {
      return this.name();
    }
  }

}
