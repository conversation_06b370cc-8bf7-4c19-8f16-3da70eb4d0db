package com.inditex.icdmdemg.domain.product;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;

public record ProductCollection(List<Product> products) {
  public Map<ProductReferenceId, Product> mappedByReferenceId() {
    return this.products.stream()
        .collect(Collectors.toMap(Product::getReferenceId, Function.identity()));
  }

  public List<String> productIds() {
    return this.products.stream().map(product -> product.getProductId().value()).distinct().toList();
  }

  public List<ProductReferenceId> referenceIds() {
    return this.products.stream().map(Product::getReferenceId).distinct().toList();
  }

}
