package com.inditex.icdmdemg.domain.distributioninner;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.UseId;

public record DistributionInnerSummaryDTO(
    ReferenceProductId referenceProductId,
    ReferenceId referenceId,
    UseId useId,
    BudgetCycle budgetCycle,
    RequestedQuantity requested,
    DistributedQuantity distributed) {
}
