package com.inditex.icdmdemg.domain.order;

import java.time.OffsetDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.inditex.icdmdemg.domain.order.entity.OrderBudgetId;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.OrderLine;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetail;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetails;
import com.inditex.icdmdemg.domain.order.entity.OrderLines;
import com.inditex.icdmdemg.domain.order.entity.OrderStatusKey;
import com.inditex.icdmdemg.domain.order.entity.OrderSupplierId;
import com.inditex.icdmdemg.domain.order.entity.OrderVersion;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.iopcmmnt.ddd.core.AggregateRoot;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.jspecify.annotations.NonNull;

@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Getter
@Accessors(fluent = true)
public class Order extends AggregateRoot<OrderId> {

  @NonNull
  private OrderStatusKey orderStatusKey;

  @NonNull
  private OrderLines orderLines;

  @NonNull
  private OrderSupplierId supplierId;

  @NonNull
  private Boolean isPublished;

  @NonNull
  private BasicAudit audit;

  @NonNull
  private final OrderVersion version;

  public Order(@NonNull final OrderId orderId, @NonNull final OrderStatusKey orderStatusKey, final @NonNull OrderLines orderLines,
      final @NonNull BasicAudit audit, @NonNull final OrderSupplierId supplierId,
      final @NonNull Boolean isPublished, @NonNull final OrderVersion version) {
    super(orderId);
    this.orderStatusKey = orderStatusKey;
    this.orderLines = orderLines;
    this.audit = audit;
    this.supplierId = supplierId;
    this.isPublished = isPublished;
    this.version = version;
  }

  public static Order create(@NonNull final OrderId orderId,
      @NonNull final OrderStatusKey orderStatusKey,
      final @NonNull OrderLines orderLines, @NonNull final OffsetDateTime now,
      final OrderSupplierId supplierId) {
    return new Order(orderId, orderStatusKey, orderLines, BasicAudit.create(now), supplierId, false,
        OrderVersion.firstVersion());

  }

  public boolean willBeUpdatedWith(final @NonNull OrderLines orderLines,
      final OrderSupplierId orderSupplierId) {
    return !Objects.equals(this.supplierId, orderSupplierId)
        || this.orderLines.willBeUpdatedWith(orderLines);
  }

  public boolean willBeUpdatedWith(final OrderStatusKey orderStatusKey) {
    return !Objects.equals(this.orderStatusKey, orderStatusKey);
  }

  public boolean willBePublished() {
    return !this.isPublished;
  }

  public Order updateStatus(@NonNull final OrderStatusKey orderStatusKey,
      @NonNull final OffsetDateTime now) {
    this.orderStatusKey = orderStatusKey;
    this.audit = this.audit.update(now);
    return this;
  }

  public Order publish(@NonNull final OffsetDateTime now) {
    this.isPublished = true;
    this.audit = this.audit.update(now);
    return this;
  }

  public Order update(final @NonNull OrderLines orderLines, final OrderSupplierId orderSupplierId,
      @NonNull final OffsetDateTime now) {
    this.orderLines = orderLines;
    this.supplierId = orderSupplierId;
    this.audit = this.audit.update(now);
    return this;
  }

  public List<ProductReferenceId> orderReferenceIdsAsProductReferenceIds() {
    return this.orderLines.value().stream()
        .map(OrderLine::orderLineQuantityDetails)
        .map(OrderLineQuantityDetails::value)
        .flatMap(Collection::stream)
        .map(OrderLineQuantityDetail::referenceId)
        .map(referenceId -> new ProductReferenceId(referenceId.value()))
        .distinct()
        .toList();
  }

  public Boolean isClosed() {
    return this.orderStatusKey.isClosed();
  }

  public Boolean isCancelled() {
    return this.orderStatusKey.isCancelled();
  }

  public Optional<OrderBudgetId> firstBudgetId() {
    return this.orderLines.firstBudgetId();
  }

}
