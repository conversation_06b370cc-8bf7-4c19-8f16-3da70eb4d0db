package com.inditex.icdmdemg.domain.nominatedprovision;

import java.math.BigDecimal;
import java.time.OffsetDateTime;

import com.inditex.icdmdemg.domain.nominatedprovision.entity.NominatedProvisionAudit;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record NominatedProvision(
    ProductId productId,
    ReferenceId referenceId,
    UseId useId,
    BudgetId budgetId,
    LocalizationId localizationId,
    Ordered ordered,
    Entered entered,
    Pending pending,
    Stock stock,
    DistributionDistributed distributionDistributed,
    DistributionRequested distributionRequested,
    NominatedProvisionAudit audit) {

  public static NominatedProvision create(
      final ProductId productId,
      final ReferenceId referenceId,
      final UseId useId,
      final BudgetId budgetId,
      final LocalizationId localizationId,
      final Ordered ordered,
      final Entered entered,
      final Pending pending,
      final Stock stock,
      final DistributionDistributed distributionDistributed,
      final DistributionRequested distributionRequested,
      final OffsetDateTime createdAt,
      final String createdBy) {
    return new NominatedProvision(productId, referenceId, useId, budgetId, localizationId, ordered, entered, pending, stock,
        distributionDistributed,
        distributionRequested, NominatedProvisionAudit.create(createdAt, createdBy));
  }

  public record BudgetId(String value) implements ValueObject<String> {

  }

  public record DistributionRequested(BigDecimal value) implements ValueObject<BigDecimal> {

    public DistributionRequested(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }

  }

  public record LocalizationId(String value) implements ValueObject<String> {

  }

  public record Ordered(BigDecimal value) implements ValueObject<BigDecimal> {

    public Ordered(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }

  }

  public record Entered(BigDecimal value) implements ValueObject<BigDecimal> {

    public Entered(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }
  }

  public record Pending(BigDecimal value) implements ValueObject<BigDecimal> {

    public Pending(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }
  }

  public record DistributionDistributed(BigDecimal value) implements ValueObject<BigDecimal> {

    public DistributionDistributed(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }
  }

  public record ProductId(String value) implements ValueObject<String> {

  }

  public record ReferenceId(String value) implements ValueObject<String> {

  }

  public record UseId(String value) implements ValueObject<String> {

  }

  public record Stock(BigDecimal value) implements ValueObject<BigDecimal> {

    public Stock(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }

  }
}
