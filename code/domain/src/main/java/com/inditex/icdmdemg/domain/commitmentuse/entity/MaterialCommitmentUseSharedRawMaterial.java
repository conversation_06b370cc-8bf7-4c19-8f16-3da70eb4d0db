package com.inditex.icdmdemg.domain.commitmentuse.entity;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;

import org.jspecify.annotations.NonNull;

public record MaterialCommitmentUseSharedRawMaterial(
    @NonNull MaterialCommitmentUseUseId materialCommitmentUseId,
    @NonNull MaterialCommitmentUseMaterialReferenceId materialCommitmentMaterialReferenceId,
    MaterialCommitmentUseBudgetId materialCommitmentBudgetId) {

  public static MaterialCommitmentUseSharedRawMaterial of(
      @NonNull final MaterialCommitmentUseUseId materialCommitmentUseUseId,
      @NonNull final MaterialCommitmentUseMaterialReferenceId materialCommitmentUseMaterialReferenceId,
      @NonNull final MaterialCommitmentUseBudgetId materialCommitmentUseBudgetId) {
    return new MaterialCommitmentUseSharedRawMaterial(
        materialCommitmentUseUseId, materialCommitmentUseMaterialReferenceId, materialCommitmentUseBudgetId);

  }

  public static MaterialCommitmentUseSharedRawMaterial of(
      final String materialCommitmentUseId,
      final String materialCommitmentMaterialReferenceId,
      final String materialCommitmentBudgetId) {
    return MaterialCommitmentUseSharedRawMaterial.of(
        new MaterialCommitmentUseUseId(materialCommitmentUseId),
        new MaterialCommitmentUseMaterialReferenceId(materialCommitmentMaterialReferenceId),
        new MaterialCommitmentUseBudgetId(materialCommitmentBudgetId));
  }

  public static MaterialCommitmentUseSharedRawMaterial of(final SharedRawMaterialNominated sharedRawMaterialNominated) {
    return MaterialCommitmentUseSharedRawMaterial.of(
        new MaterialCommitmentUseUseId(sharedRawMaterialNominated.useId().value().toString()),
        new MaterialCommitmentUseMaterialReferenceId(sharedRawMaterialNominated.referenceId().value().toString()),
        new MaterialCommitmentUseBudgetId(sharedRawMaterialNominated.budgetCycle().value()));
  }

  public static MaterialCommitmentUseSharedRawMaterial of(
      @NonNull final MaterialCommitmentUse materialCommitment) {
    return of(
        materialCommitment.getUseId(),
        materialCommitment.getMaterialReferenceId(),
        materialCommitment.getOrderLine().budgetId());
  }

}
