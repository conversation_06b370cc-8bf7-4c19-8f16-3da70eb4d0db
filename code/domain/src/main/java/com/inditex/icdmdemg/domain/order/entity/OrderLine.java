package com.inditex.icdmdemg.domain.order.entity;

import java.util.Objects;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record OrderLine(
    @NonNull OrderLineId id,
    @NonNull OrderLineServiceDate orderLineServiceDate,
    OrderLineMeasuringUnitsId orderLineMeasuringUnitsId,
    OrderBudgetId budgetId,
    @NonNull OrderLineQuantityDetails orderLineQuantityDetails) implements ValueObject<OrderLine> {

  public OrderLine value() {
    return this;
  }

  public boolean willBeUpdatedWith(final OrderLine newOrderLine) {
    return !Objects.equals(this.orderLineServiceDate, newOrderLine.orderLineServiceDate)
        || !Objects.equals(this.orderLineMeasuringUnitsId, newOrderLine.orderLineMeasuringUnitsId)
        || !Objects.equals(this.budgetId, newOrderLine.budgetId)
        || this.orderLineQuantityDetails.willBeUpdatedWith(newOrderLine.orderLineQuantityDetails);
  }
}
