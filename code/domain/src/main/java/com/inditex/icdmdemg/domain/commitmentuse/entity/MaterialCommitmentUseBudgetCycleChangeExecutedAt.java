package com.inditex.icdmdemg.domain.commitmentuse.entity;

import java.time.OffsetDateTime;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record MaterialCommitmentUseBudgetCycleChangeExecutedAt(@NonNull OffsetDateTime value) implements ValueObject<OffsetDateTime> {
  public static MaterialCommitmentUseBudgetCycleChangeExecutedAt of(final OffsetDateTime date) {
    return new MaterialCommitmentUseBudgetCycleChangeExecutedAt(date);
  }
}
