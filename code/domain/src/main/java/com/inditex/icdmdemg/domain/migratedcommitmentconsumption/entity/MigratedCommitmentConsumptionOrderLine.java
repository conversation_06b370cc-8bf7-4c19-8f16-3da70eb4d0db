package com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record MigratedCommitmentConsumptionOrderLine(
    @NonNull MigratedCommitmentConsumptionOrderId orderId,
    @NonNull MigratedCommitmentConsumptionOrderLineId orderLineId) implements ValueObject<MigratedCommitmentConsumptionOrderLine> {

  public static MigratedCommitmentConsumptionOrderLine of(final String orderId, final String orderLineId) {
    return new MigratedCommitmentConsumptionOrderLine(
        new MigratedCommitmentConsumptionOrderId(orderId),
        new MigratedCommitmentConsumptionOrderLineId(orderLineId));
  }

  public MigratedCommitmentConsumptionOrderLine value() {
    return this;
  }
}
