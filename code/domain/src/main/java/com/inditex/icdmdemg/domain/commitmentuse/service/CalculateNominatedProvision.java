package com.inditex.icdmdemg.domain.commitmentuse.service;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.Provision.ProvisionKey;

import lombok.Getter;

public class CalculateNominatedProvision {

  private CalculateNominatedProvision() {
  }

  public static CalculateNominatedProvisionResponse calculate(CalculateNominatedProvisionRequest request) {
    final Map<OrderLine, DistributionQuantities> distributedByCommitment = request.distributions().stream()
        .collect(Collectors.groupingBy(
            Distribution::orderLine,
            Collectors.reducing(
                DistributionQuantities.empty(),
                Distribution::quantities,
                DistributionQuantities::add)));

    final Map<ProvisionKey, Provision> provisionsMap = new HashMap<>();
    final var commitments = request.commitments();
    for (final Commitment commitment : commitments) {
      final var ordered = commitment.ordered();
      final var distributionRequested =
          distributedByCommitment.getOrDefault(commitment.orderLine(), DistributionQuantities.empty()).requested();
      final var distributionDistributed =
          distributedByCommitment.getOrDefault(commitment.orderLine(), DistributionQuantities.empty()).distributed();

      final var pending = CalculatorProvider.PENDING.getCalculator().apply(commitment, distributionRequested);
      final var entered = CalculatorProvider.ENTERED.getCalculator().apply(commitment, distributionRequested);
      final var stock = CalculatorProvider.STOCK.getCalculator().apply(commitment, distributionRequested);

      final var provisionKey = new ProvisionKey(commitment.orderLine.sharedRawMaterial, commitment.commitmentOrderLine().localization());
      final var provision = new Provision(provisionKey, ordered, pending, entered,
          new DistributionQuantities(distributionRequested, distributionDistributed), stock);
      provisionsMap.merge(provisionKey, provision, Provision::sum);
    }

    final List<Provision> provisions = provisionsMap.values().stream().toList();
    return new CalculateNominatedProvisionResponse(provisions);
  }

  public record CalculateNominatedProvisionResponse(List<Provision> provisions) {

  }

  public record CalculateNominatedProvisionRequest(List<Commitment> commitments, List<Distribution> distributions) {

  }

  public record Commitment(OrderLine orderLine, BigDecimal ordered, CommitmentOrderLine commitmentOrderLine) {

  }

  public record DistributionQuantities(BigDecimal requested, BigDecimal distributed) {
    public static DistributionQuantities empty() {
      return new DistributionQuantities(BigDecimal.ZERO, BigDecimal.ZERO);
    }

    public DistributionQuantities add(DistributionQuantities other) {
      return new DistributionQuantities(this.requested.add(other.requested), this.distributed.add(other.distributed));
    }

  }

  public record Distribution(OrderLine orderLine, DistributionQuantities quantities) {

  }

  public record Provision(ProvisionKey provisionKey, BigDecimal ordered, BigDecimal pending, BigDecimal entered,
      DistributionQuantities distribution, BigDecimal stock) {

    public Provision sum(Provision provision) {
      return new Provision(this.provisionKey,
          this.ordered.add(provision.ordered),
          this.pending.add(provision.pending),
          this.entered.add(provision.entered),
          this.distribution.add(provision.distribution),
          this.stock.add(provision.stock));
    }

    public record ProvisionKey(SharedRawMaterial sharedRawMaterial, String localization) {

    }
  }

  public record SharedRawMaterial(UUID referenceId, UUID useId, String budgetId) {

  }

  public record OrderLine(UUID orderId, UUID orderLineId, SharedRawMaterial sharedRawMaterial) {

  }

  public record CommitmentOrderLine(CommitmentOrderLineStatus status, String localization, OffsetDateTime expectedDate) {

  }

  public enum CommitmentOrderLineStatus {
    OPEN, CLOSED
  }

  interface Calculator {

    static BigDecimal pending(final Commitment commitment, final BigDecimal distributionRequested) {
      if (CommitmentOrderLineStatus.OPEN.equals(commitment.commitmentOrderLine().status())
          && commitment.commitmentOrderLine().expectedDate().isAfter(OffsetDateTime.now())) {
        return commitment.ordered().subtract(distributionRequested);
      }
      return BigDecimal.ZERO;
    }

    static BigDecimal entered(final Commitment commitment, final BigDecimal distributionRequested) {
      if (CommitmentOrderLineStatus.OPEN.equals(commitment.commitmentOrderLine().status())
          && commitment.commitmentOrderLine().expectedDate().isBefore(OffsetDateTime.now())) {
        return commitment.ordered();
      }
      return distributionRequested;
    }

    static BigDecimal stock(final Commitment commitment, final BigDecimal distributionRequested) {
      if (CommitmentOrderLineStatus.OPEN.equals(commitment.commitmentOrderLine().status())
          && commitment.commitmentOrderLine().expectedDate().isBefore(OffsetDateTime.now())) {
        return commitment.ordered().subtract(distributionRequested);
      }
      return BigDecimal.ZERO;
    }
  }

  @Getter
  enum CalculatorProvider {
    PENDING(Calculator::pending),
    ENTERED(Calculator::entered),
    STOCK(Calculator::stock);

    private final BiFunction<Commitment, BigDecimal, BigDecimal> calculator;

    CalculatorProvider(BiFunction<Commitment, BigDecimal, BigDecimal> calculate) {
      this.calculator = calculate;
    }
  }
}
