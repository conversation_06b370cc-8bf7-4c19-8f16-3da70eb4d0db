package com.inditex.icdmdemg.domain.nominatedprovision.entity;

import java.time.OffsetDateTime;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record NominatedProvisionAudit(
    AuditTimestamp updatedAt,
    AuditBy updatedBy) {

  public static NominatedProvisionAudit create(
      final OffsetDateTime updatedAt,
      final String updatedBy) {
    return new NominatedProvisionAudit(
        new AuditTimestamp(updatedAt),
        new AuditBy(updatedBy));
  }

  public record AuditTimestamp(OffsetDateTime value) implements ValueObject<OffsetDateTime> {

  }

  public record AuditBy(String value) implements ValueObject<String> {

  }

}
