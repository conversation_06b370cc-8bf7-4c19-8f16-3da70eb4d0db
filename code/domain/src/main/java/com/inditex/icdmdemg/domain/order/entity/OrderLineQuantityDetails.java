package com.inditex.icdmdemg.domain.order.entity;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import com.google.common.collect.Sets;
import org.jspecify.annotations.NonNull;

public record OrderLineQuantityDetails(@NonNull List<OrderLineQuantityDetail> value) implements ValueObject<List<OrderLineQuantityDetail>> {

  public boolean willBeUpdatedWith(OrderLineQuantityDetails orderLineQuantityDetails) {
    final var existingOrderLineQuantityDetailsById =
        this.value.stream().collect(Collectors.toMap(OrderLineQuantityDetail::referenceId, Function.identity()));
    final var newOrderLineQuantityDetailsById =
        orderLineQuantityDetails.value().stream().collect(Collectors.toMap(OrderLineQuantityDetail::referenceId, Function.identity()));
    return Sets.union(existingOrderLineQuantityDetailsById.keySet(), newOrderLineQuantityDetailsById.keySet()).stream()
        .anyMatch(referenceId -> this.willBeOrderLineQuantityDetailUpdated(referenceId, existingOrderLineQuantityDetailsById,
            newOrderLineQuantityDetailsById));
  }

  private boolean willBeOrderLineQuantityDetailUpdated(OrderLineQuantityDetailReferenceId referenceId,
      Map<OrderLineQuantityDetailReferenceId, OrderLineQuantityDetail> existingOrderLineQuantityDetailsById,
      Map<OrderLineQuantityDetailReferenceId, OrderLineQuantityDetail> newOrderLineQuantityDetailsById) {
    if (!existingOrderLineQuantityDetailsById.containsKey(referenceId) || !newOrderLineQuantityDetailsById.containsKey(referenceId)) {
      return true;
    }
    final var existing = existingOrderLineQuantityDetailsById.get(referenceId);
    final var newOne = newOrderLineQuantityDetailsById.get(referenceId);
    return existing.willBeUpdatedWith(newOne);
  }
}
