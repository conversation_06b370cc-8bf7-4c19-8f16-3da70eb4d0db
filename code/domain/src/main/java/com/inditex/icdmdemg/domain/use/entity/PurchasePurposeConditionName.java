package com.inditex.icdmdemg.domain.use.entity;

import java.util.Arrays;

import lombok.Getter;

@Getter
public enum PurchasePurposeConditionName {
  BUYERGROUP(1),
  BUYERSUBGROUP(2),
  BUYERCODE(3),
  FAMILY(4),
  SUPPLIER_PT(5);

  private final int priority;

  PurchasePurposeConditionName(final int priority) {
    this.priority = priority;
  }

  public static boolean contains(final String purchaseConditionName) {
    return Arrays.stream(PurchasePurposeConditionName.values())
        .anyMatch(purchasePurposeConditionName -> purchasePurposeConditionName.name().equals(purchaseConditionName));
  }
}
