package com.inditex.icdmdemg.domain.migratedcommitmentconsumption;

import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionId;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionMovementId;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionOrderLine;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionQuantity;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionReferenceId;

import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

@EqualsAndHashCode
@ToString(callSuper = true)
@Getter
@AllArgsConstructor
public class MigratedCommitmentConsumption {

  @NonNull
  private final MigratedCommitmentConsumptionId id;

  @Nullable
  private final MigratedCommitmentConsumptionMovementId movementId;

  @NonNull
  private final MigratedCommitmentConsumptionReferenceId referenceId;

  @NonNull
  private final MigratedCommitmentConsumptionOrderLine orderLine;

  @NonNull
  private final MigratedCommitmentConsumptionQuantity quantity;

}
