package com.inditex.icdmdemg.domain.consumermessage.entity;

import java.time.OffsetDateTime;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record ConsumerMessageTimestamp(@NonNull OffsetDateTime value) implements ValueObject<OffsetDateTime> {
  public static ConsumerMessageTimestamp of(final OffsetDateTime date) {
    return new ConsumerMessageTimestamp(date);
  }
}
