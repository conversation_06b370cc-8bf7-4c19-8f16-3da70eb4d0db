package com.inditex.icdmdemg.domain.shipmentwarehouse.entity;

import java.time.OffsetDateTime;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record ShipmentWarehouseTimestamp(@NonNull OffsetDateTime value) implements ValueObject<OffsetDateTime> {

  public static ShipmentWarehouseTimestamp of(final OffsetDateTime date) {
    return new ShipmentWarehouseTimestamp(date);
  }

}
