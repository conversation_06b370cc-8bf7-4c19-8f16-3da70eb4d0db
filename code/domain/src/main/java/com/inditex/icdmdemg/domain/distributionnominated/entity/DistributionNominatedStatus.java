package com.inditex.icdmdemg.domain.distributionnominated.entity;

import java.util.Objects;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.DistributedQuantity;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public enum DistributionNominatedStatus implements ValueObject<String> {
  NON_DISTRIBUTABLE,
  PENDING,
  IN_PROGRESS,
  CLOSED;

  public static DistributionNominatedStatus of(@NonNull final ProductOrderStatusInfo productOrderStatusInfo,
      @NonNull final DistributedQuantity distributedQuantity) {
    return switch (productOrderStatusInfo.status()) {
      case CLOSED -> CLOSED;
      case DRAFT, FORMALIZED -> {
        if (distributedQuantity.isDistributed()) {
          yield IN_PROGRESS;
        }
        if (productOrderStatusInfo.isPublished()) {
          yield PENDING;
        }
        yield NON_DISTRIBUTABLE;
      }
      default ->
        throw new IllegalArgumentException(
            String.format("Unexpected value in product order status: %s", productOrderStatusInfo.status()));
    };
  }

  public DistributionNominatedStatus update(@NonNull final ProductOrderStatusInfo productOrderStatusInfo,
      @NonNull final DistributedQuantity distributedQuantity) {
    return of(productOrderStatusInfo, distributedQuantity);
  }

  @Override
  public String value() {
    return this.name();
  }

  public boolean isInProgress() {
    return IN_PROGRESS.equals(this);
  }

  public boolean isClosed() {
    return CLOSED.equals(this);
  }

  public boolean isNotClosed() {
    return !this.isClosed();
  }

  public boolean isDifferentStatus(final DistributionNominatedStatus newStatus) {
    return !Objects.equals(this, newStatus);
  }
}
