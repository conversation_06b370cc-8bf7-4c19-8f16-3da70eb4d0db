package com.inditex.icdmdemg.domain.distributioninner.entity;

import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record CompositeKeyInner(
    DistributionInner.ReferenceId referenceId,
    DistributionInner.UseId useId,
    DistributionInner.BudgetCycle budgetCycle,
    DistributionInner.ProductOrderId productOrderId,
    DistributionInner.ProductVariantGroupId productVariantGroupId) {

  public CompositeKeyInner(
      final UUID referenceId,
      final UUID useId,
      final String budgetCycle,
      final UUID productOrderId,
      final UUID productVariantGroupId) {
    this(new DistributionInner.ReferenceId(referenceId),
        new DistributionInner.UseId(useId),
        new DistributionInner.BudgetCycle(budgetCycle),
        new DistributionInner.ProductOrderId(productOrderId),
        new DistributionInner.ProductVariantGroupId(productVariantGroupId));
  }
}
