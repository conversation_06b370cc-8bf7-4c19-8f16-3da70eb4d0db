package com.inditex.icdmdemg.domain.distributioninner.entity;

import java.util.Objects;

import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public enum DistributionInnerStatus implements ValueObject<String> {
  NON_DISTRIBUTABLE,
  PENDING,
  IN_PROGRESS,
  SENT,
  CLOSED,
  CANCELED;

  public static DistributionInnerStatus of(@NonNull final ProductOrderStatusInfo productOrderStatusInfo, final boolean sendToDistribution) {
    return switch (productOrderStatusInfo.status()) {
      case CLOSED -> CLOSED;
      case DELETED, CANCELLED -> CANCELED;
      case DRAFT, FORMALIZED -> {
        if (sendToDistribution) {
          yield PENDING;
        }
        yield NON_DISTRIBUTABLE;
      }
    };
  }

  public static DistributionInnerStatus of(
      @NonNull final ProductOrderStatusInfo productOrderStatusInfo,
      @NonNull final DistributionInnerStatus currentStatus) {
    return switch (productOrderStatusInfo.status()) {
      case CLOSED -> CLOSED;
      case DELETED, CANCELLED -> isInProgressOrSent(currentStatus) ? CLOSED : currentStatus;
      case DRAFT, FORMALIZED -> currentStatus;
    };
  }

  public static DistributionInnerStatus of(@NonNull final DistributionInnerStatus currentStatus,
      final boolean hasDistributionStarted, final boolean areAllLinesSent) {
    return switch (currentStatus) {
      case CLOSED -> CLOSED;
      case CANCELED -> CANCELED;
      case IN_PROGRESS, PENDING, NON_DISTRIBUTABLE, SENT -> {
        if (isInProgressOrSent(currentStatus) && areAllLinesSent) {
          yield SENT;
        }
        if (hasDistributionStarted) {
          yield IN_PROGRESS;
        }
        yield currentStatus;
      }
    };
  }

  public static DistributionInnerStatus ofRevertClose(final boolean areAllLinesSent, final boolean hasDistributionStarted) {
    if (areAllLinesSent) {
      return SENT;
    } else if (hasDistributionStarted) {
      return IN_PROGRESS;
    } else {
      return PENDING;
    }
  }

  public DistributionInnerStatus update(@NonNull final ProductOrderStatusInfo productOrderStatusInfo,
      @NonNull final DistributionInnerStatus currentStatus) {
    return of(productOrderStatusInfo, currentStatus);
  }

  public DistributionInnerStatus update(@NonNull final DistributionInnerStatus currentStatus,
      final boolean hasDistributionStarted, final boolean areAllLinesSent) {
    return of(currentStatus, hasDistributionStarted, areAllLinesSent);
  }

  public boolean isDifferentStatus(final DistributionInnerStatus newStatus) {
    return !Objects.equals(this, newStatus);
  }

  public boolean isStatusDifferentFromPendingOrSentOrInProgress() {
    return !this.isSent() && !this.isPending() && !this.isInProgress();
  }

  @Override
  public String value() {
    return this.name();
  }

  public boolean isInProgress() {
    return IN_PROGRESS.equals(this);
  }

  private static boolean isInProgressOrSent(final DistributionInnerStatus currentStatus) {
    return IN_PROGRESS.equals(currentStatus) || SENT.equals(currentStatus);
  }

  public boolean isClosed() {
    return CLOSED.equals(this);
  }

  public boolean isPending() {
    return PENDING.equals(this);
  }

  public boolean isSent() {
    return SENT.equals(this);
  }

  public boolean isNonDistributableOrPending() {
    return this.isNonDistributable() || this.isPending();
  }

  public boolean isNonDistributable() {
    return NON_DISTRIBUTABLE.equals(this);
  }

  public boolean isCanceled() {
    return CANCELED.equals(this);
  }
}
