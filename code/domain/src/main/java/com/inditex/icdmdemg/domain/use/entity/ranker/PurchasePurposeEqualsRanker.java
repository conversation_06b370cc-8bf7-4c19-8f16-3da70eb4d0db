package com.inditex.icdmdemg.domain.use.entity.ranker;

import java.util.HashSet;
import java.util.List;

public interface PurchasePurposeEqualsRanker {

  static PurchasePurposeRankResult evaluate(final List<String> purchasePurposeParameterValues,
      final List<String> usePurchasePurposeConditionValues) {

    final var isComplied = new HashSet<>(purchasePurposeParameterValues)
        .equals(new HashSet<>(usePurchasePurposeConditionValues));
    final var numCoincidences = isComplied ? purchasePurposeParameterValues.size() : 0;
    return new PurchasePurposeRankResult(numCoincidences);

  }
}
