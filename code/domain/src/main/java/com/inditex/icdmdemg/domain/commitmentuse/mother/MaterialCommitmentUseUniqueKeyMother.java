package com.inditex.icdmdemg.domain.commitmentuse.mother;

import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseUniqueKey;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

public interface MaterialCommitmentUseUniqueKeyMother {

  static MaterialCommitmentUseUniqueKey with(
      final Integer materialReferenceId,
      final Integer budgetId,
      final Integer useId) {
    return new MaterialCommitmentUseUniqueKey(
        UuidMother.fromInteger(999).toString(),
        UuidMother.fromInteger(888).toString(),
        UuidMother.fromInteger(materialReferenceId).toString(),
        UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UuidMother.fromInteger(budgetId)),
        UuidMother.fromInteger(useId).toString());
  }

}
