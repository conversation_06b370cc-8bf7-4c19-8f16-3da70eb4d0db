package com.inditex.icdmdemg.provis.domain.use;

import java.util.List;
import java.util.UUID;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

public record UseDTO(Id id, Name name, Assignable assignable, UsePurchaseType usePurchaseType) {

  public List<UsePurchaseType> getPurchaseTypesList() {
    return switch (this.usePurchaseType()) {
      case NOMINATED -> List.of(UsePurchaseType.NOMINATED);
      case INNER -> List.of(UsePurchaseType.INNER);
      case NOMINATED_INNER -> List.of(UsePurchaseType.NOMINATED, UsePurchaseType.INNER);
    };
  }

  public record Id(UUID value) implements ValueObject<UUID> {

  }

  public record Name(String value) implements ValueObject<String> {

  }

  public record Assignable(Boolean value) implements ValueObject<Boolean> {

  }

  public enum UsePurchaseType implements ValueObject<String> {
    NOMINATED, INNER, NOMINATED_INNER;

    @Override
    public String value() {
      return this.name();
    }
  }

}
