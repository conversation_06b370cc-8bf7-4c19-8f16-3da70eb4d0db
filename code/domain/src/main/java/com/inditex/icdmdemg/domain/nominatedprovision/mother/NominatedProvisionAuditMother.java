package com.inditex.icdmdemg.domain.nominatedprovision.mother;

import java.time.OffsetDateTime;

import com.inditex.icdmdemg.domain.nominatedprovision.entity.NominatedProvisionAudit;
import com.inditex.icdmdemg.domain.nominatedprovision.entity.NominatedProvisionAudit.AuditBy;
import com.inditex.icdmdemg.domain.nominatedprovision.entity.NominatedProvisionAudit.AuditTimestamp;

public interface NominatedProvisionAuditMother {

  static Builder created() {
    final var createdBy = "createdBy";
    final var now = OffsetDateTime.now();

    return new Builder()
        .createdAt(new AuditTimestamp(now))
        .createdBy(new AuditBy(createdBy));
  }

  static Builder created(OffsetDateTime createdAt, String createdBy) {
    return new Builder()
        .createdAt(new AuditTimestamp(createdAt))
        .createdBy(new AuditBy(createdBy));
  }

  class Builder {

    private AuditTimestamp updatedAt;

    private AuditBy updatedBy;

    public Builder createdAt(AuditTimestamp updatedAt) {
      this.updatedAt = updatedAt;
      return this;
    }

    public Builder createdBy(AuditBy updatedBy) {
      this.updatedBy = updatedBy;
      return this;
    }

    public NominatedProvisionAudit build() {
      return new NominatedProvisionAudit(this.updatedAt, this.updatedBy);
    }
  }
}
