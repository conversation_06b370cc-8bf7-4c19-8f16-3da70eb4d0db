package com.inditex.icdmdemg.domain.use;

import java.time.Instant;
import java.util.Arrays;
import java.util.UUID;

import com.inditex.icdmdemg.domain.shared.AggregateDomainEvent;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;

@Getter
@Accessors(fluent = true)
@EqualsAndHashCode
public class UseUnifiedEvent implements AggregateDomainEvent<UUID> {

  private final Use use;

  private final EventType type;

  public UseUnifiedEvent(final Use use, final EventType type) {
    this.use = use;
    this.type = type;
  }

  @Override
  public UUID id() {
    return this.getAggregateId();
  }

  @Override
  public UUID getAggregateId() {
    return this.use.getId().value();
  }

  @Override
  public String user() {
    return this.use.audit.updatedBy();
  }

  @Override
  public Instant occurredOn() {
    return this.use.audit.updatedAt().toInstant();
  }

  @Override
  public String eventType() {
    return this.type.value;
  }

  public enum EventType {
    CREATED("UseCreated"),
    UPDATE_TRANSLATIONS("UseRenamed"),
    UPDATE_ASSIGNABLE("UseAssignableUpdated");

    public final String value;

    EventType(final String value) {
      this.value = value;
    }

    public static EventType fromValue(final String value) {
      return Arrays.stream(EventType.values())
          .filter(eventType -> eventType.value.equals(value))
          .findFirst()
          .orElseThrow(() -> new IllegalArgumentException("Unknown event type value: " + value));
    }
  }
}
