package com.inditex.icdmdemg.provis.domain.productuse;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

public record ProductUseSummaryDTO(
    ProductId productId,
    ReferenceId referenceId,
    UseId useId,
    BudgetCycle budgetCycle,
    LocalizationType localizationType,
    LocalizationId localizationId,
    Stock stock) {

  public enum LocalizationType implements ValueObject<String> {
    WAREHOUSE_DC,
    COMMITMENT_SUPPLIER;

    @Override
    public String value() {
      return this.name();
    }
  }

  public record ProductId(UUID value) implements ValueObject<UUID> {
  }

  public record ReferenceId(UUID value) implements ValueObject<UUID> {
  }

  public record UseId(UUID value) implements ValueObject<UUID> {

  }

  public record BudgetCycle(String value) implements ValueObject<String> {
  }

  public record LocalizationId(String value) implements ValueObject<String> {
  }

  public record Stock(BigDecimal value) implements ValueObject<BigDecimal> {
    public Stock(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }

  }

}
