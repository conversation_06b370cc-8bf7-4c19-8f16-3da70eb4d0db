package com.inditex.icdmdemg.domain.shared;

import java.util.Objects;

public record EntityAndActionResult<T>(T entity, ActionType actionType) {

  public boolean isActionCreate() {
    return Objects.equals(ActionType.CREATE, this.actionType());
  }

  public boolean isActionKeep() {
    return Objects.equals(ActionType.KEEP, this.actionType());
  }

  public boolean isActionUpdate() {
    return Objects.equals(ActionType.UPDATE, this.actionType());
  }

  public boolean isActionDelete() {
    return Objects.equals(ActionType.DELETE, this.actionType());
  }

  public boolean isActionCreateOrUpdate() {
    return this.isActionCreate() || this.isActionUpdate();
  }

  public boolean isActionCreateKeepOrUpdate() {
    return this.isActionCreate() || this.isActionKeep() || this.isActionUpdate();
  }

  public static <T> EntityAndActionResult<T> created(final T newEntity) {
    return new EntityAndActionResult<>(newEntity, ActionType.CREATE);
  }

  public static <T> EntityAndActionResult<T> kept(final T newEntity) {
    return new EntityAndActionResult<>(newEntity, ActionType.KEEP);
  }

  public static <T> EntityAndActionResult<T> deleted(final T deletedEntity) {
    return new EntityAndActionResult<>(deletedEntity, ActionType.DELETE);
  }

  public static <T> EntityAndActionResult<T> updated(final T updatedEntity) {
    return new EntityAndActionResult<>(updatedEntity, ActionType.UPDATE);
  }
}
