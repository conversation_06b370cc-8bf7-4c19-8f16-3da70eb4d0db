package com.inditex.icdmdemg.domain.product.entity;

import java.time.OffsetDateTime;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record ProductFamily(
    @lombok.NonNull CampaignId campaignId,
    @lombok.NonNull FamilyId familyId,
    @lombok.NonNull Timestamp createdAt,
    @lombok.NonNull Timestamp updatedAt) {

  public static ProductFamily create(
      @NonNull final CampaignId campaignId,
      @NonNull final FamilyId familyId,
      @NonNull final OffsetDateTime now) {
    return new ProductFamily(campaignId, familyId, Timestamp.of(now), Timestamp.of(now));
  }

  public record CampaignId(String value) implements ValueObject<String> {
  }

  public record FamilyId(String value) implements ValueObject<String> {
  }

  public record Timestamp(@NonNull OffsetDateTime value) implements ValueObject<OffsetDateTime> {
    public static Timestamp of(final OffsetDateTime date) {
      return new Timestamp(date);
    }
  }
}
