package com.inditex.icdmdemg.domain.product.entity;

import java.util.List;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record ProductFamilies(List<ProductFamily> value) implements ValueObject<List<ProductFamily>> {

  public @NonNull ProductFamilies replaceFamilies(final List<ProductFamily> families) {
    return new ProductFamilies(families);
  }
}
