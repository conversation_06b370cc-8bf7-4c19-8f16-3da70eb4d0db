package com.inditex.icdmdemg.domain.distributionnominated.mother;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.AlternativeReference;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.AlternativeReference.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributionStartDate;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.Id;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.BigDecimalMother;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

public interface DistributionNominatedLineMother {

  static Builder created() {
    return random()
        .distributedQuantity(new DistributedQuantity(BigDecimalMother.zero()))
        .distributionStartDate(null)
        .alternativeReference(randomAlternativeReference());
  }

  static Builder createdWithRandomAlternativeReference() {
    return random()
        .distributedQuantity(new DistributedQuantity(BigDecimalMother.zero()))
        .distributionStartDate(null)
        .alternativeReference(randomAlternativeReference());
  }

  static Builder createdWithSpecificAlternativeReference(
      final UUID distributionNominatedLineId,
      final UUID commitmentOrderId,
      final UUID commitmentOrderLineId,
      final UUID alternativeReferenceId,
      final BigDecimal alternativeRequestedQty,
      final BigDecimal alternativeTheoreticalQty,
      final OffsetDateTime rootCreationDate) {
    return random()
        .id(new Id(distributionNominatedLineId))
        .commitmentOrder(CommitmentOrder.builder()
            .id(new CommitmentOrder.Id(commitmentOrderId))
            .lineId(new LineId(commitmentOrderLineId))
            .supplierId(new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(901))))
            .build())
        .theoreticalQuantity(new TheoreticalQuantity(BigDecimal.ZERO))
        .requestedQuantity(new RequestedQuantity(BigDecimal.ZERO))
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO))
        .distributionStartDate(null)
        .alternativeReference(AlternativeReference.builder()
            .referenceId(new ReferenceId(alternativeReferenceId))
            .requestedQuantity(new AlternativeReference.RequestedQuantity(alternativeRequestedQty))
            .theoreticalQuantity(new AlternativeReference.TheoreticalQuantity(alternativeTheoreticalQty))
            .build())
        .audit(BasicAudit.builder()
            .createdAt(rootCreationDate)
            .updatedAt(rootCreationDate)
            .build());
  }

  static Builder createdWithoutAlternativeReferenceAndWithoutDistributed(
      final BigDecimal requestedQty,
      final BigDecimal theoreticalQty,
      final OffsetDateTime rootCreationDate

  ) {
    return random()
        .theoreticalQuantity(new TheoreticalQuantity(theoreticalQty))
        .requestedQuantity(new RequestedQuantity(requestedQty))
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO))
        .alternativeReference(null)
        .distributionStartDate(null)
        .audit(BasicAudit.builder()
            .createdAt(rootCreationDate)
            .updatedAt(rootCreationDate)
            .build());
  }

  static Builder withCommitmentOrderAndQuantityAndWithoutAlternativeReference(
      final UUID commitmentOrderId,
      final UUID commitmentOrderLineId,
      final BigDecimal requestedQuantity) {
    return random()
        .requestedQuantity(new RequestedQuantity(requestedQuantity))
        .commitmentOrder(CommitmentOrder.builder()
            .id(new CommitmentOrder.Id(commitmentOrderId))
            .lineId(new LineId(commitmentOrderLineId))
            .supplierId(new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(901))))
            .build())
        .alternativeReference(null);
  }

  static Builder withCommitmentOrderAndWithAlternativeReferenceWithQuantity(
      final UUID commitmentOrderId,
      final UUID commitmentOrderLineId,
      final BigDecimal lineRequestedQuantity,
      final BigDecimal alternativeLineRequestedQuantity) {
    return random()
        .requestedQuantity(new RequestedQuantity(lineRequestedQuantity))
        .commitmentOrder(CommitmentOrder.builder()
            .id(new CommitmentOrder.Id(commitmentOrderId))
            .lineId(new LineId(commitmentOrderLineId))
            .supplierId(new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(901))))
            .build())
        .alternativeReference(new AlternativeReference(
            new ReferenceId(UUID.randomUUID()),
            new AlternativeReference.RequestedQuantity(alternativeLineRequestedQuantity),
            new AlternativeReference.TheoreticalQuantity(BigDecimalMother.random())));
  }

  static Builder createdWithQuantityAndDate(final BigDecimal lineRequestedQuantity, final OffsetDateTime rootCreationDate) {
    return random()
        .requestedQuantity(new RequestedQuantity(lineRequestedQuantity))
        .distributedQuantity(new DistributedQuantity(BigDecimalMother.zero()))
        .audit(new BasicAudit(rootCreationDate, rootCreationDate))
        .distributionStartDate(null);
  }

  static Builder createdWithIdAndDistributionStartDate(final Id id,
      final DistributionStartDate rootCreationDate) {
    return random()
        .id(id)
        .distributionStartDate(rootCreationDate);
  }

  static Builder createdWithIdAndAlternativeReference(final Id id) {
    return random()
        .id(id)
        .alternativeReference(randomAlternativeReference());
  }

  static Builder distributed() {
    return random()
        .distributedQuantity(randomDistributedQuantity())
        .distributionStartDate(randomDistributionStartDate())
        .alternativeReference(randomAlternativeReference());
  }

  static Builder distributed(DistributedQuantity distributedQuantity) {
    return random()
        .distributedQuantity(distributedQuantity)
        .distributionStartDate(randomDistributionStartDate())
        .alternativeReference(randomAlternativeReference());
  }

  private static Builder random() {
    return new Builder()
        .id(randomId())
        .commitmentOrder(randomCommitmentOrder())
        .theoreticalQuantity(randomTheoreticalQuantity())
        .requestedQuantity(randomRequestedQuantity())
        .distributedQuantity(randomDistributedQuantity())
        .distributionStartDate(randomDistributionStartDate())
        .alternativeReference(null)
        .audit(randomAudit());
  }

  static Id randomId() {
    return new Id(UUID.randomUUID());
  }

  static CommitmentOrder randomCommitmentOrder() {
    return new CommitmentOrder(
        new CommitmentOrder.Id(UUID.randomUUID()),
        new LineId(UUID.randomUUID()),
        randomSupplierId());
  }

  static AlternativeReference randomAlternativeReference() {
    return new AlternativeReference(
        new ReferenceId(UUID.randomUUID()),
        new AlternativeReference.RequestedQuantity(BigDecimalMother.random()),
        new AlternativeReference.TheoreticalQuantity(BigDecimalMother.random()));
  }

  static TheoreticalQuantity randomTheoreticalQuantity() {
    return new TheoreticalQuantity(BigDecimalMother.random());
  }

  static RequestedQuantity randomRequestedQuantity() {
    return new RequestedQuantity(BigDecimalMother.random());
  }

  static DistributedQuantity randomDistributedQuantity() {
    return new DistributedQuantity(BigDecimalMother.random());
  }

  static DistributionStartDate randomDistributionStartDate() {
    return new DistributionStartDate(OffsetDateTimeMother.random());
  }

  static BasicAudit randomAudit() {
    final var offsetDateTime = OffsetDateTimeMother.randomBetween(1000, 10000);
    return new BasicAudit(offsetDateTime, offsetDateTime);
  }

  static SupplierId randomSupplierId() {
    return new SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
  }

  class Builder {

    Id id;

    CommitmentOrder commitmentOrder;

    TheoreticalQuantity theoreticalQuantity;

    RequestedQuantity requestedQuantity;

    DistributedQuantity distributedQuantity;

    DistributionStartDate distributionStartDate;

    AlternativeReference alternativeReference;

    BasicAudit audit;

    public Builder id(final Id id) {
      this.id = id;
      return this;
    }

    public Builder commitmentOrder(final CommitmentOrder commitmentOrder) {
      this.commitmentOrder = commitmentOrder;
      return this;
    }

    public Builder theoreticalQuantity(final TheoreticalQuantity theoreticalQuantity) {
      this.theoreticalQuantity = theoreticalQuantity;
      return this;
    }

    public Builder requestedQuantity(final RequestedQuantity requestedQuantity) {
      this.requestedQuantity = requestedQuantity;
      return this;
    }

    public Builder distributedQuantity(final DistributedQuantity distributedQuantity) {
      this.distributedQuantity = distributedQuantity;
      return this;
    }

    public Builder distributionStartDate(final DistributionStartDate distributionStartDate) {
      this.distributionStartDate = distributionStartDate;
      return this;
    }

    public Builder alternativeReference(final AlternativeReference alternativeReference) {
      this.alternativeReference = alternativeReference;
      return this;
    }

    public Builder audit(final BasicAudit audit) {
      this.audit = audit;
      return this;
    }

    public DistributionNominatedLine build() {
      return new DistributionNominatedLine(
          this.id,
          this.commitmentOrder,
          this.theoreticalQuantity,
          this.requestedQuantity,
          this.distributedQuantity,
          this.alternativeReference,
          this.distributionStartDate,
          this.audit);
    }
  }

}
