package com.inditex.icdmdemg.domain.order.mother;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.order.Order;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.OrderLines;
import com.inditex.icdmdemg.domain.order.entity.OrderStatusKey;
import com.inditex.icdmdemg.domain.order.entity.OrderSupplierId;
import com.inditex.icdmdemg.domain.order.entity.OrderVersion;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.RandomValue;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;

public interface OrderMother {

  static Builder random() {
    return new Builder().orderId(randomOrderId())
        .orderStatusKey(randomOrderStatusKey())
        .orderLines(randomOrderLines())
        .audit(randomAudit())
        .supplierId(randomSupplierId())
        .isPublished(randomIsPublished())
        .version(randomVersion());
  }

  private static OrderId randomOrderId() {
    return new OrderId(UUID.randomUUID().toString());
  }

  private static OrderStatusKey randomOrderStatusKey() {
    return RandomValue.randomEnum(OrderStatusKey.class);
  }

  private static OrderLines randomOrderLines() {
    return new OrderLines(List.of(OrderLineMother.random().build(),
        OrderLineMother.random().build(),
        OrderLineMother.random().build(),
        OrderLineMother.random().build(),
        OrderLineMother.random().build()));
  }

  static BasicAudit randomAudit() {
    final var now = OffsetDateTimeMother.random();
    return new BasicAudit(now, now);
  }

  private static OrderSupplierId randomSupplierId() {
    return new OrderSupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
  }

  private static Boolean randomIsPublished() {
    return RandomValue.randomBoolean();
  }

  private static OrderVersion randomVersion() {
    return OrderVersion.firstVersion();
  }

  static Order randomWith(final OrderStatusKey orderStatusKey, final UUID orderId,
      final boolean isPublished) {
    return new Order(
        new OrderId(orderId.toString()),
        orderStatusKey,
        new OrderLines(List.of()),
        BasicAudit.create(OffsetDateTime.now()),
        new OrderSupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID())),
        isPublished,
        OrderVersion.firstVersion());
  }

  class Builder {
    private OrderId orderId;

    private OrderStatusKey orderStatusKey;

    private OrderLines orderLines;

    private BasicAudit audit;

    private OrderSupplierId supplierId;

    private Boolean isPublished;

    private OrderVersion version;

    public Builder orderId(final OrderId orderId) {
      this.orderId = orderId;
      return this;
    }

    public Builder orderStatusKey(final OrderStatusKey orderStatusKey) {
      this.orderStatusKey = orderStatusKey;
      return this;
    }

    public Builder orderLines(final OrderLines orderLines) {
      this.orderLines = orderLines;
      return this;
    }

    public Builder audit(final BasicAudit audit) {
      this.audit = audit;
      return this;
    }

    public Builder supplierId(final OrderSupplierId supplierId) {
      this.supplierId = supplierId;
      return this;
    }

    public Builder isPublished(final Boolean isPublished) {
      this.isPublished = isPublished;
      return this;
    }

    public Builder version(final OrderVersion version) {
      this.version = version;
      return this;
    }

    public Order build() {
      return new Order(this.orderId, this.orderStatusKey, this.orderLines, this.audit,
          this.supplierId, this.isPublished, this.version);
    }

  }

  static Order closedWith(final UUID orderId, final boolean isPublished) {
    return randomWith(OrderStatusKey.CLOSED, orderId, isPublished);
  }

  static Order cancelledWith(final UUID orderId, final boolean isPublished) {
    return randomWith(OrderStatusKey.CANCELLED, orderId, isPublished);
  }

}
