package com.inditex.icdmdemg.domain.shipmentwarehouse;

import java.math.BigDecimal;
import java.util.Objects;

import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseId;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseInner;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseQuantity;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTimestamp;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTrackingCode;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseVersion;

import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.jspecify.annotations.NonNull;

@EqualsAndHashCode
@ToString(callSuper = true)
@Getter
@Builder(toBuilder = true)
public class ShipmentWarehouse {

  @NonNull
  private final ShipmentWarehouseId id;

  @NonNull
  private final ShipmentWarehouseTrackingCode trackingCode;

  @NonNull
  private final ShipmentWarehouseInner distributionInner;

  @NonNull
  private ShipmentWarehouseQuantity quantity;

  private ShipmentWarehouseTimestamp startDate;

  private ShipmentWarehouseTimestamp endDate;

  private ShipmentWarehouseTimestamp lastUpdateDate;

  @NonNull
  private final ShipmentWarehouseVersion version;

  @NonNull
  private final ShipmentWarehouseTimestamp createdAt;

  @NonNull
  private ShipmentWarehouseTimestamp updatedAt;

  public ShipmentWarehouse(@NonNull final ShipmentWarehouseId id,
      @NonNull final ShipmentWarehouseTrackingCode trackingCode,
      @NonNull final ShipmentWarehouseInner distributionInner,
      @NonNull final ShipmentWarehouseQuantity quantity,
      final ShipmentWarehouseTimestamp startDate,
      final ShipmentWarehouseTimestamp endDate,
      final ShipmentWarehouseTimestamp lastUpdateDate,
      @NonNull final ShipmentWarehouseVersion version,
      @NonNull final ShipmentWarehouseTimestamp createdAt,
      @NonNull final ShipmentWarehouseTimestamp updatedAt) {
    this.id = id;
    this.trackingCode = trackingCode;
    this.distributionInner = distributionInner;
    this.quantity = quantity;
    this.startDate = startDate;
    this.endDate = endDate;
    this.lastUpdateDate = lastUpdateDate;
    this.version = version;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;
  }

  public static ShipmentWarehouse create(
      @NonNull final ShipmentWarehouseId id,
      @NonNull final ShipmentWarehouseTrackingCode trackingCode,
      @NonNull final ShipmentWarehouseInner distributionInner,
      @NonNull final ShipmentWarehouseTimestamp now) {
    return new ShipmentWarehouse(
        id,
        trackingCode,
        distributionInner,
        new ShipmentWarehouseQuantity(BigDecimal.ZERO),
        null, null, null, ShipmentWarehouseVersion.firstVersion(), now, now);
  }

  public ShipmentWarehouse updateWithDelivery(final ShipmentWarehouseQuantity shipmentWarehouseQuantity,
      final ShipmentWarehouseTimestamp lastUpdateDate, final ShipmentWarehouseTimestamp now) {
    this.updatedAt = now;
    this.quantity = shipmentWarehouseQuantity;
    this.lastUpdateDate = lastUpdateDate;
    return this;
  }

  public ShipmentWarehouse startDistribution(final ShipmentWarehouseTimestamp deliveryStartDate, final ShipmentWarehouseTimestamp now) {
    if (Objects.isNull(this.startDate)) {
      this.startDate = deliveryStartDate;
      this.updatedAt = now;
    }
    return this;
  }

  public ShipmentWarehouse endShipment(final ShipmentWarehouseTimestamp endDate, final ShipmentWarehouseTimestamp now) {
    if (Objects.isNull(this.endDate)) {
      this.endDate = endDate;
      this.updatedAt = now;
    }
    return this;
  }

}
