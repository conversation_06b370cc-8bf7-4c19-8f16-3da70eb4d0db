package com.inditex.icdmdemg.domain.product.entity;

import java.time.OffsetDateTime;
import java.util.UUID;

import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record ProductTaxonomy(@NonNull ProductId productId, @NonNull ProductTaxonomyValue taxonomyValue, @NonNull BasicAudit audit) {

  public static ProductTaxonomy create(@NonNull UUID productId, @NonNull String taxonomy, @NonNull OffsetDateTime createdAt,
      @NonNull OffsetDateTime updatedAt) {
    return new ProductTaxonomy(new ProductId(productId), new ProductTaxonomyValue(taxonomy), new BasicAudit(createdAt, updatedAt));
  }

  public ProductTaxonomy updateTaxonomyValue(@NonNull String taxonomy, @NonNull OffsetDateTime updatedAt) {
    return new ProductTaxonomy(this.productId, new ProductTaxonomyValue(taxonomy), this.audit.update(updatedAt));
  }

  public boolean willBeUpdatedWith(@NonNull String newTaxonomy) {
    return !this.taxonomyValue().value().equals(newTaxonomy);
  }

  public record ProductId(@NonNull UUID value) implements ValueObject<UUID> {

  }

  public record ProductTaxonomyValue(@NonNull String value) implements ValueObject<String> {
  }

}
