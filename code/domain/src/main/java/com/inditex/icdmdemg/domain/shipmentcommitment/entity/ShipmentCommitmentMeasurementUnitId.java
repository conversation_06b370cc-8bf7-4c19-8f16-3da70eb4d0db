package com.inditex.icdmdemg.domain.shipmentcommitment.entity;

import java.util.UUID;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record ShipmentCommitmentMeasurementUnitId(@NonNull String value) implements ValueObject<String> {

  public static ShipmentCommitmentMeasurementUnitId of(final UUID value) {
    return new ShipmentCommitmentMeasurementUnitId(String.valueOf(value));
  }

}
