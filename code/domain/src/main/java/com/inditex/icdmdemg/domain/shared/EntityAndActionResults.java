package com.inditex.icdmdemg.domain.shared;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

public abstract class EntityAndActionResults {

  private EntityAndActionResults() {
  }

  @SafeVarargs
  public static <T> List<EntityAndActionResult<T>> from(final List<EntityAndActionResult<T>>... entitiesAndActions) {
    return Arrays.stream(entitiesAndActions).flatMap(Collection::stream).toList();
  }
}
