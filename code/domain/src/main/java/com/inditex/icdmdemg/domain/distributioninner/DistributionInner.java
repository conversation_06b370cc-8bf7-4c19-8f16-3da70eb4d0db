package com.inditex.icdmdemg.domain.distributioninner;

import static com.inditex.icdmdemg.shared.utils.BooleanOperator.or;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInnerUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLines;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.AggregateRoot;
import com.inditex.iopcmmnt.ddd.core.AggregateRootId;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.jspecify.annotations.NonNull;

@Getter
@ToString
@Accessors(fluent = true)
@EqualsAndHashCode(callSuper = true)
public class DistributionInner extends AggregateRoot<Id> {

  @NonNull
  ReferenceId referenceId;

  final @NonNull UseId useId;

  @NonNull
  BudgetCycle budgetCycle;

  @NonNull
  ReferenceProductId referenceProductId;

  final @NonNull ProductOrderId productOrderId;

  @NonNull
  ProductVariantGroupId productVariantGroupId;

  @NonNull
  ConsumptionFactor consumptionFactor;

  @NonNull
  TheoreticalQuantity theoreticalQuantity;

  @NonNull
  RequestedQuantity requestedQuantity;

  @NonNull
  DistributedQuantity distributedQuantity;

  @NonNull
  DistributionInnerStatus status;

  @NonNull
  DistributionInnerLines lines;

  @NonNull
  CompleteAudit audit;

  public DistributionInner(
      final <EMAIL> Id id,
      final @lombok.NonNull ReferenceId referenceId,
      final @lombok.NonNull UseId useId,
      final @lombok.NonNull BudgetCycle budgetCycle,
      final @lombok.NonNull ReferenceProductId referenceProductId,
      final @lombok.NonNull ProductOrderId productOrderId,
      final @lombok.NonNull ProductVariantGroupId productVariantGroupId,
      final @lombok.NonNull TheoreticalQuantity theoreticalQuantity,
      final @lombok.NonNull ConsumptionFactor consumptionFactor,
      final @lombok.NonNull RequestedQuantity requestedQuantity,
      final @lombok.NonNull DistributedQuantity distributedQuantity,
      final @lombok.NonNull DistributionInnerStatus status,
      final @lombok.NonNull DistributionInnerLines lines,
      final @lombok.NonNull CompleteAudit audit) {
    super(id);
    this.referenceId = referenceId;
    this.budgetCycle = budgetCycle;
    this.referenceProductId = referenceProductId;
    this.useId = useId;
    this.productVariantGroupId = productVariantGroupId;
    this.productOrderId = productOrderId;
    this.consumptionFactor = consumptionFactor;
    this.theoreticalQuantity = theoreticalQuantity;
    this.distributedQuantity = distributedQuantity;
    this.requestedQuantity = requestedQuantity;
    this.lines = lines;
    this.status = status;
    this.audit = audit;
  }

  public static DistributionInner create(
      final DistributionInner.@NonNull Id id,
      final DistributionInner.@NonNull ReferenceId referenceId,
      final DistributionInner.@NonNull UseId useId,
      final DistributionInner.@NonNull BudgetCycle budgetCycle,
      final DistributionInner.@NonNull ReferenceProductId referenceProductId,
      final DistributionInner.@NonNull ProductOrderId productOrderId,
      final DistributionInner.@NonNull ProductVariantGroupId productVariantGroupId,
      @NonNull final ProductOrderStatusInfo productOrderStatusInfo,
      final DistributionInner.@NonNull TheoreticalQuantity theoreticalQuantity,
      final DistributionInner.@NonNull ConsumptionFactor consumptionFactor,
      final DistributionInner.@NonNull RequestedQuantity requestedQuantity,
      @NonNull final DistributionInnerLines lines,
      @NonNull final OffsetDateTime occurredOn,
      @NonNull final String triggeredBy,
      final boolean sendToDistribution) {
    final var status = DistributionInnerStatus.of(productOrderStatusInfo, sendToDistribution);
    final var distributionInner = new DistributionInner(
        id,
        referenceId,
        useId,
        budgetCycle,
        referenceProductId,
        productOrderId,
        productVariantGroupId,
        theoreticalQuantity,
        consumptionFactor,
        requestedQuantity,
        new DistributedQuantity(BigDecimal.ZERO),
        status,
        lines,
        CompleteAudit.create(triggeredBy, occurredOn));
    if (status.isNonDistributable()) {
      distributionInner.eventRegistrar().register(EventType.CREATED_NON_DISTRIBUTABLE);
    } else {
      distributionInner.eventRegistrar().register(EventType.CREATED_PENDING);
    }
    return distributionInner;
  }

  public void update(
      final @NonNull ConsumptionFactor consumptionFactor,
      final @NonNull TheoreticalQuantity theoreticalQuantity,
      final @NonNull RequestedQuantity requestedQuantity,
      final @NonNull DistributionInnerLines lines,
      final @NonNull String updatedBy,
      final @NonNull OffsetDateTime updatedAt) {

    final var isRequestedQuantityModified = this.isRequestedQuantityModified(requestedQuantity);
    final boolean isModified = Stream.of(isRequestedQuantityModified,
        !Objects.equals(this.consumptionFactor, consumptionFactor),
        !Objects.equals(this.theoreticalQuantity, theoreticalQuantity),
        this.lines.willBeUpdatedLinesOnReplace(lines.value()))
        .reduce(false, or());
    if (isModified) {
      this.consumptionFactor = consumptionFactor;
      this.theoreticalQuantity = theoreticalQuantity;
      this.requestedQuantity = requestedQuantity;
      this.lines = this.lines.replaceLines(lines.value());
      this.status = this.status.update(this.status, this.lines.hasAnyLineDistributionStarted(), this.areAllLinesSent());
      this.audit = this.audit.update(updatedBy, updatedAt);
      final var eventType = this.evaluateEventType(isRequestedQuantityModified);
      this.eventRegistrar().register(eventType);
    }
  }

  private EventType evaluateEventType(final boolean isRequestedQuantityModified) {
    return isRequestedQuantityModified
        ? this.getEventTypeIfRequestedQuantityIsModified()
        : EventType.CORRECTED;
  }

  private EventType getEventTypeIfRequestedQuantityIsModified() {
    return this.status.isNonDistributable()
        ? EventType.UPDATED_NON_DISTRIBUTABLE
        : EventType.UPDATED_PENDING;
  }

  public DistributionInner registerEvent(final DistributionInnerUnifiedEvent.EventType eventType) {
    this.eventRegistrar().register(eventType);
    return this;
  }

  public DistributionInner updateProductVariantGroupId(
      @NonNull final UUID productVariantGroupId,
      @NonNull final OffsetDateTime occurredOn,
      @NonNull final String triggeredBy) {
    if (!Objects.equals(this.productVariantGroupId().value(), productVariantGroupId)) {
      this.audit = this.audit().update(triggeredBy, occurredOn);
      this.productVariantGroupId = new ProductVariantGroupId(productVariantGroupId);
      this.eventRegistrar().register(DistributionInnerUnifiedEvent.EventType.PRODUCT_UPDATED);
    }
    return this;
  }

  public DistributionInner updateReferencesId(
      @NonNull final ReferenceId referenceId,
      @NonNull final ReferenceProductId referenceProductId,
      @NonNull final OffsetDateTime occurredOn,
      @NonNull final String triggeredBy) {
    if (!Objects.equals(this.referenceId(), referenceId) || !Objects.equals(this.referenceProductId(), referenceProductId)) {
      this.audit = this.audit().update(triggeredBy, occurredOn);
      this.referenceId = referenceId;
      this.referenceProductId = referenceProductId;
      this.eventRegistrar().register(DistributionInnerUnifiedEvent.EventType.PRODUCT_UPDATED);
    }
    return this;
  }

  public boolean isBudgetCycleModified(final @NonNull BudgetCycle budgetCycle) {
    return !this.budgetCycle.equals(budgetCycle);
  }

  public DistributionInner updateBudgetCycle(final BudgetCycle newBudgetCycle, final OffsetDateTime occurredOn, final String triggeredBy) {
    if (this.isBudgetCycleModified(newBudgetCycle)) {
      this.audit = this.audit().update(triggeredBy, occurredOn);
      this.budgetCycle = newBudgetCycle;
      this.status = DistributionInnerStatus.CLOSED;
      this.eventRegistrar().register(DistributionInnerUnifiedEvent.EventType.CLOSED);

    }
    return this;
  }

  public boolean isRequestedQuantityModified(final @NonNull RequestedQuantity requestedQuantity) {
    return !this.requestedQuantity.equals(requestedQuantity);
  }

  public boolean isRequestedQuantityIncreased(final @NonNull RequestedQuantity requestedQuantity) {
    return this.requestedQuantity.compareTo(requestedQuantity) < 0;
  }

  public DistributionInner delete(@NonNull final OffsetDateTime deletedAt, @NonNull final String deletedBy) {
    if (this.status.isInProgress() || this.status.isCanceled() || this.status.isClosed() || this.status.isSent()) {
      throw new ErrorException(new BadRequest(
          String.format("Distribution inner cannot be deleted since its status is %s", this.status.value())));
    }
    this.audit = this.audit().delete(deletedBy, deletedAt);
    this.eventRegistrar().register(EventType.DELETED);
    return this;
  }

  protected EventRegistrar eventRegistrar() {
    return new EventRegistrar(this);
  }

  public void updateStatusToPending(final OffsetDateTime updatedAt, final String updatedBy) {
    this.audit = this.audit().update(updatedBy, updatedAt);
    this.status = DistributionInnerStatus.PENDING;
    this.eventRegistrar().register(EventType.UPDATED_PENDING);
  }

  public void close(final OffsetDateTime updatedAt, final String updatedBy) {
    if (this.status.isStatusDifferentFromPendingOrSentOrInProgress()) {
      throw new ErrorException(new BadRequest(
          String.format("Distribution inner cannot be closed since its status %s is different from PENDING, SENT or IN_PROGRESS",
              this.status().value())));
    }

    this.audit = this.audit().update(updatedBy, updatedAt);
    this.status = DistributionInnerStatus.CLOSED;
    this.eventRegistrar().register(EventType.CLOSED);
  }

  public record Id(UUID value) implements AggregateRootId<UUID> {
  }

  public record UseId(UUID value) implements ValueObject<UUID> {
  }

  public record ReferenceId(UUID value) implements ValueObject<UUID> {
  }

  public record BudgetCycle(String value) implements ValueObject<String> {
  }

  public record ProductVariantGroupId(UUID value) implements ValueObject<UUID> {
  }

  public record ProductOrderId(UUID value) implements ValueObject<UUID> {
  }

  public record ReferenceProductId(UUID value) implements ValueObject<UUID> {
  }

  public record RequestedQuantity(BigDecimal value) implements ValueObject<BigDecimal> {
    public RequestedQuantity(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }

    public int compareTo(final @NonNull RequestedQuantity compared) {
      return this.value.compareTo(compared.value);
    }

  }

  public record DistributedQuantity(BigDecimal value) implements ValueObject<BigDecimal> {
    public DistributedQuantity(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }

    public boolean isDistributed() {
      return this.value.compareTo(BigDecimal.ZERO) > 0;
    }
  }

  public DistributionInner updateStatusFromOrder(@NonNull final ProductOrderStatusInfo productOrderStatusInfo,
      @NonNull final OffsetDateTime occurredOn,
      @NonNull final String triggeredBy) {
    if (productOrderStatusInfo.isCancelledOrDeleted() && this.status().isNonDistributableOrPending()) {
      this.delete(occurredOn, triggeredBy);
      return this;
    }
    final var newStatus = this.status().update(productOrderStatusInfo, this.status());
    if (this.status().isDifferentStatus(newStatus)) {
      this.audit = this.audit().update(triggeredBy, occurredOn);
      this.status = newStatus;
      if (newStatus.isClosed()) {
        this.eventRegistrar().register(DistributionInnerUnifiedEvent.EventType.CLOSED);
      }
    }
    return this;
  }

  public DistributionInner updateDistributionInnerLineWithDistribution(
      final DistributionInnerLine.Id lineId,
      final DistributionInnerLine.TrackingCode trackingCode,
      final DistributionInnerLine.DistributedQuantity distributed,
      final DistributionInnerLine.DistributionStartDate startDate,
      final DistributionInnerLine.DistributionEndDate endDate,
      final String updatedBy,
      final OffsetDateTime now) {
    final var updatedLines = this.lines.updateLineDistribution(lineId, trackingCode, distributed, startDate, endDate, now);
    if (this.lines.willBeUpdatedLinesOnReplace(updatedLines)) {
      this.audit = this.audit.update(updatedBy, now);
      this.lines = this.lines.replaceLines(updatedLines);
      this.distributedQuantity = this.lines().totalDistributed();
      this.status = this.status.update(this.status, this.lines.hasAnyLineDistributionStarted(), this.areAllLinesSent());
      this.eventRegistrar().register(DistributionInnerUnifiedEvent.EventType.DISTRIBUTED);
    }
    return this;
  }

  public void revertClose(final String triggeredBy, final OffsetDateTime updatedAt) {
    if (!this.status.isClosed()) {
      throw new ErrorException(new BadRequest(
          String.format("No reversion allowed for current DI status %s", this.status.value())));
    }

    this.status = DistributionInnerStatus.ofRevertClose(this.areAllLinesSent(), this.lines().hasAnyLineDistributionStarted());
    this.audit = this.audit.update(triggeredBy, updatedAt);
    final var eventType = (this.status.isSent() || this.status.isInProgress()) ? EventType.DISTRIBUTED : EventType.UPDATED_PENDING;
    this.eventRegistrar().register(eventType);
  }

  private boolean areAllLinesSent() {
    return this.lines().value().stream().allMatch(DistributionInnerLine::isSent);
  }

  public record ConsumptionFactor(BigDecimal value) implements ValueObject<BigDecimal> {
    public ConsumptionFactor(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }
  }

  public record TheoreticalQuantity(BigDecimal value) implements ValueObject<BigDecimal> {
    public TheoreticalQuantity(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }
  }

  public record EventRegistrar(DistributionInner distributionInner) {
    public void register(final EventType event) {
      this.distributionInner.registerEvent(new DistributionInnerUnifiedEvent(this.distributionInner, event));
    }
  }

  public DistributionInner decreaseRequestedQuantity(
      final RequestedQuantity newRequestedQuantity,
      final OffsetDateTime updatedAt) {

    if (this.isNewRequestedSmallerThanTotalDistributed(newRequestedQuantity)) {
      throw new ErrorException(new BadRequest("Not enough requested quantity to decrease"));
    }

    var pendingDecrease = this.requestedQuantity().value().subtract(newRequestedQuantity.value());
    if (pendingDecrease.compareTo(BigDecimal.ZERO) == 0) {
      return this;
    }

    final var distributionInnerLinesList = this.getLinesOrderedToDecrease();

    for (final var line : distributionInnerLinesList) {
      if (pendingDecrease.compareTo(BigDecimal.ZERO) > 0) {
        final var maxToReduceRequestedInLine = line.requestedQuantity().value().subtract(line.distributedQuantity().value());
        final var quantityToReduceInLine = pendingDecrease.min(maxToReduceRequestedInLine);
        if (quantityToReduceInLine.compareTo(BigDecimal.ZERO) > 0) {
          final var updatedLine =
              line.decrementRequestedQuantity(new DistributionInnerLine.RequestedQuantity(quantityToReduceInLine), updatedAt);
          this.lines = this.lines().updateLine(updatedLine, this.requestedQuantity.value());
          pendingDecrease = pendingDecrease.subtract(quantityToReduceInLine);
        }
      }
    }

    return this;
  }

  private boolean isNewRequestedSmallerThanTotalDistributed(final RequestedQuantity newRequestedQuantity) {
    return newRequestedQuantity.value().compareTo(this.distributedQuantity().value()) < 0;
  }

  private List<DistributionInnerLine> getLinesOrderedToDecrease() {
    final var distributionInnerLinesList = new ArrayList<>(this.lines().value());

    distributionInnerLinesList.sort(Comparator.comparingInt((final DistributionInnerLine d) -> {
      if (d.distributionEndDate() != null) {
        return 0;
      } else if (d.distributionStartDate() != null) {
        return 2;
      } else {
        return 1;
      }
    }));

    return Collections.unmodifiableList(distributionInnerLinesList);
  }

  public record PendingAssignedQuantity(BigDecimal value) implements ValueObject<BigDecimal> {
    public PendingAssignedQuantity(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }

    public static PendingAssignedQuantity calculate(final RequestedQuantity requestedQuantity,
        final DistributedQuantity distributedQuantity,
        final DistributionInnerStatus distributionInnerStatus) {
      if (distributionInnerStatus.isSent() || distributionInnerStatus.isClosed()) {
        return new PendingAssignedQuantity(BigDecimal.ZERO);
      }
      return new PendingAssignedQuantity(requestedQuantity.value()
          .subtract(distributedQuantity.value()).max(BigDecimal.ZERO));
    }
  }

  public PendingAssignedQuantity pendingAssigned() {
    return PendingAssignedQuantity.calculate(this.requestedQuantity, this.distributedQuantity, this.status);
  }

}
