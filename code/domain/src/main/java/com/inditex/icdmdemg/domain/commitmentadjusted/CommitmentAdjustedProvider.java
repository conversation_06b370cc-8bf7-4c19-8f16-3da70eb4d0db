package com.inditex.icdmdemg.domain.commitmentadjusted;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseCollection;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.MigratedCommitmentConsumption;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.MigratedCommitmentConsumptionCollection;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.MigratedCommitmentConsumptionRepository;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.MigratedCommitmentConsumptionRepository.CommitmentInfo;

import lombok.RequiredArgsConstructor;
import org.jspecify.annotations.NonNull;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;
import org.springframework.data.domain.SliceImpl;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CommitmentAdjustedProvider {
  private final MigratedCommitmentConsumptionRepository migratedCommitmentConsumptionRepository;

  private final MaterialCommitmentUseRepository materialCommitmentUseRepository;

  public Slice<CommitmentAdjusted> findBySharedRawMaterialPaged(
      final MaterialCommitmentUseSharedRawMaterial sharedRawMaterial, final Pageable pageable) {
    return this.findByAnySharedRawMaterial(List.of(sharedRawMaterial), pageable);
  }

  public List<CommitmentAdjusted> findByAnySharedRawMaterial(
      final List<MaterialCommitmentUseSharedRawMaterial> sharedRawMaterialList) {
    return this.findByAnySharedRawMaterial(sharedRawMaterialList, Pageable.unpaged()).getContent();
  }

  private Slice<CommitmentAdjusted> findByAnySharedRawMaterial(
      final List<MaterialCommitmentUseSharedRawMaterial> sharedRawMaterialList, final Pageable pageable) {
    if (sharedRawMaterialList.isEmpty()) {
      return new SliceImpl<>(Collections.emptyList());
    }
    final var materialCommitmentSlice = this.materialCommitmentUseRepository.findByAnySharedRawMaterial(sharedRawMaterialList, pageable);
    final var materialCommitmentSliceContent = new MaterialCommitmentUseCollection(materialCommitmentSlice
        .getContent());
    final var commitmentsInfo = this.getCommitmentsInfo(materialCommitmentSliceContent);
    final var migratedCommitmentConsumptionCollection =
        this.migratedCommitmentConsumptionRepository.findByCommitmentsInfo(commitmentsInfo);
    final var consumptionMapByOrderInfo = this.groupCommitmentConsumptionByOrderInfo(migratedCommitmentConsumptionCollection);

    final var commitmentsAdjusted = materialCommitmentSliceContent.assignable().stream()
        .map(materialCommitmentUse -> {
          final var orderInfo = CommonMaterialCommitmentOrderInfo.of(materialCommitmentUse);
          final var materialCommitmentUseQuantity = materialCommitmentUse.getQuantity().value();
          final var consumptionQuantity = consumptionMapByOrderInfo.getOrDefault(orderInfo, BigDecimal.ZERO);
          final var updatedQuantity = materialCommitmentUseQuantity.subtract(consumptionQuantity);
          return CommitmentAdjusted.of(materialCommitmentUse, updatedQuantity.max(BigDecimal.ZERO));
        }).toList();
    return new SliceImpl<>(commitmentsAdjusted, pageable, materialCommitmentSlice.hasNext());
  }

  private List<CommitmentInfo> getCommitmentsInfo(
      final MaterialCommitmentUseCollection materialCommitmentUseCollection) {
    return materialCommitmentUseCollection.materialCommitmentUses().stream()
        .map(materialCommitmentUse -> CommitmentInfo
            .of(materialCommitmentUse.getOrderLine().orderId().value(),
                materialCommitmentUse.getOrderLine().orderLineId().value(),
                materialCommitmentUse.getMaterialReferenceId().value()))
        .toList();
  }

  private Map<CommonMaterialCommitmentOrderInfo, @NonNull BigDecimal> groupCommitmentConsumptionByOrderInfo(
      final MigratedCommitmentConsumptionCollection migratedCommitmentConsumptionCollection) {
    return migratedCommitmentConsumptionCollection.migratedCommitmentConsumptions().stream()
        .collect(Collectors.toMap(
            CommonMaterialCommitmentOrderInfo::of,
            migratedCommitmentConsumption -> migratedCommitmentConsumption.getQuantity().value(),
            BigDecimal::add));
  }

  private record CommonMaterialCommitmentOrderInfo(String orderId, String orderLineId, String referenceId) {

    static CommonMaterialCommitmentOrderInfo of(final MigratedCommitmentConsumption migratedCommitmentConsumption) {
      return new CommonMaterialCommitmentOrderInfo(
          migratedCommitmentConsumption.getOrderLine().orderId().value(),
          migratedCommitmentConsumption.getOrderLine().orderLineId().value(),
          migratedCommitmentConsumption.getReferenceId().value());
    }

    static CommonMaterialCommitmentOrderInfo of(final MaterialCommitmentUse materialCommitmentUse) {
      return new CommonMaterialCommitmentOrderInfo(materialCommitmentUse.getOrderLine().orderId().value(),
          materialCommitmentUse.getOrderLine().orderLineId().value(),
          materialCommitmentUse.getMaterialReferenceId().value());
    }

  }
}
