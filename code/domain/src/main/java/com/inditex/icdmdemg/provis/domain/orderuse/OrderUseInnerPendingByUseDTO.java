package com.inditex.icdmdemg.provis.domain.orderuse;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record OrderUseInnerPendingByUseDTO(
    UseId useId,
    Pending pending) {

  public record UseId(UUID value) implements ValueObject<UUID> {

  }

  public record Pending(BigDecimal value) implements ValueObject<BigDecimal> {

    public Pending(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }
  }
}
