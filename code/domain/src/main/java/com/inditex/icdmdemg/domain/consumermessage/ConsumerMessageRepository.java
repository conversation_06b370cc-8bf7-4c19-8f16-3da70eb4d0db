package com.inditex.icdmdemg.domain.consumermessage;

import java.util.Optional;

import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageId;

import org.jspecify.annotations.NullMarked;

@NullMarked
public interface ConsumerMessageRepository {

  Optional<ConsumerMessage> findById(ConsumerMessageId message);

  void create(ConsumerMessage message) throws DuplicatedMessageException;

}
