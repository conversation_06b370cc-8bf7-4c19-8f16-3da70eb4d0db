package com.inditex.icdmdemg.domain.distributionnominated;

import java.time.Instant;
import java.util.Arrays;
import java.util.UUID;

import com.inditex.icdmdemg.domain.shared.AggregateDomainEvent;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;

@Getter
@Accessors(fluent = true)
@EqualsAndHashCode
public class DistributionNominatedUnifiedEvent implements AggregateDomainEvent<UUID> {

  private final DistributionNominated distributionNominated;

  private final EventType type;

  public DistributionNominatedUnifiedEvent(final DistributionNominated distributionNominated, final EventType type) {
    this.distributionNominated = distributionNominated;
    this.type = type;
  }

  @Override
  public UUID id() {
    return this.getAggregateId();
  }

  @Override
  public UUID getAggregateId() {
    return this.distributionNominated.getId().value();
  }

  @Override
  public String user() {
    return this.distributionNominated.audit.updatedBy();
  }

  @Override
  public Instant occurredOn() {
    return this.distributionNominated.audit.updatedAt().toInstant();
  }

  @Override
  public String eventType() {
    return this.type.value;
  }

  public enum EventType {
    CREATED("DistributionRequested"),
    UPDATED("DistributionRequested"),
    CORRECTED("DistributionCorrected"),
    DISTRIBUTED("DistributionDistributed"),
    DELETED("DistributionDeleted"),
    PRODUCT_UPDATED("DistributionProductUpdated"),
    DISTRIBUTION_RECONCILED("DistributionReconciled"),
    CLOSED("DistributionClosed");

    public final String value;

    EventType(final String value) {
      this.value = value;
    }

    public static EventType fromValue(final String value) {
      return Arrays.stream(EventType.values())
          .filter(eventType -> eventType.value.equals(value))
          .findFirst()
          .orElseThrow(() -> new IllegalArgumentException("Unknown event type value: " + value));
    }
  }

}
