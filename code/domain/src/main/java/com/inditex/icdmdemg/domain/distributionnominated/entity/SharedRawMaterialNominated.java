package com.inditex.icdmdemg.domain.distributionnominated.entity;

import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.UseId;

import org.jspecify.annotations.NonNull;

public record SharedRawMaterialNominated(
    DistributionNominated.@NonNull ReferenceId referenceId,
    DistributionNominated.@NonNull UseId useId,
    DistributionNominated.@NonNull BudgetCycle budgetCycle) {

  public SharedRawMaterialNominated(
      final UUID referenceId,
      final UUID useId,
      final String budgetCycle) {
    this(new ReferenceId(referenceId), new UseId(useId), new BudgetCycle(budgetCycle));
  }

  public SharedRawMaterialNominated doBudgetCycleChange(final BudgetCycle newBudgetCycle) {
    return new SharedRawMaterialNominated(
        this.referenceId().value(),
        this.useId().value(),
        newBudgetCycle.value());
  }

}
