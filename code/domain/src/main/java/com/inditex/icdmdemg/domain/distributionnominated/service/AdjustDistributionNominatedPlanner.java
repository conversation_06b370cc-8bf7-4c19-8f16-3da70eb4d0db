package com.inditex.icdmdemg.domain.distributionnominated.service;

import static java.util.Comparator.comparing;

import java.util.List;

public class AdjustDistributionNominatedPlanner extends DistributionNominatedPlanner {

  public AdjustDistributionNominatedPlanner(List<Request> requests, List<Line> lines, List<Commitment> commitments,
      List<Line> initialFilteredLines,
      DistributionNominatedPlannerType plannerType) {
    super(requests, lines, commitments, initialFilteredLines, plannerType);
  }

  @Override
  protected void execute() {
    this.unsatisfyRequests();
    this.satisfyRequests();
  }

  private void unsatisfyRequests() {
    final var decreasableLines = this.lines.values().stream().sorted(this.deallocationOrder()).toList();
    for (final var decreasableLine : decreasableLines) {
      final var compositeCommitmentId = decreasableLine.compositeCommitmentId();
      final var commitmentQuantity = this.lookUpCommitment(compositeCommitmentId).map(Commitment::quantity).orElse(ZERO);
      final var commitmentUsed = this.commitmentsAdjusted().get(compositeCommitmentId);
      final var overUsed = commitmentUsed.subtract(commitmentQuantity).max(ZERO);
      if (overUsed.compareTo(ZERO) > 0 && decreasableLine.isNotDistributed()) {
        final var adjustment = overUsed.min(decreasableLine.requested());
        this.adjustments.subtract(decreasableLine.compositeId(), adjustment);
      }
    }
  }

  private void satisfyRequests() {
    final var sortedRequests = this.requests.values().stream().sorted(comparing(Request::creationDate)).toList();
    for (final var request : sortedRequests) {
      this.allocate(request.id(), request.requested());
    }
  }
}
