package com.inditex.icdmdemg.domain.shipmentcommitment;

import java.util.List;
import java.util.Optional;

import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentDistributionNominatedLineId;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentId;

import org.jspecify.annotations.NullMarked;

@NullMarked
public interface ShipmentCommitmentRepository {

  ShipmentCommitment save(ShipmentCommitment shipmentCommitment);

  Optional<ShipmentCommitment> findById(ShipmentCommitmentId id);

  List<ShipmentCommitment> findByDistributionNominatedLineId(ShipmentCommitmentDistributionNominatedLineId id);

  void delete(ShipmentCommitmentId id);
}
