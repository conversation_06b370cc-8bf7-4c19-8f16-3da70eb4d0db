package com.inditex.icdmdemg.domain.order.mother;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.order.entity.OrderBudgetId;
import com.inditex.icdmdemg.domain.order.entity.OrderLine;
import com.inditex.icdmdemg.domain.order.entity.OrderLineId;
import com.inditex.icdmdemg.domain.order.entity.OrderLineMeasuringUnitsId;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetails;
import com.inditex.icdmdemg.domain.order.entity.OrderLineServiceDate;

public interface OrderLineMother {

  static Builder random() {
    return new Builder().id(randomOrderLineId())
        .orderLineServiceDate(randomOrderLineServiceDate())
        .orderLineMeasuringUnitsId(randomOrderLineMeasuringUnitsId())
        .budgetId(randomOrderBudgetId())
        .orderLineQuantityDetails(randomOrderLineQuantityDetails());
  }

  private static OrderLineId randomOrderLineId() {
    return new OrderLineId(UUID.randomUUID().toString());
  }

  private static OrderLineServiceDate randomOrderLineServiceDate() {
    return new OrderLineServiceDate(OffsetDateTime.now());
  }

  private static OrderLineMeasuringUnitsId randomOrderLineMeasuringUnitsId() {
    return new OrderLineMeasuringUnitsId(UUID.randomUUID().toString());
  }

  private static OrderBudgetId randomOrderBudgetId() {
    return new OrderBudgetId(UUID.randomUUID().toString());
  }

  private static OrderLineQuantityDetails randomOrderLineQuantityDetails() {
    return new OrderLineQuantityDetails(List.of(OrderLineQuantityDetailMother.random().build()));
  }

  class Builder {
    OrderLineId id;

    OrderLineServiceDate orderLineServiceDate;

    OrderLineMeasuringUnitsId orderLineMeasuringUnitsId;

    OrderBudgetId budgetId;

    OrderLineQuantityDetails orderLineQuantityDetails;

    public Builder id(final OrderLineId id) {
      this.id = id;
      return this;
    }

    public Builder orderLineServiceDate(final OrderLineServiceDate orderLineServiceDate) {
      this.orderLineServiceDate = orderLineServiceDate;
      return this;
    }

    public Builder orderLineMeasuringUnitsId(final OrderLineMeasuringUnitsId orderLineMeasuringUnitsId) {
      this.orderLineMeasuringUnitsId = orderLineMeasuringUnitsId;
      return this;
    }

    public Builder budgetId(final OrderBudgetId budgetId) {
      this.budgetId = budgetId;
      return this;
    }

    public Builder orderLineQuantityDetails(final OrderLineQuantityDetails orderLineQuantityDetails) {
      this.orderLineQuantityDetails = orderLineQuantityDetails;
      return this;
    }

    public OrderLine build() {
      return new OrderLine(this.id, this.orderLineServiceDate, this.orderLineMeasuringUnitsId, this.budgetId,
          this.orderLineQuantityDetails);
    }

  }
}
