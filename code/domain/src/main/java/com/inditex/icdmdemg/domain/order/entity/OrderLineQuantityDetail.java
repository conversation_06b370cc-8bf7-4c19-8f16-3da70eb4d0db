package com.inditex.icdmdemg.domain.order.entity;

import java.util.Objects;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record OrderLineQuantityDetail(
    @NonNull OrderLineQuantity orderLineQuantity,
    @NonNull OrderLineQuantityDetailReferenceId referenceId) implements ValueObject<OrderLineQuantityDetail> {

  public OrderLineQuantityDetail value() {
    return this;
  }

  public boolean willBeUpdatedWith(final OrderLineQuantityDetail newOne) {
    return !Objects.equals(this.orderLineQuantity, newOne.orderLineQuantity);
  }
}
