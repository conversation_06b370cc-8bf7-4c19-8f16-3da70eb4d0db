package com.inditex.icdmdemg.domain.distributionnominated.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.domain.commitmentuse.entity.PartialQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Line.CompositeLineId;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan.Preselected;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;

public class PreselectedDistributionNominatedPlanner extends DistributionNominatedPlanner {

  private final Preselected plan;

  public PreselectedDistributionNominatedPlanner(final List<Request> requests, final List<Line> lines, final List<Commitment> commitments,
      final List<Line> initialFilteredLines,
      final Preselected plan,
      final DistributionNominatedPlannerType plannerType) {
    super(requests, lines, commitments, initialFilteredLines, plannerType);
    this.plan = plan;
  }

  @Override
  protected void execute() {
    switch (this.plan) {
      case Preselected.Create(final var id, final var requestedQuantity, final var lines) ->
        this.newRequestPreselected(id, requestedQuantity, lines);
      case Preselected.Modify(final var id, final var requestedQuantity, final var lines) ->
        this.modifyRequestPreselected(id, requestedQuantity, lines);
      default -> throw new ErrorException(new BadRequest("Invalid plan"));
    }
  }

  public void newRequestPreselected(final Id id, final RequestedQuantity requestedQuantity, final List<Line> lines) {
    this.lookUpRequest(id).ifPresent(request -> {
      throw new ErrorException(new BadRequest("Request already exists"));
    });
    this.requestedQuantityIsValid(requestedQuantity, lines);

    for (final var line : lines) {
      final var requestId = line.rootId();
      final var commitmentOrder = line.commitmentOrder();
      final var available = this.availableCommitmentQuantityForAllocate(requestId, commitmentOrder);

      if (line.requested().compareTo(available) > 0) {
        throw new ErrorException(new BadRequest(
            String.format("Insufficient or invalid commitment with orderId %s and lineId %s. Only quantity %s can be allocated",
                commitmentOrder.id().value(), commitmentOrder.lineId().value(), available)));
      }

      this.adjustments.add(line.compositeId(), line.requested());
    }
  }

  public void modifyRequestPreselected(final Id id, final RequestedQuantity requestedQuantity, final List<Line> lines) {
    this.lookUpRequest(id).orElseThrow(() -> new ErrorException(new BadRequest("Distribution Nominated Not Found")));
    this.requestedQuantityIsValid(requestedQuantity, lines);

    final var existingLines = this.lookUpRequestLines(id);
    final var existingLineIds = existingLines.stream().map(Line::compositeId).collect(Collectors.toSet());
    final var actualLines = new ArrayList<>(lines);
    final var actualLineIds = actualLines.stream().map(Line::compositeId).collect(Collectors.toSet());
    if (!actualLineIds.containsAll(existingLineIds)) {
      existingLineIds.removeAll(actualLineIds);
      final var missingLines = existingLines.stream()
          .filter(line -> existingLineIds.contains(line.compositeId()))
          .map(this::keepExistingLineWithoutRequested)
          .toList();
      actualLines.addAll(missingLines);
    }

    for (final var line : actualLines) {
      final var requestId = line.rootId();
      final var commitmentOrder = line.commitmentOrder();
      final var actualLineRequested = line.requested();
      final var existingLine = this.lookUpLine(line.compositeId());
      final var existingLineRequested = existingLine.map(Line::requested).orElse(ZERO);

      if (actualLineRequested.compareTo(existingLineRequested) < 0) {
        if (existingLine.map(Line::isDistributed).orElse(false)) {
          throw new ErrorException(new BadRequest("Cannot decrease requested quantity of a distributed line"));
        }
        final var deallocation = existingLineRequested.subtract(actualLineRequested).max(ZERO);
        this.adjustments.subtract(line.compositeId(), deallocation);
      } else if (actualLineRequested.compareTo(existingLineRequested) > 0) {
        final var increase = actualLineRequested.subtract(existingLineRequested).max(ZERO);
        final var available = this.availableCommitmentQuantityForAllocate(requestId, commitmentOrder);
        if (increase.compareTo(available) > 0) {
          throw new ErrorException(new BadRequest(
              String.format("Insufficient or invalid commitment with orderId %s and lineId %s. Only quantity %s can be allocated",
                  commitmentOrder.id().value(), commitmentOrder.lineId().value(),
                  available.add(existingLineRequested))));
        }
        this.adjustments.add(line.compositeId(), increase);
      }
    }
  }

  private void requestedQuantityIsValid(final RequestedQuantity requestedQuantity, final List<Line> lines) {
    final var requested = requestedQuantity.value();
    if (requested.compareTo(ZERO) <= 0) {
      throw new ErrorException(new BadRequest("Requested should be greater than zero"));
    }
    final var requestedInLines = lines.stream().map(Line::requested).reduce(BigDecimal::add).orElse(ZERO);
    if (requestedInLines.compareTo(requested) != 0) {
      throw new ErrorException(new BadRequest("Requested in lines should be equal to requested quantity"));
    }
  }

  private BigDecimal availableCommitmentQuantityForAllocate(final Id requestId, final CommitmentOrder commitmentOrder) {
    return this.availableCommitmentsForAllocateAdjusted(requestId)
        .stream().filter(pq -> pq.entity().commitmentOrder().equals(commitmentOrder))
        .findFirst()
        .map(PartialQuantity::partialQuantity)
        .orElse(ZERO);
  }

  private Line keepExistingLineWithoutRequested(final Line line) {
    return new Line(line.rootId(), line.commitmentOrder(), line.referenceId(), line.targetReferenceId(), BigDecimal.ZERO,
        line.distributed(), line.creationDate());
  }

  private Optional<Line> lookUpLine(final CompositeLineId compositeLineId) {
    return Optional.ofNullable(this.lines.get(compositeLineId));
  }

}
