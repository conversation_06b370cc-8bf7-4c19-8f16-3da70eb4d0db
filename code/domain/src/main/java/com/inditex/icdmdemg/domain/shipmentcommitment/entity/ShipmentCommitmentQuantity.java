package com.inditex.icdmdemg.domain.shipmentcommitment.entity;

import java.math.BigDecimal;
import java.util.Objects;

import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record ShipmentCommitmentQuantity(@NonNull BigDecimal value) implements ValueObject<BigDecimal> {

  public ShipmentCommitmentQuantity(final BigDecimal value) {
    this.value = Objects.isNull(value) ? BigDecimal.ZERO : NumericUtils.roundUpScale2(value);
  }

}
