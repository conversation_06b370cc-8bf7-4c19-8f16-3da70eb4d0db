package com.inditex.icdmdemg.domain.shipmentcommitment.mother;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.util.UUID;

import com.inditex.icdmdemg.domain.shipmentcommitment.ShipmentCommitment;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentAudit;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentDistributedQuantity;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentDistributionNominatedLineId;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentId;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentMeasurementUnitId;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentQuantity;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentTimeStamp;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.audit.ShipmentCommitmentCreatedAt;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.audit.ShipmentCommitmentUpdatedAt;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.audit.ShipmentCommitmentVersion;
import com.inditex.icdmdemg.shared.utils.RandomValue;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

import org.jspecify.annotations.NonNull;

public interface ShipmentCommitmentMother {

  static ShipmentCommitment savedShipmentCommitment() {
    return new ShipmentCommitment(
        new ShipmentCommitmentId("3ceabf62-bcde-43ea-afc7-1c3e3f318bbd"),
        new ShipmentCommitmentDistributionNominatedLineId("d1ebe98b-07f8-487b-a2d0-d07fd3d24a2e"),
        new ShipmentCommitmentTimeStamp(OffsetDateTime.of(2023, 11, 22, 17, 35, 40, 0, ZoneOffset.UTC)),
        new ShipmentCommitmentDistributedQuantity(new ShipmentCommitmentQuantity(BigDecimal.valueOf(12.34)),
            new ShipmentCommitmentMeasurementUnitId("dde76539-e10e-4558-822f-f2e4f5093529")),
        new ShipmentCommitmentAudit(new ShipmentCommitmentCreatedAt(OffsetDateTime.of(2023, 11, 23, 17, 35, 40, 0, ZoneOffset.UTC)),
            new ShipmentCommitmentUpdatedAt(OffsetDateTime.of(2023, 11, 24, 17, 35, 40, 0, ZoneOffset.UTC)),
            new ShipmentCommitmentVersion((short) 0)));
  }

  static ShipmentCommitment savedShipmentCommitmentWithoutMeasurementUnitId() {
    return new ShipmentCommitment(
        new ShipmentCommitmentId("34657dab-e951-4e40-8676-2f55605c2bc1"),
        new ShipmentCommitmentDistributionNominatedLineId("d1ebe98b-07f8-487b-a2d0-d07fd3d24a2b"),
        new ShipmentCommitmentTimeStamp(OffsetDateTime.of(2023, 11, 22, 17, 35, 40, 0, ZoneOffset.UTC)),
        new ShipmentCommitmentDistributedQuantity(new ShipmentCommitmentQuantity(BigDecimal.valueOf(56.78)), null),
        new ShipmentCommitmentAudit(new ShipmentCommitmentCreatedAt(OffsetDateTime.of(2023, 11, 23, 17, 35, 40, 0, ZoneOffset.UTC)),
            new ShipmentCommitmentUpdatedAt(OffsetDateTime.of(2023, 11, 24, 17, 35, 40, 0, ZoneOffset.UTC)),
            new ShipmentCommitmentVersion((short) 3)));
  }

  static ShipmentCommitment updated(final ShipmentCommitment from, final OffsetDateTime now) {
    return new ShipmentCommitment(
        from.getId(),
        from.getDistributionNominatedLineId(),
        from.getSentDate(),
        new ShipmentCommitmentDistributedQuantity(
            new ShipmentCommitmentQuantity(RandomValue.randomPositiveBigDecimal()),
            acceptNullElseMap(UUID.randomUUID(), ShipmentCommitmentMeasurementUnitId::of)),
        new ShipmentCommitmentAudit(from.getAudit().createdAt(), new ShipmentCommitmentUpdatedAt(now),
            from.getAudit().version()));
  }

  static ShipmentCommitment created(final OffsetDateTime now) {
    return with(UUID.randomUUID(), UUID.randomUUID(), now, now, RandomValue.randomPositiveBigDecimal(), UUID.randomUUID());
  }

  static ShipmentCommitment random() {
    final var now = OffsetDateTime.now();
    return with(UUID.randomUUID(), UUID.randomUUID(), now, now, RandomValue.randomPositiveBigDecimal(),
        UUID.randomUUID());
  }

  static ShipmentCommitment with(final UUID shipmentId, final UUID distributionNominatedLineId, final OffsetDateTime startTimeStamp,
      final OffsetDateTime auditTimeStamp, final BigDecimal quantity, final UUID measurementUnitId) {
    return new ShipmentCommitment(
        ShipmentCommitmentId.of(shipmentId),
        ShipmentCommitmentDistributionNominatedLineId.of(distributionNominatedLineId),
        new ShipmentCommitmentTimeStamp(startTimeStamp),
        new ShipmentCommitmentDistributedQuantity(new ShipmentCommitmentQuantity(quantity),
            acceptNullElseMap(measurementUnitId, ShipmentCommitmentMeasurementUnitId::of)),
        new ShipmentCommitmentAudit(new ShipmentCommitmentCreatedAt(auditTimeStamp), new ShipmentCommitmentUpdatedAt(auditTimeStamp),
            new ShipmentCommitmentVersion((short) 0)));
  }

  static ShipmentCommitment with(
      final int shipmentId,
      final int distributionNominatedLineId,
      @NonNull final OffsetDateTime sendDate,
      final BigDecimal quantity) {
    return with(
        UuidMother.fromInteger(shipmentId),
        UuidMother.fromInteger(distributionNominatedLineId),
        sendDate,
        OffsetDateTime.now(),
        quantity,
        UUID.randomUUID());
  }

}
