package com.inditex.icdmdemg.domain.use.mother;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAuditMother;
import com.inditex.icdmdemg.domain.use.Use;
import com.inditex.icdmdemg.domain.use.Use.Customer;
import com.inditex.icdmdemg.domain.use.Use.Id;
import com.inditex.icdmdemg.domain.use.Use.Taxonomy;
import com.inditex.icdmdemg.domain.use.entity.AssignableType;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeCondition;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionName;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues;
import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues.ConditionValue;
import com.inditex.icdmdemg.domain.use.entity.PurchaseType;
import com.inditex.icdmdemg.domain.use.entity.UseConditions;
import com.inditex.icdmdemg.domain.use.entity.UseName;
import com.inditex.icdmdemg.domain.use.entity.UseName.Lang;
import com.inditex.icdmdemg.domain.use.entity.UseName.Name;
import com.inditex.icdmdemg.domain.use.entity.UseNames;
import com.inditex.icdmdemg.domain.use.entity.UsePurchasePurposeCondition;
import com.inditex.icdmdemg.shared.utils.RandomValue;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

public interface UseMother {

  static Id generateId() {
    return new Id(UuidMother.fromInteger(1));
  }

  static AssignableType generateAssignableType() {
    return AssignableType.NOMINATED;
  }

  static Taxonomy generateTaxonomy() {
    return new Taxonomy("FABRIC");
  }

  static Customer generateCustomer() {
    return new Customer("urn:BUYER:123e4567-e89b-12d3-a456-************");
  }

  static PurchaseType generatePurchaseType() {
    return PurchaseType.NOMINATED;
  }

  static PurchaseType generatePurchaseTypes() {
    return PurchaseType.NOMINATED_INNER;
  }

  static UseNames generateTreeUseNamesWithRegion(final OffsetDateTime now) {
    return new UseNames(List.of(new UseName(new Name("descripcion uso"),
        new Lang("es"),
        new BasicAudit(now, now)),
        new UseName(
            new Name("descripcio do usinho"),
            new Lang("pt-PT"),
            new BasicAudit(now, now)),
        new UseName(
            new Name("use description"),
            new Lang("en"),
            new BasicAudit(now, now))));
  }

  static List<UsePurchasePurposeCondition> generateCondition(final OffsetDateTime now) {
    return List.of(new UsePurchasePurposeCondition(
        PurchasePurposeCondition.EQUALS,
        PurchasePurposeConditionName.BUYERGROUP,
        new PurchasePurposeConditionValues(List.of(new ConditionValue("urn:BUYERGROUP:123e4567-e89b-12d3-a456-************"))),
        new BasicAudit(now, now)));
  }

  static UseConditions generateConditions(final OffsetDateTime now) {
    return new UseConditions(List.of(new UsePurchasePurposeCondition(
        PurchasePurposeCondition.ALL_IN,
        PurchasePurposeConditionName.BUYERSUBGROUP,
        new PurchasePurposeConditionValues(List.of(
            new ConditionValue("urn:BUYERSUBGROUP:123e4567-e89b-12d3-a456-************"),
            new ConditionValue("urn:BUYERSUBGROUP:542c8136-b788b-12d3-a456-526655444159"))),
        new BasicAudit(now, now)),
        new UsePurchasePurposeCondition(
            PurchasePurposeCondition.EQUALS,
            PurchasePurposeConditionName.BUYERGROUP,
            new PurchasePurposeConditionValues(
                List.of(new ConditionValue("urn:BUYERGROUP:123e4567-e89b-12d3-a456-************"))),
            new BasicAudit(now, now))));
  }

  static CompleteAudit generateAggregateAudit(final OffsetDateTime now) {
    return new CompleteAudit("admin", now, "admin", now, null, (short) 0);
  }

  static Use withAllArgumentsOnlyMandatoryNames(final OffsetDateTime now) {
    return new Use(
        generateId(),
        generateAssignableType(),
        generateTaxonomy(),
        generateCustomer(),
        generatePurchaseType(),
        UseNamesMother.generateNames(now),
        UseConditionsMother.generateCondition(now),
        generateAggregateAudit(now));
  }

  static Use withAllArgumentsAndTreeNamesAndTwoConditions(final OffsetDateTime now) {
    return new Use(
        generateId(),
        generateAssignableType(),
        generateTaxonomy(),
        generateCustomer(),
        generatePurchaseTypes(),
        UseNamesMother.generateTreeNames(now),
        UseConditionsMother.generateConditions(now),
        generateAggregateAudit(now));
  }

  static Use withAllArgumentsAndTreeNamesWithRegion(final OffsetDateTime now) {
    return new Use(
        randomId(),
        null,
        randomTaxonomy(),
        randomCustomer(),
        randomPurchaseType(),
        generateTreeUseNamesWithRegion(now),
        generateConditions(now),
        generateAggregateAudit(now));
  }

  static Use withTreeNamesAndTwoConditionsNullAssignable(final OffsetDateTime now) {
    return new Use(
        generateId(),
        null,
        generateTaxonomy(),
        generateCustomer(),
        generatePurchaseTypes(),
        UseNamesMother.generateTreeNames(now),
        UseConditionsMother.generateConditions(now),
        generateAggregateAudit(now));
  }

  private static Builder random() {
    return new Builder()
        .id(randomId())
        .assignable(randomAssignable())
        .taxonomy(randomTaxonomy())
        .customer(randomCustomer())
        .purchaseType(randomPurchaseType())
        .useNames(UseNamesMother.defaultUseNames())
        .conditions(UseConditionsMother.randomConditions())
        .audit(CompleteAuditMother.created());
  }

  static Builder generateBuild() {
    return random();
  }

  static Id randomId() {
    return new Id(UUID.randomUUID());
  }

  static AssignableType randomAssignable() {
    final var randomPosition = RandomValue.randomPositiveInteger(2);
    return randomPosition == 0 ? null : randomAssignableType();
  }

  static Taxonomy randomTaxonomy() {
    final var values = List.of("FABRIC", "FIBER", "YARN", "LINING", "TRIMMING");
    final var randomPosition = RandomValue.randomPositiveInteger(values.size());
    return new Taxonomy(values.get(randomPosition));
  }

  static Customer randomCustomer() {
    final var values = List.of("urn:BUYER:e54c6902-b64b-4e93-87aa-afa0ad696749", "urn:BUYER:d8be08e6-45cf-479f-8ebd-b734d71b0448",
        "urn:BUYER:91bd4022-28e6-4343-b9b6-4af00064308e");
    return new Customer(values.get(RandomValue.randomPositiveInteger(values.size())));
  }

  static PurchaseType randomPurchaseType() {
    final var randomPosition = RandomValue.randomPositiveInteger(PurchaseType.values().length);
    return PurchaseType.values()[randomPosition];
  }

  static AssignableType randomAssignableType() {
    final var randomPosition = RandomValue.randomPositiveInteger(AssignableType.values().length);
    return AssignableType.values()[randomPosition];
  }

  class Builder {

    Id id;

    AssignableType assignable;

    Taxonomy taxonomy;

    Customer customer;

    PurchaseType purchaseType;

    UseNames names;

    UseConditions conditions;

    CompleteAudit audit;

    public Builder id(final Id id) {
      this.id = id;
      return this;
    }

    public Builder assignable(final AssignableType assignable) {
      this.assignable = assignable;
      return this;
    }

    public Builder taxonomy(final Taxonomy taxonomy) {
      this.taxonomy = taxonomy;
      return this;
    }

    public Builder customer(final Customer customer) {
      this.customer = customer;
      return this;
    }

    public Builder purchaseType(final PurchaseType purchaseType) {
      this.purchaseType = purchaseType;
      return this;
    }

    public Builder useNames(final UseNames names) {
      this.names = names;
      return this;
    }

    public Builder conditions(final UseConditions conditions) {
      this.conditions = conditions;
      return this;
    }

    public Builder audit(final CompleteAudit audit) {
      this.audit = audit;
      return this;
    }

    public Use build() {
      return new Use(this.id, this.assignable, this.taxonomy, this.customer, this.purchaseType, this.names, this.conditions, this.audit);
    }
  }
}
