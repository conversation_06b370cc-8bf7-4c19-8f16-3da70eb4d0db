package com.inditex.icdmdemg.domain.consumermessage;

import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageId;
import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageName;
import com.inditex.icdmdemg.domain.consumermessage.entity.ConsumerMessageTimestamp;

import org.jspecify.annotations.NonNull;

public record ConsumerMessage(@NonNull ConsumerMessageId id, @NonNull ConsumerMessageName name,
    @NonNull ConsumerMessageTimestamp createdAt) {

}
