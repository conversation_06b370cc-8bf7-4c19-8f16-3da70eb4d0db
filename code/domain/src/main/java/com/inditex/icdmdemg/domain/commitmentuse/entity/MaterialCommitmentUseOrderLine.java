package com.inditex.icdmdemg.domain.commitmentuse.entity;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

public record MaterialCommitmentUseOrderLine(
    MaterialCommitmentUseOrderId orderId,
    MaterialCommitmentUseBudgetId budgetId,
    MaterialCommitmentUseOrderLineId orderLineId) implements ValueObject<MaterialCommitmentUseOrderLine> {
  public MaterialCommitmentUseOrderLine value() {
    return this;
  }
}
