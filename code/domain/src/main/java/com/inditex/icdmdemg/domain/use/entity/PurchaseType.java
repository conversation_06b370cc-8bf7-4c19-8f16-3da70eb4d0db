package com.inditex.icdmdemg.domain.use.entity;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;

import lombok.Getter;

@Getter
public enum PurchaseType {
  NOMINATED(List.of(PurchaseType.NOMINATED_VALUE)),
  INNER(List.of(PurchaseType.INNER_VALUE)),
  NOMINATED_INNER(List.of(PurchaseType.NOMINATED_VALUE, PurchaseType.INNER_VALUE));

  private static final String NOMINATED_VALUE = "NOMINATED";

  private static final String INNER_VALUE = "INNER";

  private final List<String> stringList;

  PurchaseType(final List<String> stringList) {
    this.stringList = stringList;
  }

  public static PurchaseType fromValues(final Collection<String> inputValues) {
    final var inputSet = new HashSet<>(inputValues);

    return Arrays.stream(values())
        .filter(type -> new HashSet<>(type.getStringList()).equals(inputSet))
        .findFirst().orElseThrow(() -> new IllegalArgumentException(
            String.format("No valid purchase type value found for input list: %s", inputValues)));
  }
}
