package com.inditex.icdmdemg.domain.shipmentcommitment.entity;

import java.util.UUID;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record ShipmentCommitmentDistributionNominatedLineId(@NonNull String value) implements ValueObject<String> {

  public static ShipmentCommitmentDistributionNominatedLineId of(@NonNull final UUID value) {
    return new ShipmentCommitmentDistributionNominatedLineId(String.valueOf(value));
  }
}
