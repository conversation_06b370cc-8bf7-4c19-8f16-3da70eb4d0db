package com.inditex.icdmdemg.domain.nominatedprovision.mother;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.BudgetId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.DistributionDistributed;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.DistributionRequested;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Entered;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.LocalizationId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Ordered;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Pending;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.ProductId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.ReferenceId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Stock;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.UseId;
import com.inditex.icdmdemg.domain.nominatedprovision.entity.NominatedProvisionAudit;
import com.inditex.icdmdemg.domain.nominatedprovision.entity.NominatedProvisionSharedRawMaterial;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;

public interface NominatedProvisionMother {

  static Builder withSharedRawMaterial(NominatedProvisionSharedRawMaterial sharedRawMaterial) {
    return random()
        .useId(sharedRawMaterial.useId())
        .referenceId(sharedRawMaterial.referenceId())
        .budgetId(sharedRawMaterial.budgetId());

  }

  static Builder randomNominatedProvision() {
    return random();
  }

  static Builder of(NominatedProvision existing) {
    return new Builder()
        .productId(existing.productId())
        .referenceId(existing.referenceId())
        .useId(existing.useId())
        .budgetId(existing.budgetId())
        .localizationId(existing.localizationId())
        .ordered(existing.ordered())
        .entered(existing.entered())
        .pending(existing.pending())
        .distributed(existing.distributionDistributed())
        .stock(existing.stock())
        .distributionRequested(existing.distributionRequested())
        .audit(existing.audit());
  }

  static NominatedProvisionSharedRawMaterial randomSharedRawMaterial() {
    return new NominatedProvisionSharedRawMaterial(
        new UseId(UUID.randomUUID().toString()),
        new ReferenceId(UUID.randomUUID().toString()),
        new BudgetId(UUID.randomUUID().toString()));
  }

  static Builder random() {
    return new Builder()
        .productId(randomProductId())
        .referenceId(randomReferenceId())
        .useId(randomUseId())
        .budgetId(randomBudgetId())
        .localizationId(randomLocalizationId())
        .ordered(randomOrdered())
        .entered(randomEntered())
        .pending(randomPending())
        .distributed(randomDistributed())
        .stock(randomStock())
        .distributionRequested(randomDistributionRequested())
        .audit(NominatedProvisionAuditMother.created().build());
  }

  private static ProductId randomProductId() {
    return new ProductId(UUID.randomUUID().toString());
  }

  private static ReferenceId randomReferenceId() {
    return new ReferenceId(UUID.randomUUID().toString());
  }

  private static UseId randomUseId() {
    return new UseId(UUID.randomUUID().toString());
  }

  private static BudgetId randomBudgetId() {
    return new BudgetId(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID().toString()));
  }

  private static LocalizationId randomLocalizationId() {
    return new LocalizationId(UUID.randomUUID().toString());
  }

  private static Entered randomEntered() {
    return new Entered(BigDecimal.ONE);
  }

  static Ordered randomOrdered() {
    return new Ordered(BigDecimal.ONE);
  }

  static Pending randomPending() {
    return new Pending(BigDecimal.ONE);
  }

  static DistributionDistributed randomDistributed() {
    return new DistributionDistributed(BigDecimal.ONE);
  }

  static Stock randomStock() {
    return new Stock(BigDecimal.ONE);
  }

  private static DistributionRequested randomDistributionRequested() {
    return new DistributionRequested(BigDecimal.ONE);
  }

  class Builder {
    private ProductId productId;

    private ReferenceId referenceId;

    private UseId useId;

    private BudgetId budgetId;

    private LocalizationId localizationId;

    private Ordered ordered;

    private Entered entered;

    private Pending pending;

    private DistributionDistributed distributionDistributed;

    private Stock stock;

    private DistributionRequested distributionRequested;

    private NominatedProvisionAudit audit;

    public Builder productId(final ProductId productId) {
      this.productId = productId;
      return this;
    }

    public Builder referenceId(final ReferenceId referenceId) {
      this.referenceId = referenceId;
      return this;
    }

    public Builder useId(final UseId useId) {
      this.useId = useId;
      return this;
    }

    public Builder budgetId(final BudgetId budgetId) {
      this.budgetId = budgetId;
      return this;
    }

    public Builder localizationId(final LocalizationId localizationId) {
      this.localizationId = localizationId;
      return this;
    }

    public Builder ordered(final Ordered ordered) {
      this.ordered = ordered;
      return this;
    }

    public Builder entered(final Entered entered) {
      this.entered = entered;
      return this;
    }

    public Builder pending(final Pending pending) {
      this.pending = pending;
      return this;
    }

    public Builder distributed(final DistributionDistributed distributionDistributed) {
      this.distributionDistributed = distributionDistributed;
      return this;
    }

    public Builder stock(final Stock stock) {
      this.stock = stock;
      return this;
    }

    public Builder distributionRequested(final DistributionRequested distributionRequested) {
      this.distributionRequested = distributionRequested;
      return this;
    }

    public Builder audit(final NominatedProvisionAudit audit) {
      this.audit = audit;
      return this;
    }

    public NominatedProvision build() {
      return new NominatedProvision(this.productId, this.referenceId, this.useId, this.budgetId, this.localizationId, this.ordered,
          this.entered, this.pending, this.stock, this.distributionDistributed, this.distributionRequested, this.audit);
    }
  }
}
