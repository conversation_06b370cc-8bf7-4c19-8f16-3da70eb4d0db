package com.inditex.icdmdemg.domain.use;

import java.time.OffsetDateTime;
import java.util.Objects;
import java.util.UUID;

import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.use.Use.Id;
import com.inditex.icdmdemg.domain.use.UseUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.use.entity.AssignableType;
import com.inditex.icdmdemg.domain.use.entity.PurchaseType;
import com.inditex.icdmdemg.domain.use.entity.UseConditions;
import com.inditex.icdmdemg.domain.use.entity.UseNames;
import com.inditex.iopcmmnt.ddd.core.AggregateRoot;
import com.inditex.iopcmmnt.ddd.core.AggregateRootId;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

@Getter
@ToString
@Accessors(fluent = true)
@EqualsAndHashCode(callSuper = true)
public class Use extends AggregateRoot<Id> {

  @Nullable
  AssignableType assignable;

  @NonNull
  Taxonomy taxonomy;

  @NonNull
  Customer customer;

  @NonNull
  PurchaseType purchaseType;

  @NonNull
  UseNames names;

  @NonNull
  UseConditions conditions;

  @NonNull
  CompleteAudit audit;

  public Use(
      final <EMAIL> Id id,
      AssignableType assignable,
      @lombok.NonNull final Taxonomy taxonomy,
      @lombok.NonNull final Customer customer,
      @lombok.NonNull final PurchaseType purchaseType,
      @lombok.NonNull final UseNames names,
      @lombok.NonNull final UseConditions conditions,
      @lombok.NonNull final CompleteAudit audit) {
    super(id);
    this.assignable = assignable;
    this.taxonomy = taxonomy;
    this.customer = customer;
    this.purchaseType = purchaseType;
    this.names = names;
    this.conditions = conditions;
    this.audit = audit;
  }

  public static Use create(final Use.@NonNull Id id,
      AssignableType assignable,
      @NonNull final Taxonomy taxonomy,
      @NonNull final Customer customer,
      @NonNull final PurchaseType purchaseType,
      @NonNull final UseNames names,
      @NonNull final UseConditions conditions,
      @NonNull final CompleteAudit audit) {
    final var use = new Use(id, assignable, taxonomy, customer, purchaseType, names, conditions, audit);
    use.eventRegister().register(EventType.CREATED);
    return use;
  }

  public Use update(
      final AssignableType assignable,
      @NonNull final UseNames useNames,
      @NonNull final OffsetDateTime occurredOn,
      @NonNull final String triggeredBy) {

    final var isAssignableModified = !Objects.equals(this.assignable, assignable);
    final var isNamesModified = !Objects.equals(this.names, useNames);

    if (isNamesModified) {
      this.names = useNames;
      this.audit = this.audit().update(triggeredBy, occurredOn);
      this.eventRegister().register(EventType.UPDATE_TRANSLATIONS);
    }
    if (isAssignableModified) {
      this.assignable = assignable;
      this.audit = this.audit().update(triggeredBy, occurredOn);
      this.eventRegister().register(EventType.UPDATE_ASSIGNABLE);
    }
    return this;
  }

  public record Id(@NonNull UUID value) implements AggregateRootId<UUID> {
  }

  public record Taxonomy(@NonNull String value) implements ValueObject<String> {
  }

  public record Customer(@NonNull String value) implements ValueObject<String> {
  }

  protected Use.EventRegister eventRegister() {
    return new Use.EventRegister(this);
  }

  public record EventRegister(Use use) {
    public void register(final EventType event) {
      this.use.registerEvent(new UseUnifiedEvent(this.use, event));
    }
  }
}
