package com.inditex.icdmdemg.domain.migratedcommitmentconsumption.mother;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.MigratedCommitmentConsumption;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionId;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionMovementId;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionOrderId;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionOrderLine;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionOrderLineId;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionQuantity;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionReferenceId;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

public interface MigratedCommitmentConsumptionMother {

  static MigratedCommitmentConsumption with(final Integer movementId, final Integer referenceId, final Integer orderId,
      final Integer orderLineId, final BigDecimal quantity) {
    return new MigratedCommitmentConsumption(
        new MigratedCommitmentConsumptionId(UUID.randomUUID().toString()),
        new MigratedCommitmentConsumptionMovementId(String.valueOf(UuidMother.fromInteger(movementId))),
        new MigratedCommitmentConsumptionReferenceId(String.valueOf(UuidMother.fromInteger(referenceId))),
        new MigratedCommitmentConsumptionOrderLine(
            new MigratedCommitmentConsumptionOrderId(String.valueOf(UuidMother.fromInteger(orderId))),
            new MigratedCommitmentConsumptionOrderLineId(String.valueOf(UuidMother.fromInteger(orderLineId)))),
        new MigratedCommitmentConsumptionQuantity(quantity));
  }
}
