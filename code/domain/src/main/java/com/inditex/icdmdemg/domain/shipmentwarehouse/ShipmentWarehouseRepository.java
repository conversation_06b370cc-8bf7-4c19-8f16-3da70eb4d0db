package com.inditex.icdmdemg.domain.shipmentwarehouse;

import java.util.Optional;

import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseId;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTrackingCode;

import org.jspecify.annotations.NullMarked;

@NullMarked
public interface ShipmentWarehouseRepository {

  Optional<ShipmentWarehouse> findById(ShipmentWarehouseId id);

  Optional<ShipmentWarehouse> findByTrackingCode(
      ShipmentWarehouseTrackingCode shipmentWarehouseTrackingCode);

  ShipmentWarehouse save(ShipmentWarehouse shipmentWarehouseToSave);

}
