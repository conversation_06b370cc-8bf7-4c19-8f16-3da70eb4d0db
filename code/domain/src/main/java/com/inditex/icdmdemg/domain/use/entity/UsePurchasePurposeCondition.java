package com.inditex.icdmdemg.domain.use.entity;

import java.util.Comparator;

import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;

import lombok.AccessLevel;
import lombok.Builder;

@Builder(access = AccessLevel.PRIVATE, toBuilder = true)
public record UsePurchasePurposeCondition(
    @lombok.NonNull PurchasePurposeCondition condition,
    @lombok.NonNull PurchasePurposeConditionName name,
    @lombok.NonNull PurchasePurposeConditionValues values,
    @lombok.NonNull BasicAudit audit) {

  public static Comparator<UsePurchasePurposeCondition> comparator() {
    return Comparator
        .comparing(UsePurchasePurposeCondition::condition)
        .thenComparing(UsePurchasePurposeCondition::name)
        .thenComparing(condition -> condition.values().sortAndJoin());
  }
}
