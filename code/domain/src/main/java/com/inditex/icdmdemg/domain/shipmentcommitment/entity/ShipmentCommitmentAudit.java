package com.inditex.icdmdemg.domain.shipmentcommitment.entity;

import java.time.OffsetDateTime;

import com.inditex.icdmdemg.domain.shipmentcommitment.entity.audit.ShipmentCommitmentCreatedAt;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.audit.ShipmentCommitmentUpdatedAt;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.audit.ShipmentCommitmentVersion;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record ShipmentCommitmentAudit(
    @NonNull ShipmentCommitmentCreatedAt createdAt,
    @NonNull ShipmentCommitmentUpdatedAt updatedAt,
    @NonNull ShipmentCommitmentVersion version) implements ValueObject<ShipmentCommitmentAudit> {

  public static ShipmentCommitmentAudit create(final OffsetDateTime now) {
    return new ShipmentCommitmentAudit(
        new ShipmentCommitmentCreatedAt(now),
        new ShipmentCommitmentUpdatedAt(now),
        ShipmentCommitmentVersion.first());
  }

  public ShipmentCommitmentAudit update(final OffsetDateTime now) {
    return new ShipmentCommitmentAudit(
        this.createdAt,
        new ShipmentCommitmentUpdatedAt(now),
        this.version);
  }

  @Override
  public ShipmentCommitmentAudit value() {
    return this;
  }

}
