package com.inditex.icdmdemg.domain.use.entity.ranker;

import java.util.List;

public interface PurchasePurposeAnyInRanker {

  static PurchasePurposeRankResult evaluate(final List<String> purchasePurposeParameterValues,
      final List<String> usePurchasePurposeConditionValues) {
    final var numberOfParametersComplied = (int) purchasePurposeParameterValues.stream()
        .filter(usePurchasePurposeConditionValues::contains).count();
    return new PurchasePurposeRankResult(numberOfParametersComplied);
  }
}
