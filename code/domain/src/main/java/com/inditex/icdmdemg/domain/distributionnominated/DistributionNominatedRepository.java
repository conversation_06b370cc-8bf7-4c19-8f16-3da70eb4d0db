package com.inditex.icdmdemg.domain.distributionnominated;

import java.util.List;
import java.util.Optional;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedStatus;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;

import org.jspecify.annotations.NullMarked;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;

@NullMarked
public interface DistributionNominatedRepository {

  Optional<DistributionNominated> findById(Id id);

  Optional<DistributionNominated> findByLineId(final DistributionNominatedLine.Id lineId);

  List<DistributionNominated> findByProductOrderId(final ProductOrderId productOrderId);

  List<DistributionNominated> findByCommitmentOrderAndReferenceId(
      final CommitmentOrder commitmentOrder,
      final ReferenceId referenceId);

  List<DistributionNominated> findBySharedRawMaterial(final SharedRawMaterialNominated sharedRawMaterial);

  List<DistributionNominated> findByAnySharedRawMaterial(final List<SharedRawMaterialNominated> sharedRawMaterialList);

  List<DistributionNominated> findByProductVariantGroupIds(final List<ProductVariantGroupId> productVariantGroupIds);

  List<DistributionNominated> findByReferenceIds(final List<ReferenceId> referenceIds);

  Slice<DistributionNominated> findByCriteria(
      final List<ReferenceId> referenceIds,
      final List<ProductOrderId> productOrderIds,
      final List<DistributionNominatedLine.Id> lineIds,
      final List<CommitmentOrder.Id> commitmentOrderIds,
      final List<BudgetCycle> budgetCycles,
      final List<DistributionNominatedStatus> statusList,
      final Pageable pageable);

  List<DistributionNominated> findByCommitmentOrderIdAndOrderLineId(
      final CommitmentOrder.Id commitmentOrderId, final CommitmentOrder.LineId commitmentOrderLineId);

  void save(final DistributionNominated distributionNominated);
}
