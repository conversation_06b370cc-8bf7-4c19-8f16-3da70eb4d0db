package com.inditex.icdmdemg.domain.commitmentuse;

import static com.inditex.icdmdemg.shared.utils.Nullables.acceptNullElseMap;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Objects;

import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeExecutedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeRequestedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseMaterialReferenceId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLine;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLineId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseQuantity;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseServiceLocalizationId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseServiceLocalizationType;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseTimestamp;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseUseId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseVersion;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.jspecify.annotations.NonNull;

@EqualsAndHashCode
@ToString(callSuper = true)
@Getter
public class MaterialCommitmentUse {

  private final MaterialCommitmentUseId id;

  private MaterialCommitmentUseQuantity quantity;

  private MaterialCommitmentUseTimestamp expectedDate;

  @NonNull
  private final MaterialCommitmentUseMaterialReferenceId materialReferenceId;

  @NonNull
  private final MaterialCommitmentUseUseId useId;

  @NonNull
  private final MaterialCommitmentUseOrderLine orderLine;

  private MaterialCommitmentUseStatus status;

  private MaterialCommitmentUseServiceLocalizationId serviceLocalizationId;

  private MaterialCommitmentUseServiceLocalizationType serviceLocalizationType;

  private MaterialCommitmentUseBudgetCycleChangeRequestedAt budgetCycleChangeRequestedAt;

  private MaterialCommitmentUseBudgetCycleChangeExecutedAt budgetCycleChangeExecutedAt;

  @NonNull
  private final MaterialCommitmentUseVersion version;

  private BasicAudit audit;

  private MaterialCommitmentUseTimestamp processedAt;

  public MaterialCommitmentUse(
      @NonNull final MaterialCommitmentUseId id,
      final MaterialCommitmentUseMaterialReferenceId materialReferenceId,
      final MaterialCommitmentUseUseId useId,
      final MaterialCommitmentUseQuantity quantity,
      final MaterialCommitmentUseTimestamp expectedDate,
      final MaterialCommitmentUseOrderLine orderLine,
      final MaterialCommitmentUseStatus status,
      final MaterialCommitmentUseServiceLocalizationId localizationId,
      final MaterialCommitmentUseServiceLocalizationType localizationType,
      final MaterialCommitmentUseBudgetCycleChangeRequestedAt budgetCycleChangeRequestedAt,
      final MaterialCommitmentUseBudgetCycleChangeExecutedAt budgetCycleChangeExecutedAt,
      @NonNull final MaterialCommitmentUseVersion version,
      final BasicAudit audit,
      final MaterialCommitmentUseTimestamp processedAt) {
    this.id = id;
    this.materialReferenceId = materialReferenceId;
    this.useId = useId;
    this.quantity = quantity;
    this.expectedDate = expectedDate;
    this.orderLine = orderLine;
    this.status = status;
    this.serviceLocalizationId = localizationId;
    this.serviceLocalizationType = localizationType;
    this.budgetCycleChangeRequestedAt = budgetCycleChangeRequestedAt;
    this.budgetCycleChangeExecutedAt = budgetCycleChangeExecutedAt;
    this.version = version;
    this.audit = audit;
    this.processedAt = processedAt;
  }

  public static MaterialCommitmentUse create(
      @NonNull final MaterialCommitmentUseId id,
      final MaterialCommitmentUseMaterialReferenceId materialReferenceId,
      final MaterialCommitmentUseUseId useId,
      final MaterialCommitmentUseQuantity quantity,
      final MaterialCommitmentUseTimestamp expectedDate,
      final MaterialCommitmentUseOrderLine orderLine,
      final MaterialCommitmentUseStatus status,
      final MaterialCommitmentUseServiceLocalizationId serviceLocalizationId,
      final MaterialCommitmentUseServiceLocalizationType serviceLocalizationType,
      @NonNull final OffsetDateTime now,
      final MaterialCommitmentUseBudgetCycleChangeRequestedAt budgetCycleChangeRequestedDate) {
    return new MaterialCommitmentUse(id, materialReferenceId, useId, quantity,
        expectedDate, orderLine, status, serviceLocalizationId, serviceLocalizationType,
        budgetCycleChangeRequestedDate,
        null,
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(now), null);
  }

  public MaterialCommitmentUse modifyWithUseCommitmentOrder(final OffsetDateTime newExpectedDate,
      final BigDecimal newQuantity,
      final String newStatus,
      final String newServiceLocalizationId,
      final String newServiceLocalizationType,
      @NonNull final OffsetDateTime now,
      final OffsetDateTime budgetCycleChangeRequestedAt) {
    this.quantity = acceptNullElseMap(newQuantity, MaterialCommitmentUseQuantity::new);
    this.expectedDate = acceptNullElseMap(newExpectedDate, MaterialCommitmentUseTimestamp::of);
    this.status = acceptNullElseMap(newStatus, MaterialCommitmentUseStatus::new);
    this.serviceLocalizationId = acceptNullElseMap(newServiceLocalizationId, MaterialCommitmentUseServiceLocalizationId::new);
    this.serviceLocalizationType = acceptNullElseMap(newServiceLocalizationType, MaterialCommitmentUseServiceLocalizationType::new);
    this.processedAt = this.unprocess(newExpectedDate, now);
    this.budgetCycleChangeRequestedAt =
        acceptNullElseMap(budgetCycleChangeRequestedAt, MaterialCommitmentUseBudgetCycleChangeRequestedAt::new);
    this.audit = this.audit.update(now);
    return this;
  }

  public boolean willBeUpdatedWith(final OffsetDateTime newExpectedDate, final BigDecimal newQuantity,
      final String newStatus, final String newServiceLocalizationId, final String newServiceLocalizationType,
      final OffsetDateTime newBudgetCycleChangeRequestedAt) {
    return !Objects.equals(this.expectedDate.value(), newExpectedDate)
        || this.quantity.value().compareTo(newQuantity) != 0
        || !Objects.equals(this.status.value(), newStatus)
        || !Objects.equals(this.serviceLocalizationId.value(), newServiceLocalizationId)
        || !Objects.equals(this.serviceLocalizationType.value(), newServiceLocalizationType)
        || !Objects.equals(acceptNullElseMap(this.budgetCycleChangeRequestedAt, MaterialCommitmentUseBudgetCycleChangeRequestedAt::value),
            newBudgetCycleChangeRequestedAt);
  }

  public MaterialCommitmentUseSharedRawMaterial sharedRawMaterial() {
    return MaterialCommitmentUseSharedRawMaterial.of(this);
  }

  public MaterialCommitmentUse setAsProcessed(final MaterialCommitmentUseTimestamp now) {
    this.processedAt = now;
    return this;
  }

  public MaterialCommitmentUse executeBudgetCycleChange(final OffsetDateTime executedAt) {
    this.budgetCycleChangeExecutedAt = new MaterialCommitmentUseBudgetCycleChangeExecutedAt(executedAt);
    return this;
  }

  public record MaterialCommitmentUseCompositeId(
      MaterialCommitmentUseMaterialReferenceId materialReferenceId,
      MaterialCommitmentUseUseId useId,
      MaterialCommitmentUseOrderLine orderLine) {

    public static MaterialCommitmentUseCompositeId of(SharedRawMaterialNominated sharedRawMaterial, MaterialCommitmentUseOrderId orderId,
        MaterialCommitmentUseOrderLineId orderLineId) {
      return new MaterialCommitmentUseCompositeId(
          new MaterialCommitmentUseMaterialReferenceId(sharedRawMaterial.referenceId().value().toString()),
          new MaterialCommitmentUseUseId(sharedRawMaterial.useId().value().toString()),
          new MaterialCommitmentUseOrderLine(
              new MaterialCommitmentUseOrderId(orderId.value()),
              new MaterialCommitmentUseBudgetId(sharedRawMaterial.budgetCycle().value()),
              new MaterialCommitmentUseOrderLineId(orderLineId.value())));
    }
  }

  public boolean isBudgetCycleChangeInProgress() {
    return isFirstBudgetCycleChange(this.budgetCycleChangeRequestedAt, this.budgetCycleChangeExecutedAt)
        || isMultipleBudgetCycleChange(this.budgetCycleChangeRequestedAt, this.budgetCycleChangeExecutedAt);
  }

  public static boolean isBudgetCycleChangeInProgress(
      final MaterialCommitmentUseBudgetCycleChangeRequestedAt budgetCycleChangeRequestedAt,
      final MaterialCommitmentUseBudgetCycleChangeExecutedAt budgetCycleChangeExecutedAt) {
    return isFirstBudgetCycleChange(budgetCycleChangeRequestedAt, budgetCycleChangeExecutedAt)
        || isMultipleBudgetCycleChange(budgetCycleChangeRequestedAt, budgetCycleChangeExecutedAt);
  }

  private static boolean isFirstBudgetCycleChange(
      final MaterialCommitmentUseBudgetCycleChangeRequestedAt budgetCycleChangeRequestedAt,
      final MaterialCommitmentUseBudgetCycleChangeExecutedAt budgetCycleChangeExecutedAt) {
    return Objects.nonNull(budgetCycleChangeRequestedAt) && Objects.isNull(budgetCycleChangeExecutedAt);
  }

  private static boolean isMultipleBudgetCycleChange(
      final MaterialCommitmentUseBudgetCycleChangeRequestedAt budgetCycleChangedRequestedAt,
      final MaterialCommitmentUseBudgetCycleChangeExecutedAt budgetCycleChangeExecutedAt) {
    return Objects.nonNull(budgetCycleChangedRequestedAt)
        && budgetCycleChangedRequestedAt.value().isAfter(budgetCycleChangeExecutedAt.value());
  }

  private MaterialCommitmentUseTimestamp unprocess(final OffsetDateTime expectedDate, final OffsetDateTime now) {
    return (!this.isEligibleForProcessing(expectedDate, now)) ? null : this.processedAt;
  }

  private boolean isEligibleForProcessing(final OffsetDateTime expectedDate, final OffsetDateTime now) {
    return expectedDate.isBefore(now) || expectedDate.isEqual(now);
  }
}
