package com.inditex.icdmdemg.domain.commitmentuse.entity;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record MaterialCommitmentUseStatus(@NonNull String value) implements ValueObject<String> {

  public static MaterialCommitmentUseStatus of(final MaterialCommitmentUseStatusEnum materialCommitmentUseStatusEnum) {
    return new MaterialCommitmentUseStatus(materialCommitmentUseStatusEnum.name());
  }

  public boolean isClosed() {
    return this.value.toUpperCase().equals(MaterialCommitmentUseStatusEnum.CLOSED.name());
  }

  public boolean isOpen() {
    return this.value.toUpperCase().equals(MaterialCommitmentUseStatusEnum.OPEN.name());
  }

  public enum MaterialCommitmentUseStatusEnum {
    DRAFT, IN_PROGRESS, CANCELED, CLOSED, OPEN
  }
}
