package com.inditex.icdmdemg.domain.shared.audit;

import java.time.OffsetDateTime;

import lombok.Builder;
import lombok.NonNull;

@Builder(toBuilder = true)
public record BasicAudit(
    @NonNull OffsetDateTime createdAt,
    @NonNull OffsetDateTime updatedAt) {

  public static BasicAudit create(final OffsetDateTime createdAt) {
    return new BasicAudit(createdAt, createdAt);
  }

  public BasicAudit update(final OffsetDateTime now) {
    return new BasicAudit(this.createdAt, now);
  }
}
