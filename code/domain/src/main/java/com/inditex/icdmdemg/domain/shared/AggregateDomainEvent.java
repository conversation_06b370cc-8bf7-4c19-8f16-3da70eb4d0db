package com.inditex.icdmdemg.domain.shared;

import java.time.Instant;
import java.util.UUID;

import com.inditex.iopcmmnt.ddd.core.DomainEvent;

public interface AggregateDomainEvent<T> extends DomainEvent<T> {

  T getAggregateId();

  default String user() {
    return "UNKNOWN";
  }

  default UUID idempotencyId() {
    return UUID.randomUUID();
  }

  default Instant occurredOn() {
    return Instant.now();
  }

}
