package com.inditex.icdmdemg.provis.domain.productuse;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record ProductUseInnerStockByUseDTO(
    UseId useId,
    Stock stock) {

  public record UseId(UUID value) implements ValueObject<UUID> {

  }

  public record Stock(BigDecimal value) implements ValueObject<BigDecimal> {
    public Stock(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }

  }

}
