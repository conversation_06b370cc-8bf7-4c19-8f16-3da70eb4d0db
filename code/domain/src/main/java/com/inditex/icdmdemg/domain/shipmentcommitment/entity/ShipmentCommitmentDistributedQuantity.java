package com.inditex.icdmdemg.domain.shipmentcommitment.entity;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record ShipmentCommitmentDistributedQuantity(
    @NonNull ShipmentCommitmentQuantity quantity,
    ShipmentCommitmentMeasurementUnitId measurementUnitId) implements ValueObject<ShipmentCommitmentDistributedQuantity> {

  @Override
  public ShipmentCommitmentDistributedQuantity value() {
    return this;
  }
}
