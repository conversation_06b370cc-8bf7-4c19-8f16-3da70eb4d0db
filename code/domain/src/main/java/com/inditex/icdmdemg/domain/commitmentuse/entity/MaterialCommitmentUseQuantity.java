package com.inditex.icdmdemg.domain.commitmentuse.entity;

import java.math.BigDecimal;

import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record MaterialCommitmentUseQuantity(@NonNull BigDecimal value) implements ValueObject<BigDecimal> {

  public MaterialCommitmentUseQuantity(@NonNull final BigDecimal value) {
    this.value = NumericUtils.roundUpScale2(value);
  }
}
