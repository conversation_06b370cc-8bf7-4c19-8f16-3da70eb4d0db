package com.inditex.icdmdemg.domain.use.entity;

import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import lombok.AccessLevel;
import lombok.Builder;

@Builder(access = AccessLevel.PRIVATE, toBuilder = true)
public record UseName(
    @lombok.NonNull Name name,
    @lombok.NonNull Lang lang,
    @lombok.NonNull BasicAudit audit) {

  public record Name(String value) implements ValueObject<String> {
  }

  public record Lang(String value) implements ValueObject<String> {
  }
}
