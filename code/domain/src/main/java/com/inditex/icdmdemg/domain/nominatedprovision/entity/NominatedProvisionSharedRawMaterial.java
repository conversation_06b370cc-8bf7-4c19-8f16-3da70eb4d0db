package com.inditex.icdmdemg.domain.nominatedprovision.entity;

import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.BudgetId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.ReferenceId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.UseId;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record NominatedProvisionSharedRawMaterial(
    UseId useId,
    ReferenceId referenceId,
    BudgetId budgetId) {

}
