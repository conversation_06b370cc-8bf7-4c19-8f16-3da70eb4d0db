package com.inditex.icdmdemg.domain.use.entity;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.domain.use.entity.PurchasePurposeConditionValues.ConditionValue;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record PurchasePurposeConditionValues(List<ConditionValue> value) implements ValueObject<List<ConditionValue>> {

  public static final String DELIMITER = ",";

  public record ConditionValue(String value) implements ValueObject<String> {
  }

  public String join() {
    return this.value.stream().map(ConditionValue::value).collect(Collectors.joining(DELIMITER));
  }

  public String sortAndJoin() {
    return this.value.stream().map(ConditionValue::value).sorted(Comparator.naturalOrder()).collect(Collectors.joining(DELIMITER));
  }

  public static PurchasePurposeConditionValues fromStrings(final String values) {
    return new PurchasePurposeConditionValues(Arrays.stream(values.split(DELIMITER)).map(ConditionValue::new).toList());

  }
}
