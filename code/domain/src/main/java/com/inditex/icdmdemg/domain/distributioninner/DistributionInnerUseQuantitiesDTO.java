package com.inditex.icdmdemg.domain.distributioninner;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.UseId;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record DistributionInnerUseQuantitiesDTO(
    UseId useId,
    RequestedQuantity requested,
    DistributedQuantity distributed) {
}
