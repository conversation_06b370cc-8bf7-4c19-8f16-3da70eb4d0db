package com.inditex.icdmdemg.domain.commitmentuse.entity;

import java.time.OffsetDateTime;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record MaterialCommitmentUseTimestamp(@NonNull OffsetDateTime value) implements ValueObject<OffsetDateTime> {
  public static MaterialCommitmentUseTimestamp of(final OffsetDateTime date) {
    return new MaterialCommitmentUseTimestamp(date);
  }
}
