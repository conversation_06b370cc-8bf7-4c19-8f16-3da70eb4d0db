package com.inditex.icdmdemg.domain.product.entity;

import java.time.OffsetDateTime;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record ProductOwner(
    @lombok.NonNull OwnerId ownerId,
    @lombok.NonNull Timestamp createdAt,
    @lombok.NonNull Timestamp updatedAt) {

  public static ProductOwner create(
      @NonNull final OwnerId ownerId,
      @NonNull final OffsetDateTime now) {
    return new ProductOwner(ownerId, Timestamp.of(now), Timestamp.of(now));
  }

  public record OwnerId(String value) implements ValueObject<String> {
  }

  public record Timestamp(@NonNull OffsetDateTime value) implements ValueObject<OffsetDateTime> {
    public static Timestamp of(final OffsetDateTime date) {
      return new Timestamp(date);
    }
  }
}
