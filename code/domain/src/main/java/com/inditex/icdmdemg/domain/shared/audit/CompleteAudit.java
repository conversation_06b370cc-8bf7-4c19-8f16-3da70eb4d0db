package com.inditex.icdmdemg.domain.shared.audit;

import java.time.OffsetDateTime;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.NonNull;

@Builder(access = AccessLevel.PRIVATE, toBuilder = true)
public record CompleteAudit(
    @NonNull String createdBy,
    @NonNull OffsetDateTime createdAt,
    @NonNull String updatedBy,
    @NonNull OffsetDateTime updatedAt,
    OffsetDateTime deletedAt,
    short version) {

  public static CompleteAudit create(@NonNull final String createdBy, @NonNull final OffsetDateTime createdAt) {
    return CompleteAudit.builder()
        .createdBy(createdBy)
        .createdAt(createdAt)
        .updatedBy(createdBy)
        .updatedAt(createdAt)
        .version((short) 0)
        .build();
  }

  public CompleteAudit update(@NonNull final String updatedBy, @NonNull final OffsetDateTime updatedAt) {
    return CompleteAudit.builder()
        .createdBy(this.createdBy)
        .createdAt(this.createdAt)
        .updatedAt(updatedAt)
        .updatedBy(updatedBy)
        .version(this.version)
        .build();
  }

  public CompleteAudit delete(@NonNull final String deletedBy, @NonNull final OffsetDateTime deletedAt) {
    return CompleteAudit.builder()
        .createdBy(this.createdBy)
        .createdAt(this.createdAt)
        .updatedAt(deletedAt)
        .updatedBy(deletedBy)
        .deletedAt(deletedAt)
        .version(this.version)
        .build();
  }
}
