package com.inditex.icdmdemg.domain.distributioninner.entity;

import static java.math.RoundingMode.HALF_UP;
import static java.util.Objects.isNull;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import lombok.AccessLevel;
import lombok.Builder;
import org.jspecify.annotations.NonNull;

@Builder(access = AccessLevel.PRIVATE, toBuilder = true)
public record DistributionInnerLine(
    @lombok.NonNull Id id,
    TrackingCode trackingCode,
    @lombok.NonNull TheoreticalQuantity theoreticalQuantity,
    @lombok.NonNull RequestedQuantity requestedQuantity,
    @lombok.NonNull DistributedQuantity distributedQuantity,
    DistributionStartDate distributionStartDate,
    DistributionEndDate distributionEndDate,
    @lombok.NonNull BasicAudit audit) {

  public static DistributionInnerLine create(
      @NonNull final Id id,
      @NonNull final TheoreticalQuantity theoreticalQuantity,
      @NonNull final RequestedQuantity requestedQuantity,
      @NonNull final OffsetDateTime occurredAt) {
    return DistributionInnerLine.builder()
        .id(id)
        .theoreticalQuantity(theoreticalQuantity)
        .requestedQuantity(requestedQuantity)
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO))
        .distributionStartDate(null)
        .distributionEndDate(null)
        .audit(BasicAudit.create(occurredAt))
        .build();
  }

  public boolean isAlive() {
    return this.distributionStartDate() == null;
  }

  public boolean isProcessStarted() {
    return !isNull(this.distributionStartDate());
  }

  public boolean isSent() {
    return !isNull(this.distributionEndDate());
  }

  public DistributionInnerLine incrementRequestedQuantity(
      final @NonNull RequestedQuantity increasedQuantity,
      final @NonNull OffsetDateTime updatedAt) {
    return this.toBuilder()
        .requestedQuantity(this.requestedQuantity.increment(increasedQuantity))
        .audit(this.audit().update(updatedAt))
        .build();
  }

  public DistributionInnerLine decrementRequestedQuantity(
      final @NonNull RequestedQuantity decreasedQuantity,
      final @NonNull OffsetDateTime updatedAt) {
    return this.toBuilder()
        .requestedQuantity(this.requestedQuantity.decrement(decreasedQuantity))
        .audit(this.audit().update(updatedAt))
        .build();
  }

  public DistributionInnerLine adjustTheoretical(final @NonNull BigDecimal rootRequestedQuantity) {
    return this.toBuilder()
        .theoreticalQuantity(TheoreticalQuantity.computeLineTheoretical(this.requestedQuantity, rootRequestedQuantity))
        .build();
  }

  public DistributionInnerLine updateLineDistribution(final @NonNull TrackingCode trackingCode,
      final DistributedQuantity distributedQuantity,
      final DistributionStartDate distributionStartDate,
      final DistributionEndDate distributionEndDate,
      final OffsetDateTime updatedAt) {
    return this.toBuilder()
        .trackingCode(trackingCode)
        .distributedQuantity(distributedQuantity)
        .distributionStartDate(distributionStartDate)
        .distributionEndDate(distributionEndDate)
        .audit(this.audit.update(updatedAt))
        .build();
  }

  public record Id(UUID value) implements ValueObject<UUID> {
  }

  public record TrackingCode(String value) implements ValueObject<String> {
  }

  public record TheoreticalQuantity(BigDecimal value) implements ValueObject<BigDecimal> {
    public TheoreticalQuantity {
      value = NumericUtils.roundUpScale2(value);
    }

    public static TheoreticalQuantity computeLineTheoretical(
        final @NonNull RequestedQuantity lineRequested,
        final @NonNull BigDecimal rootRequested) {
      if (Stream.of(lineRequested.value, rootRequested).anyMatch(value -> value.compareTo(BigDecimal.ZERO) == 0)) {
        return new TheoreticalQuantity(BigDecimal.ZERO.setScale(2, HALF_UP));
      }
      return new TheoreticalQuantity(lineRequested.value.multiply(BigDecimal.valueOf(100)).divide(rootRequested, HALF_UP)
          .max(BigDecimal.ZERO).min(BigDecimal.valueOf(100)));
    }
  }

  public record RequestedQuantity(BigDecimal value) implements ValueObject<BigDecimal> {
    public RequestedQuantity {
      value = NumericUtils.roundUpScale2(value);
    }

    public RequestedQuantity increment(final @NonNull RequestedQuantity increasedQuantity) {
      return new RequestedQuantity(this.value().add(increasedQuantity.value()));
    }

    public RequestedQuantity decrement(final @NonNull RequestedQuantity decreasedQuantity) {
      return new RequestedQuantity(this.value().subtract(decreasedQuantity.value()));
    }

  }

  public record DistributedQuantity(@NonNull BigDecimal value) implements ValueObject<BigDecimal> {
    public DistributedQuantity {
      value = NumericUtils.roundUpScale2(value);
    }
  }

  public record DistributionStartDate(OffsetDateTime value) implements ValueObject<OffsetDateTime> {
  }

  public record DistributionEndDate(OffsetDateTime value) implements ValueObject<OffsetDateTime> {
  }
}
