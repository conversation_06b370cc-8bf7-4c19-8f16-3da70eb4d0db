package com.inditex.icdmdemg.domain.order.mother;

import java.util.UUID;

import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantity;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetail;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetailReferenceId;
import com.inditex.icdmdemg.shared.utils.RandomValue;

public interface OrderLineQuantityDetailMother {

  static Builder random() {
    return new Builder()
        .orderLineQuantity(randomOrderLineQuantity())
        .referenceId(randomOrderLineQuantityDetailReferenceId());
  }

  private static OrderLineQuantity randomOrderLineQuantity() {
    return new OrderLineQuantity(RandomValue.randomPositiveInteger());
  }

  private static OrderLineQuantityDetailReferenceId randomOrderLineQuantityDetailReferenceId() {
    return new OrderLineQuantityDetailReferenceId(UUID.randomUUID().toString());
  }

  class Builder {
    OrderLineQuantity orderLineQuantity;

    OrderLineQuantityDetailReferenceId referenceId;

    public Builder orderLineQuantity(final OrderLineQuantity orderLineQuantity) {
      this.orderLineQuantity = orderLineQuantity;
      return this;
    }

    public Builder referenceId(final OrderLineQuantityDetailReferenceId referenceId) {
      this.referenceId = referenceId;
      return this;
    }

    public OrderLineQuantityDetail build() {
      return new OrderLineQuantityDetail(this.orderLineQuantity, this.referenceId);
    }
  }
}
