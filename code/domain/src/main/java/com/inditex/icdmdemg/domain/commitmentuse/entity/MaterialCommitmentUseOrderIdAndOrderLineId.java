package com.inditex.icdmdemg.domain.commitmentuse.entity;

import org.jspecify.annotations.NonNull;

public record MaterialCommitmentUseOrderIdAndOrderLineId(
    @NonNull MaterialCommitmentUseOrderId materialCommitmentOrderId,
    @NonNull MaterialCommitmentUseOrderLineId materialCommitmentOrderLineId) {

  public static MaterialCommitmentUseOrderIdAndOrderLineId of(
      @NonNull final MaterialCommitmentUseOrderId materialCommitmentOrderId,
      @NonNull final MaterialCommitmentUseOrderLineId materialCommitmentOrderLineId) {
    return new MaterialCommitmentUseOrderIdAndOrderLineId(
        materialCommitmentOrderId,
        materialCommitmentOrderLineId);
  }

  public static MaterialCommitmentUseOrderIdAndOrderLineId of(
      @NonNull final String materialCommitmentOrderId,
      @NonNull final String materialCommitmentOrderLineId) {
    return new MaterialCommitmentUseOrderIdAndOrderLineId(
        new MaterialCommitmentUseOrderId(materialCommitmentOrderId),
        new MaterialCommitmentUseOrderLineId(materialCommitmentOrderLineId));
  }

}
