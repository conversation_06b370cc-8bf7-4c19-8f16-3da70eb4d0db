package com.inditex.icdmdemg.domain.commitmentuse;

import java.time.OffsetDateTime;
import java.util.List;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse.MaterialCommitmentUseCompositeId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderIdAndOrderLineId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;

import org.jspecify.annotations.NullMarked;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;

@NullMarked
public interface MaterialCommitmentUseRepository {

  void delete(MaterialCommitmentUse materialCommitmentUseToDelete);

  MaterialCommitmentUse save(MaterialCommitmentUse materialCommitmentUseToSave);

  MaterialCommitmentUseCollection saveAll(final List<MaterialCommitmentUse> materialCommitmentUseListToSave);

  MaterialCommitmentUseCollection findByAnyOrderIdAndOrderLineId(
      final List<MaterialCommitmentUseOrderIdAndOrderLineId> matComOrderIdAndLineIds);

  MaterialCommitmentUseCollection findByAnySharedRawMaterial(
      final List<MaterialCommitmentUseSharedRawMaterial> sharedRawMaterialList);

  Slice<MaterialCommitmentUse> findByAnySharedRawMaterial(
      final List<MaterialCommitmentUseSharedRawMaterial> sharedRawMaterials,
      final Pageable pageable);

  MaterialCommitmentUseCollection findNotProcessedAndExpectedDateIsLessThan(OffsetDateTime time);

  MaterialCommitmentUseCollection findByAnyCompositeId(List<MaterialCommitmentUseCompositeId> compositeIds);
}
