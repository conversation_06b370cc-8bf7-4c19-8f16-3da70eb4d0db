package com.inditex.icdmdemg.domain.shared.audit;

import java.time.OffsetDateTime;

import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;

public interface CompleteAuditMother {

  String CREATED_BY = "CREATED_BY";

  String UPDATED_BY = "UPDATED_BY";

  static CompleteAudit created() {
    final var createdAt = OffsetDateTimeMother.random();
    return CompleteAudit.create(
        CREATED_BY,
        createdAt);
  }

  static CompleteAudit createdWith(final OffsetDateTime createdAt) {
    return CompleteAudit.create(
        CREATED_BY,
        createdAt);
  }

  static CompleteAudit createdWith(final OffsetDateTime createdAt, final String createdBy) {
    return CompleteAudit.create(
        createdBy,
        createdAt);
  }

  static CompleteAudit updated() {
    return new CompleteAudit(
        CREATED_BY,
        OffsetDateTimeMother.random().minusDays(5),
        UPDATED_BY,
        OffsetDateTimeMother.random(),
        null,
        (short) 1);
  }

  static CompleteAudit update(final CompleteAudit from, final String updatedBy, final OffsetDateTime updatedAt) {
    return new CompleteAudit(
        from.createdBy(),
        from.createdAt(),
        updatedBy,
        updatedAt,
        from.deletedAt(),
        from.version());
  }

  static CompleteAudit deleted() {
    return new CompleteAudit(
        CREATED_BY,
        OffsetDateTimeMother.random().minusDays(5),
        UPDATED_BY,
        OffsetDateTimeMother.random().minusDays(3),
        OffsetDateTimeMother.random(),
        (short) 1);
  }
}
