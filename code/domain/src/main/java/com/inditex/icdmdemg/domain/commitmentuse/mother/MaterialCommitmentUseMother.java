package com.inditex.icdmdemg.domain.commitmentuse.mother;

import static com.inditex.icdmdemg.shared.utils.RandomValue.randomDate;
import static com.inditex.icdmdemg.shared.utils.RandomValue.randomPositiveBigDecimal;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Objects;
import java.util.UUID;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeExecutedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeRequestedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseMaterialReferenceId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLine;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseOrderLineId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseQuantity;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseServiceLocalizationId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseServiceLocalizationType;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus.MaterialCommitmentUseStatusEnum;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseTimestamp;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseUseId;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseVersion;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

public interface MaterialCommitmentUseMother {

  String WAREHOUSE = "WAREHOUSE";

  static MaterialCommitmentUse randomExisting() {
    return new MaterialCommitmentUse(
        new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseQuantity(randomPositiveBigDecimal()),
        new MaterialCommitmentUseTimestamp(randomDate()),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(UUID.randomUUID().toString()),
            new MaterialCommitmentUseBudgetId(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new MaterialCommitmentUseOrderLineId(UUID.randomUUID().toString())),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseServiceLocalizationType(WAREHOUSE),
        MaterialCommitmentUseBudgetCycleChangeRequestedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseBudgetCycleChangeExecutedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.now()),
        null);
  }

  static MaterialCommitmentUse withCompositeIdInProgress(final UUID materialOrderId, final UUID materialOrderLineId, final UUID budgetId,
      final UUID materialReferenceId, final UUID useId) {
    return new MaterialCommitmentUse(
        new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId.toString()),
        new MaterialCommitmentUseUseId(useId.toString()),
        new MaterialCommitmentUseQuantity(randomPositiveBigDecimal()),
        new MaterialCommitmentUseTimestamp(randomDate()),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(materialOrderId.toString()),
            new MaterialCommitmentUseBudgetId(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId)),
            new MaterialCommitmentUseOrderLineId(materialOrderLineId.toString())),
        new MaterialCommitmentUseStatus(MaterialCommitmentUseStatusEnum.OPEN.name()),
        new MaterialCommitmentUseServiceLocalizationId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseServiceLocalizationType(WAREHOUSE),
        MaterialCommitmentUseBudgetCycleChangeRequestedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseBudgetCycleChangeExecutedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.now()),
        null);
  }

  static MaterialCommitmentUse withCompositeIdAndStatus(final UUID materialOrderId, final UUID materialOrderLineId, final UUID budgetId,
      final UUID materialReferenceId, final UUID useId, final String status, final String localizationId) {
    return new MaterialCommitmentUse(
        new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId.toString()),
        new MaterialCommitmentUseUseId(useId.toString()),
        new MaterialCommitmentUseQuantity(randomPositiveBigDecimal()),
        new MaterialCommitmentUseTimestamp(randomDate()),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(materialOrderId.toString()),
            new MaterialCommitmentUseBudgetId(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId)),
            new MaterialCommitmentUseOrderLineId(materialOrderLineId.toString())),
        new MaterialCommitmentUseStatus(status),
        new MaterialCommitmentUseServiceLocalizationId(localizationId),
        new MaterialCommitmentUseServiceLocalizationType(WAREHOUSE),
        MaterialCommitmentUseBudgetCycleChangeRequestedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseBudgetCycleChangeExecutedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.now()),
        null);
  }

  static MaterialCommitmentUse withCompositeIdAndQuantity(
      final UUID materialOrderId, final UUID materialOrderLineId,
      final UUID budgetId, final UUID materialReferenceId, final UUID useId,
      final BigDecimal quantity, final String materialSupplierId) {
    return new MaterialCommitmentUse(
        new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId.toString()),
        new MaterialCommitmentUseUseId(useId.toString()),
        new MaterialCommitmentUseQuantity(quantity),
        new MaterialCommitmentUseTimestamp(OffsetDateTime.now()),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(materialOrderId.toString()),
            new MaterialCommitmentUseBudgetId(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId)),
            new MaterialCommitmentUseOrderLineId(materialOrderLineId.toString())),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(materialSupplierId),
        new MaterialCommitmentUseServiceLocalizationType(WAREHOUSE),
        MaterialCommitmentUseBudgetCycleChangeRequestedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseBudgetCycleChangeExecutedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.now()),
        null);
  }

  static MaterialCommitmentUse withBudgetCycleChange(
      final UUID materialOrderId, final UUID materialOrderLineId,
      final UUID materialReferenceId, final UUID useId, final UUID budgetId,
      final BigDecimal quantity) {
    return new MaterialCommitmentUse(
        new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId.toString()),
        new MaterialCommitmentUseUseId(useId.toString()),
        new MaterialCommitmentUseQuantity(quantity),
        new MaterialCommitmentUseTimestamp(OffsetDateTime.now()),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(materialOrderId.toString()),
            new MaterialCommitmentUseBudgetId(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId)),
            new MaterialCommitmentUseOrderLineId(materialOrderLineId.toString())),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseServiceLocalizationType(WAREHOUSE),
        MaterialCommitmentUseBudgetCycleChangeRequestedAt.of(OffsetDateTime.now()),
        null,
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.now()),
        null);
  }

  static MaterialCommitmentUse withCompositeIdAndQuantityAndBudgetCycleChange(
      final UUID materialOrderId, final UUID materialOrderLineId,
      final UUID budgetId, final UUID materialReferenceId, final UUID useId,
      final BigDecimal quantity, final String materialSupplierId) {
    return new MaterialCommitmentUse(
        new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId.toString()),
        new MaterialCommitmentUseUseId(useId.toString()),
        new MaterialCommitmentUseQuantity(quantity),
        new MaterialCommitmentUseTimestamp(OffsetDateTime.now()),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(materialOrderId.toString()),
            new MaterialCommitmentUseBudgetId(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId)),
            new MaterialCommitmentUseOrderLineId(materialOrderLineId.toString())),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(materialSupplierId),
        new MaterialCommitmentUseServiceLocalizationType(WAREHOUSE),
        MaterialCommitmentUseBudgetCycleChangeRequestedAt.of(OffsetDateTime.now()),
        null,
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.now()),
        null);
  }

  static MaterialCommitmentUse with(
      final UUID materialOrderId, final UUID materialOrderLineId,
      final UUID materialReferenceId, final UUID useId, final UUID budgetId,
      final BigDecimal quantity) {
    return new MaterialCommitmentUse(
        new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId.toString()),
        new MaterialCommitmentUseUseId(useId.toString()),
        new MaterialCommitmentUseQuantity(quantity),
        new MaterialCommitmentUseTimestamp(OffsetDateTime.now()),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(materialOrderId.toString()),
            new MaterialCommitmentUseBudgetId(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(budgetId)),
            new MaterialCommitmentUseOrderLineId(materialOrderLineId.toString())),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseServiceLocalizationType(WAREHOUSE),
        null, null,
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.now()),
        null);
  }

  static MaterialCommitmentUse with(
      final Integer materialCommitmentId,
      final BigDecimal quantity,
      final OffsetDateTime expectedDate,
      final Integer useId,
      final Integer materialReferenceId,
      final Integer budgetId,
      final MaterialCommitmentUseStatusEnum materialCommitmentUseStatusEnum,
      final Integer orderId,
      final Integer orderLineId) {
    return new MaterialCommitmentUse(
        new MaterialCommitmentUseId(UuidMother.fromInteger(materialCommitmentId).toString()),
        new MaterialCommitmentUseMaterialReferenceId(UuidMother.fromInteger(materialReferenceId).toString()),
        new MaterialCommitmentUseUseId(UuidMother.fromInteger(useId).toString()),
        new MaterialCommitmentUseQuantity(NumericUtils.roundUpScale2(quantity)),
        new MaterialCommitmentUseTimestamp(expectedDate),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(UuidMother.fromInteger(orderId).toString()),
            new MaterialCommitmentUseBudgetId(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UuidMother.fromInteger(budgetId))),
            new MaterialCommitmentUseOrderLineId(UuidMother.fromInteger(orderLineId).toString())),
        Objects.isNull(materialCommitmentUseStatusEnum) ? null : MaterialCommitmentUseStatus.of(materialCommitmentUseStatusEnum),
        new MaterialCommitmentUseServiceLocalizationId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseServiceLocalizationType(WAREHOUSE),
        MaterialCommitmentUseBudgetCycleChangeRequestedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseBudgetCycleChangeExecutedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.now()),
        null);
  }

  static MaterialCommitmentUse with(
      final Integer materialCommitmentId,
      final BigDecimal quantity,
      final OffsetDateTime expectedDate,
      final Integer useId,
      final Integer materialReferenceId,
      final Integer budgetId,
      final MaterialCommitmentUseStatusEnum materialCommitmentUseStatusEnum,
      final Integer orderId,
      final Integer orderLineId,
      final Integer supplierId) {
    return new MaterialCommitmentUse(
        new MaterialCommitmentUseId(UuidMother.fromInteger(materialCommitmentId).toString()),
        new MaterialCommitmentUseMaterialReferenceId(UuidMother.fromInteger(materialReferenceId).toString()),
        new MaterialCommitmentUseUseId(UuidMother.fromInteger(useId).toString()),
        new MaterialCommitmentUseQuantity(NumericUtils.roundUpScale2(quantity)),
        new MaterialCommitmentUseTimestamp(expectedDate),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(UuidMother.fromInteger(orderId).toString()),
            new MaterialCommitmentUseBudgetId(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UuidMother.fromInteger(budgetId))),
            new MaterialCommitmentUseOrderLineId(UuidMother.fromInteger(orderLineId).toString())),
        Objects.isNull(materialCommitmentUseStatusEnum) ? null : MaterialCommitmentUseStatus.of(materialCommitmentUseStatusEnum),
        new MaterialCommitmentUseServiceLocalizationId(
            UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId))),
        new MaterialCommitmentUseServiceLocalizationType(WAREHOUSE),
        MaterialCommitmentUseBudgetCycleChangeRequestedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseBudgetCycleChangeExecutedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.now()),
        null);
  }

  static MaterialCommitmentUse with(final UUID orderId,
      final UUID orderLineId,
      final UUID materialReferenceId,
      final BigDecimal quantity) {
    return new MaterialCommitmentUse(
        new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(materialReferenceId.toString()),
        new MaterialCommitmentUseUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseQuantity(NumericUtils.roundUpScale2(quantity)),
        new MaterialCommitmentUseTimestamp(randomDate()),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(orderId.toString()),
            new MaterialCommitmentUseBudgetId(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new MaterialCommitmentUseOrderLineId(orderLineId.toString())),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseServiceLocalizationType(WAREHOUSE),
        MaterialCommitmentUseBudgetCycleChangeRequestedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseBudgetCycleChangeExecutedAt.of(OffsetDateTime.now()),
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.now()),
        null);
  }

  static MaterialCommitmentUse with(final String orderId, final String orderLineId, final BigDecimal quantity,
      final OffsetDateTime expectedDate) {
    return new MaterialCommitmentUse(
        new MaterialCommitmentUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseMaterialReferenceId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseUseId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseQuantity(NumericUtils.roundUpScale2(quantity)),
        new MaterialCommitmentUseTimestamp(expectedDate),
        new MaterialCommitmentUseOrderLine(
            new MaterialCommitmentUseOrderId(orderId),
            new MaterialCommitmentUseBudgetId(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new MaterialCommitmentUseOrderLineId(orderLineId)),
        MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN),
        new MaterialCommitmentUseServiceLocalizationId(UUID.randomUUID().toString()),
        new MaterialCommitmentUseServiceLocalizationType(WAREHOUSE),
        null, null,
        MaterialCommitmentUseVersion.firstVersion(),
        BasicAudit.create(OffsetDateTime.now()),
        null);
  }
}
