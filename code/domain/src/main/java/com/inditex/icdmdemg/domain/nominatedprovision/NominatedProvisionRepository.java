package com.inditex.icdmdemg.domain.nominatedprovision;

import java.util.List;

import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.BudgetId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.ProductId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.ReferenceId;
import com.inditex.icdmdemg.domain.nominatedprovision.entity.NominatedProvisionSharedRawMaterial;

import org.jspecify.annotations.NullMarked;

@NullMarked
public interface NominatedProvisionRepository {

  NominatedProvision save(NominatedProvision nominatedProvisionToSave);

  List<NominatedTotalQuantityByUseDTO> findNominatedTotalQuantityByUseDTO(
      final ReferenceId referenceId,
      final List<NominatedTotalQuantityByUseDTO.UseId> uses,
      final BudgetId budgetCycle);

  NominatedProvisionCollection findByAnySharedRawMaterial(
      final List<NominatedProvisionSharedRawMaterial> sharedRawMaterialList);

  NominatedProvisionCollection findByProductsAndReferencesAndBudgets(
      final List<ProductId> referenceProductIds,
      final List<ReferenceId> referenceIds,
      final List<BudgetId> budgetCycles);

  void deleteAllByAnySharedRawMaterial(final List<NominatedProvisionSharedRawMaterial> sharedRawMaterialList);
}
