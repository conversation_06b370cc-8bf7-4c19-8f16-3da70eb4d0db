package com.inditex.icdmdemg.domain.distributionnominated.service;

import java.math.BigDecimal;
import java.util.List;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan.Auto;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;

public class AutomaticDistributionNominatedPlanner extends DistributionNominatedPlanner {

  private final Auto plan;

  public AutomaticDistributionNominatedPlanner(List<Request> requests, List<Line> lines, List<Commitment> commitments,
      List<Line> initialFilteredLines, Auto plan,
      DistributionNominatedPlannerType plannerType) {
    super(requests, lines, commitments, initialFilteredLines, plannerType);
    this.plan = plan;
  }

  @Override
  protected void execute() {
    switch (this.plan) {
      case Auto.Create create -> this.newRequest(create.id(), create.requestedQuantity().value());
      case Auto.Modify modify -> this.modifyRequest(modify.id(), modify.requestedQuantity().value());
    }
  }

  private void newRequest(final Id id, final BigDecimal requested) {
    this.lookUpRequest(id).ifPresent(request -> {
      throw new ErrorException(new BadRequest("Request already exists"));
    });
    if (requested.compareTo(ZERO) <= 0) {
      throw new ErrorException(new BadRequest("Requested should be greater than zero"));
    }
    this.increaseRequest(id, requested);
  }

  private void modifyRequest(final Id id, final BigDecimal requested) {
    final var request = this.lookUpRequest(id).orElseThrow(() -> new ErrorException(new BadRequest("Distribution Nominated Not Found")));
    if (requested.compareTo(ZERO) <= 0) {
      throw new ErrorException(new BadRequest("Requested should be greater than zero"));
    }
    if (requested.compareTo(request.requested()) < 0) {
      this.decreaseRequest(id, request.requested(), requested);
    } else if (requested.compareTo(request.requested()) > 0) {
      this.increaseRequest(id, requested);
    }
  }

  private void increaseRequest(final Id id, final BigDecimal requested) {
    this.allocate(id, requested);
    final var allocated = this.linesRequestedForIdAdjusted(id);

    if (!this.plannerType.equals(DistributionNominatedPlannerType.BUDGET_CYCLE_CHANGE) && requested.compareTo(allocated) != 0) {
      throw new ErrorException(new BadRequest(
          String.format("Insufficient commitment. Only quantity %s can be allocated", allocated)));
    }
  }

  private void decreaseRequest(final Id id, final BigDecimal existingRequested, final BigDecimal requested) {
    this.deallocate(id, existingRequested, requested);
    final var allocated = this.linesRequestedForIdAdjusted(id);

    if (requested.compareTo(allocated) != 0) {
      throw new ErrorException(new BadRequest(
          """
              The quantity decrease could not be processed: the new requested quantity is smaller than the distributed quantity
              or the undistributed lines are insufficient to reduce quantity
              """));
    }
  }

  private void deallocate(final Id id, final BigDecimal existingRequested, final BigDecimal requested) {
    final var requestLines = this.lookUpRequestLines(id);
    final var sortedLines = requestLines.stream().sorted(this.deallocationOrder()).toList();

    final var decreaseRequested = existingRequested.subtract(requested).max(ZERO);

    var deallocated = ZERO;
    for (final var line : sortedLines) {
      final var remaining = decreaseRequested.subtract(deallocated).max(ZERO);
      final var deallocation = line.isDistributed()
          ? ZERO
          : remaining.min(line.requested());
      deallocated = deallocated.add(deallocation);
      this.adjustments.subtract(line.compositeId(), deallocation);
    }
  }
}
