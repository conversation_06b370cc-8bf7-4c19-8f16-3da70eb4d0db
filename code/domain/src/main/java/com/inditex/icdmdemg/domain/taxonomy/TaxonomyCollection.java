package com.inditex.icdmdemg.domain.taxonomy;

import java.io.Serial;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import lombok.Value;

@Value
public class TaxonomyCollection implements ValueObject<Set<Taxonomy>> {

  @Serial
  private static final long serialVersionUID = 1L;

  Set<Taxonomy> set;

  public TaxonomyCollection() {
    this.set = new HashSet<>();
  }

  public TaxonomyCollection(final Set<Taxonomy> set) {
    this.set = new HashSet<>(set);
  }

  @Override
  public Set<Taxonomy> value() {
    return Collections.unmodifiableSet(this.set);
  }

  public void add(final Taxonomy taxonomy) {
    this.set.add(taxonomy);
  }

}
