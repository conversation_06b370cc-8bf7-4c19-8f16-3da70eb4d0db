package com.inditex.icdmdemg.domain.distributioninner;

import java.time.Instant;
import java.util.Arrays;
import java.util.UUID;

import com.inditex.icdmdemg.domain.shared.AggregateDomainEvent;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.experimental.Accessors;

@Getter
@Accessors(fluent = true)
@EqualsAndHashCode
public class DistributionInnerUnifiedEvent implements AggregateDomainEvent<UUID> {

  private static final String DISTRIBUTION_REQUESTED = "DistributionRequested";

  private final DistributionInner distributionInner;

  private final EventType type;

  public DistributionInnerUnifiedEvent(final DistributionInner distributionInner, final EventType type) {
    this.distributionInner = distributionInner;
    this.type = type;
  }

  @Override
  public UUID id() {
    return this.getAggregateId();
  }

  @Override
  public UUID getAggregateId() {
    return this.distributionInner.getId().value();
  }

  @Override
  public String user() {
    return this.distributionInner.audit.updatedBy();
  }

  @Override
  public Instant occurredOn() {
    return this.distributionInner.audit.updatedAt().toInstant();
  }

  @Override
  public String eventType() {
    return this.type.value;
  }

  public enum EventType {
    CREATED_PENDING(DISTRIBUTION_REQUESTED),
    UPDATED_PENDING(DISTRIBUTION_REQUESTED),
    CREATED_NON_DISTRIBUTABLE("DistributionCreated"),
    UPDATED_NON_DISTRIBUTABLE("DistributionUpdated"),
    CORRECTED("DistributionCorrected"),
    DISTRIBUTED("DistributionDistributed"),
    DELETED("DistributionDeleted"),
    PRODUCT_UPDATED("DistributionProductUpdated"),
    CANCELED("DistributionCanceled"),
    CLOSED("DistributionClosed");

    public final String value;

    EventType(final String value) {
      this.value = value;
    }

    public static EventType fromValue(final String value) {
      return Arrays.stream(EventType.values())
          .filter(eventType -> eventType.value.equals(value))
          .findFirst()
          .orElseThrow(() -> new IllegalArgumentException("Unknown event type value: " + value));
    }
  }

}
