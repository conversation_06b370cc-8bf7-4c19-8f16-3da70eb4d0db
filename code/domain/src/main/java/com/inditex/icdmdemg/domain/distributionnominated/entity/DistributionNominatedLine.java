package com.inditex.icdmdemg.domain.distributionnominated.entity;

import static java.math.RoundingMode.HALF_UP;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import lombok.AccessLevel;
import lombok.Builder;
import org.jspecify.annotations.NonNull;

@Builder(access = AccessLevel.PRIVATE, toBuilder = true)
public record DistributionNominatedLine(
    @lombok.NonNull Id id,
    @lombok.NonNull CommitmentOrder commitmentOrder,
    @lombok.NonNull TheoreticalQuantity theoreticalQuantity,
    @lombok.NonNull RequestedQuantity requestedQuantity,
    @lombok.NonNull DistributedQuantity distributedQuantity,
    AlternativeReference alternativeReference,
    DistributionStartDate distributionStartDate,
    @lombok.NonNull BasicAudit audit) {

  public static DistributionNominatedLine create(
      @NonNull final Id id,
      @NonNull final CommitmentOrder commitmentOrder,
      @NonNull final TheoreticalQuantity theoreticalQuantity,
      @NonNull final RequestedQuantity requestedQuantity,
      @NonNull final OffsetDateTime now) {
    return new DistributionNominatedLine(id, commitmentOrder, theoreticalQuantity, requestedQuantity,
        new DistributedQuantity(BigDecimal.ZERO), null, null, new BasicAudit(now, now));
  }

  public static DistributionNominatedLine create(
      @NonNull final Id id,
      @NonNull final CommitmentOrder commitmentOrder,
      @NonNull final RequestedQuantity lineRequestedQuantity,
      final DistributionNominated.@NonNull RequestedQuantity parentRequestedQuantity,
      @NonNull final OffsetDateTime now) {
    final var theoreticalQuantity =
        TheoreticalQuantity.computedBy(lineRequestedQuantity, parentRequestedQuantity);
    return create(id, commitmentOrder, theoreticalQuantity, lineRequestedQuantity, now);
  }

  public DistributionNominatedLine updateRequested(
      @NonNull final RequestedQuantity lineRequestedQuantity,
      final DistributionNominated.@NonNull RequestedQuantity parentRequestedQuantity,
      @NonNull final OffsetDateTime now) {
    final var newTheoreticalQuantity = TheoreticalQuantity.computedBy(lineRequestedQuantity, parentRequestedQuantity);
    if (Objects.equals(lineRequestedQuantity, this.requestedQuantity) && Objects.equals(newTheoreticalQuantity, this.theoreticalQuantity)) {
      return this;
    }
    return new DistributionNominatedLine(this.id, this.commitmentOrder, newTheoreticalQuantity, lineRequestedQuantity,
        this.distributedQuantity, this.alternativeReference, this.distributionStartDate, this.audit.update(now));
  }

  public DistributionNominatedLine updateRequested(
      @NonNull final RequestedQuantity lineRequestedQuantity,
      @NonNull final OffsetDateTime now) {
    if (Objects.equals(lineRequestedQuantity, this.requestedQuantity)) {
      return this;
    }
    return new DistributionNominatedLine(this.id, this.commitmentOrder, this.theoreticalQuantity, lineRequestedQuantity,
        this.distributedQuantity, this.alternativeReference, this.distributionStartDate, this.audit.update(now));
  }

  public DistributionNominatedLine updateAlternative(
      final AlternativeReference.@NonNull ReferenceId alternativeReferenceId,
      final AlternativeReference.@NonNull RequestedQuantity alternativeRequestedQuantity,
      final DistributionNominated.@NonNull RequestedQuantity parentRequestedQuantity,
      @NonNull final OffsetDateTime now) {
    final var newTheoreticalQuantity =
        AlternativeReference.TheoreticalQuantity.computedBy(alternativeRequestedQuantity, parentRequestedQuantity);
    final var newAlternativeReference = new AlternativeReference(
        alternativeReferenceId, alternativeRequestedQuantity, newTheoreticalQuantity);
    if (Objects.equals(newAlternativeReference, this.alternativeReference())) {
      return this;
    }
    final var alternative = alternativeRequestedQuantity.value().compareTo(BigDecimal.ZERO) == 0
        ? null
        : newAlternativeReference;
    return new DistributionNominatedLine(this.id, this.commitmentOrder, this.theoreticalQuantity, this.requestedQuantity,
        this.distributedQuantity, alternative, this.distributionStartDate, this.audit.update(now));
  }

  public DistributionNominatedLine removeAlternative(@NonNull final OffsetDateTime now) {
    if (this.alternativeReference == null) {
      return this;
    }
    return new DistributionNominatedLine(this.id, this.commitmentOrder, this.theoreticalQuantity, this.requestedQuantity,
        this.distributedQuantity, null, this.distributionStartDate, this.audit.update(now));
  }

  public DistributionNominatedLine regularizeAlternative(
      final DistributionNominated.@NonNull RequestedQuantity parentRequestedQuantity,
      @NonNull final OffsetDateTime now) {
    return this
        .updateRequested(new RequestedQuantity(this.requestedQuantity.value().add(this.alternativeReference.requestedQuantity().value())),
            parentRequestedQuantity, now)
        .removeAlternative(now);
  }

  public DistributionNominatedLine updateDistribution(
      @NonNull final DistributedQuantity distributedQuantity,
      @NonNull final DistributionStartDate distributionStartDate,
      @NonNull final OffsetDateTime now) {
    return new DistributionNominatedLine(
        this.id,
        this.commitmentOrder,
        this.theoreticalQuantity,
        this.requestedQuantity,
        distributedQuantity,
        this.alternativeReference,
        distributionStartDate,
        this.audit.update(now));
  }

  public DistributionNominatedLine updateCommitmentSupplierId(final CommitmentOrder.SupplierId supplierId, final OffsetDateTime now) {
    if (!Objects.equals(this.commitmentOrder().supplierId(), supplierId)) {
      return new DistributionNominatedLine(
          this.id,
          new CommitmentOrder(this.commitmentOrder.id(), this.commitmentOrder.lineId(), supplierId),
          this.theoreticalQuantity,
          this.requestedQuantity,
          this.distributedQuantity,
          this.alternativeReference,
          this.distributionStartDate,
          this.audit.update(now));
    }
    return this;
  }

  private static BigDecimal computeLineTheoretical(final BigDecimal lineRequested, final BigDecimal rootRequested) {
    if (Stream.of(lineRequested, rootRequested).anyMatch(value -> value.compareTo(BigDecimal.ZERO) == 0)) {
      return BigDecimal.ZERO.setScale(2, HALF_UP);
    }
    return lineRequested.multiply(BigDecimal.valueOf(100)).divide(rootRequested, HALF_UP)
        .max(BigDecimal.ZERO).min(BigDecimal.valueOf(100));
  }

  public DistributionNominatedLine updateToZeroRequestedQuantity(@NonNull final OffsetDateTime now) {
    return new DistributionNominatedLine(
        this.id,
        this.commitmentOrder,
        new TheoreticalQuantity(BigDecimal.ZERO),
        new RequestedQuantity(BigDecimal.ZERO),
        this.distributedQuantity,
        this.alternativeReference,
        this.distributionStartDate,
        this.audit.update(now));
  }

  public record Id(UUID value) implements ValueObject<UUID> {
  }

  public record TheoreticalQuantity(BigDecimal value) implements ValueObject<BigDecimal> {
    public TheoreticalQuantity {
      value = NumericUtils.roundUpScale2(value);
    }

    public static TheoreticalQuantity computedBy(
        final RequestedQuantity lineRequestedQuantity,
        final DistributionNominated.RequestedQuantity parentRequestedQuantity) {
      return new TheoreticalQuantity(
          computeLineTheoretical(lineRequestedQuantity.value(), parentRequestedQuantity.value()));
    }
  }

  public record RequestedQuantity(BigDecimal value) implements ValueObject<BigDecimal> {
    public RequestedQuantity {
      value = NumericUtils.roundUpScale2(value);
    }

    public static RequestedQuantity zero() {
      return new RequestedQuantity(BigDecimal.ZERO);
    }

    public static RequestedQuantity add(final RequestedQuantity quantity1, final RequestedQuantity quantity2) {
      return new RequestedQuantity(quantity1.value().add(quantity2.value()));
    }
  }

  public record DistributedQuantity(BigDecimal value) implements ValueObject<BigDecimal> {
    public DistributedQuantity {
      value = NumericUtils.roundUpScale2(value);
    }

    public static DistributedQuantity zero() {
      return new DistributedQuantity(BigDecimal.ZERO);
    }

    public static DistributedQuantity add(final DistributedQuantity quantity1, final DistributedQuantity quantity2) {
      return new DistributedQuantity(quantity1.value().add(quantity2.value()));
    }
  }

  public record Quantities(RequestedQuantity requested, DistributedQuantity distributed) {
    public static Quantities zero() {
      return new Quantities(RequestedQuantity.zero(), DistributedQuantity.zero());
    }

    public static Quantities add(final Quantities quantities1, final Quantities quantities2) {
      return new Quantities(
          RequestedQuantity.add(quantities1.requested(), quantities2.requested()),
          DistributedQuantity.add(quantities1.distributed(), quantities2.distributed()));
    }
  }

  public record DistributionStartDate(OffsetDateTime value) implements ValueObject<OffsetDateTime> {
  }

  @Builder(toBuilder = true)
  public record CommitmentOrder(
      @NonNull Id id,
      @NonNull LineId lineId,
      @NonNull SupplierId supplierId) {

    public record Id(UUID value) implements ValueObject<UUID> {
    }

    public record LineId(UUID value) implements ValueObject<UUID> {
    }

    public record SupplierId(@NonNull String value) implements ValueObject<String> {
    }
  }

  @Builder(toBuilder = true)
  public record AlternativeReference(
      @NonNull ReferenceId referenceId,
      @NonNull RequestedQuantity requestedQuantity,
      @NonNull TheoreticalQuantity theoreticalQuantity) {

    public record ReferenceId(@NonNull UUID value) implements ValueObject<UUID> {
    }

    public record RequestedQuantity(@NonNull BigDecimal value) implements ValueObject<BigDecimal> {
      public RequestedQuantity {
        value = NumericUtils.roundUpScale2(value);
      }
    }

    public record TheoreticalQuantity(@NonNull BigDecimal value) implements ValueObject<BigDecimal> {
      public TheoreticalQuantity {
        value = NumericUtils.roundUpScale2(value);
      }

      public static TheoreticalQuantity computedBy(
          final RequestedQuantity lineRequestedQuantity,
          final DistributionNominated.RequestedQuantity parentRequestedQuantity) {
        return new TheoreticalQuantity(
            computeLineTheoretical(lineRequestedQuantity.value(), parentRequestedQuantity.value()));
      }
    }
  }

}
