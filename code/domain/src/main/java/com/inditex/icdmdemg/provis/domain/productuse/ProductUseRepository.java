package com.inditex.icdmdemg.provis.domain.productuse;

import java.util.List;

import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO.BudgetCycle;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO.ProductId;
import com.inditex.icdmdemg.provis.domain.productuse.ProductUseSummaryDTO.ReferenceId;

import org.jspecify.annotations.NullMarked;

@NullMarked
public interface ProductUseRepository {

  List<ProductUseSummaryDTO> findProductUseSummaries(
      List<ProductId> productIds,
      List<ReferenceId> referenceIds,
      List<BudgetCycle> budgetCycles);

  List<ProductUseInnerStockByUseDTO> findProductUseInnerStockByUseDTO(
      ProductUseSummaryDTO.ReferenceId referenceId,
      List<ProductUseInnerStockByUseDTO.UseId> uses,
      ProductUseSummaryDTO.BudgetCycle budgetCycle);
}
