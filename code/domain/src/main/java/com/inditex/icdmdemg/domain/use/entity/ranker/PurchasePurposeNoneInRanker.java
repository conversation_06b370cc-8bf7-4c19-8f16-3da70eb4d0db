package com.inditex.icdmdemg.domain.use.entity.ranker;

import java.util.List;

public interface PurchasePurposeNoneInRanker {

  static PurchasePurposeRankResult evaluate(final List<String> purchasePurposeParameterValues,
      final List<String> usePurchasePurposeConditionValues) {

    final var isComplied = purchasePurposeParameterValues.stream()
        .noneMatch(usePurchasePurposeConditionValues::contains);
    final var numCoincidences = isComplied ? purchasePurposeParameterValues.size() : 0;
    return new PurchasePurposeRankResult(numCoincidences);
  }
}
