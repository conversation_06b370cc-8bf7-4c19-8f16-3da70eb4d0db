package com.inditex.icdmdemg.domain.commitmentuse;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.jspecify.annotations.NonNull;

public record MaterialCommitmentUseCollection(@NonNull List<MaterialCommitmentUse> materialCommitmentUses) {
  public static MaterialCommitmentUseCollection empty() {
    return new MaterialCommitmentUseCollection(Collections.emptyList());
  }

  public List<MaterialCommitmentUse> closed() {
    return this.materialCommitmentUses.stream().filter(materialCommitmentUse -> materialCommitmentUse.getStatus().isClosed()).toList();
  }

  public List<MaterialCommitmentUse> open() {
    return this.materialCommitmentUses.stream().filter(materialCommitmentUse -> materialCommitmentUse.getStatus().isOpen()).toList();
  }

  public List<MaterialCommitmentUse> assignable() {
    return this.materialCommitmentUses.stream().filter(this::isAssignable).toList();
  }

  public boolean isAssignable(final MaterialCommitmentUse materialCommitmentUse) {
    return Objects.nonNull(materialCommitmentUse.getStatus());
  }

  public boolean isEmpty() {
    return this.materialCommitmentUses.isEmpty();
  }

}
