package com.inditex.icdmdemg.domain.use.mother;

import java.time.OffsetDateTime;
import java.util.List;

import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.domain.use.entity.UseName;
import com.inditex.icdmdemg.domain.use.entity.UseName.Lang;
import com.inditex.icdmdemg.domain.use.entity.UseName.Name;
import com.inditex.icdmdemg.domain.use.entity.UseNames;

public interface UseNamesMother {

  static UseNames generateNames(final OffsetDateTime now) {
    return new UseNames(List.of(
        new UseName(new Name("descripcion uso"), new Lang("es"), new BasicAudit(now, now)),
        new UseName(new Name("use description"), new Lang("en"), new BasicAudit(now, now))));
  }

  static UseNames generateTreeNames(final OffsetDateTime now) {
    return new UseNames(List.of(
        new UseName(new Name("descripcion uso"), new Lang("es"), new BasicAudit(now, now)),
        new UseName(new Name("descripcio do usinho"), new Lang("pt"), new BasicAudit(now, now)),
        new UseName(new Name("use description"), new Lang("en"), new BasicAudit(now, now))));
  }

  static UseNames defaultUseNames() {
    return new UseNames(
        List.of(
            new UseName(new Name("Nombre_1"), new Lang("es"), new BasicAudit(OffsetDateTime.now(), OffsetDateTime.now())),
            new UseName(new Name("Name_1"), new Lang("en"), new BasicAudit(OffsetDateTime.now(), OffsetDateTime.now()))));
  }
}
