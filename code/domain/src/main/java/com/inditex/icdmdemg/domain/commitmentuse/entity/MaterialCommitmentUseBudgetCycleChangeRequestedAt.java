package com.inditex.icdmdemg.domain.commitmentuse.entity;

import java.time.OffsetDateTime;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record MaterialCommitmentUseBudgetCycleChangeRequestedAt(@NonNull OffsetDateTime value) implements ValueObject<OffsetDateTime> {
  public static MaterialCommitmentUseBudgetCycleChangeRequestedAt of(final OffsetDateTime date) {
    return new MaterialCommitmentUseBudgetCycleChangeRequestedAt(date);
  }
}
