package com.inditex.icdmdemg.domain.use.entity;

import java.util.List;
import java.util.Locale;
import java.util.Optional;
import java.util.stream.Collectors;

import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record UseNames(List<UseName> value) implements ValueObject<List<UseName>> {

  private static final List<String> MANDATORY_LANGUAGES = List.of("en", "es");

  private static final Locale DEFAULT_LOCALE = Locale.ENGLISH;

  public UseNames(final List<UseName> value) {
    final var languages = value.stream()
        .map(useName -> useName.lang().value())
        .collect(Collectors.toSet());

    if (!languages.containsAll(MANDATORY_LANGUAGES)) {
      throw new ErrorException(new BadRequest("Languages must contain 'es' and 'en'"));
    }

    this.value = List.copyOf(value);
  }

  public String getNameTranslated(final Locale locale) {
    final var useName = this.getUseNameByLocale(locale).orElseGet(this::getDefaultLocale);

    return useName.name().value();
  }

  private Optional<UseName> getUseNameByLocale(final Locale locale) {
    return this.value.stream().filter(it -> it.lang().value().equalsIgnoreCase(locale.getLanguage())).findFirst();
  }

  private UseName getDefaultLocale() {
    return this.getUseNameByLocale(DEFAULT_LOCALE)
        .orElseThrow(() -> new ErrorException(new BadRequest("Not exist language by default to translate Use")));
  }

}
