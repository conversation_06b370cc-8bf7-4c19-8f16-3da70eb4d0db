package com.inditex.icdmdemg.domain.shipmentwarehouse.mother;

import static com.inditex.icdmdemg.shared.utils.RandomValue.randomDate;
import static com.inditex.icdmdemg.shared.utils.RandomValue.randomString;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.inditex.icdmdemg.domain.shipmentwarehouse.ShipmentWarehouse;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseId;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseInner;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseInnerId;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseInnerLineId;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseQuantity;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTimestamp;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTrackingCode;
import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseVersion;
import com.inditex.icdmdemg.shared.utils.Nullables;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

public interface ShipmentWarehouseMother {

  static ShipmentWarehouse of(final ShipmentWarehouse shipmentWarehouse) {
    return new ShipmentWarehouse(
        shipmentWarehouse.getId(),
        shipmentWarehouse.getTrackingCode(),
        shipmentWarehouse.getDistributionInner(),
        shipmentWarehouse.getQuantity(),
        shipmentWarehouse.getStartDate(),
        shipmentWarehouse.getEndDate(),
        shipmentWarehouse.getLastUpdateDate(),
        shipmentWarehouse.getVersion(),
        shipmentWarehouse.getCreatedAt(),
        shipmentWarehouse.getUpdatedAt());
  }

  static ShipmentWarehouse with(final int trackingCode, final ShipmentWarehouseInner distributionInner, final BigDecimal quantity,
      final OffsetDateTime startDate, final OffsetDateTime endDate) {
    return new ShipmentWarehouse(
        new ShipmentWarehouseId(UUID.randomUUID().toString()),
        new ShipmentWarehouseTrackingCode(String.valueOf(UuidMother.fromInteger(trackingCode))),
        distributionInner,
        new ShipmentWarehouseQuantity(quantity),
        Nullables.acceptNullElseMap(startDate, ShipmentWarehouseTimestamp::of),
        Nullables.acceptNullElseMap(endDate, ShipmentWarehouseTimestamp::of),
        ShipmentWarehouseTimestamp.of(OffsetDateTime.MAX),
        ShipmentWarehouseVersion.firstVersion(),
        ShipmentWarehouseTimestamp.of(OffsetDateTime.MAX),
        ShipmentWarehouseTimestamp.of(OffsetDateTime.MAX));
  }

  static ShipmentWarehouse with(
      final ShipmentWarehouseQuantity shipmentWarehouseQuantity) {
    return new ShipmentWarehouse(
        new ShipmentWarehouseId(UUID.randomUUID().toString()),
        new ShipmentWarehouseTrackingCode(randomString()),
        new ShipmentWarehouseInner(
            new ShipmentWarehouseInnerId(UUID.randomUUID().toString()),
            new ShipmentWarehouseInnerLineId(UUID.randomUUID().toString())),
        shipmentWarehouseQuantity,
        new ShipmentWarehouseTimestamp(randomDate()),
        null,
        new ShipmentWarehouseTimestamp(randomDate()),
        ShipmentWarehouseVersion.firstVersion(),
        new ShipmentWarehouseTimestamp(randomDate()),
        new ShipmentWarehouseTimestamp(randomDate()));
  }

  static ShipmentWarehouse with(
      final ShipmentWarehouseTimestamp shipmentStartDate,
      final ShipmentWarehouseQuantity shipmentWarehouseQuantity) {
    return new ShipmentWarehouse(
        new ShipmentWarehouseId(UUID.randomUUID().toString()),
        new ShipmentWarehouseTrackingCode(randomString()),
        new ShipmentWarehouseInner(
            new ShipmentWarehouseInnerId(UUID.randomUUID().toString()),
            new ShipmentWarehouseInnerLineId(UUID.randomUUID().toString())),
        shipmentWarehouseQuantity,
        shipmentStartDate,
        null,
        new ShipmentWarehouseTimestamp(randomDate()),
        ShipmentWarehouseVersion.firstVersion(),
        new ShipmentWarehouseTimestamp(randomDate()),
        new ShipmentWarehouseTimestamp(randomDate()));
  }

  static ShipmentWarehouse with(
      final ShipmentWarehouseInner distributionInner,
      final ShipmentWarehouseTrackingCode shipmentWarehouseTrackingCode,
      final ShipmentWarehouseTimestamp endDate) {
    return new ShipmentWarehouse(
        new ShipmentWarehouseId(UUID.randomUUID().toString()),
        shipmentWarehouseTrackingCode,
        distributionInner,
        new ShipmentWarehouseQuantity(NumericUtils.roundUpScale2(BigDecimal.ZERO)),
        new ShipmentWarehouseTimestamp(OffsetDateTime.MAX),
        endDate, null,
        ShipmentWarehouseVersion.firstVersion(),
        ShipmentWarehouseTimestamp.of(OffsetDateTime.MAX),
        ShipmentWarehouseTimestamp.of(OffsetDateTime.MAX));
  }

  static ShipmentWarehouse with(
      final ShipmentWarehouseInner distributionInner,
      final ShipmentWarehouseTrackingCode shipmentWarehouseTrackingCode,
      final ShipmentWarehouseTimestamp startDate,
      final ShipmentWarehouseTimestamp endDate) {
    return new ShipmentWarehouse(
        new ShipmentWarehouseId(UUID.randomUUID().toString()),
        shipmentWarehouseTrackingCode,
        distributionInner,
        new ShipmentWarehouseQuantity(NumericUtils.roundUpScale2(BigDecimal.ZERO)),
        startDate,
        endDate, null,
        ShipmentWarehouseVersion.firstVersion(),
        ShipmentWarehouseTimestamp.of(OffsetDateTime.MAX),
        ShipmentWarehouseTimestamp.of(OffsetDateTime.MAX));
  }

  static ShipmentWarehouse randomStarted() {
    return new ShipmentWarehouse(
        new ShipmentWarehouseId(UUID.randomUUID().toString()),
        new ShipmentWarehouseTrackingCode(randomString()),
        new ShipmentWarehouseInner(
            new ShipmentWarehouseInnerId(UUID.randomUUID().toString()),
            new ShipmentWarehouseInnerLineId(UUID.randomUUID().toString())),
        new ShipmentWarehouseQuantity(NumericUtils.roundUpScale2(BigDecimal.ZERO)),
        new ShipmentWarehouseTimestamp(OffsetDateTime.MAX),
        null, null,
        ShipmentWarehouseVersion.firstVersion(),
        ShipmentWarehouseTimestamp.of(OffsetDateTime.MAX),
        ShipmentWarehouseTimestamp.of(OffsetDateTime.MAX));
  }

  static ShipmentWarehouse randomCreated() {
    return new ShipmentWarehouse(
        new ShipmentWarehouseId(UUID.randomUUID().toString()),
        new ShipmentWarehouseTrackingCode(randomString()),
        new ShipmentWarehouseInner(
            new ShipmentWarehouseInnerId(UUID.randomUUID().toString()),
            new ShipmentWarehouseInnerLineId(UUID.randomUUID().toString())),
        new ShipmentWarehouseQuantity(NumericUtils.roundUpScale2(BigDecimal.ZERO)),
        null, null, null,
        ShipmentWarehouseVersion.firstVersion(),
        ShipmentWarehouseTimestamp.of(OffsetDateTime.MAX),
        ShipmentWarehouseTimestamp.of(OffsetDateTime.MAX));
  }

}
