package com.inditex.icdmdemg.domain.commitmentuse.entity;

import java.util.Comparator;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record MaterialCommitmentUseUniqueKey(
    @NonNull String orderId,
    @NonNull String orderLineId,
    @NonNull String materialReferenceId,
    @NonNull String budgetId,
    @NonNull String useId) implements ValueObject<MaterialCommitmentUseUniqueKey>, Comparable<MaterialCommitmentUseUniqueKey> {

  public static MaterialCommitmentUseUniqueKey fromMaterialCommitmentUse(final MaterialCommitmentUse materialCommitment) {
    return new MaterialCommitmentUseUniqueKey(materialCommitment.getOrderLine().orderId().value(),
        materialCommitment.getOrderLine().orderLineId().value(),
        materialCommitment.getMaterialReferenceId().value(),
        materialCommitment.getOrderLine().budgetId().value(),
        materialCommitment.getUseId().value());
  }

  public MaterialCommitmentUseUniqueKey value() {
    return this;
  }

  @Override
  public int compareTo(final @NonNull MaterialCommitmentUseUniqueKey o) {
    return Comparator.comparing(MaterialCommitmentUseUniqueKey::orderId)
        .thenComparing(MaterialCommitmentUseUniqueKey::orderLineId)
        .thenComparing(MaterialCommitmentUseUniqueKey::materialReferenceId)
        .thenComparing(MaterialCommitmentUseUniqueKey::budgetId)
        .thenComparing(MaterialCommitmentUseUniqueKey::useId)
        .compare(this, o);
  }

}
