package com.inditex.icdmdemg.domain.commitmentadjusted;

import java.math.BigDecimal;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUse;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;

public record CommitmentAdjusted(
    MaterialCommitmentUse materialCommitmentUse,
    BigDecimal adjustedQuantity) {

  public static CommitmentAdjusted of(final MaterialCommitmentUse entity, final BigDecimal partialQuantity) {
    return new CommitmentAdjusted(entity, partialQuantity);
  }

  public MaterialCommitmentUseSharedRawMaterial sharedRawMaterial() {
    return this.materialCommitmentUse.sharedRawMaterial();
  }
}
