package com.inditex.icdmdemg.domain.shipmentcommitment.entity;

import java.util.UUID;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record ShipmentCommitmentId(@NonNull String value) implements ValueObject<String> {

  public static ShipmentCommitmentId of(@NonNull final UUID value) {
    return new ShipmentCommitmentId(String.valueOf(value));
  }

}
