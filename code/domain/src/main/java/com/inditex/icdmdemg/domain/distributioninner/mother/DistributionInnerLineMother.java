package com.inditex.icdmdemg.domain.distributioninner.mother;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.DistributionEndDate;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.Id;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.shared.utils.mother.BigDecimalMother;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;

public interface DistributionInnerLineMother {

  static Builder created() {
    return random()
        .distributedQuantity(new DistributedQuantity(BigDecimalMother.zero()))
        .trackingCode(null)
        .distributionStartDate(null)
        .distributionEndDate(null);
  }

  static Builder createdOf(
      Id id,
      TheoreticalQuantity theoreticalQuantity,
      RequestedQuantity requestedQuantity,
      OffsetDateTime occurredAt) {
    return new Builder()
        .id(id)
        .trackingCode(null)
        .theoreticalQuantity(theoreticalQuantity)
        .requestedQuantity(requestedQuantity)
        .distributedQuantity(new DistributedQuantity(BigDecimalMother.zero()))
        .distributionStartDate(null)
        .distributionEndDate(null)
        .audit(new BasicAudit(occurredAt, occurredAt));
  }

  static Builder startedButNotDistributed() {
    return random()
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO))
        .requestedQuantity(randomRequestedQuantity())
        .distributionStartDate(randomDistributionStartDate())
        .distributionEndDate(null);
  }

  static Builder inProgress() {
    return random()
        .distributedQuantity(randomDistributedQuantity())
        .requestedQuantity(randomRequestedQuantity())
        .distributionStartDate(randomDistributionStartDate())
        .distributionEndDate(null);
  }

  static Builder sent() {
    return random()
        .distributedQuantity(randomDistributedQuantity())
        .requestedQuantity(randomRequestedQuantity())
        .distributionStartDate(randomDistributionStartDate())
        .distributionEndDate(randomDistributionEndDate());
  }

  private static Builder random() {
    return new Builder()
        .id(randomId())
        .trackingCode(randomTrackingCode())
        .theoreticalQuantity(randomTheoreticalQuantity())
        .requestedQuantity(randomRequestedQuantity())
        .distributedQuantity(randomDistributedQuantity())
        .distributionStartDate(randomDistributionStartDate())
        .distributionEndDate(randomDistributionEndDate())
        .audit(randomAudit());
  }

  static Builder distributed() {
    return random().distributionStartDate(randomDistributionStartDate())
        .distributedQuantity(new DistributedQuantity(BigDecimal.valueOf(1).setScale(2, RoundingMode.HALF_UP)));
  }

  static DistributionInnerLine.Id randomId() {
    return new DistributionInnerLine.Id(UUID.randomUUID());
  }

  static DistributionInnerLine.TrackingCode randomTrackingCode() {
    return new DistributionInnerLine.TrackingCode(BigDecimalMother.random().toPlainString());
  }

  static DistributionInnerLine.TheoreticalQuantity randomTheoreticalQuantity() {
    return new DistributionInnerLine.TheoreticalQuantity(BigDecimalMother.random());
  }

  static DistributionInnerLine.RequestedQuantity randomRequestedQuantity() {
    return new DistributionInnerLine.RequestedQuantity(BigDecimalMother.random());
  }

  static DistributionInnerLine.DistributedQuantity randomDistributedQuantity() {
    return new DistributionInnerLine.DistributedQuantity(BigDecimalMother.random());
  }

  static DistributionInnerLine.DistributionStartDate randomDistributionStartDate() {
    return new DistributionInnerLine.DistributionStartDate(OffsetDateTimeMother.random());
  }

  static DistributionInnerLine.DistributionEndDate randomDistributionEndDate() {
    return new DistributionEndDate(OffsetDateTimeMother.random());
  }

  static BasicAudit randomAudit() {
    final var offsetDateTime = OffsetDateTimeMother.random();
    return new BasicAudit(offsetDateTime, offsetDateTime);
  }

  static BasicAudit updateAudit(final BasicAudit from, final OffsetDateTime updatedAt) {
    return new BasicAudit(
        from.createdAt(),
        updatedAt);
  }

  static BasicAudit createAudit(final OffsetDateTime createdAt) {
    return new BasicAudit(createdAt, createdAt);
  }

  class Builder {

    DistributionInnerLine.Id id;

    DistributionInnerLine.TrackingCode trackingCode;

    DistributionInnerLine.TheoreticalQuantity theoreticalQuantity;

    DistributionInnerLine.RequestedQuantity requestedQuantity;

    DistributionInnerLine.DistributedQuantity distributedQuantity;

    DistributionInnerLine.DistributionStartDate distributionStartDate;

    DistributionInnerLine.DistributionEndDate distributionEndDate;

    BasicAudit audit;

    public Builder id(final DistributionInnerLine.Id id) {
      this.id = id;
      return this;
    }

    public Builder trackingCode(final DistributionInnerLine.TrackingCode trackingCode) {
      this.trackingCode = trackingCode;
      return this;
    }

    public Builder theoreticalQuantity(final DistributionInnerLine.TheoreticalQuantity theoreticalQuantity) {
      this.theoreticalQuantity = theoreticalQuantity;
      return this;
    }

    public Builder requestedQuantity(final DistributionInnerLine.RequestedQuantity requestedQuantity) {
      this.requestedQuantity = requestedQuantity;
      return this;
    }

    public Builder distributedQuantity(final DistributionInnerLine.DistributedQuantity distributedQuantity) {
      this.distributedQuantity = distributedQuantity;
      return this;
    }

    public Builder distributionStartDate(final DistributionInnerLine.DistributionStartDate distributionStartDate) {
      this.distributionStartDate = distributionStartDate;
      return this;
    }

    public Builder distributionEndDate(final DistributionInnerLine.DistributionEndDate distributionEndDate) {
      this.distributionEndDate = distributionEndDate;
      return this;
    }

    public Builder audit(final BasicAudit audit) {
      this.audit = audit;
      return this;
    }

    public DistributionInnerLine build() {
      return new DistributionInnerLine(
          this.id,
          this.trackingCode,
          this.theoreticalQuantity,
          this.requestedQuantity,
          this.distributedQuantity,
          this.distributionStartDate,
          this.distributionEndDate,
          this.audit);
    }
  }

}
