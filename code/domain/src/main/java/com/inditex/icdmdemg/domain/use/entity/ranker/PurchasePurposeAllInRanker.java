package com.inditex.icdmdemg.domain.use.entity.ranker;

import java.util.HashSet;
import java.util.List;

public interface PurchasePurposeAllInRanker {
  static PurchasePurposeRankResult evaluate(final List<String> purchasePurposeParameterValues,
      final List<String> usePurchasePurposeConditionValues) {

    final var isComplied = new HashSet<>(purchasePurposeParameterValues)
        .containsAll(usePurchasePurposeConditionValues);
    final var numCoincidences = isComplied ? usePurchasePurposeConditionValues.size() : 0;
    return new PurchasePurposeRankResult(numCoincidences);
  }
}
