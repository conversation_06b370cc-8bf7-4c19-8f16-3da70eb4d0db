package com.inditex.icdmdemg.domain.nominatedprovision;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record NominatedTotalQuantityByUseDTO(
    UseId useId,
    TotalQuantity totalQuantity) {

  public record TotalQuantity(BigDecimal value) implements ValueObject<BigDecimal> {

    public TotalQuantity(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }
  }

  public record UseId(UUID value) implements ValueObject<UUID> {

  }
}
