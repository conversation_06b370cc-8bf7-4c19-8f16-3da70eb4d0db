package com.inditex.icdmdemg.domain.distributionnominated.entity;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.DistributionStartDate;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.Id;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.Quantities;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record DistributionNominatedLines(List<DistributionNominatedLine> value) implements ValueObject<List<DistributionNominatedLine>> {

  public boolean willBeUpdatedLinesOnReplace(final List<DistributionNominatedLine> lines) {
    final var currentSet = new HashSet<>(this.value);
    final var newSet = new HashSet<>(lines);
    return !Objects.equals(currentSet, newSet);
  }

  public @NonNull DistributionNominatedLines replaceLines(final List<DistributionNominatedLine> lines) {
    return new DistributionNominatedLines(lines);
  }

  public @NonNull DistributionNominatedLines updateLineDistribution(
      final Id distributionNominatedLineId,
      final DistributedQuantity distributedQuantity,
      final DistributionStartDate distributionStartDate,
      final OffsetDateTime now) {
    final var updated = this.value.stream()
        .map(line -> line.id().equals(distributionNominatedLineId)
            ? line.updateDistribution(distributedQuantity, distributionStartDate, now)
            : line)
        .toList();
    return this.replaceLines(updated);
  }

  public DistributionNominated.DistributedQuantity calculateTotalDistributedQuantity() {
    final var quantities = this.value.stream()
        .map(line -> line.distributedQuantity().value())
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    return new DistributionNominated.DistributedQuantity(quantities);
  }

  public @NonNull BigDecimal calculateTotalRequestedQuantity() {
    return this.value.stream()
        .map(line -> line.requestedQuantity().value())
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  public DistributionNominatedLines updateToZeroRequestedQuantityOfAllLines(@NonNull final OffsetDateTime now) {
    final var updatedNominatedLines = this.value.stream()
        .map(distributionNominatedLine -> distributionNominatedLine.updateToZeroRequestedQuantity(now))
        .toList();
    return this.replaceLines(updatedNominatedLines);
  }

  public Optional<DistributionNominatedLine> getLineByCommitmentOrder(final CommitmentOrder commitmentOrder) {
    return this.value().stream()
        .filter(line -> line.commitmentOrder().equals(commitmentOrder))
        .findFirst();
  }

  public Optional<DistributionNominatedLine> getLineById(final Id lineId) {
    return this.value().stream()
        .filter(line -> line.id().equals(lineId))
        .findFirst();
  }

  public @NonNull DistributionNominatedLines updateLine(
      final Id lineIdToUpdate,
      final DistributionNominatedLine updatedLine) {

    final var updated = this.value.stream()
        .map(line -> line.id().equals(lineIdToUpdate)
            ? updatedLine
            : line)
        .toList();
    return this.replaceLines(updated);
  }

  public @NonNull DistributionNominatedLines closeLines(@NonNull final OffsetDateTime now) {
    final List<DistributionNominatedLine> linesUpdated = this.value().stream()
        .map(line -> line.updateRequested(new DistributionNominatedLine.RequestedQuantity(line.distributedQuantity().value()), now))
        .toList();
    return this.replaceLines(linesUpdated);
  }

  public @NonNull Quantities quantitiesFromCommitmentOrders(@NonNull final List<CommitmentOrder> commitmentsOrders) {
    return this.value().stream()
        .filter(line -> commitmentsOrders.contains(line.commitmentOrder()))
        .map(line -> new Quantities(line.requestedQuantity(), line.distributedQuantity()))
        .reduce(Quantities.zero(), Quantities::add);
  }

  public @NonNull DistributionNominatedLines updateSupplierInLinesWithCommitmentOrder(
      final CommitmentOrder.Id commitmentOrderId,
      final LineId commitmentOrderLineId,
      final SupplierId supplierId, final OffsetDateTime now) {

    final List<DistributionNominatedLine> updatedLines = this.value().stream()
        .map(line -> {
          if (line.commitmentOrder().id().equals(commitmentOrderId)
              && line.commitmentOrder().lineId().equals(commitmentOrderLineId)) {
            return line.updateCommitmentSupplierId(supplierId, now);
          }
          return line;
        })
        .toList();

    return new DistributionNominatedLines(updatedLines);
  }
}
