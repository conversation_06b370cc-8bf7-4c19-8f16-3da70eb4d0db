package com.inditex.icdmdemg.domain.distributionnominated;

import static com.inditex.icdmdemg.shared.utils.BooleanOperator.or;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLines;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedPlan;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedStatus;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.iopcmmnt.ddd.core.AggregateRoot;
import com.inditex.iopcmmnt.ddd.core.AggregateRootId;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.jspecify.annotations.NonNull;
import org.jspecify.annotations.Nullable;

@Getter
@ToString
@Accessors(fluent = true)
@EqualsAndHashCode(callSuper = true)
public class DistributionNominated extends AggregateRoot<Id> {

  @NonNull
  ReferenceId referenceId;

  @NonNull
  final UseId useId;

  @NonNull
  BudgetCycle budgetCycle;

  @NonNull
  ReferenceProductId referenceProductId;

  @NonNull
  final ProductOrderId productOrderId;

  @NonNull
  ProductSupplierId productSupplierId;

  @NonNull
  ProductVariantGroupId productVariantGroupId;

  @NonNull
  TheoreticalQuantity theoreticalQuantity;

  @NonNull
  ConsumptionFactor consumptionFactor;

  @NonNull
  RequestedQuantity requestedQuantity;

  @NonNull
  DistributedQuantity distributedQuantity;

  @NonNull
  BudgetCycleChangePendingQuantity budgetCycleChangePendingQuantity;

  @NonNull
  DistributionNominatedStatus status;

  @NonNull
  DistributionNominatedPlan plan;

  @NonNull
  DistributionNominatedLines lines;

  @NonNull
  CompleteAudit audit;

  public DistributionNominated(
      final <EMAIL> Id id,
      @lombok.NonNull final ReferenceId referenceId,
      @lombok.NonNull final UseId useId,
      @lombok.NonNull final BudgetCycle budgetCycle,
      @lombok.NonNull final ReferenceProductId referenceProductId,
      @lombok.NonNull final ProductOrderId productOrderId,
      @lombok.NonNull final ProductSupplierId productSupplierId,
      @lombok.NonNull final ProductVariantGroupId productVariantGroupId,
      @lombok.NonNull final TheoreticalQuantity theoreticalQuantity,
      @lombok.NonNull final ConsumptionFactor consumptionFactor,
      @lombok.NonNull final RequestedQuantity requestedQuantity,
      @lombok.NonNull final DistributedQuantity distributedQuantity,
      @lombok.NonNull final BudgetCycleChangePendingQuantity budgetCycleChangePendingQuantity,
      @lombok.NonNull final DistributionNominatedStatus status,
      @lombok.NonNull final DistributionNominatedPlan plan,
      @lombok.NonNull final DistributionNominatedLines lines,
      @lombok.NonNull final CompleteAudit audit) {
    super(id);
    this.referenceId = referenceId;
    this.useId = useId;
    this.budgetCycle = budgetCycle;
    this.referenceProductId = referenceProductId;
    this.productOrderId = productOrderId;
    this.productSupplierId = productSupplierId;
    this.productVariantGroupId = productVariantGroupId;
    this.theoreticalQuantity = theoreticalQuantity;
    this.consumptionFactor = consumptionFactor;
    this.requestedQuantity = requestedQuantity;
    this.distributedQuantity = distributedQuantity;
    this.budgetCycleChangePendingQuantity = budgetCycleChangePendingQuantity;
    this.status = status;
    this.plan = plan;
    this.lines = lines;
    this.audit = audit;
  }

  public static DistributionNominated create(
      @NonNull final Id id,
      @NonNull final ReferenceId referenceId,
      @NonNull final UseId useId,
      @NonNull final BudgetCycle budgetCycle,
      @NonNull final ReferenceProductId referenceProductId,
      @NonNull final ProductOrderId productOrderId,
      @NonNull final ProductSupplierId productSupplierId,
      @NonNull final ProductVariantGroupId productVariantGroupId,
      @NonNull final ProductOrderStatusInfo productOrderStatusInfo,
      @NonNull final TheoreticalQuantity theoreticalQuantity,
      @NonNull final ConsumptionFactor consumptionFactor,
      @NonNull final RequestedQuantity requestedQuantity,
      @NonNull final DistributionNominatedLines lines,
      @NonNull final DistributionNominatedPlan plan,
      @NonNull final OffsetDateTime occurredOn,
      @NonNull final String triggeredBy) {

    final var distributed = new DistributedQuantity(BigDecimal.ZERO);
    final var budgetCycleChangePendingQuantity = new BudgetCycleChangePendingQuantity(BigDecimal.ZERO);

    final var distributionNominated = new DistributionNominated(
        id,
        referenceId,
        useId,
        budgetCycle,
        referenceProductId,
        productOrderId,
        productSupplierId,
        productVariantGroupId,
        theoreticalQuantity,
        consumptionFactor,
        requestedQuantity,
        distributed,
        budgetCycleChangePendingQuantity,
        DistributionNominatedStatus.of(productOrderStatusInfo, distributed),
        plan,
        lines,
        CompleteAudit.create(triggeredBy, occurredOn));
    distributionNominated.eventRegistrar().register(EventType.CREATED);
    return distributionNominated;
  }

  public DistributionNominated adjust(@NonNull final List<DistributionNominatedLine> lines,
      @NonNull final OffsetDateTime occurredOn,
      @NonNull final String triggeredBy) {
    if (this.lines.willBeUpdatedLinesOnReplace(lines)) {
      this.audit = this.audit().update(triggeredBy, occurredOn);
      this.lines = this.lines().replaceLines(lines);
      this.eventRegistrar().register(EventType.UPDATED);
    }
    return this;
  }

  public DistributionNominated update(
      @NonNull final TheoreticalQuantity theoreticalQuantity,
      @NonNull final ConsumptionFactor consumptionFactor,
      @NonNull final RequestedQuantity requestedQuantity,
      @NonNull final List<DistributionNominatedLine> lines,
      @NonNull final DistributionNominatedPlan plan,
      @NonNull final OffsetDateTime occurredOn,
      @NonNull final String triggeredBy) {
    final var isRequestedOrLinesOrPlanModified =
        !Objects.equals(this.requestedQuantity(), requestedQuantity)
            || this.lines.willBeUpdatedLinesOnReplace(lines) || !Objects.equals(this.plan(), plan);

    final var isModified = Stream.of(
        isRequestedOrLinesOrPlanModified,
        !Objects.equals(this.consumptionFactor(), consumptionFactor),
        !Objects.equals(this.theoreticalQuantity(), theoreticalQuantity))
        .reduce(false, or());

    if (isModified) {
      this.plan = plan;
      this.audit = this.audit().update(triggeredBy, occurredOn);
      this.theoreticalQuantity = theoreticalQuantity;
      this.consumptionFactor = consumptionFactor;
      this.requestedQuantity = requestedQuantity;
      this.lines = this.lines().replaceLines(lines);
      if (isRequestedOrLinesOrPlanModified && this.status().isNotClosed()) {
        this.eventRegistrar().register(EventType.UPDATED);
      } else {
        this.eventRegistrar().register(EventType.CORRECTED);
      }
    }

    return this;
  }

  public DistributionNominated updateProductSupplierId(
      @NonNull final ProductSupplierId productSupplierId,
      @NonNull final OffsetDateTime now,
      @NonNull final String triggeredBy) {

    if (this.isProductSupplierModified(productSupplierId)) {
      this.productSupplierId = productSupplierId;
      this.audit.update(triggeredBy, now);
      this.eventRegistrar().register(EventType.UPDATED);
    }

    return this;
  }

  public DistributionNominated updateBudgetCycle(
      @NonNull final BudgetCycle budgetCycle,
      @NonNull final OffsetDateTime now,
      @NonNull final String triggeredBy) {

    if (this.isBudgetCycleModified(budgetCycle)) {
      this.budgetCycle = budgetCycle;

      this.budgetCycleChangePendingQuantity = new BudgetCycleChangePendingQuantity(this.requestedQuantity.value);

      this.lines = this.lines().updateToZeroRequestedQuantityOfAllLines(now);

      this.audit.update(triggeredBy, now);
      this.eventRegistrar().register(EventType.UPDATED);
    }

    return this;
  }

  public boolean isDistributionNominatedModified(final ProductSupplierId productSupplierId, final BudgetCycle budgetCycle) {
    return this.isProductSupplierModified(productSupplierId) || this.isBudgetCycleModified(budgetCycle);
  }

  private boolean isBudgetCycleModified(final BudgetCycle budgetCycle) {
    return !Objects.equals(this.budgetCycle(), budgetCycle);
  }

  private boolean isProductSupplierModified(final ProductSupplierId productSupplierId) {
    return !Objects.equals(this.productSupplierId(), productSupplierId);
  }

  public DistributionNominated updateWithOrderState(
      @NonNull final ProductOrderStatusInfo productOrderStatusInfo,
      @NonNull final OffsetDateTime occurredOn,
      @NonNull final String triggeredBy,
      @NonNull final DistributionNominatedCloseConfigProvider closeConfigProvider) {
    return productOrderStatusInfo.isCancelled() || productOrderStatusInfo.isDeleted()
        ? this.deleteNoValidation(occurredOn, triggeredBy)
        : this.calculateNewStatus(productOrderStatusInfo, occurredOn, triggeredBy, closeConfigProvider);
  }

  private DistributionNominated calculateNewStatus(final ProductOrderStatusInfo productOrderStatusInfo, final OffsetDateTime occurredOn,
      final String triggeredBy, final DistributionNominatedCloseConfigProvider closeConfigProvider) {
    final var newStatus = this.status().update(productOrderStatusInfo, this.distributedQuantity);
    if (this.status().isDifferentStatus(newStatus)) {
      this.audit = this.audit().update(triggeredBy, occurredOn);
      this.status = newStatus;
      if (newStatus.isClosed()) {
        final var shouldAdjustRequestedQuantityOnClose =
            closeConfigProvider.shouldAdjustFor(this.useId().value().toString(), this.audit().createdAt());
        this.closeDistributionNominated(occurredOn, shouldAdjustRequestedQuantityOnClose);
        this.eventRegistrar().register(EventType.CLOSED);
      } else {
        this.eventRegistrar().register(EventType.UPDATED);
      }
    }
    return this;
  }

  public DistributionNominated updateDistribution(
      final DistributionNominatedLine.@NonNull Id distributionNominatedLineId,
      final DistributionNominatedLine.@NonNull DistributedQuantity totalLineQuantity,
      final DistributionNominatedLine.@NonNull DistributionStartDate distributionStartDate,
      @NonNull final ProductOrderStatusInfo orderInfo,
      @NonNull final OffsetDateTime occurredOn,
      @NonNull final String triggeredBy) {
    this.audit = this.audit().update(triggeredBy, occurredOn);
    this.lines = this.lines().updateLineDistribution(distributionNominatedLineId, totalLineQuantity, distributionStartDate, occurredOn);
    this.distributedQuantity = this.lines().calculateTotalDistributedQuantity();
    this.status = this.status().update(orderInfo, this.distributedQuantity);
    this.eventRegistrar().register(EventType.DISTRIBUTED);
    return this;
  }

  public DistributionNominated updateProductVariantGroupId(
      @NonNull final UUID productVariantGroupId,
      @NonNull final OffsetDateTime occurredOn,
      @NonNull final String triggeredBy) {
    if (!Objects.equals(this.productVariantGroupId().value(), productVariantGroupId)) {
      this.audit = this.audit().update(triggeredBy, occurredOn);
      this.productVariantGroupId = new ProductVariantGroupId(productVariantGroupId);
      this.eventRegistrar().register(EventType.PRODUCT_UPDATED);
    }
    return this;
  }

  public DistributionNominated updateReferencesId(
      @NonNull final ReferenceId referenceId,
      @NonNull final ReferenceProductId referenceProductId,
      @NonNull final OffsetDateTime occurredOn,
      @NonNull final String triggeredBy) {
    if (!Objects.equals(this.referenceId(), referenceId) || !Objects.equals(this.referenceProductId(), referenceProductId)) {
      this.audit = this.audit().update(triggeredBy, occurredOn);
      this.referenceId = referenceId;
      this.referenceProductId = referenceProductId;
      this.eventRegistrar().register(EventType.PRODUCT_UPDATED);
    }
    return this;
  }

  public DistributionNominated registerEvent(final EventType eventType) {
    this.eventRegistrar().register(eventType);
    return this;
  }

  public SharedRawMaterialNominated sharedRawMaterial() {
    return new SharedRawMaterialNominated(this.referenceId().value(), this.useId().value(), this.budgetCycle().value());
  }

  protected EventRegistrar eventRegistrar() {
    return new EventRegistrar(this);
  }

  public DistributionNominated regularize(final DistributionNominatedLine.Id lineIdToRegularize, final OffsetDateTime now,
      final String triggeredBy) {
    this.lines.getLineById(lineIdToRegularize).ifPresent(line -> {
      final var regularizedLine = line.regularizeAlternative(this.requestedQuantity, now);
      this.lines = this.lines.updateLine(lineIdToRegularize, regularizedLine);
      this.audit = this.audit.update(triggeredBy, now);
      this.eventRegistrar().register(EventType.DISTRIBUTION_RECONCILED);
    });
    return this;
  }

  public boolean isBudgetCycleChangeInProgress() {
    return this.budgetCycleChangePendingQuantity().hasValue();
  }

  public DistributionNominated delete(@NonNull final OffsetDateTime deletedAt,
      @NonNull final String deletedBy) {
    if (this.isBudgetCycleChangeInProgress()) {
      throw new ErrorException(
          new BadRequest("Distribution nominated cannot be deleted since its budgetCycle change has not been completed"));
    }
    this.deleteNoValidation(deletedAt, deletedBy);
    return this;
  }

  private DistributionNominated deleteNoValidation(@NonNull final OffsetDateTime deletedAt,
      @NonNull final String deletedBy) {
    this.audit = this.audit().delete(deletedBy, deletedAt);
    this.eventRegistrar().register(EventType.DELETED);
    return this;
  }

  private void closeDistributionNominated(@NonNull final OffsetDateTime now,
      @Nullable final Boolean shouldAdjustRequestedQuantity) {
    if (this.distributedQuantity().value.compareTo(this.requestedQuantity.value) < 0
        && Boolean.TRUE.equals(shouldAdjustRequestedQuantity)) {
      this.lines = this.lines().closeLines(now);
      this.requestedQuantity = new RequestedQuantity(this.distributedQuantity().value);
    }
  }

  public Quantities quantitiesFromCommitmentOrders(final List<CommitmentOrder> commitmentsOrders) {
    final var quantities = this.lines().quantitiesFromCommitmentOrders(commitmentsOrders);
    return new Quantities(new RequestedQuantity(quantities.requested().value()), new DistributedQuantity(quantities.distributed().value()));
  }

  public DistributionNominated updateLineSupplier(final CommitmentOrder.Id commitmentOrderId, final LineId commitmentOrderLineId,
      final SupplierId supplierId, final OffsetDateTime now, final String triggeredBy) {
    final var newLines = this.lines().updateSupplierInLinesWithCommitmentOrder(commitmentOrderId, commitmentOrderLineId, supplierId, now);
    if (this.lines.willBeUpdatedLinesOnReplace(newLines.value())) {
      this.audit = this.audit().update(triggeredBy, now);
      this.lines = this.lines().replaceLines(newLines.value());
      this.eventRegistrar().register(EventType.UPDATED);
    }
    return this;
  }

  public DistributionNominated recalculateBudgetCycleChangePendingQuantity(@NonNull final OffsetDateTime now,
      @NonNull final String triggeredBy) {
    final var calculatedBudgetCyclePendingQ =
        new BudgetCycleChangePendingQuantity(this.getDifferenceBetweenRootRequestedAndLinesRequested().max(BigDecimal.ZERO));
    if (this.budgetCycleChangePendingQuantity != calculatedBudgetCyclePendingQ) {
      this.budgetCycleChangePendingQuantity = calculatedBudgetCyclePendingQ;
      this.audit = this.audit().update(triggeredBy, now);
    }
    return this;
  }

  private BigDecimal getDifferenceBetweenRootRequestedAndLinesRequested() {
    return this.requestedQuantity.value().subtract(this.lines()
        .calculateTotalRequestedQuantity());
  }

  public record Id(UUID value) implements AggregateRootId<UUID> {
  }

  public record ReferenceId(UUID value) implements ValueObject<UUID> {
  }

  public record UseId(UUID value) implements ValueObject<UUID> {
  }

  public record BudgetCycle(String value) implements ValueObject<String> {
  }

  public record ReferenceProductId(UUID value) implements ValueObject<UUID> {
  }

  public record ProductOrderId(UUID value) implements ValueObject<UUID> {
  }

  public record ProductSupplierId(String value) implements ValueObject<String> {
  }

  public record ProductVariantGroupId(UUID value) implements ValueObject<UUID> {
  }

  public record TheoreticalQuantity(BigDecimal value) implements ValueObject<BigDecimal> {
    public TheoreticalQuantity(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }
  }

  public record DistributedQuantity(BigDecimal value) implements ValueObject<BigDecimal> {

    public DistributedQuantity(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }

    public boolean isDistributed() {
      return this.value.compareTo(BigDecimal.ZERO) > 0;
    }
  }

  public record BudgetCycleChangePendingQuantity(BigDecimal value) implements ValueObject<BigDecimal> {
    public BudgetCycleChangePendingQuantity(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }

    public boolean hasValue() {
      return this.value().compareTo(BigDecimal.ZERO) != 0;
    }
  }

  public record RequestedQuantity(BigDecimal value) implements ValueObject<BigDecimal> {
    public RequestedQuantity(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }
  }

  public record Quantities(RequestedQuantity requested, DistributedQuantity distributed) {
  }

  public record ConsumptionFactor(BigDecimal value) implements ValueObject<BigDecimal> {
    public ConsumptionFactor(final BigDecimal value) {
      this.value = NumericUtils.roundUpScale2(value);
    }
  }

  public record EventRegistrar(DistributionNominated distributionNominated) {
    public void register(final EventType event) {
      this.distributionNominated.registerEvent(new DistributionNominatedUnifiedEvent(this.distributionNominated, event));
    }
  }
}
