package com.inditex.icdmdemg.domain.order.entity;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import com.google.common.collect.Sets;
import org.jspecify.annotations.NonNull;

public record OrderLines(@NonNull List<OrderLine> value) implements ValueObject<List<OrderLine>> {

  public boolean willBeUpdatedWith(final OrderLines orderLines) {
    final var existingOrderLinesById = this.value.stream().collect(Collectors.toMap(OrderLine::id, Function.identity()));
    final var newOrderLinesById = orderLines.value().stream().collect(Collectors.toMap(OrderLine::id, Function.identity()));
    return Sets.union(existingOrderLinesById.keySet(), newOrderLinesById.keySet()).stream()
        .anyMatch(orderLineId -> this.willBeOrderLineUpdated(orderLineId, existingOrderLinesById, newOrderLinesById));
  }

  public Optional<OrderBudgetId> firstBudgetId() {
    return this.value().stream().findFirst().map(OrderLine::budgetId);
  }

  private boolean willBeOrderLineUpdated(final OrderLineId orderLineId, final Map<OrderLineId, OrderLine> existingOrderLines,
      final Map<OrderLineId, OrderLine> newOrderLines) {
    if (!existingOrderLines.containsKey(orderLineId) || !newOrderLines.containsKey(orderLineId)) {
      return true;
    }
    final var exitingOrderline = existingOrderLines.get(orderLineId);
    final var newOrderLine = newOrderLines.get(orderLineId);
    return exitingOrderline.willBeUpdatedWith(newOrderLine);
  }

}
