package com.inditex.icdmdemg.domain.order;

import java.util.List;

import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.iopcmmnt.ddd.core.AggregateRootRepository;

import org.jspecify.annotations.NullMarked;

@NullMarked
public interface OrderRepository extends AggregateRootRepository<Order, OrderId> {

  List<Order> findByProductReferenceIds(List<ProductReferenceId> productReferenceIds);

  void deleteById(final OrderId id);

}
