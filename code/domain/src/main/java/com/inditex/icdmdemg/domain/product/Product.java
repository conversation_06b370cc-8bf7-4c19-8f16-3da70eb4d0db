package com.inditex.icdmdemg.domain.product;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.product.entity.ProductCampaign;
import com.inditex.icdmdemg.domain.product.entity.ProductColor;
import com.inditex.icdmdemg.domain.product.entity.ProductFamilies;
import com.inditex.icdmdemg.domain.product.entity.ProductFamily;
import com.inditex.icdmdemg.domain.product.entity.ProductId;
import com.inditex.icdmdemg.domain.product.entity.ProductOriginMarket;
import com.inditex.icdmdemg.domain.product.entity.ProductOwner;
import com.inditex.icdmdemg.domain.product.entity.ProductOwners;
import com.inditex.icdmdemg.domain.product.entity.ProductQuality;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.domain.product.entity.ProductSupplier;
import com.inditex.icdmdemg.domain.product.entity.ProductVersion;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.jspecify.annotations.NonNull;

@EqualsAndHashCode
@ToString(callSuper = true)
@Getter
public class Product {

  @NonNull
  private final ProductId productId;

  @NonNull
  private final ProductReferenceId referenceId;

  private ProductColor colorId;

  private ProductSupplier supplierId;

  private ProductCampaign campaignId;

  private ProductOriginMarket originMarketId;

  private ProductQuality quality;

  @NonNull
  private ProductFamilies families;

  @NonNull
  private ProductOwners owners;

  @NonNull
  private final ProductVersion version;

  private BasicAudit audit;

  public Product(
      @NonNull final ProductId productId,
      @NonNull final ProductReferenceId referenceId,
      final ProductColor colorId,
      final ProductSupplier supplierId,
      final ProductCampaign campaignId,
      final ProductOriginMarket originMarketId,
      final ProductQuality quality,
      @NonNull final ProductFamilies families,
      @NonNull final ProductOwners productOwners,
      @NonNull final ProductVersion version,
      @NonNull final BasicAudit audit) {
    this.productId = productId;
    this.referenceId = referenceId;
    this.colorId = colorId;
    this.supplierId = supplierId;
    this.campaignId = campaignId;
    this.originMarketId = originMarketId;
    this.quality = quality;
    this.families = new ProductFamilies(new ArrayList<>(families.value()));
    this.owners = new ProductOwners(new ArrayList<>(productOwners.value()));
    this.version = version;
    this.audit = audit;
  }

  public static Product create(
      @NonNull final ProductId productId,
      @NonNull final ProductReferenceId referenceId,
      final ProductColor colorId,
      final ProductSupplier supplierId,
      final ProductCampaign campaignId,
      final ProductOriginMarket originMarketId,
      final ProductQuality quality,
      @NonNull final ProductFamilies productFamilies,
      @NonNull final ProductOwners productOwners,
      @NonNull final OffsetDateTime now) {
    return new Product(productId, referenceId, colorId, supplierId, campaignId, originMarketId, quality, productFamilies,
        productOwners, ProductVersion.firstVersion(), BasicAudit.create(now));
  }

  public Product modify(
      final ProductColor colorId,
      final ProductSupplier supplierId,
      final ProductCampaign campaignId,
      final ProductOriginMarket originMarketId,
      final ProductQuality quality,
      final List<ProductFamily> families,
      final List<ProductOwner> owners,
      @NonNull final OffsetDateTime now) {
    this.quality = quality;
    this.colorId = colorId;
    this.supplierId = supplierId;
    this.campaignId = campaignId;
    this.originMarketId = originMarketId;
    this.families = this.families.replaceFamilies(families);
    this.owners = this.owners.replaceOwners(owners);
    this.audit = this.audit.update(now);
    return this;
  }

  public boolean willBeUpdatedWith(final ProductColor color,
      final ProductSupplier supplier, final ProductCampaign campaign, final ProductOriginMarket originMarket,
      final ProductQuality quality,
      final ProductFamilies families,
      final ProductOwners owners) {
    return !Objects.equals(this.colorId, color)
        || !Objects.equals(this.supplierId, supplier)
        || !Objects.equals(this.campaignId, campaign)
        || !Objects.equals(this.originMarketId, originMarket)
        || !Objects.equals(this.quality, quality)
        || !Objects.equals(this.families, families)
        || !Objects.equals(this.owners, owners);
  }

  public EquivalentReference equivalentReference() {
    return new EquivalentReference(this.productId, this.colorId, this.supplierId, this.campaignId, this.originMarketId, this.quality);
  }

  public record EquivalentReference(
      ProductId productId,
      ProductColor colorId,
      ProductSupplier supplierId,
      ProductCampaign campaignId,
      ProductOriginMarket originMarketId,
      ProductQuality quality) {

    public EquivalentReference withColorId(final ProductColor productColor) {
      return new EquivalentReference(this.productId, productColor, this.supplierId, this.campaignId, this.originMarketId, this.quality);
    }

    public boolean allNonNull() {
      return Stream.of(this.productId, this.colorId, this.supplierId, this.colorId, this.originMarketId, this.quality)
          .allMatch(Objects::nonNull);
    }
  }
}
