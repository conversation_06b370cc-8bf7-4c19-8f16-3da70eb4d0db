package com.inditex.icdmdemg.domain.distributioninner.mother;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Random;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ConsumptionFactor;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.Id;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.UseId;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLines;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAuditMother;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.BigDecimalMother;

public interface DistributionInnerMother {

  Random RANDOM = new Random();

  static Builder of(final DistributionInner existing) {
    return new Builder()
        .id(existing.getId())
        .referenceId(existing.referenceId())
        .useId(existing.useId())
        .budgetCycle(existing.budgetCycle())
        .productOrderId(existing.productOrderId())
        .productVariantGroupId(existing.productVariantGroupId())
        .requestedQuantity(existing.requestedQuantity())
        .theoreticalQuantity(existing.theoreticalQuantity())
        .consumptionFactor(existing.consumptionFactor())
        .distributedQuantity(existing.distributedQuantity())
        .status(existing.status())
        .lines(existing.lines().value())
        .audit(existing.audit());
  }

  static Builder created(
      final Id rootId,
      final DistributionInnerLine.Id lineId,
      final ReferenceId referenceId,
      final UseId useId,
      final BudgetCycle budgetCycle,
      final ReferenceProductId referenceProductId,
      final ProductOrderId productOrderId,
      final ProductVariantGroupId productVariantGroupId,
      final DistributionInnerStatus status,
      final TheoreticalQuantity theoreticalQuantity,
      final ConsumptionFactor consumptionFactor,
      final RequestedQuantity requestedQuantity,
      final OffsetDateTime occurredAt,
      final String triggeredBy) {
    return new Builder()
        .id(rootId)
        .referenceId(referenceId)
        .useId(useId)
        .budgetCycle(budgetCycle)
        .referenceProductId(referenceProductId)
        .productOrderId(productOrderId)
        .productVariantGroupId(productVariantGroupId)
        .requestedQuantity(requestedQuantity)
        .theoreticalQuantity(theoreticalQuantity)
        .consumptionFactor(consumptionFactor)
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO))
        .status(status)
        .lines(
            new DistributionInnerLines(
                List.of(
                    DistributionInnerLineMother.createdOf(
                        lineId,
                        new DistributionInnerLine.TheoreticalQuantity(BigDecimal.valueOf(100)),
                        new DistributionInnerLine.RequestedQuantity(requestedQuantity.value()),
                        occurredAt)
                        .build())))
        .audit(CompleteAudit.create(triggeredBy, occurredAt));
  }

  static Builder pending() {
    return random()
        .status(DistributionInnerStatus.PENDING)
        .lines(List.of(DistributionInnerLineMother.created().build()))
        .audit(CompleteAuditMother.created());
  }

  static Builder sent() {
    return random()
        .status(DistributionInnerStatus.SENT)
        .lines(List.of(DistributionInnerLineMother.created().build()))
        .audit(CompleteAuditMother.created());
  }

  static Builder nonDistributable() {
    return random()
        .status(DistributionInnerStatus.NON_DISTRIBUTABLE)
        .lines(List.of(DistributionInnerLineMother.created().build()))
        .audit(CompleteAuditMother.created());
  }

  static Builder inProgress() {
    return random()
        .status(DistributionInnerStatus.IN_PROGRESS)
        .lines(List.of(DistributionInnerLineMother.created().build()))
        .audit(CompleteAuditMother.created());
  }

  static Builder deleted() {
    return random()
        .status(DistributionInnerStatus.PENDING)
        .lines(List.of(DistributionInnerLineMother.created().build()))
        .audit(CompleteAuditMother.deleted());
  }

  static Builder withStatus(final DistributionInnerStatus status) {
    return random()
        .status(status)
        .lines(List.of(DistributionInnerLineMother.distributed().build()))
        .audit(CompleteAuditMother.created());
  }

  private static Builder random() {
    return new Builder()
        .id(randomId())
        .referenceId(randomReferenceId())
        .referenceProductId(randomReferenceProductId())
        .useId(randomUseId())
        .budgetCycle(randomBudgetCycle())
        .productOrderId(randomProductOrderId())
        .productVariantGroupId(randomProductVariantGroupId())
        .theoreticalQuantity(randomTheoreticalQuantity())
        .consumptionFactor(randomConsumptionFactor())
        .requestedQuantity(randomRequestedQuantity())
        .distributedQuantity(randomDistributedQuantity())
        .status(randomStatus())
        .lines(List.of(DistributionInnerLineMother.created().build()));
  }

  static DistributionInnerMother.Builder closed() {
    return random()
        .status(DistributionInnerStatus.CLOSED)
        .lines(List.of(DistributionInnerLineMother.created().build()))
        .audit(CompleteAuditMother.created());
  }

  static DistributionInnerMother.Builder canceled() {
    return random()
        .status(DistributionInnerStatus.CANCELED)
        .lines(List.of(DistributionInnerLineMother.created().build()))
        .audit(CompleteAuditMother.created());
  }

  static DistributionInner.Id randomId() {
    return new DistributionInner.Id(UUID.randomUUID());
  }

  static DistributionInner.ReferenceId randomReferenceId() {
    return new DistributionInner.ReferenceId(UUID.randomUUID());
  }

  static DistributionInner.ReferenceProductId randomReferenceProductId() {
    return new DistributionInner.ReferenceProductId(UUID.randomUUID());
  }

  static DistributionInner.UseId randomUseId() {
    return new DistributionInner.UseId(UUID.randomUUID());
  }

  static DistributionInner.BudgetCycle randomBudgetCycle() {
    return new DistributionInner.BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID()));
  }

  static DistributionInner.ProductOrderId randomProductOrderId() {
    return new DistributionInner.ProductOrderId(UUID.randomUUID());
  }

  static DistributionInner.ProductVariantGroupId randomProductVariantGroupId() {
    return new DistributionInner.ProductVariantGroupId(UUID.randomUUID());
  }

  static DistributionInner.TheoreticalQuantity randomTheoreticalQuantity() {
    return new DistributionInner.TheoreticalQuantity(BigDecimalMother.random());
  }

  static DistributionInner.ConsumptionFactor randomConsumptionFactor() {
    return new DistributionInner.ConsumptionFactor(BigDecimalMother.random());
  }

  static DistributionInner.RequestedQuantity randomRequestedQuantity() {
    return new DistributionInner.RequestedQuantity(BigDecimalMother.random());
  }

  static DistributionInner.DistributedQuantity randomDistributedQuantity() {
    return new DistributionInner.DistributedQuantity(BigDecimalMother.random());
  }

  static DistributionInnerStatus randomStatus() {
    return DistributionInnerStatus.values()[RANDOM.nextInt(DistributionInnerStatus.values().length)];
  }

  class Builder {

    DistributionInner.Id id;

    DistributionInner.ReferenceId referenceId;

    DistributionInner.UseId useId;

    DistributionInner.BudgetCycle budgetCycle;

    DistributionInner.ReferenceProductId referenceProductId;

    DistributionInner.ProductOrderId productOrderId;

    DistributionInner.ProductVariantGroupId productVariantGroupId;

    DistributionInner.TheoreticalQuantity theoreticalQuantity;

    DistributionInner.ConsumptionFactor consumptionFactor;

    DistributionInner.RequestedQuantity requestedQuantity;

    DistributionInner.DistributedQuantity distributedQuantity;

    DistributionInnerStatus status;

    DistributionInnerLines lines;

    CompleteAudit audit;

    public Builder id(final DistributionInner.Id id) {
      this.id = id;
      return this;
    }

    public Builder referenceId(final DistributionInner.ReferenceId referenceId) {
      this.referenceId = referenceId;
      return this;
    }

    public Builder useId(final DistributionInner.UseId useId) {
      this.useId = useId;
      return this;
    }

    public Builder budgetCycle(final DistributionInner.BudgetCycle budgetCycle) {
      this.budgetCycle = budgetCycle;
      return this;
    }

    public Builder referenceProductId(final DistributionInner.ReferenceProductId referenceProductId) {
      this.referenceProductId = referenceProductId;
      return this;
    }

    public Builder productOrderId(final DistributionInner.ProductOrderId productOrderId) {
      this.productOrderId = productOrderId;
      return this;
    }

    public Builder productVariantGroupId(final DistributionInner.ProductVariantGroupId productVariantGroupId) {
      this.productVariantGroupId = productVariantGroupId;
      return this;
    }

    public Builder theoreticalQuantity(final DistributionInner.TheoreticalQuantity theoreticalQuantity) {
      this.theoreticalQuantity = theoreticalQuantity;
      return this;
    }

    public Builder consumptionFactor(final DistributionInner.ConsumptionFactor consumptionFactor) {
      this.consumptionFactor = consumptionFactor;
      return this;
    }

    public Builder requestedQuantity(final DistributionInner.RequestedQuantity requestedQuantity) {
      this.requestedQuantity = requestedQuantity;
      return this;
    }

    public Builder distributedQuantity(final DistributionInner.DistributedQuantity distributedQuantity) {
      this.distributedQuantity = distributedQuantity;
      return this;
    }

    public Builder status(final DistributionInnerStatus status) {
      this.status = status;
      return this;
    }

    public Builder lines(final DistributionInnerLines lines) {
      this.lines = lines;
      return this;
    }

    public Builder lines(final List<DistributionInnerLine> lines) {
      this.lines = new DistributionInnerLines(lines);
      return this;
    }

    public Builder audit(final CompleteAudit audit) {
      this.audit = audit;
      return this;
    }

    public DistributionInner build() {
      return new DistributionInner(this.id, this.referenceId, this.useId, this.budgetCycle, this.referenceProductId, this.productOrderId,
          this.productVariantGroupId, this.theoreticalQuantity, this.consumptionFactor,
          this.requestedQuantity, this.distributedQuantity, this.status, this.lines, this.audit);
    }
  }
}
