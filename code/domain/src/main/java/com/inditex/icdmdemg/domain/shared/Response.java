package com.inditex.icdmdemg.domain.shared;

import java.util.Optional;

import com.inditex.icdmdemg.domain.shared.exception.ErrorException;

public record Response<R>(Optional<R> response, Optional<Error> error) {

  public static <R> Response<R> ofResponseAndError(final R response, final Error error) {
    return new Response<>(Optional.ofNullable(response), Optional.ofNullable(error));
  }

  public static Response<Void> nothing() {
    return ofResponseAndError(null, null);
  }

  public static <R> Response<R> ofResponse(final R response) {
    return ofResponseAndError(response, null);
  }

  public static <R> Response<R> ofError(final Error error) {
    return ofResponseAndError(null, error);
  }

  public void throwIfError() {
    this.error().ifPresent(err -> {
      throw new ErrorException(err);
    });
  }

  public R responseOrThrowIfError() {
    this.throwIfError();
    return this.response.get();
  }
}
