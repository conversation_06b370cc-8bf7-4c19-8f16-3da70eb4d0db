package com.inditex.icdmdemg.domain.distributioninner.entity;

import java.util.UUID;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner;

import org.jspecify.annotations.NonNull;

public record SharedRawMaterialInner(
    DistributionInner.@NonNull ReferenceId referenceId,
    DistributionInner.@NonNull UseId useId,
    DistributionInner.@NonNull BudgetCycle budgetCycle) {

  public SharedRawMaterialInner(
      final UUID referenceId,
      final UUID useId,
      final String budgetCycle) {
    this(new DistributionInner.ReferenceId(referenceId), new DistributionInner.UseId(useId),
        new DistributionInner.BudgetCycle(budgetCycle));
  }

}
