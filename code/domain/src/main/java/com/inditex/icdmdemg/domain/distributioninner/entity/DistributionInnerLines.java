package com.inditex.icdmdemg.domain.distributioninner.entity;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.DistributionEndDate;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.DistributionStartDate;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLine.TrackingCode;
import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NullMarked;

@NullMarked
public record DistributionInnerLines(List<DistributionInnerLine> value) implements ValueObject<List<DistributionInnerLine>> {

  public boolean willBeUpdatedLinesOnReplace(final List<DistributionInnerLine> lines) {
    final var currentSet = new HashSet<>(this.value);
    final var newSet = new HashSet<>(lines);
    return !Objects.equals(currentSet, newSet);
  }

  public DistributionInnerLines replaceLines(final List<DistributionInnerLine> lines) {
    return new DistributionInnerLines(lines);
  }

  public Optional<DistributionInnerLine> getAliveLine() {
    return this.value.stream()
        .filter(DistributionInnerLine::isAlive)
        .findFirst();
  }

  public boolean hasAnyLineDistributionStarted() {
    return this.value.stream()
        .anyMatch(DistributionInnerLine::isProcessStarted);
  }

  public DistributionInnerLines updateLine(
      final DistributionInnerLine updatedLine,
      final BigDecimal rootRequestedQuantity) {
    final var updated = this.value.stream()
        .map(line -> line.id().equals(updatedLine.id())
            ? updatedLine
            : line)
        .toList();
    return this.replaceLinesAdjustingTheoretical(updated, rootRequestedQuantity);
  }

  public DistributionInnerLines addLine(
      final DistributionInnerLine distributionInnerLine,
      final BigDecimal rootRequestedQuantity) {
    final var newLines = new ArrayList<>(this.value);
    newLines.add(distributionInnerLine);
    return this.replaceLinesAdjustingTheoretical(List.copyOf(newLines), rootRequestedQuantity);
  }

  private DistributionInnerLines replaceLinesAdjustingTheoretical(
      final List<DistributionInnerLine> lines,
      final BigDecimal rootRequestedQuantity) {
    return new DistributionInnerLines(lines.stream().map(line -> line.adjustTheoretical(rootRequestedQuantity)).toList());
  }

  public List<DistributionInnerLine> updateLineDistribution(final DistributionInnerLine.Id lineId,
      final TrackingCode trackingCode,
      final DistributionInnerLine.DistributedQuantity distributedQuantity,
      final DistributionStartDate distributionStartDate,
      final DistributionEndDate distributionEndDate,
      final OffsetDateTime updatedAt) {
    return this.value.stream()
        .map(line -> line.id().equals(lineId)
            ? line.updateLineDistribution(trackingCode, distributedQuantity, distributionStartDate, distributionEndDate, updatedAt)
            : line)
        .toList();
  }

  public DistributedQuantity totalDistributed() {
    final var quantities = this.value.stream()
        .map(line -> line.distributedQuantity().value())
        .reduce(BigDecimal.ZERO, BigDecimal::add);
    return new DistributedQuantity(quantities);
  }

  public Optional<DistributionInnerLine> findLineById(final DistributionInnerLine.Id lineId) {
    return this.value().stream().filter(
        line -> line.id().equals(lineId))
        .findFirst();
  }
}
