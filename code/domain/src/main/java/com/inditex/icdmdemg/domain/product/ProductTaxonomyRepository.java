package com.inditex.icdmdemg.domain.product;

import java.util.Optional;
import java.util.UUID;

import com.inditex.icdmdemg.domain.product.entity.ProductTaxonomy;

import org.jspecify.annotations.NullMarked;

@NullMarked
public interface ProductTaxonomyRepository {

  Optional<ProductTaxonomy> findByReferenceId(UUID rawMaterialReference);

  Optional<ProductTaxonomy> findByProductId(ProductTaxonomy.ProductId productId);

  void deleteAllByProductId(ProductTaxonomy.ProductId productId);

  void save(ProductTaxonomy productTaxonomy);
}
