package com.inditex.icdmdemg.domain.use.entity;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;

import lombok.Getter;
import org.springframework.util.CollectionUtils;

@Getter
public enum AssignableType {

  NOMINATED(List.of(AssignableType.NOMINATED_VALUE)),
  INNER(List.of(AssignableType.INNER_VALUE)),
  NOMINATED_INNER(List.of(AssignableType.NOMINATED_VALUE, AssignableType.INNER_VALUE));

  private static final String NOMINATED_VALUE = "NOMINATED";

  private static final String INNER_VALUE = "INNER";

  private final List<String> stringList;

  AssignableType(final List<String> stringList) {
    this.stringList = stringList;
  }

  public static AssignableType fromValues(final Collection<String> inputValues) {
    if (CollectionUtils.isEmpty(inputValues)) {
      return null;
    }

    final var inputSet = new HashSet<>(inputValues);
    return Arrays.stream(values())
        .filter(type -> new HashSet<>(type.getStringList()).equals(inputSet))
        .findFirst().orElseThrow(() -> new IllegalArgumentException(
            String.format("No valid assignable value found for input list: %s", inputValues)));
  }
}
