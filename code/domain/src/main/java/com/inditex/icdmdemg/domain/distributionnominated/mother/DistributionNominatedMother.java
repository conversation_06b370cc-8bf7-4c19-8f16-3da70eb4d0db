package com.inditex.icdmdemg.domain.distributionnominated.mother;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Comparator;
import java.util.List;
import java.util.Random;
import java.util.UUID;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycleChangePendingQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ConsumptionFactor;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductSupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.UseId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLines;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedPlan;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedStatus;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo.Status;
import com.inditex.icdmdemg.domain.shared.audit.BasicAudit;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAuditMother;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.BigDecimalMother;

public interface DistributionNominatedMother {

  Random RANDOM = new Random();

  static Builder of(final DistributionNominated existing) {
    return new Builder()
        .id(existing.getId())
        .referenceId(existing.referenceId())
        .useId(existing.useId())
        .budgetCycle(existing.budgetCycle())
        .referenceProductId(existing.referenceProductId())
        .productOrderId(existing.productOrderId())
        .productSupplierId(existing.productSupplierId())
        .productVariantGroupId(existing.productVariantGroupId())
        .requestedQuantity(existing.requestedQuantity())
        .theoreticalQuantity(existing.theoreticalQuantity())
        .consumptionFactor(existing.consumptionFactor())
        .distributedQuantity(existing.distributedQuantity())
        .budgetCycleChangePendingQuantity(existing.budgetCycleChangePendingQuantity())
        .status(existing.status())
        .plan(existing.plan())
        .lines(existing.lines().value())
        .audit(existing.audit());
  }

  static Builder pending() {
    return random()
        .status(DistributionNominatedStatus.PENDING)
        .lines(List.of(DistributionNominatedLineMother.created().build()))
        .audit(CompleteAuditMother.created());
  }

  static Builder pendingWithProductOrderIdAndProductVariantGroupIdAndOneLine(final UUID productOrderId, final UUID productVariantGroupId) {
    return random()
        .status(DistributionNominatedStatus.PENDING)
        .lines(List.of(DistributionNominatedLineMother.created().build()))
        .productOrderId(new ProductOrderId(productOrderId))
        .productVariantGroupId(new ProductVariantGroupId(productVariantGroupId))
        .audit(CompleteAuditMother.created());
  }

  static Builder inProgressWithProductOrderIdAndProductVariantGroupIdAndOneLine(final UUID productOrderId,
      final UUID productVariantGroupId) {
    return random()
        .status(DistributionNominatedStatus.IN_PROGRESS)
        .distributedQuantity(new DistributedQuantity(BigDecimalMother.random()))
        .lines(List.of(DistributionNominatedLineMother.distributed().build()))
        .productOrderId(new ProductOrderId(productOrderId))
        .productVariantGroupId(new ProductVariantGroupId(productVariantGroupId))
        .audit(CompleteAuditMother.created());
  }

  static Builder pendingWithOneLineAndRequestedQuantity(final BigDecimal quantity) {
    final var audit = CompleteAuditMother.created();
    return random()
        .status(DistributionNominatedStatus.PENDING)
        .requestedQuantity(new RequestedQuantity(quantity))
        .lines(List.of(DistributionNominatedLineMother.createdWithQuantityAndDate(quantity, audit.createdAt()).build()))
        .audit(audit);
  }

  static Builder pendingWithIdAndSharedRawMaterialAndLinesAndQuantities(final UUID id,
      final SharedRawMaterialNominated sharedRawMaterialNominated,
      final BigDecimal requestQuantity, final BigDecimal consumptionFactor,
      final BigDecimal theoreticalQuantity, final List<DistributionNominatedLine> lines) {
    final var olderLineCreatedAt = lines.stream().min(Comparator.comparing(line -> line.audit().createdAt()))
        .map(DistributionNominatedLine::audit).map(BasicAudit::createdAt);
    final var audit = olderLineCreatedAt
        .map(CompleteAuditMother::createdWith)
        .orElse(CompleteAuditMother.created());
    return random()
        .id(new Id(id))
        .status(DistributionNominatedStatus.PENDING)
        .referenceId(sharedRawMaterialNominated.referenceId())
        .useId(sharedRawMaterialNominated.useId())
        .budgetCycle(sharedRawMaterialNominated.budgetCycle())
        .requestedQuantity(new RequestedQuantity(requestQuantity))
        .consumptionFactor(new ConsumptionFactor(consumptionFactor))
        .theoreticalQuantity(new TheoreticalQuantity(theoreticalQuantity))
        .lines(lines)
        .plan(DistributionNominatedPlan.AUTO)
        .audit(audit);
  }

  static Builder withTwoLinesAndAlternativeReferenceInOneOfThem(
      final UUID distributionNominatedId,
      final UUID distributionNominatedLineWithAlternativeReferenceId,
      final UUID commitmentOrderIdForAlternativeReference,
      final UUID commitmentOrderLineIdForAlternativeReference,
      final BigDecimal requestedQuantityInAlternativeReference,
      final BigDecimal requestedQuantityInLineWithoutAlternativeReference,
      final UUID referenceId,
      final UUID alternativeReferenceId,
      final OffsetDateTime createdAt) {

    return random()
        .id(new Id(distributionNominatedId))
        .status(randomStatus())
        .referenceId(new ReferenceId(referenceId))
        .useId(randomUseId())
        .budgetCycle(randomBudgetCycle())
        .productOrderId(randomProductOrderId())
        .productVariantGroupId(randomProductVariantGroupId())
        .theoreticalQuantity(
            new TheoreticalQuantity(requestedQuantityInAlternativeReference.add(requestedQuantityInLineWithoutAlternativeReference)))
        .requestedQuantity(
            new RequestedQuantity(requestedQuantityInAlternativeReference.add(requestedQuantityInLineWithoutAlternativeReference)))
        .distributedQuantity(new DistributedQuantity(BigDecimal.ZERO))
        .audit(CompleteAuditMother.createdWith(createdAt))
        .lines(new DistributionNominatedLines(
            List.of(
                DistributionNominatedLineMother.createdWithSpecificAlternativeReference(
                    distributionNominatedLineWithAlternativeReferenceId,
                    commitmentOrderIdForAlternativeReference,
                    commitmentOrderLineIdForAlternativeReference,
                    alternativeReferenceId,
                    requestedQuantityInAlternativeReference,
                    requestedQuantityInAlternativeReference,
                    createdAt).build(),
                DistributionNominatedLineMother.createdWithoutAlternativeReferenceAndWithoutDistributed(
                    requestedQuantityInLineWithoutAlternativeReference,
                    requestedQuantityInLineWithoutAlternativeReference,
                    createdAt).build())));

  }

  static Builder withCommitmentOrderAndReferenceAndRequestedQuantity(
      final UUID commitmentOrderId,
      final UUID commitmentOrderLineId,
      final UUID materialReferenceId,
      final BigDecimal lineRequestedQuantity) {
    return random()
        .referenceId(new ReferenceId(materialReferenceId))
        .requestedQuantity(new RequestedQuantity(lineRequestedQuantity))
        .lines(List.of(DistributionNominatedLineMother.withCommitmentOrderAndQuantityAndWithoutAlternativeReference(
            commitmentOrderId,
            commitmentOrderLineId, lineRequestedQuantity).build()))
        .audit(CompleteAuditMother.created());
  }

  static Builder withOneLinewithCommitmentOrderAndReferenceAndAlternativeReference(
      final UUID commitmentOrderId,
      final UUID commitmentOrderLineId,
      final UUID materialReferenceId,
      final BigDecimal lineRequestedQuantity,
      final BigDecimal alternativeLineRequestedQuantity) {
    return random()
        .referenceId(new ReferenceId(materialReferenceId))
        .requestedQuantity(new RequestedQuantity(lineRequestedQuantity))
        .lines(List.of(DistributionNominatedLineMother.withCommitmentOrderAndWithAlternativeReferenceWithQuantity(
            commitmentOrderId,
            commitmentOrderLineId,
            lineRequestedQuantity,
            alternativeLineRequestedQuantity).build()))
        .audit(CompleteAuditMother.created());
  }

  static Builder pendingWithLines(final List<DistributionNominatedLine> lines) {
    return random()
        .status(DistributionNominatedStatus.PENDING)
        .lines(lines)
        .audit(CompleteAuditMother.created());
  }

  static Builder closed() {
    return random()
        .status(DistributionNominatedStatus.CLOSED)
        .lines(List.of(DistributionNominatedLineMother.created().build()))
        .audit(CompleteAuditMother.created());
  }

  static Builder inProgress() {
    return random()
        .status(DistributionNominatedStatus.IN_PROGRESS)
        .lines(List.of(DistributionNominatedLineMother.distributed().build(), DistributionNominatedLineMother.created().build()))
        .audit(CompleteAuditMother.created());
  }

  static Builder distributed() {
    return random()
        .status(DistributionNominatedStatus.IN_PROGRESS)
        .lines(List.of(DistributionNominatedLineMother.distributed().build(), DistributionNominatedLineMother.created().build()))
        .audit(CompleteAuditMother.created());
  }

  static Builder inProgressWithAlternativeReference() {
    return random()
        .status(DistributionNominatedStatus.IN_PROGRESS)
        .lines(List.of(DistributionNominatedLineMother.createdWithRandomAlternativeReference().build()))
        .audit(CompleteAuditMother.created());
  }

  static Builder inProgressWithLines(final List<DistributionNominatedLine> lines) {
    return random()
        .status(DistributionNominatedStatus.IN_PROGRESS)
        .lines(lines)
        .audit(CompleteAuditMother.created());
  }

  static Builder deleted() {
    return random()
        .status(DistributionNominatedStatus.PENDING)
        .lines(List.of(DistributionNominatedLineMother.created().build()))
        .audit(CompleteAuditMother.deleted());
  }

  private static Builder random() {
    return new Builder()
        .id(randomId())
        .referenceId(randomReferenceId())
        .useId(randomUseId())
        .budgetCycle(randomBudgetCycle())
        .referenceProductId(randomReferenceProductId())
        .productOrderId(randomProductOrderId())
        .productSupplierId(randomProductSupplierId())
        .productVariantGroupId(randomProductVariantGroupId())
        .theoreticalQuantity(randomTheoreticalQuantity())
        .consumptionFactor(randomConsumptionFactor())
        .requestedQuantity(randomRequestedQuantity())
        .distributedQuantity(randomDistributedQuantity())
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .status(randomStatus())
        .plan(randomPlan())
        .lines(List.of(DistributionNominatedLineMother.created().build()));
  }

  static Builder withStatus(final DistributionNominatedStatus status) {
    return random()
        .status(status)
        .lines(List.of(DistributionNominatedLineMother.distributed().build(), DistributionNominatedLineMother.created().build()))
        .audit(CompleteAuditMother.created());
  }

  static Id randomId() {
    return new Id(UUID.randomUUID());
  }

  static ReferenceId randomReferenceId() {
    return new ReferenceId(UUID.randomUUID());
  }

  static UseId randomUseId() {
    return new UseId(UUID.randomUUID());
  }

  static BudgetCycle randomBudgetCycle() {
    return new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID()));
  }

  static BudgetCycleChangePendingQuantity randomBudgetCycleChangePendingQuantity() {
    return new BudgetCycleChangePendingQuantity(BigDecimalMother.random());
  }

  static ReferenceProductId randomReferenceProductId() {
    return new ReferenceProductId(UUID.randomUUID());
  }

  static ProductOrderId randomProductOrderId() {
    return new ProductOrderId(UUID.randomUUID());
  }

  static ProductSupplierId randomProductSupplierId() {
    return new ProductSupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));
  }

  static ProductVariantGroupId randomProductVariantGroupId() {
    return new ProductVariantGroupId(UUID.randomUUID());
  }

  static TheoreticalQuantity randomTheoreticalQuantity() {
    return new TheoreticalQuantity(BigDecimalMother.random());
  }

  static ConsumptionFactor randomConsumptionFactor() {
    return new ConsumptionFactor(BigDecimalMother.random());
  }

  static RequestedQuantity randomRequestedQuantity() {
    return new RequestedQuantity(BigDecimalMother.random());
  }

  static DistributedQuantity randomDistributedQuantity() {
    return new DistributedQuantity(BigDecimalMother.random());
  }

  static DistributionNominatedStatus randomStatus() {
    return DistributionNominatedStatus.values()[RANDOM.nextInt(DistributionNominatedStatus.values().length)];
  }

  static DistributionNominatedPlan randomPlan() {
    return DistributionNominatedPlan.values()[RANDOM.nextInt(DistributionNominatedPlan.values().length)];
  }

  static ProductOrderStatusInfo randomProductOrderInfo() {
    return new ProductOrderStatusInfo(Status.values()[RANDOM.nextInt(Status.values().length)], RANDOM.nextBoolean());
  }

  class Builder {

    Id id;

    ReferenceId referenceId;

    UseId useId;

    BudgetCycle budgetCycle;

    ReferenceProductId referenceProductId;

    ProductOrderId productOrderId;

    ProductSupplierId productSupplierId;

    ProductVariantGroupId productVariantGroupId;

    TheoreticalQuantity theoreticalQuantity;

    ConsumptionFactor consumptionFactor;

    RequestedQuantity requestedQuantity;

    DistributedQuantity distributedQuantity;

    DistributionNominatedStatus status;

    BudgetCycleChangePendingQuantity budgetCycleChangePendingQuantity;

    DistributionNominatedPlan plan;

    DistributionNominatedLines lines;

    CompleteAudit audit;

    public Builder id(final Id id) {
      this.id = id;
      return this;
    }

    public Builder referenceId(final ReferenceId referenceId) {
      this.referenceId = referenceId;
      return this;
    }

    public Builder useId(final UseId useId) {
      this.useId = useId;
      return this;
    }

    public Builder budgetCycle(final BudgetCycle budgetCycle) {
      this.budgetCycle = budgetCycle;
      return this;
    }

    public Builder referenceProductId(ReferenceProductId referenceProductId) {
      this.referenceProductId = referenceProductId;
      return this;
    }

    public Builder productOrderId(final ProductOrderId productOrderId) {
      this.productOrderId = productOrderId;
      return this;
    }

    public Builder productSupplierId(final ProductSupplierId productSupplierId) {
      this.productSupplierId = productSupplierId;
      return this;
    }

    public Builder productVariantGroupId(final ProductVariantGroupId productVariantGroupId) {
      this.productVariantGroupId = productVariantGroupId;
      return this;
    }

    public Builder theoreticalQuantity(final TheoreticalQuantity theoreticalQuantity) {
      this.theoreticalQuantity = theoreticalQuantity;
      return this;
    }

    public Builder consumptionFactor(final ConsumptionFactor consumptionFactor) {
      this.consumptionFactor = consumptionFactor;
      return this;
    }

    public Builder requestedQuantity(final RequestedQuantity requestedQuantity) {
      this.requestedQuantity = requestedQuantity;
      return this;
    }

    public Builder distributedQuantity(final DistributedQuantity distributedQuantity) {
      this.distributedQuantity = distributedQuantity;
      return this;
    }

    public Builder budgetCycleChangePendingQuantity(final BudgetCycleChangePendingQuantity budgetCycleChangePendingQuantity) {
      this.budgetCycleChangePendingQuantity = budgetCycleChangePendingQuantity;
      return this;
    }

    public Builder status(final DistributionNominatedStatus status) {
      this.status = status;
      return this;
    }

    public Builder plan(final DistributionNominatedPlan plan) {
      this.plan = plan;
      return this;
    }

    public Builder lines(final DistributionNominatedLines lines) {
      this.lines = lines;
      return this;
    }

    public Builder lines(final List<DistributionNominatedLine> lines) {
      this.lines = new DistributionNominatedLines(lines);
      return this;
    }

    public Builder audit(final CompleteAudit audit) {
      this.audit = audit;
      return this;
    }

    public DistributionNominated build() {
      return new DistributionNominated(this.id, this.referenceId, this.useId, this.budgetCycle, this.referenceProductId,
          this.productOrderId, this.productSupplierId, this.productVariantGroupId, this.theoreticalQuantity, this.consumptionFactor,
          this.requestedQuantity, this.distributedQuantity, this.budgetCycleChangePendingQuantity, this.status, this.plan, this.lines,
          this.audit);
    }
  }
}
