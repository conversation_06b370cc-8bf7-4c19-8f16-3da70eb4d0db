package com.inditex.icdmdemg.domain.shipmentwarehouse.entity;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public record ShipmentWarehouseInner(
    @NonNull ShipmentWarehouseInnerId innerId,
    @NonNull ShipmentWarehouseInnerLineId innerLineId) implements ValueObject<ShipmentWarehouseInner> {
  public ShipmentWarehouseInner value() {
    return this;
  }
}
