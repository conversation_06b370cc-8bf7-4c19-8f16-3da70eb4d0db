package com.inditex.icdmdemg.domain.distributioninner;

import java.util.List;
import java.util.Optional;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.UseId;
import com.inditex.icdmdemg.domain.distributioninner.entity.CompositeKeyInner;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.distributioninner.entity.SharedRawMaterialInner;

import org.jspecify.annotations.NullMarked;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Slice;

@NullMarked
public interface DistributionInnerRepository {

  Optional<DistributionInner> findById(DistributionInner.Id id);

  void save(final DistributionInner distributionInner);

  Slice<DistributionInner> findByCriteria(
      final List<ReferenceId> referenceIds,
      final List<ProductOrderId> productOrderIds,
      final List<BudgetCycle> budgetCycles,
      final List<DistributionInnerStatus> statusList,
      final Pageable pageable);

  Optional<DistributionInner> findByCompositeKey(CompositeKeyInner compositeKey);

  List<DistributionInner> findByProductOrderId(DistributionInner.ProductOrderId productOrderId);

  List<DistributionInner> findByProductVariantGroupIds(final List<ProductVariantGroupId> productVariantGroupIds);

  List<DistributionInner> findByReferenceIds(final List<ReferenceId> referenceIds);

  List<DistributionInner> findBySharedRawMaterial(final SharedRawMaterialInner sharedRawMaterial);

  List<DistributionInnerSummaryDTO> findDistributionInnerSummaries(
      final List<ReferenceProductId> referenceProductIds,
      final List<ReferenceId> referenceIds,
      final List<BudgetCycle> budgetCycles,
      final List<DistributionInnerStatus> excludedStatus);

  List<DistributionInnerUseQuantitiesDTO> findDistributionInnerUseQuantities(final List<UseId> useIds,
      final ReferenceId referenceId, final BudgetCycle budgetCycle);
}
