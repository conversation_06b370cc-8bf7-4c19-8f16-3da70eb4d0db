package com.inditex.icdmdemg.domain.shipmentcommitment;

import java.time.OffsetDateTime;

import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentAudit;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentDistributedQuantity;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentDistributionNominatedLineId;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentId;
import com.inditex.icdmdemg.domain.shipmentcommitment.entity.ShipmentCommitmentTimeStamp;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.jspecify.annotations.NonNull;

@EqualsAndHashCode
@ToString(callSuper = true)
@Getter
public class ShipmentCommitment {

  @NonNull
  private final ShipmentCommitmentId id;

  @NonNull
  private final ShipmentCommitmentDistributionNominatedLineId distributionNominatedLineId;

  @NonNull
  private final ShipmentCommitmentTimeStamp sentDate;

  @NonNull
  private final ShipmentCommitmentDistributedQuantity distributedQuantity;

  @NonNull
  private final ShipmentCommitmentAudit audit;

  public ShipmentCommitment(
      @NonNull final ShipmentCommitmentId id,
      @NonNull final ShipmentCommitmentDistributionNominatedLineId distributionNominatedLineId,
      @NonNull final ShipmentCommitmentTimeStamp sentDate,
      @NonNull final ShipmentCommitmentDistributedQuantity distributedQuantity,
      @NonNull final ShipmentCommitmentAudit audit) {
    this.id = id;
    this.distributionNominatedLineId = distributionNominatedLineId;
    this.sentDate = sentDate;
    this.distributedQuantity = distributedQuantity;
    this.audit = audit;
  }

  public static ShipmentCommitment create(
      @NonNull final ShipmentCommitmentId id,
      @NonNull final ShipmentCommitmentDistributionNominatedLineId distributionNominatedId,
      @NonNull final ShipmentCommitmentTimeStamp sentDate,
      @NonNull final ShipmentCommitmentDistributedQuantity distributedQuantity,
      @NonNull final OffsetDateTime now) {
    return new ShipmentCommitment(
        id,
        distributionNominatedId,
        sentDate,
        distributedQuantity,
        ShipmentCommitmentAudit.create(now));
  }

  public ShipmentCommitment update(
      @NonNull final ShipmentCommitmentDistributedQuantity distributedQuantity,
      @NonNull final OffsetDateTime now) {
    return new ShipmentCommitment(
        this.id,
        this.distributionNominatedLineId,
        this.sentDate,
        distributedQuantity,
        this.audit.update(now));
  }

}
