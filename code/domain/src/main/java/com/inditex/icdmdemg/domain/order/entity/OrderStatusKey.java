package com.inditex.icdmdemg.domain.order.entity;

import com.inditex.iopcmmnt.ddd.core.ValueObject;

import org.jspecify.annotations.NonNull;

public enum OrderStatusKey implements ValueObject<String> {

  DRAFT("DRAFT"),
  FORMALIZED("FORMALIZED"),
  CANC<PERSON>LED("CANCELLED"),
  CLOSED("CLOSED");

  private final String value;

  OrderStatusKey(@NonNull final String value) {
    this.value = value;
  }

  @Override
  public String value() {
    return this.value;
  }

  public Boolean isClosed() {
    return this == CLOSED;
  }

  public Boolean isCancelled() {
    return this == CANCELLED;
  }

}
