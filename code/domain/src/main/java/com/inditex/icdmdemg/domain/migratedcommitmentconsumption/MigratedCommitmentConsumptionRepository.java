package com.inditex.icdmdemg.domain.migratedcommitmentconsumption;

import java.util.List;
import java.util.Optional;

import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionId;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionOrderId;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionOrderLineId;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionReferenceId;

import org.jspecify.annotations.NullMarked;

@NullMarked
public interface MigratedCommitmentConsumptionRepository {

  void save(MigratedCommitmentConsumption migratedCommitmentConsumptionToSave);

  void delete(MigratedCommitmentConsumption migratedCommitmentConsumptionToDelete);

  Optional<MigratedCommitmentConsumption> find(MigratedCommitmentConsumptionId id);

  MigratedCommitmentConsumptionCollection findByCommitmentsInfo(List<CommitmentInfo> commitmentInfoList);

  record CommitmentInfo(
      MigratedCommitmentConsumptionOrderId orderId,
      MigratedCommitmentConsumptionOrderLineId orderLineId,
      MigratedCommitmentConsumptionReferenceId referenceId) {
    public static CommitmentInfo of(final String orderId, final String orderLineId, final String referenceId) {
      return new CommitmentInfo(
          new MigratedCommitmentConsumptionOrderId(orderId),
          new MigratedCommitmentConsumptionOrderLineId(orderLineId),
          new MigratedCommitmentConsumptionReferenceId(referenceId));
    }
  }
}
