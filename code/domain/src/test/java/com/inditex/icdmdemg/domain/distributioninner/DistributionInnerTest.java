package com.inditex.icdmdemg.domain.distributioninner;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.BudgetCycle;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ConsumptionFactor;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductOrderId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.UseId;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerLines;
import com.inditex.icdmdemg.domain.distributioninner.entity.DistributionInnerStatus;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;

import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
class DistributionInnerTest {

  @ParameterizedTest
  @MethodSource("provideNotNullableParams")
  void should_not_create_distribution_inner_with_null_values(
      final DistributionInner.Id id,
      final ReferenceId referenceId,
      final UseId useId,
      final BudgetCycle budgetCycle,
      final ReferenceProductId referenceProductId,
      final ProductOrderId productOrderId,
      final ProductVariantGroupId productVariantGroupId,
      final TheoreticalQuantity theoreticalQuantity,
      final ConsumptionFactor consumptionFactor,
      final RequestedQuantity requestedQuantity,
      final DistributedQuantity distributedQuantity,
      final DistributionInnerStatus status,
      final DistributionInnerLines lines,
      final CompleteAudit audit) {
    assertThatThrownBy(
        () -> new DistributionInner(id, referenceId, useId, budgetCycle, referenceProductId, productOrderId,
            productVariantGroupId, theoreticalQuantity, consumptionFactor, requestedQuantity,
            distributedQuantity, status, lines, audit))
                .isInstanceOf(NullPointerException.class);
  }

  private static Stream<Arguments> provideNotNullableParams() {
    return Stream.of(
        Arguments.of(new DistributionInner.Id(UUID.randomUUID()),
            new ReferenceId(UUID.randomUUID()),
            new UseId(UUID.randomUUID()),
            new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new ReferenceProductId(UUID.randomUUID()), new ProductOrderId(UUID.randomUUID()),
            new ProductVariantGroupId(UUID.randomUUID()),
            new TheoreticalQuantity(BigDecimal.ONE), new ConsumptionFactor(BigDecimal.ONE), new RequestedQuantity(BigDecimal.ONE),
            new DistributedQuantity(BigDecimal.ONE), DistributionInnerStatus.NON_DISTRIBUTABLE,
            new DistributionInnerLines(List.of()),
            null),
        Arguments.of(new DistributionInner.Id(UUID.randomUUID()),
            new ReferenceId(UUID.randomUUID()),
            new UseId(UUID.randomUUID()),
            new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new ReferenceProductId(UUID.randomUUID()), new ProductOrderId(UUID.randomUUID()),
            new ProductVariantGroupId(UUID.randomUUID()),
            new TheoreticalQuantity(BigDecimal.ONE), new ConsumptionFactor(BigDecimal.ONE), new RequestedQuantity(BigDecimal.ONE),
            new DistributedQuantity(BigDecimal.ONE), DistributionInnerStatus.NON_DISTRIBUTABLE,
            null,
            CompleteAudit.create("audit1", OffsetDateTime.now())),
        Arguments.of(new DistributionInner.Id(UUID.randomUUID()),
            new ReferenceId(UUID.randomUUID()),
            new UseId(UUID.randomUUID()),
            new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new ReferenceProductId(UUID.randomUUID()), new ProductOrderId(UUID.randomUUID()),
            new ProductVariantGroupId(UUID.randomUUID()),
            new TheoreticalQuantity(BigDecimal.ONE), new ConsumptionFactor(BigDecimal.ONE), new RequestedQuantity(BigDecimal.ONE),
            new DistributedQuantity(BigDecimal.ONE), null,
            new DistributionInnerLines(List.of()),
            CompleteAudit.create("audit1", OffsetDateTime.now())),
        Arguments.of(new DistributionInner.Id(UUID.randomUUID()),
            new ReferenceId(UUID.randomUUID()),
            new UseId(UUID.randomUUID()),
            new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new ReferenceProductId(UUID.randomUUID()), new ProductOrderId(UUID.randomUUID()),
            new ProductVariantGroupId(UUID.randomUUID()),
            new TheoreticalQuantity(BigDecimal.ONE), new ConsumptionFactor(BigDecimal.ONE), new RequestedQuantity(BigDecimal.ONE),
            null, DistributionInnerStatus.NON_DISTRIBUTABLE,
            new DistributionInnerLines(List.of()),
            CompleteAudit.create("audit1", OffsetDateTime.now())),
        Arguments.of(new DistributionInner.Id(UUID.randomUUID()),
            new ReferenceId(UUID.randomUUID()),
            new UseId(UUID.randomUUID()),
            new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new ReferenceProductId(UUID.randomUUID()), new ProductOrderId(UUID.randomUUID()),
            new ProductVariantGroupId(UUID.randomUUID()),
            new TheoreticalQuantity(BigDecimal.ONE), new ConsumptionFactor(BigDecimal.ONE), null,
            new DistributedQuantity(BigDecimal.ONE), DistributionInnerStatus.NON_DISTRIBUTABLE,
            new DistributionInnerLines(List.of()),
            CompleteAudit.create("audit1", OffsetDateTime.now())),
        Arguments.of(new DistributionInner.Id(UUID.randomUUID()),
            new ReferenceId(UUID.randomUUID()),
            new UseId(UUID.randomUUID()),
            new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new ReferenceProductId(UUID.randomUUID()), new ProductOrderId(UUID.randomUUID()),
            new ProductVariantGroupId(UUID.randomUUID()),
            new TheoreticalQuantity(BigDecimal.ONE), null, new RequestedQuantity(BigDecimal.ONE),
            new DistributedQuantity(BigDecimal.ONE), DistributionInnerStatus.NON_DISTRIBUTABLE,
            new DistributionInnerLines(List.of()),
            CompleteAudit.create("audit1", OffsetDateTime.now())),
        Arguments.of(new DistributionInner.Id(UUID.randomUUID()),
            new ReferenceId(UUID.randomUUID()),
            new UseId(UUID.randomUUID()),
            new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new ReferenceProductId(UUID.randomUUID()), new ProductOrderId(UUID.randomUUID()),
            new ProductVariantGroupId(UUID.randomUUID()),
            null, new ConsumptionFactor(BigDecimal.ONE), new RequestedQuantity(BigDecimal.ONE),
            new DistributedQuantity(BigDecimal.ONE), DistributionInnerStatus.NON_DISTRIBUTABLE,
            new DistributionInnerLines(List.of()),
            CompleteAudit.create("audit1", OffsetDateTime.now())),
        Arguments.of(new DistributionInner.Id(UUID.randomUUID()),
            new ReferenceId(UUID.randomUUID()),
            new UseId(UUID.randomUUID()),
            new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new ReferenceProductId(UUID.randomUUID()), new ProductOrderId(UUID.randomUUID()),
            null,
            new TheoreticalQuantity(BigDecimal.ONE), new ConsumptionFactor(BigDecimal.ONE), new RequestedQuantity(BigDecimal.ONE),
            new DistributedQuantity(BigDecimal.ONE), DistributionInnerStatus.NON_DISTRIBUTABLE,
            new DistributionInnerLines(List.of()),
            CompleteAudit.create("audit1", OffsetDateTime.now())),
        Arguments.of(new DistributionInner.Id(UUID.randomUUID()),
            new ReferenceId(UUID.randomUUID()),
            new UseId(UUID.randomUUID()),
            new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new ReferenceProductId(UUID.randomUUID()), null,
            new ProductVariantGroupId(UUID.randomUUID()),
            new TheoreticalQuantity(BigDecimal.ONE), new ConsumptionFactor(BigDecimal.ONE), new RequestedQuantity(BigDecimal.ONE),
            new DistributedQuantity(BigDecimal.ONE), DistributionInnerStatus.NON_DISTRIBUTABLE,
            new DistributionInnerLines(List.of()),
            CompleteAudit.create("audit1", OffsetDateTime.now())),
        Arguments.of(new DistributionInner.Id(UUID.randomUUID()),
            new ReferenceId(UUID.randomUUID()),
            new UseId(UUID.randomUUID()),
            new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            null,
            new ProductOrderId(UUID.randomUUID()),
            new ProductVariantGroupId(UUID.randomUUID()),
            new TheoreticalQuantity(BigDecimal.ONE), new ConsumptionFactor(BigDecimal.ONE), new RequestedQuantity(BigDecimal.ONE),
            new DistributedQuantity(BigDecimal.ONE), DistributionInnerStatus.NON_DISTRIBUTABLE,
            new DistributionInnerLines(List.of()),
            CompleteAudit.create("audit1", OffsetDateTime.now())),
        Arguments.of(new DistributionInner.Id(UUID.randomUUID()),
            new ReferenceId(UUID.randomUUID()),
            new UseId(UUID.randomUUID()),
            null,
            new ReferenceProductId(UUID.randomUUID()), new ProductOrderId(UUID.randomUUID()),
            new ProductVariantGroupId(UUID.randomUUID()),
            new TheoreticalQuantity(BigDecimal.ONE), new ConsumptionFactor(BigDecimal.ONE), new RequestedQuantity(BigDecimal.ONE),
            new DistributedQuantity(BigDecimal.ONE), DistributionInnerStatus.NON_DISTRIBUTABLE,
            new DistributionInnerLines(List.of()),
            CompleteAudit.create("audit1", OffsetDateTime.now())),
        Arguments.of(new DistributionInner.Id(UUID.randomUUID()),
            new ReferenceId(UUID.randomUUID()),
            null,
            new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new ReferenceProductId(UUID.randomUUID()), new ProductOrderId(UUID.randomUUID()),
            new ProductVariantGroupId(UUID.randomUUID()),
            new TheoreticalQuantity(BigDecimal.ONE), new ConsumptionFactor(BigDecimal.ONE), new RequestedQuantity(BigDecimal.ONE),
            new DistributedQuantity(BigDecimal.ONE), DistributionInnerStatus.NON_DISTRIBUTABLE,
            new DistributionInnerLines(List.of()),
            CompleteAudit.create("audit1", OffsetDateTime.now())),
        Arguments.of(new DistributionInner.Id(UUID.randomUUID()),
            null,
            new UseId(UUID.randomUUID()),
            new BudgetCycle(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
            new ReferenceProductId(UUID.randomUUID()), new ProductOrderId(UUID.randomUUID()),
            new ProductVariantGroupId(UUID.randomUUID()),
            new TheoreticalQuantity(BigDecimal.ONE), new ConsumptionFactor(BigDecimal.ONE), new RequestedQuantity(BigDecimal.ONE),
            new DistributedQuantity(BigDecimal.ONE), DistributionInnerStatus.NON_DISTRIBUTABLE,
            new DistributionInnerLines(List.of()),
            CompleteAudit.create("audit1", OffsetDateTime.now())));
  }
}
