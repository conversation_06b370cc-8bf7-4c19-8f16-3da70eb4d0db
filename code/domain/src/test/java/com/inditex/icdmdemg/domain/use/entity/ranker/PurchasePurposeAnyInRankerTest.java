package com.inditex.icdmdemg.domain.use.entity.ranker;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;
import java.util.stream.Stream;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class PurchasePurposeAnyInRankerTest {

  @ParameterizedTest
  @MethodSource("provideParamsWithCoincidences")
  void should_return_true_when_condition_is_any_in(final List<String> listParamsValues,
      final List<String> listConditionValues, final int expectedCoincidences) {

    final var result = PurchasePurposeAnyInRanker.evaluate(listParamsValues,
        listConditionValues);
    assertThat(result.isComplied()).isTrue();
    assertThat(result.coincidences()).isEqualTo(expectedCoincidences);
  }

  public static Stream<Arguments> provideParamsWithCoincidences() {
    return Stream.of(
        Arguments.of(List.of("T1", "T2"),
            List.of("T1", "T2", "T3"), 2),
        Arguments.of(List.of("T1", "T2"), List.of("T3", "T1"), 1),
        Arguments.of(List.of("T1", "T2"), List.of("T1", "T2"), 2));
  }

  @ParameterizedTest
  @MethodSource("provideParamsWithNoCoincidences")
  void should_return_false_and_no_coincidences_when_condition_is_any_in(final List<String> listParamsValues,
      final List<String> listConditionValues) {

    final var result = PurchasePurposeAnyInRanker.evaluate(listParamsValues,
        listConditionValues);
    assertThat(result.isComplied()).isFalse();
    assertThat(result.coincidences()).isZero();
  }

  public static Stream<Arguments> provideParamsWithNoCoincidences() {
    return Stream
        .of(Arguments.of(List.of("T1", "T2"), List.of("T3", "T4")));
  }

}
