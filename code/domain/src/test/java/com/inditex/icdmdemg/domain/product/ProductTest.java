package com.inditex.icdmdemg.domain.product;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.product.entity.ProductCampaign;
import com.inditex.icdmdemg.domain.product.entity.ProductColor;
import com.inditex.icdmdemg.domain.product.entity.ProductFamilies;
import com.inditex.icdmdemg.domain.product.entity.ProductFamily;
import com.inditex.icdmdemg.domain.product.entity.ProductId;
import com.inditex.icdmdemg.domain.product.entity.ProductOriginMarket;
import com.inditex.icdmdemg.domain.product.entity.ProductOwner;
import com.inditex.icdmdemg.domain.product.entity.ProductOwners;
import com.inditex.icdmdemg.domain.product.entity.ProductQuality;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.domain.product.entity.ProductSupplier;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

@TestInstance(Lifecycle.PER_CLASS)
class ProductTest {

  @Test
  void should_create() {
    final var id = String.valueOf(UUID.randomUUID());
    final var reference = String.valueOf(UUID.randomUUID());
    final var color = UUID.randomUUID();
    final var supplier = String.valueOf(UUID.randomUUID());
    final var campaign = String.valueOf(UUID.randomUUID());
    final var originMarket = UUID.randomUUID();
    final var quality = 333;
    final var families = new ProductFamilies(List.of());
    final var owners = new ProductOwners(List.of());
    final Product result = Product.create(
        new ProductId(id),
        new ProductReferenceId(reference),
        new ProductColor(color),
        new ProductSupplier(supplier),
        new ProductCampaign(campaign),
        new ProductOriginMarket(originMarket),
        new ProductQuality(quality),
        families,
        owners,
        OffsetDateTime.now());

    assertThat(result.getProductId().value()).isEqualTo(id);
    assertThat(result.getReferenceId().value()).isEqualTo(reference);
  }

  @Test
  void should_modify_product() {
    final var color = Instancio.create(ProductColor.class);
    final var supplier = Instancio.create(ProductSupplier.class);
    final var campaign = Instancio.create(ProductCampaign.class);
    final var originMarket = Instancio.create(ProductOriginMarket.class);
    final var quality = Instancio.create(ProductQuality.class);
    final var families = Instancio.createList(ProductFamily.class);
    final var owners = Instancio.createList(ProductOwner.class);
    final var now = OffsetDateTime.now();
    final var sut = Instancio.create(Product.class);

    final var result = sut.modify(color, supplier, campaign, originMarket, quality, families, owners, now);

    assertThat(result.getAudit().updatedAt()).isEqualTo(now);
  }

  @ParameterizedTest
  @MethodSource("createBombIdAndProductTaxonomiesParams")
  void should_update_when_some_is_different(final Product sut,
      final ProductColor color,
      final ProductSupplier supplier,
      final ProductCampaign campaign,
      final ProductOriginMarket originMarket,
      final ProductQuality quality,
      final ProductFamilies families,
      final ProductOwners owners,
      final boolean expected) {
    assertThat(sut.willBeUpdatedWith(color, supplier, campaign, originMarket, quality, families, owners))
        .isEqualTo(expected);
  }

  private Stream<Arguments> createBombIdAndProductTaxonomiesParams() {
    final var sut = Instancio.create(Product.class);
    final var color = Instancio.create(ProductColor.class);
    final var supplier = Instancio.create(ProductSupplier.class);
    final var campaign = Instancio.create(ProductCampaign.class);
    final var originMarket = Instancio.create(ProductOriginMarket.class);
    final var quality = Instancio.create(ProductQuality.class);
    final var families = new ProductFamilies(Instancio.createList(ProductFamily.class));
    final var owners = new ProductOwners(Instancio.createList(ProductOwner.class));

    return Stream.of(
        Arguments.of(sut, sut.getColorId(), sut.getSupplierId(), sut.getCampaignId(),
            sut.getOriginMarketId(),
            sut.getQuality(), sut.getFamilies(), sut.getOwners(), false),
        Arguments.of(sut, color, sut.getSupplierId(), sut.getCampaignId(), sut.getOriginMarketId(),
            sut.getQuality(), sut.getFamilies(), sut.getOwners(), true),
        Arguments.of(sut, sut.getColorId(), supplier, sut.getCampaignId(), sut.getOriginMarketId(),
            sut.getQuality(), sut.getFamilies(), sut.getOwners(), true),
        Arguments.of(sut, sut.getColorId(), sut.getSupplierId(), campaign, sut.getOriginMarketId(),
            sut.getQuality(), sut.getFamilies(), sut.getOwners(), true),
        Arguments.of(sut, sut.getColorId(), sut.getSupplierId(), sut.getCampaignId(), originMarket,
            sut.getQuality(), sut.getFamilies(), sut.getOwners(), true),
        Arguments.of(sut, sut.getColorId(), sut.getSupplierId(), sut.getCampaignId(),
            sut.getOriginMarketId(),
            quality, sut.getFamilies(), sut.getOwners(), true),
        Arguments.of(sut, sut.getColorId(), sut.getSupplierId(), sut.getCampaignId(),
            sut.getOriginMarketId(),
            sut.getQuality(), families, sut.getOwners(), true),
        Arguments.of(sut, sut.getColorId(), sut.getSupplierId(), sut.getCampaignId(),
            sut.getOriginMarketId(),
            sut.getQuality(), sut.getFamilies(), owners, true));
  }
}
