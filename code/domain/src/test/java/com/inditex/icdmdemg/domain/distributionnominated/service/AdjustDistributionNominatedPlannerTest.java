package com.inditex.icdmdemg.domain.distributionnominated.service;

import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.created;
import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.kept;
import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.updated;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.params.ParameterizedTest.INDEX_PLACEHOLDER;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeRequestedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus.MaterialCommitmentUseStatusEnum;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycleChangePendingQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedLineMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Commitment;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.DistributionNominatedPlannerType;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Line;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan.Adjust;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Request;
import com.inditex.icdmdemg.domain.shared.EntityAndActionResult;
import com.inditex.icdmdemg.shared.utils.CurrentMethodName;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.icdmdemg.shared.utils.RandomValue;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

@TestInstance(Lifecycle.PER_CLASS)
class AdjustDistributionNominatedPlannerTest {

  public static final String PARAMETERIZED_TEST_DESCRIPTION = "[" + INDEX_PLACEHOLDER + "] [{0}]";

  @ParameterizedTest(name = PARAMETERIZED_TEST_DESCRIPTION)
  @MethodSource("getInputCasesAndExpectedReturn")
  void should_return_correct_list_from_different_inputs(
      final String ignoredTestName,
      final DistributionNominatedPlanner sut,
      final List<EntityAndActionResult<Line>> output) {
    final var actual = sut.executePlan();
    assertThat(actual).containsExactlyInAnyOrderElementsOf(output);
  }

  @Test
  void shouldThrowWhenLineWithoutRequest() {
    final var requests = List.<Request>of();
    final var lines = List.of(line(101, 201, 301, 901, 100, 0, 1));
    final var commitments = List.<Commitment>of();
    assertThatThrownBy(
        () -> new AdjustDistributionNominatedPlanner(requests, lines, commitments, List.of(), DistributionNominatedPlannerType.DEFAULT))
            .isInstanceOf(IllegalArgumentException.class);
  }

  @Test
  void shouldThrowWhenLineIsBeforeRequest() {
    final var requests = List.of(request(101, 100, 2));
    final var lines = List.of(line(101, 201, 301, 901, 100, 0, 1));
    final var commitments = List.<Commitment>of();
    assertThatThrownBy(
        () -> new AdjustDistributionNominatedPlanner(requests, lines, commitments, List.of(), DistributionNominatedPlannerType.DEFAULT))
            .isInstanceOf(IllegalArgumentException.class);
  }

  public Stream<Arguments> getInputCasesAndExpectedReturn() {
    return Stream.of(
        this.shouldAdjustLinesCreateUnsatisfiedRequest(),
        this.shouldAdjustLinesCreateUnsatisfiedRequestAndNotUseBudgetCycleChangeCommitments(),
        this.shouldAdjustLinesCreateUnsatisfiedRequestWhenNoCommitment(),
        this.shouldAdjustLinesDeleteLineNonDistributed(),
        this.shouldAdjustLinesNotCreateUnsatisfiedRequestWhenDistributed(),
        this.shouldAdjustLinesCreateUnsatisfiedRequestFromMostRecentRequest(),
        this.shouldAdjustLinesNotCreateUnsatisfiedRequestWhenCommitmentFillsInPartially(),
        this.shouldAdjustLinesNotCreateUnsatisfiedRequestWhenCommitment2FillsLine(),
        this.shouldAdjustLinesCreateUnsatisfiedRequestFollowingOrder(),
        this.shouldAdjustLinesSatisfyRequestWithNoLinesWithNewCommitment(),
        this.shouldAdjustLinesSatisfyRequestWithExistingIncrementedCommitment(),
        this.shouldAdjustLinesSatisfyRequestWithNewCommitment(),
        this.shouldAdjustLinesSatisfyRequestWithNewCommitmentIgnoringBudgetCycleChangeCommitment(),
        this.shouldAdjustLinesSatisfyRequestWithNewCommitmentWithSmallerDate(),
        this.shouldAdjustLinesPartiallySatisfyRequestWithNewCommitmentWithSmallerDate(),
        this.shouldAdjustLinesWithTwoUnsatisfiedRequests(),
        this.shouldAdjustLinesUpButClosedCommitmentIsUntouchable(),
        this.shouldAdjustLinesUpButBudgetCycleCommitmentIsUntouchable(),
        this.shouldAdjustLinesUpButUsedClosedCommitmentIsUntouchable(),
        this.shouldAdjustLinesDownButClosedCommitmentIsTouchable());
  }

  private Arguments shouldAdjustLinesCreateUnsatisfiedRequest() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(
                line(101, 201, 301, 901, 50, 0, 1),
                line(101, 201, 302, 901, 50, 0, 2)),
            List.of(
                commitment(201, 301, 901, 30, 1),
                commitment(201, 302, 901, 50, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(101, 201, 301, 901, 30, 0, 1)),
            kept(line(101, 201, 302, 901, 50, 0, 2))));
  }

  private Arguments shouldAdjustLinesCreateUnsatisfiedRequestAndNotUseBudgetCycleChangeCommitments() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(
                line(101, 201, 301, 901, 50, 0, 1),
                line(101, 201, 302, 901, 50, 0, 2)),
            List.of(
                budgetCycleChangeCommitment(201, 301, 901, 200, 1),
                commitment(201, 302, 901, 50, 1),
                commitment(201, 303, 901, 30, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(101, 201, 301, 901, 0, 0, 1)),
            created(line(101, 201, 303, 901, 30, 0, null)),
            kept(line(101, 201, 302, 901, 50, 0, 2))));
  }

  private Arguments shouldAdjustLinesCreateUnsatisfiedRequestWhenNoCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(
                line(101, 201, 301, 901, 50, 0, 1),
                line(101, 201, 302, 901, 50, 0, 2)),
            List.of(),
            List.of(), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(101, 201, 301, 901, 0, 0, 1)),
            updated(line(101, 201, 302, 901, 0, 0, 2))));
  }

  private Arguments shouldAdjustLinesDeleteLineNonDistributed() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(
                request(101, 100, 1),
                request(102, 200, 2)),
            List.of(
                line(101, 201, 301, 901, 50, 40, 1),
                line(101, 201, 302, 901, 50, 0, 2),
                line(102, 201, 301, 901, 200, 0, 2)),
            List.of(
                commitment(201, 301, 901, 30, 1),
                commitment(201, 302, 901, 50, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 50, 40, 1)),
            kept(line(101, 201, 302, 901, 50, 0, 2)),
            updated(line(102, 201, 301, 901, 0, 0, 2))));
  }

  private Arguments shouldAdjustLinesNotCreateUnsatisfiedRequestWhenDistributed() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(
                line(101, 201, 301, 901, 50, 40, 1),
                line(101, 201, 302, 901, 50, 0, 2)),
            List.of(
                commitment(201, 301, 901, 30, 1),
                commitment(201, 302, 901, 50, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(// ??
            kept(line(101, 201, 301, 901, 50, 40, 1)),
            kept(line(101, 201, 302, 901, 50, 0, 2))));
  }

  private Arguments shouldAdjustLinesCreateUnsatisfiedRequestFromMostRecentRequest() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(
                request(101, 100, 1),
                request(102, 200, 2)),
            List.of(
                line(101, 201, 301, 901, 100, 0, 1),
                line(102, 201, 301, 901, 100, 0, 3),
                line(102, 201, 302, 901, 100, 0, 4)),
            List.of(
                commitment(201, 301, 901, 150, 1),
                commitment(201, 302, 901, 100, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(102, 201, 301, 901, 50, 0, 3)),
            kept(line(101, 201, 301, 901, 100, 0, 1)),
            kept(line(102, 201, 302, 901, 100, 0, 4))));
  }

  private Arguments shouldAdjustLinesNotCreateUnsatisfiedRequestWhenCommitmentFillsInPartially() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(
                request(101, 100, 1),
                request(102, 200, 2)),
            List.of(
                line(101, 201, 301, 901, 100, 0, 1),
                line(102, 201, 301, 901, 100, 0, 2),
                line(102, 201, 302, 901, 100, 0, 2)),
            List.of(
                commitment(201, 301, 901, 150, 1),
                commitment(201, 302, 901, 200, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 100, 0, 1)),
            updated(line(102, 201, 301, 901, 50, 0, 2)),
            updated(line(102, 201, 302, 901, 150, 0, 2))));
  }

  private Arguments shouldAdjustLinesNotCreateUnsatisfiedRequestWhenCommitment2FillsLine() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(
                line(101, 201, 301, 901, 50, 0, 1),
                line(101, 201, 302, 901, 50, 0, 2)),
            List.of(
                commitment(201, 301, 901, 30, 1),
                commitment(201, 302, 901, 100, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(101, 201, 301, 901, 30, 0, 1)),
            updated(line(101, 201, 302, 901, 70, 0, 2))));
  }

  private Arguments shouldAdjustLinesCreateUnsatisfiedRequestFollowingOrder() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(
                request(101, 100, 1),
                request(102, 200, 1)),
            List.of(
                line(101, 201, 301, 901, 100, 0, 1),
                line(102, 201, 301, 901, 100, 0, 2),
                line(102, 201, 302, 901, 100, 0, 2)),
            List.of(
                commitment(201, 301, 901, 150, 1),
                commitment(201, 302, 901, 100, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(102, 201, 301, 901, 50, 0, 2)),
            kept(line(101, 201, 301, 901, 100, 0, 1)),
            kept(line(102, 201, 302, 901, 100, 0, 2))));
  }

  private Arguments shouldAdjustLinesSatisfyRequestWithExistingIncrementedCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(request(101, 50, 1)),
            List.of(
                line(101, 201, 301, 901, 30, 0, 1)),
            List.of(
                commitment(201, 301, 901, 50, 1),
                commitment(201, 302, 901, 50, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(101, 201, 301, 901, 50, 0, 1))));
  }

  private Arguments shouldAdjustLinesSatisfyRequestWithNoLinesWithNewCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(request(101, 50, 1)),
            List.of(),
            List.of(
                commitment(201, 301, 901, 50, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            created(line(101, 201, 301, 901, 50, 0, null))));
  }

  private Arguments shouldAdjustLinesSatisfyRequestWithNewCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(request(101, 50, 1)),
            List.of(
                line(101, 201, 301, 901, 30, 0, 1)),
            List.of(
                commitment(201, 301, 901, 30, 1),
                commitment(201, 302, 901, 50, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 30, 0, 1)),
            created(line(101, 201, 302, 901, 20, 0, null))));
  }

  private Arguments shouldAdjustLinesSatisfyRequestWithNewCommitmentIgnoringBudgetCycleChangeCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(request(101, 50, 1)),
            List.of(
                line(101, 201, 301, 901, 30, 0, 1)),
            List.of(
                commitment(201, 301, 901, 30, 1),
                budgetCycleChangeCommitment(201, 303, 901, 1000, 0),
                commitment(201, 302, 901, 50, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 30, 0, 1)),
            created(line(101, 201, 302, 901, 20, 0, null))));
  }

  private Arguments shouldAdjustLinesSatisfyRequestWithNewCommitmentWithSmallerDate() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(request(101, 50, 1)),
            List.of(
                line(101, 201, 301, 901, 30, 0, 1)),
            List.of(
                commitment(201, 301, 901, 30, 1),
                commitment(201, 302, 901, 50, 1),
                commitment(201, 303, 901, 100, 2)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 30, 0, 1)),
            created(line(101, 201, 302, 901, 20, 0, null))));
  }

  private Arguments shouldAdjustLinesPartiallySatisfyRequestWithNewCommitmentWithSmallerDate() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(request(101, 200, 1)),
            List.of(
                line(101, 201, 301, 901, 30, 0, 1)),
            List.of(
                commitment(201, 301, 901, 30, 1),
                commitment(201, 302, 901, 50, 1),
                commitment(201, 303, 901, 100, 2)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 30, 0, 1)),
            created(line(101, 201, 302, 901, 50, 0, null)),
            created(line(101, 201, 303, 901, 100, 0, null))));
  }

  private Arguments shouldAdjustLinesWithTwoUnsatisfiedRequests() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(
                request(101, 1000, 1),
                request(102, 5000, 1)),
            List.of(
                line(101, 201, 301, 901, 0, 0, 1),
                line(102, 201, 301, 901, 0, 0, 1)),
            List.of(
                commitment(201, 302, 901, 4000, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 0, 0, 1)),
            kept(line(102, 201, 301, 901, 0, 0, 1)),
            created(line(101, 201, 302, 901, 1000, 0, null)),
            created(line(102, 201, 302, 901, 3000, 0, null))));
  }

  private Arguments shouldAdjustLinesUpButClosedCommitmentIsUntouchable() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(
                request(101, 1000, 1)),
            List.of(
                line(101, 201, 301, 901, 500, 0, 1)),
            List.of(
                commitment(201, 301, 901, 500, 1),
                closedCommitment(201, 302, 901, 4000, 2)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 500, 0, 1))));
  }

  private Arguments shouldAdjustLinesUpButBudgetCycleCommitmentIsUntouchable() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(
                request(101, 1000, 1)),
            List.of(
                line(101, 201, 301, 901, 500, 0, 1)),
            List.of(
                commitment(201, 301, 901, 500, 1),
                budgetCycleChangeCommitment(201, 302, 901, 4000, 2)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 500, 0, 1))));
  }

  private Arguments shouldAdjustLinesUpButUsedClosedCommitmentIsUntouchable() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(
                request(101, 1000, 1)),
            List.of(
                line(101, 201, 301, 901, 500, 0, 1)),
            List.of(
                closedCommitment(201, 301, 901, 1000, 1)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 500, 0, 1))));
  }

  private Arguments shouldAdjustLinesDownButClosedCommitmentIsTouchable() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AdjustDistributionNominatedPlanner(
            List.of(
                request(101, 1000, 1)),
            List.of(
                line(101, 201, 301, 901, 500, 0, 1),
                line(101, 201, 302, 901, 500, 0, 1)),
            List.of(
                closedCommitment(201, 301, 901, 400, 1),
                commitment(201, 302, 901, 500, 2)),
            List.of(),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(101, 201, 301, 901, 400, 0, 1)),
            kept(line(101, 201, 302, 901, 500, 0, 1))));
  }

  private static Request request(final Integer id, final double quantity, final Integer creationDay) {
    return new Request(new Id(UuidMother.fromInteger(id)), BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_UP),
        OffsetDateTimeMother.fromInteger(creationDay));
  }

  static Commitment commitment(final Integer orderId, final Integer lineId, final Integer supplierId, final Integer referenceId,
      final Integer targetReferenceId,
      final MaterialCommitmentUseStatus status,
      final double quantity, final Integer expectedDay,
      final MaterialCommitmentUseBudgetCycleChangeRequestedAt budgetCycleChangeRequestedDate) {
    return new Commitment(
        new CommitmentOrder(
            new CommitmentOrder.Id(UuidMother.fromInteger(orderId)),
            new CommitmentOrder.LineId(UuidMother.fromInteger(lineId)),
            new CommitmentOrder.SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId)))),
        new ReferenceId(UuidMother.fromInteger(referenceId)),
        new ReferenceId(UuidMother.fromInteger(targetReferenceId)),
        status,
        BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_UP),
        budgetCycleChangeRequestedDate, null,
        OffsetDateTimeMother.fromInteger(expectedDay));
  }

  private static Commitment budgetCycleChangeCommitment(final Integer orderId, final Integer lineId, final Integer supplierId,
      final double quantity,
      final Integer expectedDay) {
    final var commitmentStatus = MaterialCommitmentUseStatus.of(RandomValue.randomEnum(MaterialCommitmentUseStatusEnum.class));
    return commitment(orderId, lineId, supplierId, 401, 401, commitmentStatus, quantity, expectedDay,
        new MaterialCommitmentUseBudgetCycleChangeRequestedAt(OffsetDateTime.now()));
  }

  private static Commitment commitment(final Integer orderId, final Integer lineId, final Integer supplierId, final double quantity,
      final Integer expectedDay) {
    final var commitmentStatus = MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN);
    return commitment(orderId, lineId, supplierId, 401, 401, commitmentStatus, quantity, expectedDay, null);
  }

  private static Commitment closedCommitment(final Integer orderId, final Integer lineId, final Integer supplierId, final double quantity,
      final Integer expectedDay) {
    final var commitmentStatus = MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.CLOSED);
    return commitment(orderId, lineId, supplierId, 401, 401, commitmentStatus, quantity, expectedDay, null);
  }

  private static Line line(final Integer rootId, final Integer orderId, final Integer lineId, final Integer supplierId,
      final Integer referenceId,
      final Integer targetReferenceId, final double requested, final double distributed, final Integer creationDay) {
    return new Line(
        new Id(UuidMother.fromInteger(rootId)),
        new CommitmentOrder(
            new CommitmentOrder.Id(UuidMother.fromInteger(orderId)),
            new CommitmentOrder.LineId(UuidMother.fromInteger(lineId)),
            new CommitmentOrder.SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId)))),
        new ReferenceId(UuidMother.fromInteger(referenceId)),
        new ReferenceId(UuidMother.fromInteger(targetReferenceId)),
        BigDecimal.valueOf(requested).setScale(2, RoundingMode.HALF_UP),
        BigDecimal.valueOf(distributed).setScale(2, RoundingMode.HALF_UP),
        Optional.ofNullable(creationDay).map(OffsetDateTimeMother::fromInteger));
  }

  private static Line line(final Integer rootId, final Integer orderId, final Integer lineId, final Integer supplierId,
      final double requested,
      final double distributed,
      final Integer creationDay) {
    return line(rootId, orderId, lineId, supplierId, 401, 401, requested, distributed, creationDay);
  }

  @Test
  void shouldAdjustDefaultDNButNotBudgetCycleChangeDN() {
    final var sharedRawMaterial = new SharedRawMaterialNominated(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID().toString());
    final var defaultCommitment = MaterialCommitmentUseMother.with(
        UUID.randomUUID(),
        UUID.randomUUID(),
        sharedRawMaterial.referenceId().value(), sharedRawMaterial.useId().value(),
        UUID.fromString(sharedRawMaterial.budgetCycle().value()),
        BigDecimal.valueOf(20000));

    final var defaultDistributionNominatedLine =
        DistributionNominatedLineMother.created()
            .commitmentOrder(
                new CommitmentOrder(
                    new CommitmentOrder.Id(UUID.fromString(defaultCommitment.getOrderLine().orderId().value())),
                    new CommitmentOrder.LineId(UUID.fromString(defaultCommitment.getOrderLine().orderLineId().value())),
                    new CommitmentOrder.SupplierId(defaultCommitment.getServiceLocalizationId().value())))
            .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(100)))
            .alternativeReference(null)
            .build();
    final var defaultDistributionNominated =
        DistributionNominatedMother
            .pendingWithLines(List.of(defaultDistributionNominatedLine))
            .referenceId(sharedRawMaterial.referenceId())
            .useId(sharedRawMaterial.useId())
            .budgetCycle(sharedRawMaterial.budgetCycle())
            .requestedQuantity(new DistributionNominated.RequestedQuantity(BigDecimal.valueOf(500)))
            .build();

    final var budgetCycleChangeDistributionNominatedLine =
        DistributionNominatedLineMother.created()
            .alternativeReference(null)
            .requestedQuantity(new RequestedQuantity(BigDecimal.ZERO)).build();
    final var budgetCycleChangeDistributionNominated = DistributionNominatedMother
        .pendingWithLines(List.of(budgetCycleChangeDistributionNominatedLine))
        .referenceId(sharedRawMaterial.referenceId())
        .useId(sharedRawMaterial.useId())
        .budgetCycle(sharedRawMaterial.budgetCycle())
        .requestedQuantity(
            new com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity(BigDecimal.valueOf(50)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.valueOf(50)))
        .build();

    final var distributions = List.of(defaultDistributionNominated, budgetCycleChangeDistributionNominated);
    final var adjustedCommitments = List.of(new CommitmentAdjusted(defaultCommitment, BigDecimal.valueOf(500)));

    final var updatedLine = new Line(new Id(defaultDistributionNominated.getId().value()),
        defaultDistributionNominatedLine.commitmentOrder(),
        defaultDistributionNominated.referenceId(), defaultDistributionNominated.referenceId(),
        defaultDistributionNominated.requestedQuantity().value(), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.of(defaultDistributionNominatedLine.audit().createdAt()));
    final var keptLine = new Line(
        new Id(budgetCycleChangeDistributionNominated.getId()
            .value()),
        budgetCycleChangeDistributionNominatedLine.commitmentOrder(),
        budgetCycleChangeDistributionNominated.referenceId(), budgetCycleChangeDistributionNominated.referenceId(),
        budgetCycleChangeDistributionNominatedLine.requestedQuantity().value(),
        budgetCycleChangeDistributionNominatedLine.distributedQuantity().value(),
        Optional.of(budgetCycleChangeDistributionNominatedLine.audit().createdAt()));

    final var planner =
        DistributionNominatedPlanner.fromDistributions(sharedRawMaterial, distributions, adjustedCommitments, new Adjust(),
            DistributionNominatedPlannerType.DEFAULT);
    final var adjustedLines = planner.executePlan();

    assertThat(adjustedLines).containsExactlyInAnyOrderElementsOf(List.of(
        updated(updatedLine),
        kept(keptLine)));
  }

  @Test
  void should_adjust_budget_cycle_change_DN_but_not_default_DN() {
    final var sharedRawMaterial = new SharedRawMaterialNominated(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID().toString());
    final var budgetCycleChangeCommitment = MaterialCommitmentUseMother.withBudgetCycleChange(
        UUID.randomUUID(),
        UUID.randomUUID(),
        sharedRawMaterial.referenceId().value(), sharedRawMaterial.useId().value(),
        UUID.fromString(sharedRawMaterial.budgetCycle().value()),
        BigDecimal.valueOf(20000));

    final var defaultLameDistributionNominatedLine =
        DistributionNominatedLineMother.created()
            .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(100)))
            .alternativeReference(null)
            .build();
    final var defaultDistributionNominated =
        DistributionNominatedMother
            .pendingWithLines(List.of(defaultLameDistributionNominatedLine))
            .referenceId(sharedRawMaterial.referenceId())
            .useId(sharedRawMaterial.useId())
            .budgetCycle(sharedRawMaterial.budgetCycle())
            .requestedQuantity(new DistributionNominated.RequestedQuantity(BigDecimal.valueOf(500)))
            .build();

    final var budgetCycleChangeDistributionNominatedLine =
        DistributionNominatedLineMother.created()
            .alternativeReference(null)
            .requestedQuantity(new RequestedQuantity(BigDecimal.ZERO)).build();
    final var budgetCycleChangeDistributionNominated = DistributionNominatedMother
        .pendingWithLines(List.of(budgetCycleChangeDistributionNominatedLine))
        .referenceId(sharedRawMaterial.referenceId())
        .useId(sharedRawMaterial.useId())
        .budgetCycle(sharedRawMaterial.budgetCycle())
        .requestedQuantity(
            new com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity(BigDecimal.valueOf(50)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.valueOf(50)))
        .build();

    final var distributions = List.of(defaultDistributionNominated, budgetCycleChangeDistributionNominated);
    final var adjustedCommitments = List.of(new CommitmentAdjusted(budgetCycleChangeCommitment, BigDecimal.valueOf(500)));

    final var keptLine1 = new Line(new Id(defaultDistributionNominated.getId().value()),
        defaultLameDistributionNominatedLine.commitmentOrder(),
        defaultDistributionNominated.referenceId(), defaultDistributionNominated.referenceId(),
        defaultLameDistributionNominatedLine.requestedQuantity().value(), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.of(defaultLameDistributionNominatedLine.audit().createdAt()));
    final var keepLine2 = new Line(
        new Id(budgetCycleChangeDistributionNominated.getId()
            .value()),
        budgetCycleChangeDistributionNominatedLine.commitmentOrder(),
        budgetCycleChangeDistributionNominated.referenceId(), budgetCycleChangeDistributionNominated.referenceId(),
        NumericUtils.roundUpScale2(BigDecimal.ZERO),
        budgetCycleChangeDistributionNominatedLine.distributedQuantity().value(),
        Optional.of(budgetCycleChangeDistributionNominatedLine.audit().createdAt()));
    final var createdLine = new Line(
        new Id(budgetCycleChangeDistributionNominated.getId()
            .value()),
        new CommitmentOrder(
            new CommitmentOrder.Id(UUID.fromString(budgetCycleChangeCommitment.getOrderLine().orderId().value())),
            new CommitmentOrder.LineId(UUID.fromString(budgetCycleChangeCommitment.getOrderLine().orderLineId().value())),
            new CommitmentOrder.SupplierId(budgetCycleChangeCommitment.getServiceLocalizationId().value())),
        budgetCycleChangeDistributionNominated.referenceId(), budgetCycleChangeDistributionNominated.referenceId(),
        budgetCycleChangeDistributionNominated.requestedQuantity().value(),
        budgetCycleChangeDistributionNominatedLine.distributedQuantity().value(),
        Optional.empty());

    final var planner =
        DistributionNominatedPlanner.fromDistributions(sharedRawMaterial, distributions, adjustedCommitments,
            new Adjust(),
            DistributionNominatedPlannerType.BUDGET_CYCLE_CHANGE);
    final var adjustedLines = planner.executePlan();

    assertThat(adjustedLines).containsExactlyInAnyOrderElementsOf(List.of(
        kept(keptLine1),
        kept(keepLine2),
        created(createdLine)));
  }

  @Test
  void should_end_adjusting_budget_cycle_change_when_already_some_quantity_assigned_before() {
    final var sharedRawMaterial = new SharedRawMaterialNominated(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID().toString());
    final var firstBudgetCycleChangeCommitment = MaterialCommitmentUseMother.withBudgetCycleChange(
        UUID.randomUUID(),
        UUID.randomUUID(),
        sharedRawMaterial.referenceId().value(), sharedRawMaterial.useId().value(),
        UUID.fromString(sharedRawMaterial.budgetCycle().value()),
        BigDecimal.valueOf(100));
    final var secondBudgetCycleChangeCommitment = MaterialCommitmentUseMother.withBudgetCycleChange(
        UUID.fromString(firstBudgetCycleChangeCommitment.getOrderLine().orderId().value()),
        UUID.randomUUID(),
        sharedRawMaterial.referenceId().value(), sharedRawMaterial.useId().value(),
        UUID.fromString(sharedRawMaterial.budgetCycle().value()),
        BigDecimal.valueOf(400));

    final var oldBudgetCycleLine =
        DistributionNominatedLineMother.created()
            .alternativeReference(null)
            .requestedQuantity(new RequestedQuantity(BigDecimal.ZERO)).build();
    final var budgetCycleChangeFirstLine =
        DistributionNominatedLineMother.created()
            .commitmentOrder(new CommitmentOrder(
                new CommitmentOrder.Id(UUID.fromString(firstBudgetCycleChangeCommitment.getOrderLine().orderId().value())),
                new LineId(UUID.fromString(firstBudgetCycleChangeCommitment.getOrderLine().orderLineId().value())),
                new SupplierId(firstBudgetCycleChangeCommitment.getServiceLocalizationId().value())))
            .alternativeReference(null)
            .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(100))).build();
    final var budgetCycleChangeDistributionNominated = DistributionNominatedMother
        .pendingWithLines(List.of(oldBudgetCycleLine, budgetCycleChangeFirstLine))
        .referenceId(sharedRawMaterial.referenceId())
        .useId(sharedRawMaterial.useId())
        .budgetCycle(sharedRawMaterial.budgetCycle())
        .requestedQuantity(
            new com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity(BigDecimal.valueOf(500)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.valueOf(500)))
        .build();

    final var distributions = List.of(budgetCycleChangeDistributionNominated);
    final var adjustedCommitments = List.of(new CommitmentAdjusted(firstBudgetCycleChangeCommitment, BigDecimal.valueOf(100)),
        new CommitmentAdjusted(secondBudgetCycleChangeCommitment, BigDecimal.valueOf(400)));

    final var keptLine1 = new Line(
        new Id(budgetCycleChangeDistributionNominated.getId()
            .value()),
        oldBudgetCycleLine.commitmentOrder(),
        budgetCycleChangeDistributionNominated.referenceId(),
        budgetCycleChangeDistributionNominated.referenceId(),
        NumericUtils.roundUpScale2(BigDecimal.ZERO),
        oldBudgetCycleLine.distributedQuantity().value(),
        Optional.of(oldBudgetCycleLine.audit().createdAt()));
    final var keepLine2 = new Line(
        new Id(budgetCycleChangeDistributionNominated.getId()
            .value()),
        budgetCycleChangeFirstLine.commitmentOrder(),
        budgetCycleChangeDistributionNominated.referenceId(),
        budgetCycleChangeDistributionNominated.referenceId(),
        NumericUtils.roundUpScale2(BigDecimal.valueOf(100)),
        budgetCycleChangeFirstLine.distributedQuantity().value(),
        Optional.of(budgetCycleChangeFirstLine.audit().createdAt()));
    final var createdLine = new Line(
        new Id(budgetCycleChangeDistributionNominated.getId()
            .value()),
        new CommitmentOrder(
            new CommitmentOrder.Id(UUID.fromString(secondBudgetCycleChangeCommitment.getOrderLine().orderId().value())),
            new LineId(UUID.fromString(secondBudgetCycleChangeCommitment.getOrderLine().orderLineId().value())),
            new SupplierId(secondBudgetCycleChangeCommitment.getServiceLocalizationId().value())),
        budgetCycleChangeDistributionNominated.referenceId(),
        budgetCycleChangeDistributionNominated.referenceId(),
        NumericUtils.roundUpScale2(BigDecimal.valueOf(400)),
        NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.empty());

    final var planner =
        DistributionNominatedPlanner.fromDistributions(sharedRawMaterial, distributions, adjustedCommitments,
            new Adjust(),
            DistributionNominatedPlannerType.BUDGET_CYCLE_CHANGE);
    final var adjustedLines = planner.executePlan();

    assertThat(adjustedLines).containsExactlyInAnyOrderElementsOf(List.of(
        kept(keptLine1),
        kept(keepLine2),
        created(createdLine)));
  }
}
