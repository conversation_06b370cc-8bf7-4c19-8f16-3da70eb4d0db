package com.inditex.icdmdemg.domain.commitmentadjusted;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.ParameterizedTest.INDEX_PLACEHOLDER;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.verify;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseCollection;
import com.inditex.icdmdemg.domain.commitmentuse.MaterialCommitmentUseRepository;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseSharedRawMaterial;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.MigratedCommitmentConsumptionCollection;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.MigratedCommitmentConsumptionRepository;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.MigratedCommitmentConsumptionRepository.CommitmentInfo;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.mother.MigratedCommitmentConsumptionMother;
import com.inditex.icdmdemg.shared.utils.CurrentMethodName;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.SliceImpl;

@TestInstance(Lifecycle.PER_CLASS)
class CommitmentAdjustedProviderTest {
  private CommitmentAdjustedProvider sut;

  private static final String PARAMETERIZED_TEST_DESCRIPTION = "[" + INDEX_PLACEHOLDER + "] [{0}]";

  private MaterialCommitmentUseRepository materialCommitmentUseRepository;

  private MigratedCommitmentConsumptionRepository migratedCommitmentConsumptionRepository;

  @BeforeAll
  void setUp() {
    this.materialCommitmentUseRepository = mock(MaterialCommitmentUseRepository.class);
    this.migratedCommitmentConsumptionRepository = mock(MigratedCommitmentConsumptionRepository.class);

    this.sut = new CommitmentAdjustedProvider(this.migratedCommitmentConsumptionRepository, this.materialCommitmentUseRepository);
  }

  @AfterEach
  void resetMocks() {
    reset(this.materialCommitmentUseRepository, this.migratedCommitmentConsumptionRepository);
  }

  @Test
  void should_return_empty_with_empty_input() {

    final var actual =
        this.sut.findByAnySharedRawMaterial(List.of());

    assertThat(actual).isEmpty();
  }

  @ParameterizedTest(name = PARAMETERIZED_TEST_DESCRIPTION)
  @MethodSource("providedCommitmentAndMigratedQuantities")
  void should_return_correct_updated_quantities_commitments(
      final String ignoredTestName,
      final MigratedCommitmentConsumptionCollection migratedCommitmentConsumptionCollection,
      final MaterialCommitmentUseCollection materialCommitmentUseCollection,
      final List<CommitmentAdjusted> output) {
    final var expected = materialCommitmentUseCollection.materialCommitmentUses().stream()
        .map(materialCommitmentUse -> CommitmentInfo.of(
            materialCommitmentUse.getOrderLine().orderId().value(),
            materialCommitmentUse.getOrderLine().orderLineId().value(),
            materialCommitmentUse.getMaterialReferenceId().value()))
        .toList();

    doReturn(migratedCommitmentConsumptionCollection).when(this.migratedCommitmentConsumptionRepository)
        .findByCommitmentsInfo(any());
    doReturn(new SliceImpl<>(materialCommitmentUseCollection.materialCommitmentUses())).when(this.materialCommitmentUseRepository)
        .findByAnySharedRawMaterial(any(), any());

    final var actual =
        this.sut.findBySharedRawMaterialPaged(
            MaterialCommitmentUseSharedRawMaterial.of(UuidMother.fromInteger(1).toString(), UuidMother.fromInteger(2).toString(),
                UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UuidMother.fromInteger(3))),
            Pageable.unpaged());

    assertThat(actual).isNotNull().containsExactlyInAnyOrderElementsOf(output);
    verify(this.migratedCommitmentConsumptionRepository)
        .findByCommitmentsInfo(expected);
  }

  public Stream<Arguments> providedCommitmentAndMigratedQuantities() {
    return Stream.of(
        this.shouldReturnEmptyWhenEmpty(),
        this.shouldUpdateMaterialCommitmentsWithMigratedConsumptions(),
        this.shouldLeaveCommitmentQuantityIfNotContainedInConsumptions(),
        this.shouldUpdateQuantityForTwoCommitmentsIfContainedInConsumptions());
  }

  public Arguments shouldReturnEmptyWhenEmpty() {
    final var materialCommitmentUseCollection =
        new MaterialCommitmentUseCollection(List.of());
    final var migratedCommitmentConsumptionCollection = new MigratedCommitmentConsumptionCollection(
        List.of());
    final var expectedOutput = List.of();
    return Arguments.of(CurrentMethodName.get(), migratedCommitmentConsumptionCollection, materialCommitmentUseCollection, expectedOutput);
  }

  public Arguments shouldUpdateMaterialCommitmentsWithMigratedConsumptions() {
    final var orderId = UuidMother.fromInteger(222);
    final var orderLineId = UuidMother.fromInteger(333);
    final var materialReferenceId = UuidMother.fromInteger(444);
    final var materialCommitmentUse = MaterialCommitmentUseMother.with(orderId, orderLineId, materialReferenceId, BigDecimal.TEN);
    final var materialCommitmentUseCollection =
        new MaterialCommitmentUseCollection(List.of(materialCommitmentUse));
    final var migratedCommitmentConsumptionCollection = new MigratedCommitmentConsumptionCollection(
        List.of(
            MigratedCommitmentConsumptionMother.with(111, 444, 222, 333, BigDecimal.ONE),
            MigratedCommitmentConsumptionMother.with(111, 444, 222, 333, BigDecimal.ONE)));

    final var expectedOutput = List.of(CommitmentAdjusted.of(materialCommitmentUse, NumericUtils.roundUpScale2(BigDecimal.valueOf(8))));

    return Arguments.of(CurrentMethodName.get(), migratedCommitmentConsumptionCollection, materialCommitmentUseCollection, expectedOutput);
  }

  public Arguments shouldLeaveCommitmentQuantityIfNotContainedInConsumptions() {
    final var orderId1 = UuidMother.fromInteger(111);
    final var orderLineId1 = UuidMother.fromInteger(222);
    final var materialReferenceId1 = UuidMother.fromInteger(333);
    final var orderId2 = UuidMother.fromInteger(444);
    final var orderLineId2 = UuidMother.fromInteger(555);
    final var materialReferenceId2 = UuidMother.fromInteger(666);

    final var materialCommitmentUse1 =
        MaterialCommitmentUseMother.with(orderId1, orderLineId1, materialReferenceId1, BigDecimal.TEN);
    final var materialCommitmentUse2 =
        MaterialCommitmentUseMother.with(orderId2, orderLineId2, materialReferenceId2, BigDecimal.valueOf(50));
    final var materialCommitmentUseCollection =
        new MaterialCommitmentUseCollection(List.of(materialCommitmentUse1, materialCommitmentUse2));

    final var migratedCommitmentConsumptionCollection = new MigratedCommitmentConsumptionCollection(List.of(
        MigratedCommitmentConsumptionMother.with(444, 333, 111, 222,
            BigDecimal.ONE),
        MigratedCommitmentConsumptionMother.with(444, 333, 111, 222,
            BigDecimal.valueOf(6))));

    final var expectedOutput = List.of(CommitmentAdjusted.of(materialCommitmentUse1, NumericUtils.roundUpScale2(BigDecimal.valueOf(3))),
        CommitmentAdjusted.of(materialCommitmentUse2, materialCommitmentUse2.getQuantity().value()));

    return Arguments.of(CurrentMethodName.get(), migratedCommitmentConsumptionCollection, materialCommitmentUseCollection, expectedOutput);
  }

  public Arguments shouldUpdateQuantityForTwoCommitmentsIfContainedInConsumptions() {
    final var orderId1 = UuidMother.fromInteger(111);
    final var orderLineId1 = UuidMother.fromInteger(222);
    final var materialReferenceId1 = UuidMother.fromInteger(333);
    final var orderId2 = UuidMother.fromInteger(444);
    final var orderLineId2 = UuidMother.fromInteger(555);
    final var materialReferenceId2 = UuidMother.fromInteger(666);

    final var materialCommitmentUse1 =
        MaterialCommitmentUseMother.with(orderId1, orderLineId1, materialReferenceId1, BigDecimal.TEN);
    final var materialCommitmentUse2 =
        MaterialCommitmentUseMother.with(orderId2, orderLineId2, materialReferenceId2, BigDecimal.valueOf(50));
    final var materialCommitmentUseCollection =
        new MaterialCommitmentUseCollection(List.of(materialCommitmentUse1, materialCommitmentUse2));

    final var migratedCommitmentConsumptionCollection = new MigratedCommitmentConsumptionCollection(
        List.of(
            MigratedCommitmentConsumptionMother.with(444, 333, 111, 222,
                BigDecimal.ONE),
            MigratedCommitmentConsumptionMother.with(444, 333, 111, 222,
                BigDecimal.ONE),
            MigratedCommitmentConsumptionMother.with(444, 333, 111, 222,
                BigDecimal.TEN),
            MigratedCommitmentConsumptionMother.with(444, 666, 444, 555,
                BigDecimal.TEN)));

    final var expectedOutput = List.of(CommitmentAdjusted.of(materialCommitmentUse1, BigDecimal.ZERO),
        CommitmentAdjusted.of(materialCommitmentUse2, NumericUtils.roundUpScale2(BigDecimal.valueOf(40))));

    return Arguments.of(CurrentMethodName.get(), migratedCommitmentConsumptionCollection, materialCommitmentUseCollection, expectedOutput);
  }

}
