package com.inditex.icdmdemg.domain.use.entity;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

import java.util.Locale;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.use.mother.UseNamesMother;

import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
class UseNamesTest {

  @ParameterizedTest
  @MethodSource("provideTranslateParams")
  void should_translate(
      final Locale locale,
      final String nameTranslated) {
    final var useNames = UseNamesMother.defaultUseNames();

    final var nameValueTranslated = useNames.getNameTranslated(locale);
    assertThat(nameValueTranslated).isEqualTo(nameTranslated);
  }

  public Stream<Arguments> provideTranslateParams() {
    return Stream.of(
        Arguments.of(
            Locale.of("es"),
            "Nombre_1"),
        Arguments.of(
            Locale.of("Es"),
            "Nombre_1"),
        Arguments.of(
            Locale.of("es", "ES"),
            "Nombre_1"),
        Arguments.of(
            Locale.of("EN"),
            "Name_1"),
        Arguments.of(
            Locale.of("fr"),
            "Name_1"),
        Arguments.of(
            Locale.of(""),
            "Name_1"));
  }
}
