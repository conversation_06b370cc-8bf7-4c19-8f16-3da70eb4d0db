package com.inditex.icdmdemg.domain.distributionnominated.service;

import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.created;
import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.kept;
import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.updated;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.params.ParameterizedTest.INDEX_PLACEHOLDER;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeRequestedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus.MaterialCommitmentUseStatusEnum;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycleChangePendingQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedLineMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Commitment;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.DistributionNominatedPlannerType;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Line;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan.Auto;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan.Auto.Create;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan.Auto.Modify;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Request;
import com.inditex.icdmdemg.domain.shared.EntityAndActionResult;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.CurrentMethodName;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.icdmdemg.shared.utils.RandomValue;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

@TestInstance(Lifecycle.PER_CLASS)
class AutomaticDistributionNominatedPlannerTest {

  public static final String PARAMETERIZED_TEST_DESCRIPTION = "[" + INDEX_PLACEHOLDER + "] [{0}]";

  @ParameterizedTest(name = PARAMETERIZED_TEST_DESCRIPTION)
  @MethodSource("getInputCasesAndExpectedReturn")
  void should_return_correct_list_from_different_inputs(
      final String ignoredTestName,
      final DistributionNominatedPlanner sut,
      final List<EntityAndActionResult<Line>> output) {
    final var actual = sut.executePlan();
    assertThat(actual).containsExactlyInAnyOrderElementsOf(output);
  }

  @ParameterizedTest(name = PARAMETERIZED_TEST_DESCRIPTION)
  @MethodSource("getInputCasesAndExpectedThrowable")
  void should_throw_when_error(
      final String ignoredTestName,
      final DistributionNominatedPlanner sut,
      final Throwable exception) {
    assertThatThrownBy(sut::execute)
        .isInstanceOf(exception.getClass())
        .hasMessage(exception.getMessage());
  }

  public Stream<Arguments> getInputCasesAndExpectedReturn() {
    return Stream.of(
        this.shouldNewRequestFillWithOneCommitment(),
        this.shouldNewRequestFillWithTwoCommitments(),
        this.shouldNewRequestFillWithTwoCommitmentsSortingByDatetime(),
        this.shouldNewRequestFillWithSecondCommitmentFirstUsed(),
        this.shouldNewRequestFillWithOnlyTwoCommitments(),
        this.shouldNewRequestFillWithAlternative(),
        this.shouldNewRequestFillWithAlternativeButPrioritizeTarget(),
        this.shouldNewRequestFillWithNoClosedCommitment(),
        this.shouldNewRequestFillWithNoBudgetCycleCommitment(),

        this.shouldModifyRequestKeepWhenRequestedIsTheSame(),
        this.shouldModifyRequestUpWithOneCommitment(),
        this.shouldModifyRequestUpWithTwoCommitments(),
        this.shouldModifyRequestUpWithThreeCommitments(),
        this.shouldModifyRequestUpWithThreeCommitmentsButPrioritizeUsedFirst(),
        this.shouldModifyRequestUpIgnoringBudgetCycleCommitments(),
        this.shouldModifyRequestUpWithThreeCommitmentsButPrioritizeUsedFirstInSameRequest(),
        this.shouldModifyRequestUpWhenLinePartiallyDistributed(),
        this.shouldModifyRequestUpWhenLineTotallyDistributed(),
        this.shouldModifyRequestUpWhenLineOverDistributed(),
        this.shouldModifyRequestUpWithAlternative(),
        this.shouldModifyRequestUpWithPartiallyAlternative(),
        this.shouldModifyRequestUpWithNoClosed(),
        this.shouldModifyRequestDownWithOneCommitment(),
        this.shouldModifyRequestDownWithTwoCommitments(),
        this.shouldModifyRequestDownWithThreeCommitments(),
        this.shouldModifyRequestDownWithThreeCommitmentsButDeleteFromLastCommitmentFirst(),
        this.shouldModifyRequestDownWhenOneLinePartiallyDistributedGoDownOnTheOther(),
        this.shouldModifyRequestDownWithAlternative(),
        this.shouldModifyRequestDownWithAlternativeTotallyDeleted(),
        this.shouldModifyRequestDownWithNoClosed());
  }

  public Stream<Arguments> getInputCasesAndExpectedThrowable() {
    return Stream.of(
        this.shouldNewRequestThrowWhenRequestQuantityZero(),
        this.shouldNewRequestThrowWhenIdAlreadyExists(),
        this.shouldNewRequestThrowWhenNotEnoughCommitment(),

        this.shouldThrowWhenModifyAndRequestedIsZero(),
        this.shouldThrowWhenModifyDownAndLineIsDistributed(),
        this.shouldThrowWhenModifyRequestUpAndNotEnoughQuantityBecauseBudgetCycleCommitmentsNotAvailable(),
        this.shouldThrowWhenModifyRequestUpAndNotEnoughQuantity());
  }

  private Arguments shouldNewRequestFillWithOneCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(commitment(201, 301, 901, 500, 1)),
            List.of(),
            newRequestPlan(101, 500), DistributionNominatedPlannerType.DEFAULT),
        List.of(created(line(101, 201, 301, 901, 500, 0, null))));
  }

  private Arguments shouldNewRequestFillWithTwoCommitments() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(
                commitment(201, 301, 901, 100, 1),
                commitment(201, 302, 901, 300, 2)),
            List.of(),
            newRequestPlan(101, 350), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            created(line(101, 201, 301, 901, 100, 0, null)),
            created(line(101, 201, 302, 901, 250, 0, null))));
  }

  private Arguments shouldNewRequestFillWithOnlyTwoCommitments() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(
                commitment(201, 301, 901, 100, 1),
                commitment(201, 302, 901, 300, 2),
                commitment(201, 303, 901, 300, 3),
                commitment(201, 304, 901, 300, 4)),
            List.of(),
            newRequestPlan(101, 350), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            created(line(101, 201, 301, 901, 100, 0, null)),
            created(line(101, 201, 302, 901, 250, 0, null))));
  }

  private Arguments shouldNewRequestFillWithTwoCommitmentsSortingByDatetime() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(
                commitment(201, 301, 901, 100, 2),
                commitment(201, 302, 901, 300, 1)),
            List.of(),
            newRequestPlan(101, 350), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            created(line(101, 201, 301, 901, 50, 0, null)),
            created(line(101, 201, 302, 901, 300, 0, null))));
  }

  private Arguments shouldNewRequestFillWithSecondCommitmentFirstUsed() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 0, 1)),
            List.of(
                commitment(201, 301, 901, 100, 1),
                commitment(201, 302, 901, 300, 2)),
            List.of(),
            newRequestPlan(102, 50), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 100, 0, 1)),
            created(line(102, 201, 302, 901, 50, 0, null))));
  }

  private Arguments shouldNewRequestFillWithAlternative() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(altCommitment(201, 301, 901, 500, 1)),
            List.of(),
            newRequestPlan(101, 500), DistributionNominatedPlannerType.DEFAULT),
        List.of(created(altLine(101, 201, 301, 901, 500, null))));
  }

  private Arguments shouldNewRequestFillWithAlternativeButPrioritizeTarget() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(
                altCommitment(201, 301, 901, 500, 1),
                commitment(201, 301, 901, 500, 1)),
            List.of(),
            newRequestPlan(101, 900), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            created(line(101, 201, 301, 901, 500, 0, null)),
            created(altLine(101, 201, 301, 901, 400, null))));
  }

  private Arguments shouldNewRequestFillWithNoClosedCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(
                closedCommitment(201, 301, 901, 500, 1),
                commitment(201, 302, 901, 500, 2)),
            List.of(),
            newRequestPlan(101, 500), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            created(line(101, 201, 302, 901, 500, 0, null))));
  }

  private Arguments shouldNewRequestFillWithNoBudgetCycleCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(
                budgetChangeCommitment(201, 301, 901, 500, 1),
                commitment(201, 302, 901, 500, 2)),
            List.of(),
            newRequestPlan(101, 500), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            created(line(101, 201, 302, 901, 500, 0, null))));
  }

  private Arguments shouldNewRequestThrowWhenRequestQuantityZero() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(commitment(201, 301, 901, 100, 1)),
            List.of(),
            newRequestPlan(101, 0), DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest("Requested should be greater than zero")));
  }

  private Arguments shouldNewRequestThrowWhenIdAlreadyExists() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 0, 1)),
            List.of(commitment(201, 301, 901, 100, 1)),
            List.of(),
            newRequestPlan(101, 50), DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest("Request already exists")));
  }

  private Arguments shouldNewRequestThrowWhenNotEnoughCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(
                commitment(201, 301, 901, 100, 1),
                budgetChangeCommitment(401, 501, 901, 100, 1)),
            List.of(),
            newRequestPlan(102, 150), DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest("Insufficient commitment. Only quantity 100.00 can be allocated")));
  }

  private Arguments shouldThrowWhenModifyRequestUpAndNotEnoughQuantity() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 0, 1)),
            List.of(commitment(201, 301, 901, 200, 1)),
            List.of(),
            modifyRequestPlan(101, 300), DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest("Insufficient commitment. Only quantity 200.00 can be allocated")));
  }

  private Arguments shouldThrowWhenModifyRequestUpAndNotEnoughQuantityBecauseBudgetCycleCommitmentsNotAvailable() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 0, 1)),
            List.of(budgetChangeCommitment(201, 301, 901, 200, 1)),
            List.of(),
            modifyRequestPlan(101, 300),
            DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest("Insufficient commitment. Only quantity 100.00 can be allocated")));
  }

  private Arguments shouldThrowWhenModifyAndRequestedIsZero() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 50, 1)),
            List.of(commitment(201, 301, 901, 100, 1)),
            List.of(),
            modifyRequestPlan(101, 0), DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest("Requested should be greater than zero")));
  }

  private Arguments shouldThrowWhenModifyDownAndLineIsDistributed() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 50, 1)),
            List.of(commitment(201, 301, 901, 100, 1)),
            List.of(),
            modifyRequestPlan(101, 90), DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest(
            """
                The quantity decrease could not be processed: the new requested quantity is smaller than the distributed quantity
                or the undistributed lines are insufficient to reduce quantity
                """)));
  }

  private Arguments shouldModifyRequestKeepWhenRequestedIsTheSame() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 0, 1)),
            List.of(commitment(201, 301, 901, 150, 1)),
            List.of(),
            modifyRequestPlan(101, 100), DistributionNominatedPlannerType.DEFAULT),
        List.of(kept(line(101, 201, 301, 901, 100, 0, 1))));
  }

  private Arguments shouldModifyRequestUpWithOneCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 0, 1)),
            List.of(commitment(201, 301, 901, 150, 1)),
            List.of(),
            modifyRequestPlan(101, 150), DistributionNominatedPlannerType.DEFAULT),
        List.of(updated(line(101, 201, 301, 901, 150, 0, 1))));
  }

  private Arguments shouldModifyRequestUpWithTwoCommitments() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 0, 1)),
            List.of(
                commitment(201, 301, 901, 100, 1),
                commitment(201, 302, 901, 100, 2)),
            List.of(),
            modifyRequestPlan(101, 150), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 100, 0, 1)),
            created(line(101, 201, 302, 901, 50, 0, null))));
  }

  private Arguments shouldModifyRequestUpWithThreeCommitments() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 75, 1)),
            List.of(
                line(101, 201, 301, 901, 50, 0, 1),
                line(101, 201, 302, 901, 25, 0, 1)),
            List.of(
                commitment(201, 301, 901, 50, 1),
                commitment(201, 302, 901, 50, 2),
                commitment(201, 303, 901, 50, 3)),
            List.of(),
            modifyRequestPlan(101, 150),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 50, 0, 1)),
            updated(line(101, 201, 302, 901, 50, 0, 1)),
            created(line(101, 201, 303, 901, 50, 0, null))));
  }

  private Arguments shouldModifyRequestUpIgnoringBudgetCycleCommitments() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 75, 1)),
            List.of(
                line(101, 201, 301, 901, 50, 0, 1),
                line(101, 201, 302, 901, 25, 0, 1)),
            List.of(
                budgetChangeCommitment(201, 300, 901, 50, 1),
                commitment(201, 301, 901, 50, 2),
                commitment(201, 302, 901, 50, 3),
                commitment(201, 303, 901, 50, 4)),
            List.of(),
            modifyRequestPlan(101, 150), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 50, 0, 1)),
            updated(line(101, 201, 302, 901, 50, 0, 1)),
            created(line(101, 201, 303, 901, 50, 0, null))));
  }

  private Arguments shouldModifyRequestUpWithThreeCommitmentsButPrioritizeUsedFirst() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 75, 1)),
            List.of(
                line(101, 201, 301, 901, 50, 0, 1),
                line(101, 201, 302, 901, 25, 0, 1)),
            List.of(
                commitment(201, 301, 901, 50, 4),
                commitment(201, 302, 901, 50, 3),
                commitment(201, 303, 901, 30, 2),
                commitment(201, 304, 901, 30, 1)),
            List.of(),
            modifyRequestPlan(101, 150), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 50, 0, 1)),
            updated(line(101, 201, 302, 901, 50, 0, 1)),
            created(line(101, 201, 303, 901, 20, 0, null)),
            created(line(101, 201, 304, 901, 30, 0, null))));
  }

  private Arguments shouldModifyRequestUpWithThreeCommitmentsButPrioritizeUsedFirstInSameRequest() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(
                request(101, 75, 1),
                request(102, 75, 1)),
            List.of(
                line(101, 201, 301, 901, 50, 0, 1),
                line(101, 201, 302, 901, 25, 0, 1),
                line(102, 201, 300, 901, 25, 0, 1)),
            List.of(
                commitment(201, 300, 901, 50, 2),
                commitment(201, 301, 901, 50, 5),
                commitment(201, 302, 901, 50, 4),
                commitment(201, 303, 901, 30, 3),
                commitment(201, 304, 901, 30, 1)),
            List.of(),
            modifyRequestPlan(101, 150), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(102, 201, 300, 901, 25, 0, 1)),
            kept(line(101, 201, 301, 901, 50, 0, 1)),
            updated(line(101, 201, 302, 901, 50, 0, 1)),
            created(line(101, 201, 300, 901, 20, 0, null)),
            created(line(101, 201, 304, 901, 30, 0, null))));
  }

  private Arguments shouldModifyRequestUpWhenLinePartiallyDistributed() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 50, 1)),
            List.of(line(101, 201, 301, 901, 50, 25, 1)),
            List.of(commitment(201, 301, 901, 100, 1)),
            List.of(),
            modifyRequestPlan(101, 100), DistributionNominatedPlannerType.DEFAULT),
        List.of(updated(line(101, 201, 301, 901, 100, 25, 1))));
  }

  private Arguments shouldModifyRequestUpWhenLineTotallyDistributed() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 50, 1)),
            List.of(line(101, 201, 301, 901, 50, 50, 1)),
            List.of(commitment(201, 301, 901, 100, 1)),
            List.of(),
            modifyRequestPlan(101, 100), DistributionNominatedPlannerType.DEFAULT),
        List.of(updated(line(101, 201, 301, 901, 100, 50, 1))));
  }

  private Arguments shouldModifyRequestUpWhenLineOverDistributed() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 50, 1)),
            List.of(line(101, 201, 301, 901, 50, 60, 1)),
            List.of(commitment(201, 301, 901, 100, 1)),
            List.of(),
            modifyRequestPlan(101, 100), DistributionNominatedPlannerType.DEFAULT),
        List.of(updated(line(101, 201, 301, 901, 100, 60, 1))));
  }

  private Arguments shouldModifyRequestUpWithAlternative() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 50, 1)),
            List.of(altLine(101, 201, 301, 901, 50, 1)),
            List.of(altCommitment(201, 301, 901, 100, 1)),
            List.of(),
            modifyRequestPlan(101, 100), DistributionNominatedPlannerType.DEFAULT),
        List.of(updated(altLine(101, 201, 301, 901, 100, 1))));
  }

  private Arguments shouldModifyRequestUpWithPartiallyAlternative() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 20, 1)),
            List.of(line(101, 201, 301, 901, 20, 0, 1)),
            List.of(
                commitment(201, 301, 901, 30, 1),
                altCommitment(202, 302, 901, 100, 1)),
            List.of(),
            modifyRequestPlan(101, 50),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(101, 201, 301, 901, 30, 0, 1)),
            created(altLine(101, 202, 302, 901, 20, null))));
  }

  private Arguments shouldModifyRequestUpWithNoClosed() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 200, 1)),
            List.of(line(101, 201, 301, 901, 200, 0, 1)),
            List.of(
                closedCommitment(201, 301, 901, 400, 1),
                commitment(202, 302, 901, 100, 2)),
            List.of(),
            modifyRequestPlan(101, 250), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 200, 0, 1)),
            created(line(101, 202, 302, 901, 50, 0, null))));
  }

  private Arguments shouldModifyRequestDownWithOneCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 0, 1)),
            List.of(commitment(201, 301, 901, 150, 1)),
            List.of(),
            modifyRequestPlan(101, 50), DistributionNominatedPlannerType.DEFAULT),
        List.of(updated(line(101, 201, 301, 901, 50, 0, 1))));
  }

  private Arguments shouldModifyRequestDownWithTwoCommitments() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 50, 0, 1),
                line(101, 201, 302, 901, 50, 0, 1)),
            List.of(
                commitment(201, 301, 901, 50, 1),
                commitment(201, 302, 901, 150, 2)),
            List.of(),
            modifyRequestPlan(101, 70), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 50, 0, 1)),
            updated(line(101, 201, 302, 901, 20, 0, 1))));
  }

  private Arguments shouldModifyRequestDownWithThreeCommitments() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(
                line(101, 201, 301, 901, 50, 0, 1),
                line(101, 201, 302, 901, 40, 0, 1),
                line(101, 201, 303, 901, 10, 0, 1)),
            List.of(
                commitment(201, 301, 901, 50, 1),
                commitment(201, 302, 901, 40, 2),
                commitment(201, 303, 901, 50, 3)),
            List.of(),
            modifyRequestPlan(101, 70), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 50, 0, 1)),
            updated(line(101, 201, 302, 901, 20, 0, 1)),
            updated(line(101, 201, 303, 901, 0, 0, 1))));
  }

  private Arguments shouldModifyRequestDownWithThreeCommitmentsButDeleteFromLastCommitmentFirst() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 160, 1)),
            List.of(
                line(101, 201, 301, 901, 50, 0, 1),
                line(101, 201, 302, 901, 50, 0, 1),
                line(101, 201, 303, 901, 30, 0, 1),
                line(101, 201, 304, 901, 30, 0, 1)),
            List.of(
                commitment(201, 301, 901, 50, 4),
                commitment(201, 302, 901, 50, 3),
                commitment(201, 303, 901, 30, 2),
                commitment(201, 304, 901, 30, 1)),
            List.of(),
            modifyRequestPlan(101, 140), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(101, 201, 301, 901, 30, 0, 1)),
            kept(line(101, 201, 302, 901, 50, 0, 1)),
            kept(line(101, 201, 303, 901, 30, 0, 1)),
            kept(line(101, 201, 304, 901, 30, 0, 1))));
  }

  private Arguments shouldModifyRequestDownWhenOneLinePartiallyDistributedGoDownOnTheOther() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 75, 1)),
            List.of(
                line(101, 201, 301, 901, 50, 25, 1),
                line(101, 201, 302, 901, 25, 0, 1)),
            List.of(commitment(201, 301, 901, 100, 1)),
            List.of(),
            modifyRequestPlan(101, 60), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 50, 25, 1)),
            updated(line(101, 201, 302, 901, 10, 0, 1))));
  }

  private Arguments shouldModifyRequestDownWithAlternative() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 75, 1)),
            List.of(
                altLine(101, 201, 302, 901, 25, 1),
                line(101, 201, 302, 901, 50, 0, 1)),
            List.of(
                altCommitment(201, 302, 901, 100, 1),
                commitment(201, 302, 901, 100, 1)),
            List.of(),
            modifyRequestPlan(101, 60), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 302, 901, 50, 0, 1)),
            updated(altLine(101, 201, 302, 901, 10, 1))));
  }

  private Arguments shouldModifyRequestDownWithAlternativeTotallyDeleted() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 1000, 1)),
            List.of(
                line(101, 201, 301, 901, 500, 0, 1),
                altLine(101, 201, 302, 901, 500, 1),
                line(101, 201, 302, 901, 0, 0, 1)),
            List.of(
                altCommitment(201, 302, 901, 500, 1),
                commitment(201, 301, 901, 500, 1)),
            List.of(),
            modifyRequestPlan(101, 500), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            kept(line(101, 201, 301, 901, 500, 0, 1)),
            kept(line(101, 201, 302, 901, 0, 0, 1)),
            updated(altLine(101, 201, 302, 901, 0, 1))));
  }

  private Arguments shouldModifyRequestDownWithNoClosed() {
    return Arguments.of(
        CurrentMethodName.get(),
        new AutomaticDistributionNominatedPlanner(
            List.of(request(101, 1000, 1)),
            List.of(
                line(101, 201, 301, 901, 1000, 0, 1)),
            List.of(
                closedCommitment(201, 302, 901, 1000, 1)),
            List.of(),
            modifyRequestPlan(101, 500), DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(101, 201, 301, 901, 500, 0, 1))));
  }

  private static Plan.Auto newRequestPlan(final Integer id, final double quantity) {
    return new Create(
        new Id(UuidMother.fromInteger(id)),
        new DistributionNominated.RequestedQuantity(BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_UP)));

  }

  private static Plan.Auto modifyRequestPlan(final Integer id, final double quantity) {
    return new Modify(
        new Id(UuidMother.fromInteger(id)),
        new DistributionNominated.RequestedQuantity(BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_UP)));
  }

  private static Request request(final Integer id, final double quantity, final Integer creationDay) {
    return new Request(new Id(UuidMother.fromInteger(id)), BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_UP),
        OffsetDateTimeMother.fromInteger(creationDay));
  }

  private static Commitment commitment(final Integer orderId, final Integer lineId, final Integer supplierId, final Integer referenceId,
      final Integer targetReferenceId,
      final MaterialCommitmentUseStatus status,
      final double quantity, final Integer expectedDay,
      final MaterialCommitmentUseBudgetCycleChangeRequestedAt budgetCycleChangeRequestedDate) {
    return new Commitment(
        new CommitmentOrder(
            new CommitmentOrder.Id(UuidMother.fromInteger(orderId)),
            new CommitmentOrder.LineId(UuidMother.fromInteger(lineId)),
            new CommitmentOrder.SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId)))),
        new ReferenceId(UuidMother.fromInteger(referenceId)),
        new ReferenceId(UuidMother.fromInteger(targetReferenceId)),
        status,
        BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_UP),
        budgetCycleChangeRequestedDate, null,
        OffsetDateTimeMother.fromInteger(expectedDay));
  }

  private static Commitment commitment(final Integer orderId, final Integer lineId, final Integer supplierId, final double quantity,
      final Integer expectedDay) {
    final var commitmentStatus = MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN);
    return commitment(orderId, lineId, supplierId, 401, 401, commitmentStatus, quantity, expectedDay, null);
  }

  private static Commitment budgetChangeCommitment(final Integer orderId, final Integer lineId, final Integer supplierId,
      final double quantity,
      final Integer expectedDay) {
    final var commitmentStatus = MaterialCommitmentUseStatus.of(RandomValue.randomEnum(MaterialCommitmentUseStatusEnum.class));
    return commitment(orderId, lineId, supplierId, 401, 401, commitmentStatus, quantity, expectedDay,
        new MaterialCommitmentUseBudgetCycleChangeRequestedAt(OffsetDateTime.now()));
  }

  private static Line line(final Integer rootId, final Integer orderId, final Integer lineId, final Integer supplierId,
      final Integer referenceId,
      final Integer targetReferenceId, final double requested, final double distributed, final Integer creationDay) {
    return new Line(
        new Id(UuidMother.fromInteger(rootId)),
        new CommitmentOrder(
            new CommitmentOrder.Id(UuidMother.fromInteger(orderId)),
            new CommitmentOrder.LineId(UuidMother.fromInteger(lineId)),
            new CommitmentOrder.SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId)))),
        new ReferenceId(UuidMother.fromInteger(referenceId)),
        new ReferenceId(UuidMother.fromInteger(targetReferenceId)),
        BigDecimal.valueOf(requested).setScale(2, RoundingMode.HALF_UP),
        BigDecimal.valueOf(distributed).setScale(2, RoundingMode.HALF_UP),
        Optional.ofNullable(creationDay).map(OffsetDateTimeMother::fromInteger));
  }

  private static Line line(final Integer rootId, final Integer orderId, final Integer lineId, final Integer supplierId,
      final double requested,
      final double distributed,
      final Integer creationDay) {
    return line(rootId, orderId, lineId, supplierId, 401, 401, requested, distributed, creationDay);
  }

  private static Commitment closedCommitment(final Integer orderId, final Integer lineId, final Integer supplierId, final double quantity,
      final Integer expectedDay) {
    final var commitmentStatus = MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.CLOSED);
    return commitment(orderId, lineId, supplierId, 401, 401, commitmentStatus, quantity, expectedDay, null);
  }

  private static Commitment altCommitment(final Integer orderId, final Integer lineId, final Integer supplierId, final double quantity,
      final Integer expectedDay) {
    final var commitmentStatus = MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN);
    return commitment(orderId, lineId, supplierId, 402, 401, commitmentStatus, quantity, expectedDay, null);
  }

  private static Line altLine(final Integer rootId, final Integer orderId, final Integer lineId, final Integer supplierId,
      final double requested,
      final Integer creationDay) {
    return line(rootId, orderId, lineId, supplierId, 402, 401, requested, 0, creationDay);
  }

  @Test
  void should_modify_default_DN_ignoring_budget_cycle_change_commitments() {
    final var sharedRawMaterial = new SharedRawMaterialNominated(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID().toString());
    final var defaultCommitment = MaterialCommitmentUseMother.with(
        UUID.randomUUID(),
        UUID.randomUUID(),
        sharedRawMaterial.referenceId().value(), sharedRawMaterial.useId().value(),
        UUID.fromString(sharedRawMaterial.budgetCycle().value()),
        BigDecimal.valueOf(20000));

    final var defaultDistributionNominatedLine =
        DistributionNominatedLineMother.created()
            .commitmentOrder(
                new CommitmentOrder(
                    new CommitmentOrder.Id(UUID.fromString(defaultCommitment.getOrderLine().orderId().value())),
                    new CommitmentOrder.LineId(UUID.fromString(defaultCommitment.getOrderLine().orderLineId().value())),
                    new CommitmentOrder.SupplierId(defaultCommitment.getServiceLocalizationId().value())))
            .requestedQuantity(new RequestedQuantity(BigDecimal.valueOf(100)))
            .alternativeReference(null)
            .build();
    final var defaultDistributionNominated =
        DistributionNominatedMother
            .pendingWithLines(List.of(defaultDistributionNominatedLine))
            .referenceId(sharedRawMaterial.referenceId())
            .useId(sharedRawMaterial.useId())
            .budgetCycle(sharedRawMaterial.budgetCycle())
            .requestedQuantity(new DistributionNominated.RequestedQuantity(BigDecimal.valueOf(100)))
            .build();

    final var budgetCycleChangeDistributionNominatedLine =
        DistributionNominatedLineMother.created()
            .alternativeReference(null)
            .requestedQuantity(new RequestedQuantity(BigDecimal.ZERO)).build();
    final var budgetCycleChangeDistributionNominated = DistributionNominatedMother
        .pendingWithLines(List.of(budgetCycleChangeDistributionNominatedLine))
        .referenceId(sharedRawMaterial.referenceId())
        .useId(sharedRawMaterial.useId())
        .budgetCycle(sharedRawMaterial.budgetCycle())
        .requestedQuantity(
            new com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity(BigDecimal.valueOf(50)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.valueOf(50)))
        .build();

    final var distributions = List.of(defaultDistributionNominated, budgetCycleChangeDistributionNominated);
    final var adjustedCommitments = List.of(new CommitmentAdjusted(defaultCommitment, BigDecimal.valueOf(500)));

    final var updatedLine = new Line(new Id(defaultDistributionNominated.getId().value()),
        defaultDistributionNominatedLine.commitmentOrder(),
        defaultDistributionNominated.referenceId(), defaultDistributionNominated.referenceId(),
        NumericUtils.roundUpScale2(BigDecimal.valueOf(500)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.of(defaultDistributionNominatedLine.audit().createdAt()));
    final var keptLine = new Line(
        new Id(budgetCycleChangeDistributionNominated.getId()
            .value()),
        budgetCycleChangeDistributionNominatedLine.commitmentOrder(),
        budgetCycleChangeDistributionNominated.referenceId(), budgetCycleChangeDistributionNominated.referenceId(),
        budgetCycleChangeDistributionNominatedLine.requestedQuantity().value(),
        budgetCycleChangeDistributionNominatedLine.distributedQuantity().value(),
        Optional.of(budgetCycleChangeDistributionNominatedLine.audit().createdAt()));

    final var planner =
        DistributionNominatedPlanner.fromDistributions(sharedRawMaterial, distributions, adjustedCommitments,
            new Auto.Modify(new Id(defaultDistributionNominated.getId()
                .value()), new DistributionNominated.RequestedQuantity(BigDecimal.valueOf(500))),
            DistributionNominatedPlannerType.DEFAULT);
    final var adjustedLines = planner.executePlan();

    assertThat(adjustedLines).containsExactlyInAnyOrderElementsOf(List.of(
        updated(updatedLine),
        kept(keptLine)));
  }
}
