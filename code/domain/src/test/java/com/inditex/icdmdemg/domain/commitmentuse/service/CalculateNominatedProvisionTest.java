package com.inditex.icdmdemg.domain.commitmentuse.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.params.ParameterizedTest.INDEX_PLACEHOLDER;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.CalculateNominatedProvisionRequest;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.Commitment;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.CommitmentOrderLine;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.CommitmentOrderLineStatus;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.Distribution;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.DistributionQuantities;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.OrderLine;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.Provision;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.Provision.ProvisionKey;
import com.inditex.icdmdemg.domain.commitmentuse.service.CalculateNominatedProvision.SharedRawMaterial;
import com.inditex.icdmdemg.shared.utils.CurrentMethodName;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;

import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

@TestInstance(Lifecycle.PER_CLASS)
class CalculateNominatedProvisionTest {

  public static final String PARAMETERIZED_TEST_DESCRIPTION = "[" + INDEX_PLACEHOLDER + "] [{0}]";

  @ParameterizedTest(name = PARAMETERIZED_TEST_DESCRIPTION)
  @MethodSource("getInputCasesAndExpectedReturn")
  void should_return_correct_list_from_different_inputs(
      final String ignoredTestName,
      final List<Commitment> commitments,
      final List<Distribution> distributions,
      final List<Provision> output) {
    final var actual = CalculateNominatedProvision.calculate(new CalculateNominatedProvisionRequest(commitments, distributions));
    assertThat(actual).isNotNull();
    assertThat(actual.provisions()).containsExactlyInAnyOrderElementsOf(output);
  }

  public Stream<Arguments> getInputCasesAndExpectedReturn() {
    return Stream.of(
        // same shared raw material
        // one line commitment open
        this.shouldCreateOneProvisionWithOneLineOpenWithPastDateWithoutDistributed(),
        this.shouldCreateOneProvisionWithOneLineOpenWithPastDateWithDistributed(),
        this.shouldCreateOneProvisionWithOneLineOpenWithFutureDateWithoutDistributed(),
        this.shouldCreateOneProvisionWithOneLineOpenWithFutureDateWithDistributed(),

        // one line commitment closed
        this.shouldCreateOneProvisionWithOneLineClosedWithPastDateWithDistributed(),
        this.shouldCreateOneProvisionWithOneLineClosedWithPastDateWithoutDistributed(),
        this.shouldCreateOneProvisionWithOneLineClosedWithFutureDateWithDistributed(),
        this.shouldCreateOneProvisionWithOneLineClosedWithFutureDateWithoutDistributed(),

        // two lines commitment open
        // past
        this.shouldCreateTwoProvisionsWithTwoLinesOpenWithDifferentLocalizationWithPastDateWithoutDistributed(),
        this.shouldCreateTwoProvisionsWithTwoLinesOpenWithDifferentLocalizationWithPastDateWithDistributed(),
        this.shouldCreateOneProvisionWithTwoLinesOpenWithSameLocalizationWithPastDateWithoutDistributed(),
        this.shouldCreateOneProvisionWithTwoLinesOpenWithSameLocalizationWithPastDateWithDistributed(),
        // future
        this.shouldCreateTwoProvisionsWithTwoLinesOpenWithDifferentLocalizationWithFutureDateWithoutDistributed(),
        this.shouldCreateTwoProvisionsWithTwoLinesOpenWithDifferentLocalizationWithFutureDateWithDistributed(),
        this.shouldCreateOneProvisionWithTwoLinesOpenWithSameLocalizationWithFutureDateWithoutDistributed(),
        this.shouldCreateOneProvisionWithTwoLinesOpenWithSameLocalizationWithFutureDateWithDistributed(),

        // two lines commitment, one open one closed
        // past and same localization
        this.shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithPastDateWithAllLinesDistributed(),
        this.shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithPastDateWithNoLineDistributed(),
        this.shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithPastDateWithOpenDistributedAndClosedNotDistributed(),
        this.shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithPastDateWithOpenNotDistributedAndClosedDistributed(),
        // past and different localization
        this.shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithPastDateWithAllLinesDistributed(),
        this.shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithPastDateWithNoLineDistributed(),
        this.shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithPastDateWithOpenDistributedAndClosedNotDistributed(),
        this.shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithPastDateWithOpenNotDistributedAndClosedDistributed(),
        // future and same localization
        this.shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithFutureDateWithAllLinesDistributed(),
        this.shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithFutureDateWithNoLineDistributed(),
        this.shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithFutureDateWithOpenDistributedAndClosedNotDistributed(),
        this.shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithFutureDateWithOpenNotDistributedAndClosedDistributed(),
        // future and different localization
        this.shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithFutureDateWithAllLinesDistributed(),
        this.shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithFutureDateWithNoLineDistributed(),
        this.shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithFutureDateWithOpenDistributedAndClosedNotDistributed(),
        this.shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithFutureDateWithOpenNotDistributedAndClosedDistributed(),

        // different shared raw material
        // one line same shared raw material and one line different shared raw material
        this.shouldCreateTwoProvisionsWithOneLineOpenWithOneSharedRawMaterialAndOneDifferentLineOpenWithAnotherSharedRawMaterialWithPastDateWithAllLinesDistributed(),
        // two lines same shared raw material and localization and one line different shared raw material
        this.shouldCreateTwoProvisionsWithTwoLinesOpenWithSameRawMaterialSameLocalizationWithOneDifferentLineAnotherSharedRawMaterialWithPastDateWithAllLinesDistributed(),
        // two lines same shared raw material, localization and commitment and one line different shared raw material
        this.shouldCreateTwoProvisionsWithTwoLinesOpenWithSameRawMaterialSameLocalizationAndSameCommitmentWithOneDifferentLineAnotherSharedRawMaterialWithPastDateWithAllLinesDistributed(),
        // two lines same shared raw material and localization(in commitment) but two different SRM in two DNs and one line different shared
        // raw material
        this.shouldCreateTwoProvisionsWithTwoLinesOpenWithSameRawMaterialCommitmentAndLocalizationAndDifferentSRMInDnsWithOneDifferentLineAnotherSharedRawMaterialWithPastDateWithAllLinesDistributed(),
        // two lines different raw material same localization and one line different shared raw material
        this.shouldCreateThreeProvisionsWithTwoLinesOpenWithDifferentRawMaterialSameLocalizationWithOneDifferentLineAnotherSharedRawMaterialWithPastDateWithAllLinesDistributed(),
        // two lines same shared raw material different localization y otra línea con otro shared raw material
        this.shouldCreateThreeProvisionsWithTwoLinesOpenWithSameSharedRawMaterialDifferentLocalizationAndOneDifferentLineWithAnotherSharedRawMaterialWithPastDateWithAllLinesDistributed(),

        // un poco de to
        this.shouldCombineAllInOneShot()

    );
  }

  // --------------------------------------------------------------------------------
  // Test con "todas" las permutaciones en un solo caso
  // --------------------------------------------------------------------------------

  private Arguments shouldCombineAllInOneShot() {
    final var sharedRawMaterial = this.getSharedRawMaterial();

    // 1) OPEN + Past
    final var commitment1 = this.getCommitment(
        sharedRawMaterial,
        OffsetDateTime.MIN,
        "loc-past-open",
        CommitmentOrderLineStatus.OPEN);

    // 2) OPEN + Future
    final var commitment2 = this.getCommitment(
        sharedRawMaterial,
        OffsetDateTime.MAX,
        "loc-future-open",
        CommitmentOrderLineStatus.OPEN);

    // 3) CLOSED + Past
    final var commitment3 = this.getCommitment(
        sharedRawMaterial,
        OffsetDateTime.MIN,
        "loc-past-closed",
        CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2, commitment3);

    // Distros: 30 -> c1, 40 -> c2, 50 -> c3
    final DistributionQuantities distributionQuantities1 = new DistributionQuantities(BigDecimal.valueOf(30), BigDecimal.valueOf(20));
    final var dist1 = new Distribution(commitment1.orderLine(), distributionQuantities1);
    final DistributionQuantities distributionQuantities2 = new DistributionQuantities(BigDecimal.valueOf(40), BigDecimal.valueOf(40));
    final var dist2 = new Distribution(commitment2.orderLine(), distributionQuantities2);
    final DistributionQuantities distributionQuantities3 = new DistributionQuantities(BigDecimal.valueOf(50), BigDecimal.ZERO);
    final var dist3 = new Distribution(commitment3.orderLine(), distributionQuantities3);
    final var distributions = List.of(dist1, dist2, dist3);

    // calculate expected:

    // c1: OPEN + Past
    // ordered=100, distributed=30, pending=0, entered=100, stock=70
    final var provisionKey1 = new ProvisionKey(sharedRawMaterial, "loc-past-open");
    final var provision1 = new Provision(
        provisionKey1,
        BigDecimal.valueOf(100), // ordered
        BigDecimal.ZERO, // pending
        BigDecimal.valueOf(100), // entered
        distributionQuantities1,
        BigDecimal.valueOf(70) // stock
    );

    // c2: OPEN + Future
    // ordered=100, distributed=40, pending=60, entered=40, stock=0
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial, "loc-future-open");
    final var provision2 = new Provision(
        provisionKey2,
        BigDecimal.valueOf(100),
        BigDecimal.valueOf(60),
        BigDecimal.valueOf(40),
        distributionQuantities2,
        BigDecimal.ZERO);

    // c3: CLOSED + Past
    // ordered=100, distributed=50, pending=0, entered=50, stock=0
    final var provisionKey3 = new ProvisionKey(sharedRawMaterial, "loc-past-closed");
    final var provision3 = new Provision(
        provisionKey3,
        BigDecimal.valueOf(100),
        BigDecimal.ZERO,
        BigDecimal.valueOf(50),
        distributionQuantities3,
        BigDecimal.ZERO);

    final var expected = List.of(provision1, provision2, provision3);

    return Arguments.of(
        "shouldCombineAllInOneShot",
        commitments,
        distributions,
        expected);
  }

  // --------------------------------------------------------------------------------
  // no touch from here ***************************************************************
  // --------------------------------------------------------------------------------

  private Arguments shouldCreateThreeProvisionsWithTwoLinesOpenWithSameSharedRawMaterialDifferentLocalizationAndOneDifferentLineWithAnotherSharedRawMaterialWithPastDateWithAllLinesDistributed() {
    final var sharedRawMaterial1 = this.getSharedRawMaterial();
    final var sharedRawMaterial2 = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial1, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial2, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment3 = this.getCommitment(sharedRawMaterial2, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2, commitment3);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities2);
    final var distributedQuantities3 = new DistributionQuantities(BigDecimal.valueOf(30), BigDecimal.ZERO);
    final var distribution3 = new Distribution(commitment3.orderLine(), distributedQuantities3);
    final var distributions = List.of(distribution1, distribution2, distribution3);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment1.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = ordered;
    final var stock = ordered.subtract(distributionRequested);
    final var provisionKey = new ProvisionKey(sharedRawMaterial1, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);

    final var distributionRequested2 = distributedQuantities2.requested();
    final var ordered2 = commitment2.ordered();
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = ordered2;
    final var stock2 = ordered2.subtract(distributionRequested2);
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial2, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, distributedQuantities2, stock2);

    final var distributionRequested3 = distributedQuantities3.requested();
    final var ordered3 = commitment3.ordered();
    final var pending3 = BigDecimal.ZERO;
    final var entered3 = ordered3;
    final var stock3 = ordered3.subtract(distributionRequested3);
    final var provisionKey3 = new ProvisionKey(sharedRawMaterial2, commitment3.commitmentOrderLine().localization());
    final var provision3 = new Provision(provisionKey3, ordered3, pending3, entered3, distributedQuantities3, stock3);
    final var expected = List.of(provision, provision2, provision3);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateThreeProvisionsWithTwoLinesOpenWithDifferentRawMaterialSameLocalizationWithOneDifferentLineAnotherSharedRawMaterialWithPastDateWithAllLinesDistributed() {
    final var sharedRawMaterial1 = this.getSharedRawMaterial();
    final var sharedRawMaterial2 = this.getSharedRawMaterial();
    final var sharedRawMaterial3 = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var commitment1 = this.getCommitment(sharedRawMaterial1, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial2, pastDate,
        localization, CommitmentOrderLineStatus.OPEN);
    final var commitment3 = this.getCommitment(sharedRawMaterial3, pastDate,
        localization, CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2, commitment3);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(3));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities2);
    final var distributedQuantities3 = new DistributionQuantities(BigDecimal.valueOf(60), BigDecimal.valueOf(40));
    final var distribution3 = new Distribution(commitment3.orderLine(), distributedQuantities3);
    final var distributions = List.of(distribution1, distribution2, distribution3);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment1.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = ordered;
    final var stock = ordered.subtract(distributionRequested);
    final var provisionKey = new ProvisionKey(sharedRawMaterial1, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);

    final var distributionRequested2 = distributedQuantities2.requested();
    final var ordered2 = commitment2.ordered();
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = ordered2;
    final var stock2 = ordered2.subtract(distributionRequested2);
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial2, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, distributedQuantities2, stock2);

    final var distributionRequested3 = distributedQuantities3.requested();
    final var ordered3 = commitment3.ordered();
    final var pending3 = BigDecimal.ZERO;
    final var entered3 = ordered3;
    final var stock3 = ordered3.subtract(distributionRequested3);
    final var provisionKey3 = new ProvisionKey(sharedRawMaterial3, commitment3.commitmentOrderLine().localization());
    final var provision3 = new Provision(provisionKey3, ordered3, pending3, entered3, distributedQuantities3, stock3);
    final var expected = List.of(provision, provision2, provision3);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithTwoLinesOpenWithSameRawMaterialSameLocalizationWithOneDifferentLineAnotherSharedRawMaterialWithPastDateWithAllLinesDistributed() {
    final var sharedRawMaterial1 = this.getSharedRawMaterial();
    final var sharedRawMaterial2 = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var commitment1 = this.getCommitment(sharedRawMaterial1, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial2, pastDate,
        localization, CommitmentOrderLineStatus.OPEN);
    final var commitment3 = this.getCommitment(sharedRawMaterial2, pastDate,
        localization, CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2, commitment3);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(20));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities2);
    final var distributedQuantities3 = new DistributionQuantities(BigDecimal.valueOf(60), BigDecimal.ZERO);
    final var distribution3 = new Distribution(commitment3.orderLine(), distributedQuantities3);
    final var distributions = List.of(distribution1, distribution2, distribution3);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment1.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = ordered;
    final var stock = ordered.subtract(distributionRequested);
    final var provisionKey = new ProvisionKey(sharedRawMaterial1, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);

    final var distributionQuantities2Add3 = distributedQuantities2.add(distributedQuantities3);
    final var distributionRequested2 = distributionQuantities2Add3.requested();
    final var distributedDistributed2 = distributionQuantities2Add3.distributed();
    final var ordered2 = commitment2.ordered().add(commitment3.ordered());
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = ordered2;
    final var stock2 = ordered2.subtract(distributionRequested2);
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial2, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2,
        new DistributionQuantities(distributionRequested2, distributedDistributed2), stock2);
    final var expected = List.of(provision, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithTwoLinesOpenWithSameRawMaterialCommitmentAndLocalizationAndDifferentSRMInDnsWithOneDifferentLineAnotherSharedRawMaterialWithPastDateWithAllLinesDistributed() {
    final var sharedRawMaterial1 = this.getSharedRawMaterial();
    final var sharedRawMaterial2 = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var commitment1 = this.getCommitment(sharedRawMaterial1, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial2, pastDate,
        localization, CommitmentOrderLineStatus.OPEN);
    final var commitment3 = this.getCommitment(sharedRawMaterial2, pastDate,
        localization, CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2, commitment3);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(new OrderLine(commitment2.orderLine().orderId(),
        commitment2.orderLine().orderLineId(), new SharedRawMaterial(UUID.randomUUID(),
            commitment2.orderLine().sharedRawMaterial().useId(),
            commitment2.orderLine().sharedRawMaterial().budgetId())),
        distributedQuantities2);
    final var distributedQuantities3 = new DistributionQuantities(BigDecimal.valueOf(60), BigDecimal.valueOf(50));
    final var distribution3 = new Distribution(commitment2.orderLine(), distributedQuantities3);
    final var distributions = List.of(distribution1, distribution2, distribution3);

    final var distributionRequested1 = distributedQuantities1.requested();
    final var ordered = commitment1.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = ordered;
    final var stock = ordered.subtract(distributionRequested1);
    final var provisionKey = new ProvisionKey(sharedRawMaterial1, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);

    final var distributionRequested2 = distributedQuantities3.requested();
    final var ordered2 = commitment2.ordered().add(commitment3.ordered());
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = ordered2;
    final var stock2 = ordered2.subtract(distributionRequested2);
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial2, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, distributedQuantities3, stock2);
    final var expected = List.of(provision, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithTwoLinesOpenWithSameRawMaterialSameLocalizationAndSameCommitmentWithOneDifferentLineAnotherSharedRawMaterialWithPastDateWithAllLinesDistributed() {
    final var sharedRawMaterial1 = this.getSharedRawMaterial();
    final var sharedRawMaterial2 = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var commitment1 = this.getCommitment(sharedRawMaterial1, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial2, pastDate,
        localization, CommitmentOrderLineStatus.OPEN);
    final var commitment3 = this.getCommitment(sharedRawMaterial2, pastDate,
        localization, CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2, commitment3);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities2);
    final var distributedQuantities3 = new DistributionQuantities(BigDecimal.valueOf(60), BigDecimal.valueOf(50));
    final var distribution3 = new Distribution(commitment2.orderLine(), distributedQuantities3);
    final var distributions = List.of(distribution1, distribution2, distribution3);

    final var distributionRequested1 = distributedQuantities1.requested();
    final var ordered = commitment1.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = ordered;
    final var stock = ordered.subtract(distributionRequested1);
    final var provisionKey = new ProvisionKey(sharedRawMaterial1, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);

    final var distributionQuantities2Add3 = distributedQuantities2.add(distributedQuantities3);
    final var distributionRequested2 = distributionQuantities2Add3.requested();
    final var distributedDistributed2 = distributionQuantities2Add3.distributed();
    final var ordered2 = commitment2.ordered().add(commitment3.ordered());
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = ordered2;
    final var stock2 = ordered2.subtract(distributionRequested2);
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial2, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2,
        new DistributionQuantities(distributionRequested2, distributedDistributed2), stock2);

    final var expected = List.of(provision, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithOneLineOpenWithOneSharedRawMaterialAndOneDifferentLineOpenWithAnotherSharedRawMaterialWithPastDateWithAllLinesDistributed() {
    final var sharedRawMaterial1 = this.getSharedRawMaterial();
    final var sharedRawMaterial2 = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial1, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial2, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities2);
    final var distributions = List.of(distribution1, distribution2);

    final var distributionRequested1 = distributedQuantities1.requested();
    final var ordered = commitment1.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = ordered;
    final var stock = ordered.subtract(distributionRequested1);
    final var provisionKey = new ProvisionKey(sharedRawMaterial1, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);

    final var distributionRequested2 = distributedQuantities2.requested();
    final var ordered2 = commitment2.ordered();
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = ordered2;
    final var stock2 = ordered2.subtract(distributionRequested2);
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial2, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, distributedQuantities2, stock2);

    final var expected = List.of(provision, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithFutureDateWithOpenNotDistributedAndClosedDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();

    final var futureDate = OffsetDateTime.MAX;
    final var commitment1 = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities1);
    final var distributions = List.of(distribution2);

    final var distributionRequested = BigDecimal.ZERO;
    final var ordered = commitment1.ordered();
    final var pending = ordered;
    final var entered = distributionRequested;
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, DistributionQuantities.empty(), stock);

    final var distributionRequested2 = distributedQuantities1.requested();
    final var ordered2 = commitment2.ordered();
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = distributionRequested2;
    final var stock2 = BigDecimal.ZERO;
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, distributedQuantities1, stock2);
    final var expected = List.of(provision, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithFutureDateWithOpenDistributedAndClosedNotDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var futureDate = OffsetDateTime.MAX;
    final var commitment1 = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributions = List.of(distribution1);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment1.ordered();
    final var pending = ordered.subtract(distributionRequested);
    final var entered = distributedQuantities1.requested();
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);

    final var distributionRequested2 = BigDecimal.ZERO;
    final var ordered2 = commitment2.ordered();
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = distributionRequested2;
    final var stock2 = BigDecimal.ZERO;
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, DistributionQuantities.empty(), stock2);
    final var expected = List.of(provision, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithFutureDateWithNoLineDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var futureDate = OffsetDateTime.MAX;
    final var commitment1 = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributions = List.of();

    final var distributionRequested = BigDecimal.ZERO;
    final var ordered = commitment1.ordered();
    final var pending = ordered;
    final var entered = distributionRequested;
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, DistributionQuantities.empty(), stock);

    final var distributionRequested2 = BigDecimal.ZERO;
    final var ordered2 = commitment2.ordered();
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = distributionRequested2;
    final var stock2 = BigDecimal.ZERO;
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, DistributionQuantities.empty(), stock2);
    final var expected = List.of(provision, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithFutureDateWithAllLinesDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var futureDate = OffsetDateTime.MAX;
    final var commitment1 = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities2);
    final var distributions = List.of(distribution1, distribution2);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment1.ordered();
    final var pending = ordered.subtract(distributionRequested);
    final var entered = distributionRequested;
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);

    final var distributionRequested2 = distributedQuantities2.requested();
    final var ordered2 = commitment2.ordered();
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = distributionRequested2;
    final var stock2 = BigDecimal.ZERO;
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, distributedQuantities2, stock2);
    final var expected = List.of(provision, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithFutureDateWithOpenNotDistributedAndClosedDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var futureDate = OffsetDateTime.MAX;
    final var commitment1 = this.getCommitment(sharedRawMaterial, futureDate, localization, CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, futureDate, localization, CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities1);
    final var distributions = List.of(distribution2);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment1.ordered().add(commitment2.ordered());
    final var pending = commitment1.ordered();
    final var entered = distributionRequested;
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, localization);
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithFutureDateWithOpenDistributedAndClosedNotDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var futureDate = OffsetDateTime.MAX;
    final var commitment1 = this.getCommitment(sharedRawMaterial, futureDate, localization, CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, futureDate, localization, CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributions = List.of(distribution1);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment1.ordered().add(commitment2.ordered());
    final var pending = commitment1.ordered().subtract(distributionRequested);
    final var entered = distributionRequested;
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, localization);
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithFutureDateWithNoLineDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var futureDate = OffsetDateTime.MAX;
    final var commitment1 = this.getCommitment(sharedRawMaterial, futureDate, localization, CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, futureDate, localization, CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributions = List.of();

    final var distributionRequested = BigDecimal.ZERO;
    final var ordered = commitment1.ordered().add(commitment2.ordered());
    final var pending = commitment1.ordered();
    final var entered = distributionRequested;
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, localization);
    final var provision = new Provision(provisionKey, ordered, pending, entered, DistributionQuantities.empty(), stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithFutureDateWithAllLinesDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var futureDate = OffsetDateTime.MAX;
    final var commitment1 = this.getCommitment(sharedRawMaterial, futureDate, localization, CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, futureDate, localization, CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities2);
    final var distributions = List.of(distribution1, distribution2);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment1.ordered().add(commitment2.ordered());
    final var pending = commitment1.ordered().subtract(distributionRequested);
    final var entered = distributedQuantities1.requested().add(distributedQuantities2.requested());
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, localization);
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1.add(distributedQuantities2), stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithPastDateWithOpenNotDistributedAndClosedDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities1);
    final var distributions = List.of(distribution2);

    final var ordered = commitment1.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = commitment1.ordered();
    final var stock = commitment1.ordered();
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, DistributionQuantities.empty(), stock);

    final var distributionRequested2 = distributedQuantities1.requested();
    final var ordered2 = commitment2.ordered();
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = distributionRequested2;
    final var stock2 = BigDecimal.ZERO;
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, distributedQuantities1, stock2);
    final var expected = List.of(provision, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithPastDateWithOpenDistributedAndClosedNotDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributions = List.of(distribution1);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment1.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = commitment1.ordered();
    final var stock = commitment1.ordered().subtract(distributionRequested);
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);

    final var distributionRequested2 = BigDecimal.ZERO;
    final var ordered2 = commitment2.ordered();
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = distributionRequested2;
    final var stock2 = BigDecimal.ZERO;
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, DistributionQuantities.empty(), stock2);
    final var expected = List.of(provision, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithPastDateWithNoLineDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributions = List.of();

    final var ordered = commitment1.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = commitment1.ordered();
    final var stock = commitment1.ordered();
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, DistributionQuantities.empty(), stock);

    final var distributionRequested2 = BigDecimal.ZERO;
    final var ordered2 = commitment2.ordered();
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = distributionRequested2;
    final var stock2 = BigDecimal.ZERO;
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, DistributionQuantities.empty(), stock2);
    final var expected = List.of(provision, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithOneLineOpenOneLineClosedWithDifferentLocalizationWithPastDateWithAllLinesDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities2);
    final var distributions = List.of(distribution1, distribution2);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment1.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = commitment1.ordered();
    final var stock = commitment1.ordered().subtract(distributionRequested);
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment1.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);

    final var distributionRequested2 = distributedQuantities2.requested();
    final var ordered2 = commitment2.ordered();
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = distributionRequested2;
    final var stock2 = BigDecimal.ZERO;
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, distributedQuantities2, stock2);
    final var expected = List.of(provision, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithPastDateWithOpenNotDistributedAndClosedDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial, pastDate, localization, CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, pastDate, localization, CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities1);
    final var distributions = List.of(distribution2);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment1.ordered().add(commitment2.ordered());
    final var pending = BigDecimal.ZERO;
    final var entered = commitment1.ordered().add(distributionRequested);
    final var stock = commitment1.ordered();
    final var provisionKey = new ProvisionKey(sharedRawMaterial, localization);
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithPastDateWithOpenDistributedAndClosedNotDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial, pastDate, localization, CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, pastDate, localization, CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributions = List.of(distribution1);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment1.ordered().add(commitment2.ordered());
    final var pending = BigDecimal.ZERO;
    final var entered = commitment1.ordered();
    final var stock = commitment1.ordered().subtract(distributionRequested);
    final var provisionKey = new ProvisionKey(sharedRawMaterial, localization);
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithPastDateWithNoLineDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial, pastDate, localization, CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, pastDate, localization, CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);
    final var distributions = List.of();

    final var ordered = commitment1.ordered().add(commitment2.ordered());
    final var pending = BigDecimal.ZERO;
    final var entered = commitment1.ordered();
    final var stock = commitment1.ordered();
    final var provisionKey = new ProvisionKey(sharedRawMaterial, localization);
    final var provision = new Provision(provisionKey, ordered, pending, entered, DistributionQuantities.empty(), stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineOpenOneLineClosedWithSameLocalizationWithPastDateWithAllLinesDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial, pastDate, localization, CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, pastDate, localization, CommitmentOrderLineStatus.CLOSED);

    final var commitments = List.of(commitment1, commitment2);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities2);
    final var distributions = List.of(distribution1, distribution2);

    final var distributionRequested1 = distributedQuantities1.requested();
    final var distributionRequested2 = distributedQuantities2.requested();
    final var ordered = commitment1.ordered().add(commitment2.ordered());
    final var pending = BigDecimal.ZERO;
    final var entered = commitment1.ordered().add(distributionRequested2);
    final var stock = commitment1.ordered().subtract(distributionRequested1);
    final var provisionKey = new ProvisionKey(sharedRawMaterial, localization);
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1.add(distributedQuantities2), stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithTwoLinesOpenWithSameLocalizationWithFutureDateWithDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var futureDate = OffsetDateTime.MAX;
    final var commitment1 = this.getCommitment(sharedRawMaterial, futureDate, localization, CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, futureDate, localization, CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2);
    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities2);
    final var distributions = List.of(distribution1, distribution2);

    final var ordered = commitment1.ordered().add(commitment2.ordered());
    final var pending = ordered.subtract(distributedQuantities1.requested().add(distributedQuantities2.requested()));
    final var entered = distributedQuantities1.requested().add(distributedQuantities2.requested());
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, localization);
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1.add(distributedQuantities2), stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithTwoLinesOpenWithSameLocalizationWithFutureDateWithoutDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var futureDate = OffsetDateTime.MAX;
    final var commitment1 = this.getCommitment(sharedRawMaterial, futureDate, localization, CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, futureDate, localization, CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2);
    final var distributions = List.<Distribution>of();

    final var distributionRequested = BigDecimal.ZERO;
    final var ordered = commitment1.ordered().add(commitment2.ordered());
    final var pending = ordered.add(distributionRequested);
    final var entered = distributionRequested;
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, localization);
    final var provision = new Provision(provisionKey, ordered, pending, entered, DistributionQuantities.empty(), stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithTwoLinesOpenWithDifferentLocalizationWithFutureDateWithDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var futureDate = OffsetDateTime.MAX;
    final var commitment1 = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2);
    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities2);
    final var distributions = List.of(distribution1, distribution2);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered1 = commitment1.ordered();
    final var pending = ordered1.subtract(distributionRequested);
    final var entered1 = distributionRequested;
    final var stock1 = BigDecimal.ZERO;
    final var provisionKey1 = new ProvisionKey(sharedRawMaterial, commitment1.commitmentOrderLine().localization());
    final var provision1 = new Provision(provisionKey1, ordered1, pending, entered1, distributedQuantities1, stock1);

    final var distributionRequested2 = distributedQuantities2.requested();
    final var ordered2 = commitment2.ordered();
    final var pending2 = ordered2.subtract(distributionRequested2);
    final var entered2 = distributionRequested2;
    final var stock2 = BigDecimal.ZERO;
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, distributedQuantities2, stock2);
    final var expected = List.of(provision1, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithTwoLinesOpenWithDifferentLocalizationWithFutureDateWithoutDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var futureDate = OffsetDateTime.MAX;
    final var commitment1 = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2);
    final var distributions = List.<Distribution>of();
    final var distributionRequested = BigDecimal.ZERO;
    final var ordered1 = commitment1.ordered();
    final var pending = commitment1.ordered();
    final var entered1 = distributionRequested;
    final var stock1 = BigDecimal.ZERO;
    final var provisionKey1 = new ProvisionKey(sharedRawMaterial, commitment1.commitmentOrderLine().localization());
    final var provision1 = new Provision(provisionKey1, ordered1, pending, entered1, DistributionQuantities.empty(), stock1);

    final var ordered2 = commitment2.ordered();
    final var pending2 = commitment2.ordered();
    final var entered2 = distributionRequested;
    final var stock2 = BigDecimal.ZERO;
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, DistributionQuantities.empty(), stock2);
    final var expected = List.of(provision1, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithTwoLinesOpenWithSameLocalizationWithPastDateWithDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial, pastDate, localization, CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, pastDate, localization, CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2);
    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities2);
    final var distributions = List.of(distribution1, distribution2);

    final var ordered = commitment1.ordered().add(commitment2.ordered());
    final var pending = BigDecimal.ZERO;
    final var entered = ordered;
    final var stock = ordered.subtract(distributedQuantities1.add(distributedQuantities2).requested());
    final var provisionKey = new ProvisionKey(sharedRawMaterial, localization);
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1.add(distributedQuantities2), stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithTwoLinesOpenWithSameLocalizationWithPastDateWithoutDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var localization = UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID());
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial, pastDate, localization, CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, pastDate, localization, CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2);
    final var distributions = List.<Distribution>of();

    final var ordered = commitment1.ordered().add(commitment2.ordered());
    final var pending = BigDecimal.ZERO;
    final var entered = ordered;
    final var stock = ordered;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, localization);
    final var provision = new Provision(provisionKey, ordered, pending, entered, DistributionQuantities.empty(), stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithTwoLinesOpenWithDifferentLocalizationWithPastDateWithDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2);
    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment1.orderLine(), distributedQuantities1);
    final var distributedQuantities2 = new DistributionQuantities(BigDecimal.valueOf(20), BigDecimal.valueOf(10));
    final var distribution2 = new Distribution(commitment2.orderLine(), distributedQuantities2);
    final var distributions = List.of(distribution1, distribution2);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered1 = commitment1.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered1 = ordered1;
    final var stock1 = ordered1.subtract(distributionRequested);
    final var provisionKey1 = new ProvisionKey(sharedRawMaterial, commitment1.commitmentOrderLine().localization());
    final var provision1 = new Provision(provisionKey1, ordered1, pending, entered1, distributedQuantities1, stock1);

    final var distributionRequested2 = distributedQuantities2.requested();
    final var ordered2 = commitment2.ordered();
    final var pending2 = BigDecimal.ZERO;
    final var entered2 = ordered2;
    final var stock2 = ordered2.subtract(distributionRequested2);
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending2, entered2, distributedQuantities2, stock2);
    final var expected = List.of(provision1, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateTwoProvisionsWithTwoLinesOpenWithDifferentLocalizationWithPastDateWithoutDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var commitment1 = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitment2 = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment1, commitment2);
    final var distributions = List.<Distribution>of();

    final var ordered1 = commitment1.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered1 = commitment1.ordered();
    final var stock1 = commitment1.ordered();
    final var provisionKey1 = new ProvisionKey(sharedRawMaterial, commitment1.commitmentOrderLine().localization());
    final var provision1 = new Provision(provisionKey1, ordered1, pending, entered1, DistributionQuantities.empty(), stock1);

    final var ordered2 = commitment2.ordered();
    final var entered2 = ordered2;
    final var stock2 = ordered2;
    final var provisionKey2 = new ProvisionKey(sharedRawMaterial, commitment2.commitmentOrderLine().localization());
    final var provision2 = new Provision(provisionKey2, ordered2, pending, entered2, DistributionQuantities.empty(), stock2);
    final var expected = List.of(provision1, provision2);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineClosedWithFutureDateWithoutDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var futureDate = OffsetDateTime.MAX;
    final var commitment = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.CLOSED);
    final var commitments = List.of(commitment);

    final var distributions = List.of();

    final var distributionRequested = BigDecimal.ZERO;
    final var ordered = commitment.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = distributionRequested;
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, DistributionQuantities.empty(), stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineClosedWithFutureDateWithDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var futureDate = OffsetDateTime.MAX;
    final var commitment = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.CLOSED);
    final var commitments = List.of(commitment);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution = new Distribution(commitment.orderLine(), distributedQuantities1);
    final var distributions = List.of(distribution);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = distributionRequested;
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineClosedWithPastDateWithoutDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var commitment = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.CLOSED);
    final var commitments = List.of(commitment);

    final var distributions = List.of();

    final var distributionRequested = BigDecimal.ZERO;
    final var ordered = commitment.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = distributionRequested;
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, DistributionQuantities.empty(), stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineClosedWithPastDateWithDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var commitment = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.CLOSED);
    final var commitments = List.of(commitment);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment.orderLine(), distributedQuantities1);
    final var distributions = List.of(distribution1);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = distributionRequested;
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineOpenWithPastDateWithoutDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var commitment =
        this.getCommitment(sharedRawMaterial, pastDate, UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()),
            CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment);
    final var distributions = List.<Distribution>of();

    final var ordered = commitment.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = ordered;
    final var stock = ordered;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, DistributionQuantities.empty(), stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineOpenWithPastDateWithDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var pastDate = OffsetDateTime.MIN;
    final var commitment = this.getCommitment(sharedRawMaterial, pastDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitments = List.of(commitment);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution1 = new Distribution(commitment.orderLine(), distributedQuantities1);
    final var distributions = List.of(distribution1);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment.ordered();
    final var pending = BigDecimal.ZERO;
    final var entered = ordered;
    final var stock = ordered.subtract(distributionRequested);
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineOpenWithFutureDateWithoutDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var futureDate = OffsetDateTime.MAX;
    final var commitment = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);

    final var commitments = List.of(commitment);
    final var distributions = List.<Distribution>of();
    final var ordered = commitment.ordered();
    final var pending = ordered;
    final var entered = BigDecimal.ZERO;
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, DistributionQuantities.empty(), stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Arguments shouldCreateOneProvisionWithOneLineOpenWithFutureDateWithDistributed() {
    final var sharedRawMaterial = this.getSharedRawMaterial();
    final var futureDate = OffsetDateTime.MAX;
    final var commitment = this.getCommitment(sharedRawMaterial, futureDate,
        UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()), CommitmentOrderLineStatus.OPEN);
    final var commitments = List.of(commitment);

    final var distributedQuantities1 = new DistributionQuantities(BigDecimal.valueOf(70), BigDecimal.valueOf(60));
    final var distribution = new Distribution(commitment.orderLine(), distributedQuantities1);
    final var distributions = List.of(distribution);

    final var distributionRequested = distributedQuantities1.requested();
    final var ordered = commitment.ordered();
    final var pending = ordered.subtract(distributionRequested);
    final var entered = distributionRequested;
    final var stock = BigDecimal.ZERO;
    final var provisionKey = new ProvisionKey(sharedRawMaterial, commitment.commitmentOrderLine().localization());
    final var provision = new Provision(provisionKey, ordered, pending, entered, distributedQuantities1, stock);
    final var expected = List.of(provision);

    return Arguments.of(
        CurrentMethodName.get(),
        commitments,
        distributions,
        expected);
  }

  private Commitment getCommitment(SharedRawMaterial sharedRawMaterial, OffsetDateTime expectedDate, String localization,
      CommitmentOrderLineStatus status) {
    final var orderId = UUID.randomUUID();
    final var orderLineId = UUID.randomUUID();
    final var orderLine = new OrderLine(orderId, orderLineId, sharedRawMaterial);
    final var commitmentOrderLine = new CommitmentOrderLine(status, localization, expectedDate);
    final var ordered = BigDecimal.valueOf(100);
    return new Commitment(orderLine, ordered, commitmentOrderLine);
  }

  private SharedRawMaterial getSharedRawMaterial() {
    final var referenceId = UUID.randomUUID();
    final var useId = UUID.randomUUID();
    final var budgetCycle = UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID());
    return new SharedRawMaterial(referenceId, useId, budgetCycle);
  }
}
