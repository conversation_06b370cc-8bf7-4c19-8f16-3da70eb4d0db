package com.inditex.icdmdemg.domain.nominatedprovision;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.util.UUID;

import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.BudgetId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.DistributionDistributed;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.DistributionRequested;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Entered;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.LocalizationId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Ordered;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Pending;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.ProductId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.ReferenceId;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.Stock;
import com.inditex.icdmdemg.domain.nominatedprovision.NominatedProvision.UseId;
import com.inditex.icdmdemg.domain.nominatedprovision.mother.NominatedProvisionAuditMother;

import org.junit.jupiter.api.Test;

class NominatedProvisionTest {

  @Test
  void should_create_nominated_provision_with_defaults() {
    final var productId = new ProductId(UUID.randomUUID().toString());
    final var referenceId = new ReferenceId(UUID.randomUUID().toString());
    final var useId = new UseId(UUID.randomUUID().toString());
    final var budgetId = new BudgetId(UUID.randomUUID().toString());
    final var localizationId = new LocalizationId(UUID.randomUUID().toString());
    final var ordered = new Ordered(BigDecimal.valueOf(10));
    final var entered = new Entered(BigDecimal.valueOf(10));
    final var pending = new Pending(BigDecimal.valueOf(5));
    final var distributed = new DistributionDistributed(BigDecimal.valueOf(2));
    final var stock = new Stock(BigDecimal.valueOf(3));
    final var distributionRequested = new DistributionRequested(BigDecimal.valueOf(2));
    final var audit = NominatedProvisionAuditMother.created().build();

    final var nominatedProvision = NominatedProvision.create(
        productId,
        referenceId,
        useId,
        budgetId,
        localizationId,
        ordered,
        entered,
        pending,
        stock,
        distributed,
        distributionRequested,
        audit.updatedAt().value(),
        audit.updatedBy().value());

    assertThat(nominatedProvision).isNotNull();
    assertThat(nominatedProvision.productId()).isEqualTo(productId);
    assertThat(nominatedProvision.referenceId()).isEqualTo(referenceId);
    assertThat(nominatedProvision.useId()).isEqualTo(useId);
    assertThat(nominatedProvision.budgetId()).isEqualTo(budgetId);
    assertThat(nominatedProvision.localizationId()).isEqualTo(localizationId);
    assertThat(nominatedProvision.ordered()).isEqualTo(ordered);
    assertThat(nominatedProvision.entered()).isEqualTo(entered);
    assertThat(nominatedProvision.pending()).isEqualTo(pending);
    assertThat(nominatedProvision.distributionDistributed()).isEqualTo(distributed);
    assertThat(nominatedProvision.stock()).isEqualTo(stock);
    assertThat(nominatedProvision.distributionRequested()).isEqualTo(distributionRequested);
    assertThat(nominatedProvision.audit()).isEqualTo(audit);
  }
}
