package com.inditex.icdmdemg.domain.use;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.use.Use.Id;
import com.inditex.icdmdemg.domain.use.entity.AssignableType;
import com.inditex.icdmdemg.domain.use.entity.PurchaseType;
import com.inditex.icdmdemg.domain.use.entity.UseConditions;
import com.inditex.icdmdemg.domain.use.entity.UseNames;
import com.inditex.icdmdemg.domain.use.mother.UseConditionsMother;
import com.inditex.icdmdemg.domain.use.mother.UseMother;
import com.inditex.icdmdemg.domain.use.mother.UseNamesMother;

import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.junit.jupiter.MockitoExtension;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
class UseTest {

  @ParameterizedTest
  @MethodSource("provideNotNullableParams")
  void should_not_create_use_with_null_values(
      final Id id,
      final AssignableType assignable,
      final Use.Taxonomy taxonomy,
      final Use.Customer customer,
      final PurchaseType purchaseType,
      final UseNames names,
      final UseConditions conditions,
      final CompleteAudit audit) {

    assertThatThrownBy(
        () -> new Use(id, assignable, taxonomy, customer, purchaseType, names, conditions, audit))
            .isInstanceOf(NullPointerException.class);
  }

  public Stream<Arguments> provideNotNullableParams() {
    return Stream.of(
        Arguments.of(
            UseMother.randomId(),
            null,
            null,
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            UseMother.randomId(),
            UseMother.randomAssignable(),
            null,
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            UseMother.randomId(),
            UseMother.randomAssignable(),
            UseMother.randomTaxonomy(),
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            UseMother.randomId(),
            UseMother.randomAssignable(),
            UseMother.randomTaxonomy(),
            UseMother.randomCustomer(),
            null,
            null,
            null,
            null),
        Arguments.of(
            UseMother.randomId(),
            UseMother.randomAssignable(),
            UseMother.randomTaxonomy(),
            UseMother.randomCustomer(),
            UseMother.randomPurchaseType(),
            null,
            null,
            null),
        Arguments.of(
            UseMother.randomId(),
            UseMother.randomAssignable(),
            UseMother.randomTaxonomy(),
            UseMother.randomCustomer(),
            UseMother.randomPurchaseType(),
            UseNamesMother.defaultUseNames(),
            null,
            null),
        Arguments.of(
            UseMother.randomId(),
            UseMother.randomAssignable(),
            UseMother.randomTaxonomy(),
            UseMother.randomCustomer(),
            UseMother.randomPurchaseType(),
            UseNamesMother.defaultUseNames(),
            UseConditionsMother.randomConditions(),
            null));
  }
}
