package com.inditex.icdmdemg.domain.order;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

import java.time.OffsetDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import com.inditex.icdmdemg.domain.order.entity.OrderBudgetId;
import com.inditex.icdmdemg.domain.order.entity.OrderId;
import com.inditex.icdmdemg.domain.order.entity.OrderLine;
import com.inditex.icdmdemg.domain.order.entity.OrderLineId;
import com.inditex.icdmdemg.domain.order.entity.OrderLineMeasuringUnitsId;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantity;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetail;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetailReferenceId;
import com.inditex.icdmdemg.domain.order.entity.OrderLineQuantityDetails;
import com.inditex.icdmdemg.domain.order.entity.OrderLineServiceDate;
import com.inditex.icdmdemg.domain.order.entity.OrderLines;
import com.inditex.icdmdemg.domain.order.entity.OrderStatusKey;
import com.inditex.icdmdemg.domain.order.entity.OrderSupplierId;
import com.inditex.icdmdemg.domain.product.entity.ProductReferenceId;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class OrderTest {

  @Test
  void testProductReferenceIds() {
    final OrderId orderId = new OrderId(UUID.randomUUID().toString());
    final OrderStatusKey orderStatusId = OrderStatusKey.FORMALIZED;
    final OrderLines orderLines = new OrderLines(List.of(this.getOrderLine(), this.getOrderLine()));
    final OffsetDateTime now = OffsetDateTime.now();
    final OrderSupplierId supplierId = new OrderSupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UUID.randomUUID()));

    final Order order = Order.create(orderId, orderStatusId, orderLines, now, supplierId);

    final var result = order.orderReferenceIdsAsProductReferenceIds();

    final var expectedReferenceIds = Arrays.asList(new ProductReferenceId("referenceId1"), new ProductReferenceId("referenceId2"),
        new ProductReferenceId("referenceId3"));
    assertThat(expectedReferenceIds)
        .usingRecursiveComparison()
        .ignoringCollectionOrder()
        .isEqualTo(result);
  }

  private OrderLine getOrderLine() {
    return new OrderLine(
        new OrderLineId(UUID.randomUUID().toString()),
        new OrderLineServiceDate(OffsetDateTime.now()),
        new OrderLineMeasuringUnitsId(UUID.randomUUID().toString()),
        new OrderBudgetId(UrnConstantsEnum.BUDGET_CYCLE.prefixWithUrn(UUID.randomUUID())),
        new OrderLineQuantityDetails(
            List.of(new OrderLineQuantityDetail(new OrderLineQuantity(100),
                new OrderLineQuantityDetailReferenceId("referenceId1")),
                new OrderLineQuantityDetail(new OrderLineQuantity(200),
                    new OrderLineQuantityDetailReferenceId("referenceId2")),
                new OrderLineQuantityDetail(new OrderLineQuantity(300),
                    new OrderLineQuantityDetailReferenceId("referenceId3")))));
  }
}
