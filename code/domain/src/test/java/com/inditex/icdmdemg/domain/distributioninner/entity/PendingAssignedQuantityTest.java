package com.inditex.icdmdemg.domain.distributioninner.entity;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.PendingAssignedQuantity;
import com.inditex.icdmdemg.domain.distributioninner.DistributionInner.RequestedQuantity;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

public class PendingAssignedQuantityTest {

  @ParameterizedTest
  @MethodSource("provide_params_pending_assigned")
  void should_calculate_pending_assigned(final RequestedQuantity requestedQuantity, final DistributedQuantity distributedQuantity,
      final DistributionInnerStatus distributionInnerStatus, final Double expectedResult) {
    final var result = PendingAssignedQuantity.calculate(requestedQuantity, distributedQuantity, distributionInnerStatus);

    assertThat(result.value().doubleValue()).isEqualTo(expectedResult);
  }

  public static Stream<Arguments> provide_params_pending_assigned() {
    return Stream.of(
        Arguments.of(new RequestedQuantity(BigDecimal.valueOf(1000)), new DistributedQuantity(BigDecimal.valueOf(0)),
            DistributionInnerStatus.IN_PROGRESS, 1000.00),
        Arguments.of(new RequestedQuantity(BigDecimal.valueOf(1000)), new DistributedQuantity(BigDecimal.valueOf(750)),
            DistributionInnerStatus.IN_PROGRESS, 250.00),
        Arguments.of(new RequestedQuantity(BigDecimal.valueOf(1000)), new DistributedQuantity(BigDecimal.valueOf(1045)),
            DistributionInnerStatus.IN_PROGRESS, 0.00),
        Arguments.of(new RequestedQuantity(BigDecimal.valueOf(1000)), new DistributedQuantity(BigDecimal.valueOf(750)),
            DistributionInnerStatus.SENT, 0.00),
        Arguments.of(new RequestedQuantity(BigDecimal.valueOf(1000)), new DistributedQuantity(BigDecimal.valueOf(1045)),
            DistributionInnerStatus.SENT, 0.00),
        Arguments.of(new RequestedQuantity(BigDecimal.valueOf(1000)), new DistributedQuantity(BigDecimal.valueOf(750)),
            DistributionInnerStatus.CLOSED, 0.00)

    );
  }
}
