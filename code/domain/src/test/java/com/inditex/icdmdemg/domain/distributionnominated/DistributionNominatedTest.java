package com.inditex.icdmdemg.domain.distributionnominated;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.instancio.Select.field;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycle;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycleChangePendingQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ConsumptionFactor;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.DistributedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductOrderId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductSupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ProductVariantGroupId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceProductId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.TheoreticalQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.UseId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominatedUnifiedEvent.EventType;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLines;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedPlan;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedStatus;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo.Status;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAudit;
import com.inditex.icdmdemg.domain.shared.audit.CompleteAuditMother;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.iopcmmnt.ddd.core.DomainEvent;

import org.instancio.Instancio;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@TestInstance(Lifecycle.PER_CLASS)
@ExtendWith(MockitoExtension.class)
class DistributionNominatedTest {

  @Test
  void should_not_create_distribution_nominated_with_null_id() {
    final var audit = CompleteAuditMother.created();
    final DistributionNominatedLines lines = new DistributionNominatedLines(List.of());
    assertThatThrownBy(() -> new DistributionNominated(null, DistributionNominatedMother.randomReferenceId(),
        DistributionNominatedMother.randomUseId(), DistributionNominatedMother.randomBudgetCycle(),
        DistributionNominatedMother.randomReferenceProductId(), DistributionNominatedMother.randomProductOrderId(),
        DistributionNominatedMother.randomProductSupplierId(), DistributionNominatedMother.randomProductVariantGroupId(),
        DistributionNominatedMother.randomTheoreticalQuantity(), DistributionNominatedMother.randomConsumptionFactor(),
        DistributionNominatedMother.randomRequestedQuantity(), DistributionNominatedMother.randomDistributedQuantity(),
        DistributionNominatedMother.randomBudgetCycleChangePendingQuantity(),
        DistributionNominatedMother.randomStatus(), DistributionNominatedMother.randomPlan(), lines, audit))
            .isInstanceOf(RuntimeException.class);

    assertThatThrownBy(() -> {
      final ReferenceId referenceId = DistributionNominatedMother.randomReferenceId();
      final UseId useId = DistributionNominatedMother.randomUseId();
      final BudgetCycle budgetCycle = DistributionNominatedMother.randomBudgetCycle();
      final ReferenceProductId referenceProductId = DistributionNominatedMother.randomReferenceProductId();
      final ProductOrderId productOrderId = DistributionNominatedMother.randomProductOrderId();
      final ProductSupplierId productSupplierId = DistributionNominatedMother.randomProductSupplierId();
      final ProductVariantGroupId productVariantGroupId = DistributionNominatedMother.randomProductVariantGroupId();
      final ProductOrderStatusInfo productOrderStatusInfo = DistributionNominatedMother.randomProductOrderInfo();
      final DistributionNominatedPlan plan = DistributionNominatedMother.randomPlan();
      final TheoreticalQuantity theoreticalQuantity = DistributionNominatedMother.randomTheoreticalQuantity();
      final ConsumptionFactor consumptionFactor = DistributionNominatedMother.randomConsumptionFactor();
      final RequestedQuantity requestedQuantity = DistributionNominatedMother.randomRequestedQuantity();
      DistributionNominated.create(null, referenceId, useId, budgetCycle, referenceProductId, productOrderId, productSupplierId,
          productVariantGroupId, productOrderStatusInfo, theoreticalQuantity, consumptionFactor, requestedQuantity, lines, plan,
          audit.createdAt(), audit.createdBy());
    }).isInstanceOf(RuntimeException.class);
  }

  @ParameterizedTest
  @MethodSource("provideNotNullableParams")
  void should_not_create_distribution_nominated_with_null_values(
      final DistributionNominated.Id id,
      final ReferenceId referenceId,
      final UseId useId,
      final BudgetCycle budgetCycle,
      final ReferenceProductId referenceProductId,
      final ProductOrderId productOrderId,
      final ProductSupplierId productSupplierId,
      final ProductVariantGroupId productVariantGroupId,
      final TheoreticalQuantity theoreticalQuantity,
      final ConsumptionFactor consumptionFactor,
      final RequestedQuantity requestedQuantity,
      final DistributedQuantity distributedQuantity,
      final BudgetCycleChangePendingQuantity budgetCycleChangePendingQuantity,
      final DistributionNominatedStatus status,
      final DistributionNominatedPlan plan,
      final List<DistributionNominatedLine> lines,
      final CompleteAudit audit) {
    assertThatThrownBy(
        () -> new DistributionNominated(id, referenceId, useId, budgetCycle, referenceProductId, productOrderId, productSupplierId,
            productVariantGroupId, theoreticalQuantity, consumptionFactor, requestedQuantity, distributedQuantity,
            budgetCycleChangePendingQuantity, status, plan,
            new DistributionNominatedLines(lines), audit))
                .isInstanceOf(NullPointerException.class);

    assertThatThrownBy(() -> {
      final ProductOrderStatusInfo productOrderStatusInfo = new ProductOrderStatusInfo(Status.DRAFT, false);
      DistributionNominated.create(id, referenceId, useId, budgetCycle, referenceProductId, productOrderId,
          productSupplierId, productVariantGroupId, productOrderStatusInfo, theoreticalQuantity,
          consumptionFactor, requestedQuantity, new DistributionNominatedLines(lines), plan, audit.createdAt(), audit.createdBy());
    })
        .isInstanceOf(NullPointerException.class);
  }

  @Test
  void should_modify_product_variant_group() {
    final var randomDate = OffsetDateTimeMother.random();
    final var updatedBy = "UPDATED_BY";
    final var distributionNominated = Instancio.create(DistributionNominated.class);
    final var productVariantGroupId = DistributionNominatedMother.randomProductVariantGroupId();

    final var modifiedDistributionNominated =
        distributionNominated.updateProductVariantGroupId(productVariantGroupId.value(), randomDate, updatedBy);

    assertThat(modifiedDistributionNominated.productVariantGroupId()).isEqualTo(productVariantGroupId);
    assertThat(modifiedDistributionNominated.audit().updatedAt()).isEqualTo(randomDate);
    assertThat(modifiedDistributionNominated.audit().updatedBy()).isEqualTo(updatedBy);
  }

  public Stream<Arguments> provideNotNullableParams() {
    return Stream.of(
        Arguments.of(
            DistributionNominatedMother.randomId(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            DistributionNominatedMother.randomBudgetCycle(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            DistributionNominatedMother.randomBudgetCycle(),
            DistributionNominatedMother.randomReferenceProductId(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            DistributionNominatedMother.randomBudgetCycle(),
            DistributionNominatedMother.randomReferenceProductId(),
            DistributionNominatedMother.randomProductOrderId(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            DistributionNominatedMother.randomBudgetCycle(),
            DistributionNominatedMother.randomReferenceProductId(),
            DistributionNominatedMother.randomProductOrderId(),
            DistributionNominatedMother.randomProductSupplierId(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            DistributionNominatedMother.randomBudgetCycle(),
            DistributionNominatedMother.randomReferenceProductId(),
            DistributionNominatedMother.randomProductOrderId(),
            DistributionNominatedMother.randomProductSupplierId(),
            DistributionNominatedMother.randomProductVariantGroupId(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            DistributionNominatedMother.randomBudgetCycle(),
            DistributionNominatedMother.randomReferenceProductId(),
            DistributionNominatedMother.randomProductOrderId(),
            DistributionNominatedMother.randomProductSupplierId(),
            DistributionNominatedMother.randomProductVariantGroupId(),
            DistributionNominatedMother.randomTheoreticalQuantity(),
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            DistributionNominatedMother.randomBudgetCycle(),
            DistributionNominatedMother.randomReferenceProductId(),
            DistributionNominatedMother.randomProductOrderId(),
            DistributionNominatedMother.randomProductSupplierId(),
            DistributionNominatedMother.randomProductVariantGroupId(),
            DistributionNominatedMother.randomTheoreticalQuantity(),
            DistributionNominatedMother.randomConsumptionFactor(),
            null,
            null,
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            DistributionNominatedMother.randomBudgetCycle(),
            DistributionNominatedMother.randomReferenceProductId(),
            DistributionNominatedMother.randomProductOrderId(),
            DistributionNominatedMother.randomProductSupplierId(),
            DistributionNominatedMother.randomProductVariantGroupId(),
            DistributionNominatedMother.randomTheoreticalQuantity(),
            DistributionNominatedMother.randomConsumptionFactor(),
            DistributionNominatedMother.randomRequestedQuantity(),
            null,
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            DistributionNominatedMother.randomBudgetCycle(),
            DistributionNominatedMother.randomReferenceProductId(),
            DistributionNominatedMother.randomProductOrderId(),
            DistributionNominatedMother.randomProductSupplierId(),
            DistributionNominatedMother.randomProductVariantGroupId(),
            DistributionNominatedMother.randomTheoreticalQuantity(),
            DistributionNominatedMother.randomConsumptionFactor(),
            DistributionNominatedMother.randomRequestedQuantity(),
            DistributionNominatedMother.randomDistributedQuantity(),
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            DistributionNominatedMother.randomBudgetCycle(),
            DistributionNominatedMother.randomReferenceProductId(),
            DistributionNominatedMother.randomProductOrderId(),
            DistributionNominatedMother.randomProductSupplierId(),
            DistributionNominatedMother.randomProductVariantGroupId(),
            DistributionNominatedMother.randomTheoreticalQuantity(),
            DistributionNominatedMother.randomConsumptionFactor(),
            DistributionNominatedMother.randomRequestedQuantity(),
            DistributionNominatedMother.randomDistributedQuantity(),
            DistributionNominatedMother.randomBudgetCycleChangePendingQuantity(),
            null,
            null,
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            DistributionNominatedMother.randomBudgetCycle(),
            DistributionNominatedMother.randomReferenceProductId(),
            DistributionNominatedMother.randomProductOrderId(),
            DistributionNominatedMother.randomProductSupplierId(),
            DistributionNominatedMother.randomProductVariantGroupId(),
            DistributionNominatedMother.randomTheoreticalQuantity(),
            DistributionNominatedMother.randomConsumptionFactor(),
            DistributionNominatedMother.randomRequestedQuantity(),
            DistributionNominatedMother.randomDistributedQuantity(),
            DistributionNominatedMother.randomBudgetCycleChangePendingQuantity(),
            DistributionNominatedMother.randomStatus(),
            null,
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            DistributionNominatedMother.randomBudgetCycle(),
            DistributionNominatedMother.randomReferenceProductId(),
            DistributionNominatedMother.randomProductOrderId(),
            DistributionNominatedMother.randomProductSupplierId(),
            DistributionNominatedMother.randomProductVariantGroupId(),
            DistributionNominatedMother.randomTheoreticalQuantity(),
            DistributionNominatedMother.randomConsumptionFactor(),
            DistributionNominatedMother.randomRequestedQuantity(),
            DistributionNominatedMother.randomDistributedQuantity(),
            DistributionNominatedMother.randomBudgetCycleChangePendingQuantity(),
            DistributionNominatedMother.randomStatus(),
            DistributionNominatedMother.randomPlan(),
            null,
            null),
        Arguments.of(
            DistributionNominatedMother.randomId(),
            DistributionNominatedMother.randomReferenceId(),
            DistributionNominatedMother.randomUseId(),
            DistributionNominatedMother.randomBudgetCycle(),
            DistributionNominatedMother.randomReferenceProductId(),
            DistributionNominatedMother.randomProductOrderId(),
            DistributionNominatedMother.randomProductSupplierId(),
            DistributionNominatedMother.randomProductVariantGroupId(),
            DistributionNominatedMother.randomTheoreticalQuantity(),
            DistributionNominatedMother.randomConsumptionFactor(),
            DistributionNominatedMother.randomRequestedQuantity(),
            DistributionNominatedMother.randomDistributedQuantity(),
            DistributionNominatedMother.randomBudgetCycleChangePendingQuantity(),
            DistributionNominatedMother.randomStatus(),
            DistributionNominatedMother.randomPlan(),
            List.of(),
            null));
  }

  @Test
  void should_delete_dn_when_order_status_is_cancelled() {
    final var time = OffsetDateTime.now();
    final UseId useId = new UseId(UUID.randomUUID());
    final RequestedQuantity requestedQuantity = new RequestedQuantity(BigDecimal.valueOf(5000));
    final DistributedQuantity distributedQuantity = new DistributedQuantity(BigDecimal.valueOf(3000));

    final var line1 = Instancio.of(DistributionNominatedLine.class)
        .set(field("requestedQuantity"), new DistributionNominatedLine.RequestedQuantity(BigDecimal.valueOf(2000)))
        .set(field("distributedQuantity"), new DistributionNominatedLine.DistributedQuantity(BigDecimal.valueOf(1000)))
        .create();

    final var line2 = Instancio.of(DistributionNominatedLine.class)
        .set(field("requestedQuantity"), new DistributionNominatedLine.RequestedQuantity(BigDecimal.valueOf(2000)))
        .set(field("distributedQuantity"), new DistributionNominatedLine.DistributedQuantity(BigDecimal.valueOf(0)))
        .create();

    final var line3 = Instancio.of(DistributionNominatedLine.class)
        .set(field("requestedQuantity"), new DistributionNominatedLine.RequestedQuantity(BigDecimal.valueOf(2000)))
        .set(field("distributedQuantity"), new DistributionNominatedLine.DistributedQuantity(BigDecimal.valueOf(2000)))
        .create();

    final DistributionNominatedLines lines = new DistributionNominatedLines(List.of(line1, line2, line3));

    final var audit = Instancio.of(CompleteAudit.class)
        .set(field("createdAt"), OffsetDateTime.MAX)
        .create();

    final var dn = Instancio.of(DistributionNominated.class)
        .set(field("audit"), audit)
        .set(field("useId"), useId)
        .set(field("requestedQuantity"), requestedQuantity)
        .set(field("distributedQuantity"), distributedQuantity)
        .set(field("lines"), lines)
        .set(field("status"), DistributionNominatedStatus.PENDING)
        .set(field("budgetCycleChangePendingQuantity"), new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
        .create();

    final var distributionNominatedCloseConfigProviderMock = Mockito.mock(DistributionNominatedCloseConfigProvider.class);

    final var result =
        dn.updateWithOrderState(new ProductOrderStatusInfo(Status.CANCELLED, false), time, "test",
            distributionNominatedCloseConfigProviderMock);

    assertThat(result.audit.deletedAt()).isNotNull();
    assertThat(result.requestedQuantity().value()).isEqualTo(dn.requestedQuantity.value());
    assertThat(result.lines().value()).hasSize(3);
    assertThat(result.lines().value()).containsExactlyInAnyOrderElementsOf(List.of(line1, line2, line3));
  }

  @Test
  void should_register_updated_event_if_only_plan_changes() {
    final DistributionNominated distributionNominated =
        DistributionNominatedMother.pending()
            .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.ZERO))
            .plan(DistributionNominatedPlan.PRESELECTED).build();

    final var notUpdatedDistributionNominated = distributionNominated.update(
        distributionNominated.theoreticalQuantity(),
        distributionNominated.consumptionFactor(),
        distributionNominated.requestedQuantity(),
        distributionNominated.lines().value(),
        DistributionNominatedPlan.PRESELECTED,
        OffsetDateTimeMother.random(),
        "test");

    assertThat(notUpdatedDistributionNominated.domainEvents()).isEmpty();

    final var updatedDistributionNominated = distributionNominated.update(
        distributionNominated.theoreticalQuantity(),
        distributionNominated.consumptionFactor(),
        distributionNominated.requestedQuantity(),
        distributionNominated.lines().value(),
        DistributionNominatedPlan.AUTO,
        OffsetDateTimeMother.random(),
        "test");

    final var domainEvents = (List<DomainEvent<?>>) updatedDistributionNominated.domainEvents();
    assertThat(domainEvents).hasSize(1);
    assertThat(domainEvents.get(0).eventType()).isEqualTo(EventType.UPDATED.value);
  }
}
