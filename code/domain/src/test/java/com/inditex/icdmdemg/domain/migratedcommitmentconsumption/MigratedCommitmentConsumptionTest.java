package com.inditex.icdmdemg.domain.migratedcommitmentconsumption;

import static org.assertj.core.api.Assertions.assertThatThrownBy;

import java.util.UUID;

import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionId;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionMovementId;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionOrderId;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionOrderLine;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionOrderLineId;
import com.inditex.icdmdemg.domain.migratedcommitmentconsumption.entity.MigratedCommitmentConsumptionQuantity;
import com.inditex.icdmdemg.shared.utils.RandomValue;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;

@TestInstance(Lifecycle.PER_CLASS)
class MigratedCommitmentConsumptionTest {

  @Test
  void should_not_create_migrated_consumption_with_null_reference() {
    assertThatThrownBy(() -> new MigratedCommitmentConsumption(
        new MigratedCommitmentConsumptionId(UUID.randomUUID().toString()),
        new MigratedCommitmentConsumptionMovementId(UUID.randomUUID().toString()),
        null,
        new MigratedCommitmentConsumptionOrderLine(
            new MigratedCommitmentConsumptionOrderId(UUID.randomUUID().toString()),
            new MigratedCommitmentConsumptionOrderLineId(UUID.randomUUID().toString())),
        new MigratedCommitmentConsumptionQuantity(RandomValue.randomPositiveBigDecimal()))).isInstanceOf(RuntimeException.class);
  }
}
