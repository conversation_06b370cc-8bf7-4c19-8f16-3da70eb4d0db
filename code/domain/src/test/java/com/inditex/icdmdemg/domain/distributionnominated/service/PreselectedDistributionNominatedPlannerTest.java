package com.inditex.icdmdemg.domain.distributionnominated.service;

import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.created;
import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.kept;
import static com.inditex.icdmdemg.domain.shared.EntityAndActionResult.updated;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.params.ParameterizedTest.INDEX_PLACEHOLDER;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.OffsetDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.commitmentadjusted.CommitmentAdjusted;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseBudgetCycleChangeRequestedAt;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus;
import com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus.MaterialCommitmentUseStatusEnum;
import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.BudgetCycleChangePendingQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.Id;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.ReferenceId;
import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.LineId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.DistributionNominatedLine.CommitmentOrder.SupplierId;
import com.inditex.icdmdemg.domain.distributionnominated.entity.SharedRawMaterialNominated;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedLineMother;
import com.inditex.icdmdemg.domain.distributionnominated.mother.DistributionNominatedMother;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Commitment;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.DistributionNominatedPlannerType;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Line;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan.Preselected;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Plan.Preselected.Create;
import com.inditex.icdmdemg.domain.distributionnominated.service.DistributionNominatedPlanner.Request;
import com.inditex.icdmdemg.domain.shared.EntityAndActionResult;
import com.inditex.icdmdemg.domain.shared.Error.BadRequest;
import com.inditex.icdmdemg.domain.shared.exception.ErrorException;
import com.inditex.icdmdemg.shared.utils.CurrentMethodName;
import com.inditex.icdmdemg.shared.utils.NumericUtils;
import com.inditex.icdmdemg.shared.utils.RandomValue;
import com.inditex.icdmdemg.shared.utils.UrnConstantsEnum;
import com.inditex.icdmdemg.shared.utils.mother.OffsetDateTimeMother;
import com.inditex.icdmdemg.shared.utils.mother.UuidMother;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

@TestInstance(Lifecycle.PER_CLASS)
class PreselectedDistributionNominatedPlannerTest {
  public static final String PARAMETERIZED_TEST_DESCRIPTION = "[" + INDEX_PLACEHOLDER + "] [{0}]";

  @ParameterizedTest(name = PARAMETERIZED_TEST_DESCRIPTION)
  @MethodSource("getInputCasesAndExpectedReturn")
  void should_return_correct_list_from_different_inputs(
      final String ignoredTestName,
      final DistributionNominatedPlanner sut,
      final List<EntityAndActionResult<Line>> output) {
    final var actual = sut.executePlan();
    assertThat(actual).containsExactlyInAnyOrderElementsOf(output);
  }

  @ParameterizedTest(name = PARAMETERIZED_TEST_DESCRIPTION)
  @MethodSource("getInputCasesAndExpectedThrowable")
  void should_throw_when_error(
      final String ignoredTestName,
      final DistributionNominatedPlanner sut,
      final Throwable exception) {
    assertThatThrownBy(sut::executePlan)
        .isInstanceOf(exception.getClass())
        .hasMessage(exception.getMessage());
  }

  public Stream<Arguments> getInputCasesAndExpectedReturn() {
    return Stream.of(
        this.shouldNewRequestPreselectedFillWithOneCommitment(),
        this.shouldNewRequestPreselectedFillWithMoreThanOneCommitment(),

        this.shouldModifyRequestPreselectedKeepWhenRequestedIsTheSame(),
        this.shouldModifyRequestPreselectedUpWithOneCommitment(),
        this.shouldModifyRequestPreselectedDownWithOneCommitment(),
        this.shouldModifyRequestPreselectedUpAddingOneCommitment(),
        this.shouldModifyRequestPreselectedDownReducingOneCommitment(),
        this.shouldModifyRequestPreselectedDownKeepingLineWithOutRequested(),
        this.shouldModifyRequestPreselectedUpdateWhenIncreaseRequestedOnDistributedLine(),

        this.shouldModifyRequestPreselectedDownReducingInBudgetCycleChangeLine());
  }

  public Stream<Arguments> getInputCasesAndExpectedThrowable() {
    return Stream.of(
        this.shouldNewRequestPreselectedThrowWhenNotEnoughCommitment(),
        this.shouldNewRequestPreselectedThrowWhenRequestedQuantityDoesNotMatch(),
        this.shouldNewRequestPreselectedThrowWhenRequestedQuantityIsZero(),
        this.shouldModifyRequestPreselectedThrowWhenNotEnoughCommitment(),
        this.shouldModifyRequestPreselectedThrowWhenRequestedQuantityDoesNotMatch(),
        this.shouldModifyRequestPreselectedThrowWhenRequestedQuantityIsZero(),
        this.shouldModifyRequestPreselectedThrowWhenRequestedQuantityIsDecreasedOnDistributedLine(),

        this.shouldNewRequestPreselectedWhenTryToAssignBudgetCycleChangeCommitment(),
        this.shouldModifyRequestPreselectedThrowWhenAddBudgetCycleChangeCommitment(),
        this.shouldModifyRequestPreselectedThrowWhenAddBudgetCycleChangeCommitmentAndInsufficientNormalCommitment(),
        this.shouldModifyRequestPreselectedThrowWhenOneLineIsBudgetCycleChangeCommitment());
  }

  private Arguments shouldNewRequestPreselectedFillWithOneCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(commitment(201, 301, 901, 500, 1)),
            List.of(),
            createPreselectedPlan(101, 500, List.of(
                line(101, 201, 301, 901, 500, 0, null))),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(created(line(101, 201, 301, 901, 500, 0, null))));
  }

  private Arguments shouldNewRequestPreselectedFillWithMoreThanOneCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(
                commitment(201, 201, 901, 500, 1),
                commitment(201, 301, 901, 500, 1)),
            List.of(),
            createPreselectedPlan(101, 700, List.of(
                line(101, 201, 201, 901, 400, 0, null),
                line(101, 201, 301, 901, 300, 0, null))),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            created(line(101, 201, 201, 901, 400, 0, null)),
            created(line(101, 201, 301, 901, 300, 0, null))));
  }

  private Arguments shouldModifyRequestPreselectedKeepWhenRequestedIsTheSame() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 0, 1)),
            List.of(commitment(201, 301, 901, 150, 1)),
            List.of(),
            modifyPreselectedPlan(101, 100, List.of(
                line(101, 201, 301, 901, 100, 0, 1))),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(kept(line(101, 201, 301, 901, 100, 0, 1))));
  }

  private Arguments shouldModifyRequestPreselectedUpdateWhenIncreaseRequestedOnDistributedLine() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 150, 1)),
            List.of(line(101, 201, 301, 901, 100, 100, 1)),
            List.of(commitment(201, 301, 901, 150, 1)),
            List.of(),
            modifyPreselectedPlan(101, 150, List.of(
                line(101, 201, 301, 901, 150, 0, 1))),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(updated(line(101, 201, 301, 901, 150, 100, 1))));
  }

  private Arguments shouldModifyRequestPreselectedUpWithOneCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 50, 0, 1)),
            List.of(commitment(201, 301, 901, 150, 1)),
            List.of(),
            modifyPreselectedPlan(101, 100, List.of(
                line(101, 201, 301, 901, 100, 0, 1))),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(updated(line(101, 201, 301, 901, 100, 0, 1))));
  }

  private Arguments shouldModifyRequestPreselectedDownWithOneCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 0, 1)),
            List.of(commitment(201, 301, 901, 150, 1)),
            List.of(),
            modifyPreselectedPlan(101, 50, List.of(
                line(101, 201, 301, 901, 50, 0, 1))),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(updated(line(101, 201, 301, 901, 50, 0, 1))));
  }

  private Arguments shouldModifyRequestPreselectedUpAddingOneCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 201, 901, 100, 0, 1)),
            List.of(
                commitment(201, 201, 901, 150, 1),
                commitment(201, 301, 901, 150, 1)),
            List.of(),
            modifyPreselectedPlan(101, 200, List.of(
                line(101, 201, 201, 901, 150, 0, 1),
                line(101, 201, 301, 901, 50, 0, 1))),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(101, 201, 201, 901, 150, 0, 1)),
            created(line(101, 201, 301, 901, 50, 0, null))));
  }

  private Arguments shouldModifyRequestPreselectedDownReducingOneCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 200, 1)),
            List.of(
                line(101, 201, 201, 901, 100, 0, 1),
                line(101, 201, 301, 901, 100, 0, 1)),
            List.of(
                commitment(201, 201, 901, 150, 1),
                commitment(201, 301, 901, 150, 1)),
            List.of(),
            modifyPreselectedPlan(101, 150, List.of(
                line(101, 201, 201, 901, 150, 0, 1))),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(101, 201, 201, 901, 150, 0, 1)),
            updated(line(101, 201, 301, 901, 0, 0, 1))));
  }

  private Arguments shouldModifyRequestPreselectedDownKeepingLineWithOutRequested() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 200, 1)),
            List.of(
                line(101, 201, 201, 901, 100, 0, 1),
                line(101, 201, 301, 901, 0, 0, 1)),
            List.of(
                commitment(201, 201, 901, 150, 1),
                commitment(201, 301, 901, 150, 1)),
            List.of(),
            modifyPreselectedPlan(101, 150, List.of(
                line(101, 201, 201, 901, 150, 0, 1))),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(101, 201, 201, 901, 150, 0, 1)),
            kept(line(101, 201, 301, 901, 0, 0, 1))));
  }

  private Arguments shouldModifyRequestPreselectedDownReducingInBudgetCycleChangeLine() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 200, 1)),
            List.of(
                line(101, 201, 201, 901, 100, 0, 1),
                line(101, 201, 301, 901, 0, 0, 1)),
            List.of(
                commitment(201, 201, 901, 150, 1),
                commitment(201, 301, 901, 150, 1)),
            List.of(),
            modifyPreselectedPlan(101, 150, List.of(
                line(101, 201, 201, 901, 150, 0, 1))),
            DistributionNominatedPlannerType.DEFAULT),
        List.of(
            updated(line(101, 201, 201, 901, 150, 0, 1)),
            kept(line(101, 201, 301, 901, 0, 0, 1))));
  }

  private Arguments shouldNewRequestPreselectedThrowWhenNotEnoughCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(commitment(201, 301, 901, 450, 1)),
            List.of(),
            createPreselectedPlan(101, 500, List.of(
                line(101, 201, 301, 901, 500, 0, null))),
            DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest(
            "Insufficient or invalid commitment with orderId 00000000-0000-0000-0000-000000000201 and lineId 00000000-0000-0000-0000-000000000301. Only quantity 450.00 can be allocated")));
  }

  private Arguments shouldNewRequestPreselectedWhenTryToAssignBudgetCycleChangeCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(commitment(201, 301, 901, 450, 1),
                budgetCycleChangeCommitment(201, 302, 901, 450, 1)),
            List.of(),
            createPreselectedPlan(101, 500, List.of(
                line(101, 201, 301, 901, 450, 0, null),
                line(101, 201, 302, 901, 50, 0, null))),
            DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest(
            "Insufficient or invalid commitment with orderId 00000000-0000-0000-0000-000000000201 and lineId 00000000-0000-0000-0000-000000000302. Only quantity 0.00 can be allocated")));
  }

  private Arguments shouldNewRequestPreselectedThrowWhenRequestedQuantityDoesNotMatch() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(commitment(201, 301, 901, 300, 1)),
            List.of(),
            createPreselectedPlan(101, 500, List.of(
                line(101, 201, 301, 901, 300, 0, null))),
            DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest("Requested in lines should be equal to requested quantity")));
  }

  private Arguments shouldNewRequestPreselectedThrowWhenRequestedQuantityIsZero() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(),
            List.of(),
            List.of(commitment(201, 301, 901, 300, 1)),
            List.of(),
            createPreselectedPlan(101, 0, List.of(
                line(101, 201, 301, 901, 0, 0, null))),
            DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest("Requested should be greater than zero")));
  }

  private Arguments shouldModifyRequestPreselectedThrowWhenNotEnoughCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 0, 1)),
            List.of(commitment(201, 301, 901, 200, 1)),
            List.of(),
            modifyPreselectedPlan(101, 300, List.of(
                line(101, 201, 301, 901, 300, 0, 1))),
            DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest(
            "Insufficient or invalid commitment with orderId 00000000-0000-0000-0000-000000000201 and lineId 00000000-0000-0000-0000-000000000301. Only quantity 200.00 can be allocated")));
  }

  private Arguments shouldModifyRequestPreselectedThrowWhenRequestedQuantityDoesNotMatch() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 0, 1)),
            List.of(commitment(201, 301, 901, 200, 1)),
            List.of(),
            modifyPreselectedPlan(101, 100, List.of(
                line(101, 201, 301, 901, 200, 0, 1))),
            DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest("Requested in lines should be equal to requested quantity")));
  }

  private Arguments shouldModifyRequestPreselectedThrowWhenRequestedQuantityIsZero() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 0, 1)),
            List.of(commitment(201, 301, 901, 200, 1)),
            List.of(),
            modifyPreselectedPlan(101, 0, List.of(
                line(101, 201, 301, 901, 0, 0, 1))),
            DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest("Requested should be greater than zero")));
  }

  private Arguments shouldModifyRequestPreselectedThrowWhenAddBudgetCycleChangeCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 302, 901, 100, 0, 1)),
            List.of(budgetCycleChangeCommitment(201, 301, 901, 1000, 1),
                commitment(201, 302, 901, 500, 1)),
            List.of(),
            modifyPreselectedPlan(101, 1000, List.of(
                line(101, 201, 301, 901, 500, 0, null),
                line(101, 201, 302, 901, 500, 0, null))),
            DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest(
            "Insufficient or invalid commitment with orderId 00000000-0000-0000-0000-000000000201 and lineId 00000000-0000-0000-0000-000000000301. Only quantity 0.00 can be allocated")));
  }

  private Arguments shouldModifyRequestPreselectedThrowWhenOneLineIsBudgetCycleChangeCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 302, 901, 100, 0, 1)),
            List.of(budgetCycleChangeCommitment(201, 301, 901, 1000, 1),
                commitment(201, 302, 901, 1000, 1)),
            List.of(),
            modifyPreselectedPlan(101, 1000, List.of(
                line(101, 201, 301, 901, 500, 0, null),
                line(101, 201, 302, 901, 500, 0, null))),
            DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest(
            "Insufficient or invalid commitment with orderId 00000000-0000-0000-0000-000000000201 and lineId 00000000-0000-0000-0000-000000000301. Only quantity 0.00 can be allocated")));
  }

  private Arguments shouldModifyRequestPreselectedThrowWhenAddBudgetCycleChangeCommitmentAndInsufficientNormalCommitment() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 302, 901, 100, 0, 1)),
            List.of(budgetCycleChangeCommitment(201, 301, 901, 1000, 1),
                commitment(201, 302, 901, 450, 1)),
            List.of(),
            modifyPreselectedPlan(101, 1000, List.of(
                line(101, 201, 301, 901, 500, 0, null),
                line(101, 201, 302, 901, 500, 0, null))),
            DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest(
            "Insufficient or invalid commitment with orderId 00000000-0000-0000-0000-000000000201 and lineId 00000000-0000-0000-0000-000000000301. Only quantity 0.00 can be allocated")));
  }

  private Arguments shouldModifyRequestPreselectedThrowWhenRequestedQuantityIsDecreasedOnDistributedLine() {
    return Arguments.of(
        CurrentMethodName.get(),
        new PreselectedDistributionNominatedPlanner(
            List.of(request(101, 100, 1)),
            List.of(line(101, 201, 301, 901, 100, 3, 1)),
            List.of(commitment(201, 301, 901, 200, 1)),
            List.of(),
            modifyPreselectedPlan(101, 50, List.of(
                line(101, 201, 301, 901, 50, 0, 1))),
            DistributionNominatedPlannerType.DEFAULT),
        new ErrorException(new BadRequest("Cannot decrease requested quantity of a distributed line")));
  }

  private static Preselected.Create createPreselectedPlan(final int id, final Integer requested, final List<Line> lines) {
    return new Preselected.Create(new Id(UuidMother.fromInteger(id)), new RequestedQuantity(BigDecimal.valueOf(requested)), lines);
  }

  private static Preselected.Modify modifyPreselectedPlan(final int id, final Integer requested, final List<Line> lines) {
    return new Preselected.Modify(new Id(UuidMother.fromInteger(id)), new RequestedQuantity(BigDecimal.valueOf(requested)), lines);
  }

  private static Commitment commitment(final Integer orderId, final Integer lineId, final Integer supplierId, final Integer referenceId,
      final Integer targetReferenceId,
      final MaterialCommitmentUseStatus status,
      final double quantity, final Integer expectedDay,
      final MaterialCommitmentUseBudgetCycleChangeRequestedAt budgetCycleChangeRequestedDate) {
    return new Commitment(
        new CommitmentOrder(
            new CommitmentOrder.Id(UuidMother.fromInteger(orderId)),
            new CommitmentOrder.LineId(UuidMother.fromInteger(lineId)),
            new CommitmentOrder.SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId)))),
        new ReferenceId(UuidMother.fromInteger(referenceId)),
        new ReferenceId(UuidMother.fromInteger(targetReferenceId)),
        status,
        BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_UP),
        budgetCycleChangeRequestedDate, null,
        OffsetDateTimeMother.fromInteger(expectedDay));
  }

  private static Commitment commitment(final Integer orderId, final Integer lineId, final Integer supplierId, final double quantity,
      final Integer expectedDay) {
    final var commitmentStatus = MaterialCommitmentUseStatus.of(MaterialCommitmentUseStatusEnum.OPEN);
    return commitment(orderId, lineId, supplierId, 401, 401, commitmentStatus, quantity, expectedDay, null);
  }

  private static Commitment budgetCycleChangeCommitment(final Integer orderId, final Integer lineId, final Integer supplierId,
      final double quantity,
      final Integer expectedDay) {
    final var commitmentStatus = MaterialCommitmentUseStatus.of(RandomValue.randomEnum(MaterialCommitmentUseStatusEnum.class));
    return commitment(orderId, lineId, supplierId, 401, 401, commitmentStatus, quantity, expectedDay,
        new MaterialCommitmentUseBudgetCycleChangeRequestedAt(OffsetDateTime.now()));
  }

  private static Request request(final Integer id, final double quantity, final Integer creationDay) {
    return new Request(new Id(UuidMother.fromInteger(id)), BigDecimal.valueOf(quantity).setScale(2, RoundingMode.HALF_UP),
        OffsetDateTimeMother.fromInteger(creationDay));
  }

  private static Line line(final Integer rootId, final Integer orderId, final Integer lineId, final Integer supplierId,
      final Integer referenceId,
      final Integer targetReferenceId, final double requested, final double distributed, final Integer creationDay) {
    return new Line(
        new Id(UuidMother.fromInteger(rootId)),
        new CommitmentOrder(
            new CommitmentOrder.Id(UuidMother.fromInteger(orderId)),
            new CommitmentOrder.LineId(UuidMother.fromInteger(lineId)),
            new CommitmentOrder.SupplierId(UrnConstantsEnum.PRODUCT_SUPPLIER.prefixWithUrn(UuidMother.fromInteger(supplierId)))),
        new ReferenceId(UuidMother.fromInteger(referenceId)),
        new ReferenceId(UuidMother.fromInteger(targetReferenceId)),
        BigDecimal.valueOf(requested).setScale(2, RoundingMode.HALF_UP),
        BigDecimal.valueOf(distributed).setScale(2, RoundingMode.HALF_UP),
        Optional.ofNullable(creationDay).map(OffsetDateTimeMother::fromInteger));
  }

  private static Line line(final Integer rootId, final Integer orderId, final Integer lineId, final Integer supplierId,
      final double requested,
      final double distributed,
      final Integer creationDay) {
    return line(rootId, orderId, lineId, supplierId, 401, 401, requested, distributed, creationDay);
  }

  @Test
  void should_modify_default_DN_ignoring_budget_cycle_change_commitments() {
    final var newDNId = UUID.randomUUID();
    final var sharedRawMaterial = new SharedRawMaterialNominated(UUID.randomUUID(), UUID.randomUUID(), UUID.randomUUID().toString());
    final var defaultCommitment = MaterialCommitmentUseMother.with(
        UUID.randomUUID(),
        UUID.randomUUID(),
        sharedRawMaterial.referenceId().value(), sharedRawMaterial.useId().value(),
        UUID.fromString(sharedRawMaterial.budgetCycle().value()),
        BigDecimal.valueOf(20000));

    final CommitmentOrder commitmentOrder = new CommitmentOrder(
        new CommitmentOrder.Id(UUID.fromString(defaultCommitment.getOrderLine().orderId().value())),
        new LineId(UUID.fromString(defaultCommitment.getOrderLine().orderLineId().value())),
        new SupplierId(defaultCommitment.getServiceLocalizationId().value()));
    final var budgetCycleChangeDistributionNominatedLine =
        DistributionNominatedLineMother.created()
            .commitmentOrder(
                commitmentOrder)
            .alternativeReference(null)
            .requestedQuantity(new DistributionNominatedLine.RequestedQuantity(BigDecimal.ZERO)).build();
    final var budgetCycleChangeDistributionNominated = DistributionNominatedMother
        .pendingWithLines(List.of(budgetCycleChangeDistributionNominatedLine))
        .referenceId(sharedRawMaterial.referenceId())
        .useId(sharedRawMaterial.useId())
        .budgetCycle(sharedRawMaterial.budgetCycle())
        .requestedQuantity(
            new com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.RequestedQuantity(BigDecimal.valueOf(50)))
        .budgetCycleChangePendingQuantity(new BudgetCycleChangePendingQuantity(BigDecimal.valueOf(50)))
        .build();

    final var distributions = List.of(budgetCycleChangeDistributionNominated);
    final var adjustedCommitments = List.of(new CommitmentAdjusted(defaultCommitment, BigDecimal.valueOf(500)));

    final var createdLine = new Line(new Id(newDNId),
        commitmentOrder,
        sharedRawMaterial.referenceId(), sharedRawMaterial.referenceId(),
        NumericUtils.roundUpScale2(BigDecimal.valueOf(200)), NumericUtils.roundUpScale2(BigDecimal.ZERO),
        Optional.empty());

    final var keptLine = new Line(
        new Id(budgetCycleChangeDistributionNominated.getId()
            .value()),
        budgetCycleChangeDistributionNominatedLine.commitmentOrder(),
        budgetCycleChangeDistributionNominated.referenceId(), budgetCycleChangeDistributionNominated.referenceId(),
        budgetCycleChangeDistributionNominatedLine.requestedQuantity().value(),
        budgetCycleChangeDistributionNominatedLine.distributedQuantity().value(),
        Optional.of(budgetCycleChangeDistributionNominatedLine.audit().createdAt()));

    final var planner =
        DistributionNominatedPlanner.fromDistributions(sharedRawMaterial, distributions, adjustedCommitments,
            new Create(
                new Id(newDNId),
                new RequestedQuantity(BigDecimal.valueOf(200)),
                List.of(createdLine)),
            DistributionNominatedPlannerType.DEFAULT);
    final var adjustedLines = planner.executePlan();

    assertThat(adjustedLines).containsExactlyInAnyOrderElementsOf(List.of(
        created(createdLine),
        kept(keptLine)));
  }
}
