package com.inditex.icdmdemg.domain.use.entity.ranker;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;
import java.util.stream.Stream;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class PurchasePurposeEqualsRankerTest {

  @ParameterizedTest
  @MethodSource("provideParamsWithCoincidences")
  void should_return_true_when_condition_is_equals(final List<String> listParamsValues,
      final List<String> listConditionValues, final int expectedCoincidences) {

    final var result = PurchasePurposeEqualsRanker.evaluate(listParamsValues,
        listConditionValues);
    assertThat(result.isComplied()).isTrue();
    assertThat(result.coincidences()).isEqualTo(expectedCoincidences);
  }

  public static Stream<Arguments> provideParamsWithCoincidences() {
    return Stream.of(
        Arguments.of(List.of("T1", "T2"), List.of("T1", "T2"), 2),
        Arguments.of(List.of("T1", "T2"), List.of("T2", "T1"), 2),
        Arguments.of(List.of("T1"), List.of("T1"), 1));
  }

  @ParameterizedTest
  @MethodSource("provideParamsWithNoCoincidences")
  void should_return_false_and_no_coincidences_when_condition_is_equals(final List<String> listParamsValues,
      final List<String> listConditionValues) {

    final var result = PurchasePurposeEqualsRanker.evaluate(listParamsValues,
        listConditionValues);
    assertThat(result.isComplied()).isFalse();
    assertThat(result.coincidences()).isZero();
  }

  public static Stream<Arguments> provideParamsWithNoCoincidences() {
    return Stream.of(Arguments.of(List.of("T1", "T2"), List.of("T1", "T3")),
        Arguments.of(List.of("T1", "T2"), List.of("T4", "T5")),
        Arguments.of(List.of("T1"), List.of("T2")));
  }

}
