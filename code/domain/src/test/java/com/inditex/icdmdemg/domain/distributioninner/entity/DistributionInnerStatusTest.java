package com.inditex.icdmdemg.domain.distributioninner.entity;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo.Status;

import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

@TestInstance(Lifecycle.PER_CLASS)
class DistributionInnerStatusTest {

  @ParameterizedTest
  @MethodSource("provideStatusCasesForSendToDistributionCalculation")
  void should_calculate_status(final ProductOrderStatusInfo productOrderStatusInfo,
      final boolean sendToDistribution,
      final DistributionInnerStatus expectedStatus) {
    final var actual =
        DistributionInnerStatus.of(productOrderStatusInfo, sendToDistribution);
    assertThat(actual).isEqualTo(expectedStatus);
  }

  public Stream<Arguments> provideStatusCasesForSendToDistributionCalculation() {
    return Stream.of(
        Arguments.of(new ProductOrderStatusInfo(Status.CLOSED, false), false, DistributionInnerStatus.CLOSED),
        Arguments.of(new ProductOrderStatusInfo(Status.CLOSED, true), false, DistributionInnerStatus.CLOSED),
        Arguments.of(new ProductOrderStatusInfo(Status.CLOSED, true), true, DistributionInnerStatus.CLOSED),
        Arguments.of(new ProductOrderStatusInfo(Status.CANCELLED, false), false, DistributionInnerStatus.CANCELED),
        Arguments.of(new ProductOrderStatusInfo(Status.CANCELLED, true), false, DistributionInnerStatus.CANCELED),
        Arguments.of(new ProductOrderStatusInfo(Status.CANCELLED, true), true, DistributionInnerStatus.CANCELED),
        Arguments.of(new ProductOrderStatusInfo(Status.DRAFT, false), false, DistributionInnerStatus.NON_DISTRIBUTABLE),
        Arguments.of(new ProductOrderStatusInfo(Status.DRAFT, false), true, DistributionInnerStatus.PENDING),
        Arguments.of(new ProductOrderStatusInfo(Status.DRAFT, true), true, DistributionInnerStatus.PENDING),
        Arguments.of(new ProductOrderStatusInfo(Status.FORMALIZED, false), false, DistributionInnerStatus.NON_DISTRIBUTABLE),
        Arguments.of(new ProductOrderStatusInfo(Status.FORMALIZED, false), true, DistributionInnerStatus.PENDING),
        Arguments.of(new ProductOrderStatusInfo(Status.FORMALIZED, true), true, DistributionInnerStatus.PENDING),
        Arguments.of(new ProductOrderStatusInfo(Status.DELETED, false), false, DistributionInnerStatus.CANCELED),
        Arguments.of(new ProductOrderStatusInfo(Status.DELETED, false), true, DistributionInnerStatus.CANCELED));
  }

  @ParameterizedTest
  @MethodSource("provideStatusCasesForOrderCalculation")
  void should_calculate_status(final DistributionInnerStatus currentStatus, final ProductOrderStatusInfo productOrderStatusInfo,
      final DistributionInnerStatus expectedStatus) {
    final var actual =
        DistributionInnerStatus.of(productOrderStatusInfo, currentStatus);
    assertThat(actual).isEqualTo(expectedStatus);
  }

  public Stream<Arguments> provideStatusCasesForOrderCalculation() {
    return Stream.of(
        Arguments.of(DistributionInnerStatus.CLOSED, new ProductOrderStatusInfo(Status.CLOSED, false), DistributionInnerStatus.CLOSED),
        Arguments.of(DistributionInnerStatus.CANCELED, new ProductOrderStatusInfo(Status.CANCELLED, false),
            DistributionInnerStatus.CANCELED),
        Arguments.of(DistributionInnerStatus.PENDING, new ProductOrderStatusInfo(Status.DELETED, false), DistributionInnerStatus.PENDING),
        Arguments.of(DistributionInnerStatus.PENDING, new ProductOrderStatusInfo(Status.CANCELLED, false),
            DistributionInnerStatus.PENDING),
        Arguments.of(DistributionInnerStatus.NON_DISTRIBUTABLE, new ProductOrderStatusInfo(Status.DELETED, false),
            DistributionInnerStatus.NON_DISTRIBUTABLE),
        Arguments.of(DistributionInnerStatus.NON_DISTRIBUTABLE, new ProductOrderStatusInfo(Status.CANCELLED, false),
            DistributionInnerStatus.NON_DISTRIBUTABLE),
        Arguments.of(DistributionInnerStatus.IN_PROGRESS, new ProductOrderStatusInfo(Status.DELETED, false),
            DistributionInnerStatus.CLOSED),
        Arguments.of(DistributionInnerStatus.IN_PROGRESS, new ProductOrderStatusInfo(Status.CANCELLED, false),
            DistributionInnerStatus.CLOSED),
        Arguments.of(DistributionInnerStatus.SENT, new ProductOrderStatusInfo(Status.DELETED, false),
            DistributionInnerStatus.CLOSED),
        Arguments.of(DistributionInnerStatus.SENT, new ProductOrderStatusInfo(Status.CANCELLED, false),
            DistributionInnerStatus.CLOSED),
        Arguments.of(DistributionInnerStatus.CANCELED, new ProductOrderStatusInfo(Status.DELETED, false),
            DistributionInnerStatus.CANCELED),
        Arguments.of(DistributionInnerStatus.IN_PROGRESS, new ProductOrderStatusInfo(Status.DRAFT, false),
            DistributionInnerStatus.IN_PROGRESS),
        Arguments.of(DistributionInnerStatus.IN_PROGRESS, new ProductOrderStatusInfo(Status.FORMALIZED, true),
            DistributionInnerStatus.IN_PROGRESS),
        Arguments.of(DistributionInnerStatus.IN_PROGRESS, new ProductOrderStatusInfo(Status.FORMALIZED, false),
            DistributionInnerStatus.IN_PROGRESS),
        Arguments.of(DistributionInnerStatus.PENDING, new ProductOrderStatusInfo(Status.DRAFT, false),
            DistributionInnerStatus.PENDING),
        Arguments.of(DistributionInnerStatus.PENDING, new ProductOrderStatusInfo(Status.FORMALIZED, true),
            DistributionInnerStatus.PENDING),
        Arguments.of(DistributionInnerStatus.PENDING, new ProductOrderStatusInfo(Status.FORMALIZED, false),
            DistributionInnerStatus.PENDING),
        Arguments.of(DistributionInnerStatus.NON_DISTRIBUTABLE, new ProductOrderStatusInfo(Status.DRAFT, false),
            DistributionInnerStatus.NON_DISTRIBUTABLE),
        Arguments.of(DistributionInnerStatus.NON_DISTRIBUTABLE, new ProductOrderStatusInfo(Status.FORMALIZED, true),
            DistributionInnerStatus.NON_DISTRIBUTABLE),
        Arguments.of(DistributionInnerStatus.NON_DISTRIBUTABLE, new ProductOrderStatusInfo(Status.FORMALIZED, false),
            DistributionInnerStatus.NON_DISTRIBUTABLE));
  }

  @ParameterizedTest
  @MethodSource("provideStatusCasesForDistributionCalculation")
  void should_calculate_status(final DistributionInnerStatus currentStatus, final boolean hasDistributionStarted,
      final boolean areAllLinesSent,
      final DistributionInnerStatus expectedStatus) {
    final var actual =
        DistributionInnerStatus.of(currentStatus, hasDistributionStarted, areAllLinesSent);
    assertThat(actual).isEqualTo(expectedStatus);
  }

  public Stream<Arguments> provideStatusCasesForDistributionCalculation() {
    return Stream.of(
        Arguments.of(DistributionInnerStatus.CLOSED, false, false, DistributionInnerStatus.CLOSED),
        Arguments.of(DistributionInnerStatus.CLOSED, true, false, DistributionInnerStatus.CLOSED),
        Arguments.of(DistributionInnerStatus.CANCELED, false, false, DistributionInnerStatus.CANCELED),
        Arguments.of(DistributionInnerStatus.CANCELED, true, false, DistributionInnerStatus.CANCELED),
        Arguments.of(DistributionInnerStatus.IN_PROGRESS, false, false, DistributionInnerStatus.IN_PROGRESS),
        Arguments.of(DistributionInnerStatus.IN_PROGRESS, true, false, DistributionInnerStatus.IN_PROGRESS),
        Arguments.of(DistributionInnerStatus.PENDING, false, false, DistributionInnerStatus.PENDING),
        Arguments.of(DistributionInnerStatus.PENDING, true, false, DistributionInnerStatus.IN_PROGRESS),
        Arguments.of(DistributionInnerStatus.NON_DISTRIBUTABLE, false, false, DistributionInnerStatus.NON_DISTRIBUTABLE),
        Arguments.of(DistributionInnerStatus.NON_DISTRIBUTABLE, true, false, DistributionInnerStatus.IN_PROGRESS),
        Arguments.of(DistributionInnerStatus.IN_PROGRESS, false, true, DistributionInnerStatus.SENT),
        Arguments.of(DistributionInnerStatus.CANCELED, true, true, DistributionInnerStatus.CANCELED),
        Arguments.of(DistributionInnerStatus.NON_DISTRIBUTABLE, false, true, DistributionInnerStatus.NON_DISTRIBUTABLE),
        Arguments.of(DistributionInnerStatus.PENDING, false, true, DistributionInnerStatus.PENDING),
        Arguments.of(DistributionInnerStatus.CLOSED, false, true, DistributionInnerStatus.CLOSED),
        Arguments.of(DistributionInnerStatus.SENT, true, true, DistributionInnerStatus.SENT),
        Arguments.of(DistributionInnerStatus.SENT, true, false, DistributionInnerStatus.IN_PROGRESS));
  }
}
