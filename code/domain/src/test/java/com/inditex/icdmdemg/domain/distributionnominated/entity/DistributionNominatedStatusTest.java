package com.inditex.icdmdemg.domain.distributionnominated.entity;

import static org.assertj.core.api.Assertions.assertThat;

import java.math.BigDecimal;
import java.util.stream.Stream;

import com.inditex.icdmdemg.domain.distributionnominated.DistributionNominated.DistributedQuantity;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo;
import com.inditex.icdmdemg.domain.order.entity.ProductOrderStatusInfo.Status;
import com.inditex.icdmdemg.shared.utils.NumericUtils;

import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestInstance.Lifecycle;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

@TestInstance(Lifecycle.PER_CLASS)
class DistributionNominatedStatusTest {

  @ParameterizedTest
  @MethodSource("provideStatusCases")
  void should_calculate_status(final ProductOrderStatusInfo productOrderStatusInfo, final double distributedQuantity,
      final DistributionNominatedStatus expectedStatus) {
    final var actual =
        DistributionNominatedStatus.of(productOrderStatusInfo,
            new DistributedQuantity(NumericUtils.roundUpScale2(BigDecimal.valueOf(distributedQuantity))));
    assertThat(actual).isEqualTo(expectedStatus);
  }

  public Stream<Arguments> provideStatusCases() {
    return Stream.of(
        Arguments.of(new ProductOrderStatusInfo(Status.CLOSED, false), 0.0, DistributionNominatedStatus.CLOSED),
        Arguments.of(new ProductOrderStatusInfo(Status.CLOSED, false), 10, DistributionNominatedStatus.CLOSED),
        Arguments.of(new ProductOrderStatusInfo(Status.CLOSED, true), 0.0, DistributionNominatedStatus.CLOSED),
        Arguments.of(new ProductOrderStatusInfo(Status.CLOSED, true), 10, DistributionNominatedStatus.CLOSED),
        Arguments.of(new ProductOrderStatusInfo(Status.DRAFT, false), 0.0, DistributionNominatedStatus.NON_DISTRIBUTABLE),
        Arguments.of(new ProductOrderStatusInfo(Status.DRAFT, false), 10, DistributionNominatedStatus.IN_PROGRESS),
        Arguments.of(new ProductOrderStatusInfo(Status.DRAFT, true), 0.0, DistributionNominatedStatus.PENDING),
        Arguments.of(new ProductOrderStatusInfo(Status.DRAFT, true), 10, DistributionNominatedStatus.IN_PROGRESS),
        Arguments.of(new ProductOrderStatusInfo(Status.FORMALIZED, false), 0.0, DistributionNominatedStatus.NON_DISTRIBUTABLE),
        Arguments.of(new ProductOrderStatusInfo(Status.FORMALIZED, false), 10, DistributionNominatedStatus.IN_PROGRESS),
        Arguments.of(new ProductOrderStatusInfo(Status.FORMALIZED, true), 0.0, DistributionNominatedStatus.PENDING),
        Arguments.of(new ProductOrderStatusInfo(Status.FORMALIZED, true), 10, DistributionNominatedStatus.IN_PROGRESS));
  }
}
