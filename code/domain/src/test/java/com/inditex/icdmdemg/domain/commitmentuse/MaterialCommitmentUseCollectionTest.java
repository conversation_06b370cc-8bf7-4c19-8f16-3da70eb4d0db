package com.inditex.icdmdemg.domain.commitmentuse;

import static com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus.MaterialCommitmentUseStatusEnum.CLOSED;
import static com.inditex.icdmdemg.domain.commitmentuse.entity.MaterialCommitmentUseStatus.MaterialCommitmentUseStatusEnum.OPEN;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;

import com.inditex.icdmdemg.domain.commitmentuse.mother.MaterialCommitmentUseMother;

import org.junit.jupiter.api.Test;

class MaterialCommitmentUseCollectionTest {

  @Test
  void material_commitment_collection_empty_should_return_true() {
    final var materialCommitmentUse = MaterialCommitmentUseMother.with(
        301, BigDecimal.valueOf(5000), OffsetDateTime.parse("2023-12-20T15:11:20Z"), 501, 601, 701,
        CLOSED, 999, 888);
    final var materialCommitmentUseCollection = new MaterialCommitmentUseCollection(List.of(materialCommitmentUse));
    assertFalse(materialCommitmentUseCollection.isEmpty());
  }

  @Test
  void material_commitment_collection_not_empty_should_return_false() {
    final var materialCommitmentUseCollection = MaterialCommitmentUseCollection.empty();
    assertTrue(materialCommitmentUseCollection.isEmpty());
  }

  @Test
  void material_commitment_collection_closed_should_return_list() {
    final var materialCommitmentUse = MaterialCommitmentUseMother.with(
        301, BigDecimal.valueOf(5000), OffsetDateTime.parse("2023-12-20T15:11:20Z"), 501, 601, 701,
        CLOSED, 999, 888);
    final var materialCommitmentUse2 = MaterialCommitmentUseMother.with(
        301, BigDecimal.valueOf(5000), OffsetDateTime.parse("2023-12-20T15:11:20Z"), 501, 601, 701,
        OPEN, 999, 888);
    final var materialCommitmentUseCollection = new MaterialCommitmentUseCollection(List.of(materialCommitmentUse, materialCommitmentUse2));
    assertThat(materialCommitmentUseCollection.closed()).hasSize(1);
  }

  @Test
  void material_commitment_collection_in_progress_should_return_list() {
    final var materialCommitmentUse = MaterialCommitmentUseMother.with(
        301, BigDecimal.valueOf(5000), OffsetDateTime.parse("2023-12-20T15:11:20Z"), 501, 601, 701,
        CLOSED, 999, 888);
    final var materialCommitmentUse2 = MaterialCommitmentUseMother.with(
        301, BigDecimal.valueOf(5000), OffsetDateTime.parse("2023-12-20T15:11:20Z"), 501, 601, 701,
        OPEN, 999, 888);
    final var materialCommitmentUseCollection = new MaterialCommitmentUseCollection(List.of(materialCommitmentUse, materialCommitmentUse2));
    assertThat(materialCommitmentUseCollection.open()).hasSize(1);
  }

  @Test
  void material_commitment_collection_assignable_should_return_list_not_null() {
    final var materialCommitmentUse = MaterialCommitmentUseMother.with(
        301, BigDecimal.valueOf(5000), OffsetDateTime.parse("2023-12-20T15:11:20Z"), 501, 601, 701,
        CLOSED, 999, 888);
    final var materialCommitmentUse2 = MaterialCommitmentUseMother.with(
        301, BigDecimal.valueOf(5000), OffsetDateTime.parse("2023-12-20T15:11:20Z"), 501, 601, 701,
        null, 999, 888);
    final var materialCommitmentUseCollection =
        new MaterialCommitmentUseCollection(List.of(materialCommitmentUse, materialCommitmentUse2));
    assertThat(materialCommitmentUseCollection.assignable()).hasSize(1);
  }
}
