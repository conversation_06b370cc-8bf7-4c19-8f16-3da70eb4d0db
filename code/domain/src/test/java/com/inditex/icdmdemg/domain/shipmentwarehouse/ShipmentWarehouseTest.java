package com.inditex.icdmdemg.domain.shipmentwarehouse;

import static org.assertj.core.api.Assertions.assertThat;

import java.time.OffsetDateTime;

import com.inditex.icdmdemg.domain.shipmentwarehouse.entity.ShipmentWarehouseTimestamp;

import org.junit.jupiter.api.Test;

class ShipmentWarehouseTest {

  @Test
  void should_not_change_end_date_when_is_already_established() {

    final var endDate = OffsetDateTime.now();
    final var newEndDateIntent = OffsetDateTime.now().plusDays(2);
    final var shipmentWarehouse = ShipmentWarehouse.builder()
        .endDate(new ShipmentWarehouseTimestamp(endDate)).build();

    shipmentWarehouse.endShipment(
        new ShipmentWarehouseTimestamp(newEndDateIntent),
        new ShipmentWarehouseTimestamp(OffsetDateTime.now().plusDays(2)));

    assertThat(shipmentWarehouse.getEndDate().value()).isEqualTo(endDate);
  }
}
