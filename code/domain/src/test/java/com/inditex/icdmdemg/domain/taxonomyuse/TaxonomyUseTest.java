package com.inditex.icdmdemg.domain.taxonomyuse;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;

import com.inditex.icdmdemg.domain.taxonomyuse.entity.TaxonomyCode;
import com.inditex.icdmdemg.domain.taxonomyuse.entity.TaxonomyPath;

import org.junit.jupiter.api.Test;

class TaxonomyUseTest {

  @Test
  void should_return_taxonomy_list() {
    final var taxonomy = new TaxonomyUse(new TaxonomyCode("ZIPPER"), new TaxonomyPath("/RAW_MATERIALS/TRIMMING/ZIPPER"));
    final var expected = List.of("RAW_MATERIALS", "TRIMMING", "ZIPPER");

    final var result = taxonomy.mapToTaxonomies();

    assertThat(result).containsExactlyInAnyOrderElementsOf(expected);
  }
}
