package com.inditex.icdmdemg.domain.use.entity.ranker;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.List;
import java.util.stream.Stream;

import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

class PurchasePurposeAllInRankerTest {
  @ParameterizedTest
  @MethodSource("provideParamsWithCoincidences")
  void should_return_true_when_condition_is_all_in(final List<String> listParamsValues,
      final List<String> listConditionValues, final int expectedCoincidences) {

    final var result = PurchasePurposeAllInRanker.evaluate(listParamsValues,
        listConditionValues);
    assertThat(result.isComplied()).isTrue();
    assertThat(result.coincidences()).isEqualTo(expectedCoincidences);
  }

  public static Stream<Arguments> provideParamsWithCoincidences() {
    return Stream.of(
        Arguments.of(List.of("a", "c"), List.of("a", "c"), 2),
        Arguments.of(List.of("a", "b", "c"), List.of("a", "c"), 2));
  }

  @ParameterizedTest
  @MethodSource("provideParamsWithNoCoincidences")
  void should_return_false_and_no_coincidences_when_condition_is_all_in(final List<String> listParamsValues,
      final List<String> listConditionValues) {

    final var result = PurchasePurposeAllInRanker.evaluate(listParamsValues,
        listConditionValues);
    assertThat(result.isComplied()).isFalse();
    assertThat(result.coincidences()).isZero();
  }

  public static Stream<Arguments> provideParamsWithNoCoincidences() {
    return Stream.of(Arguments.of(List.of("a"), List.of("a", "c")),
        Arguments.of(List.of("b"), List.of("a", "c")),
        Arguments.of(List.of("c"), List.of("a", "c")),
        Arguments.of(List.of("a", "b"), List.of("a", "c")));
  }

}
