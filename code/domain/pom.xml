<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.inditex.icdmdemg</groupId>
    <artifactId>icdmdebtmgmt</artifactId>
    <version>1.91.0-SNAPSHOT</version>
    <relativePath>..</relativePath>
  </parent>

  <artifactId>icdmdebtmgmt-domain</artifactId>
  <packaging>jar</packaging>

  <name>${project.groupId}:${project.artifactId}</name>
  <description>Domain module.</description>

  <dependencies>
    <dependency>
      <groupId>org.jspecify</groupId>
      <artifactId>jspecify</artifactId>
    </dependency>

    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.iopcmmntddd</groupId>
      <artifactId>lib-iopcmmntddd-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.inditex.icdmdemg</groupId>
      <artifactId>icdmdebtmgmt-shared</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-test-jmh</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.test</groupId>
      <artifactId>test-starter-instancio</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.inditex.amigafwk.common</groupId>
      <artifactId>common-test-jmh</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.data</groupId>
      <artifactId>spring-data-commons</artifactId>
    </dependency>
  </dependencies>

  <build>
    <finalName>${project.artifactId}-${project.version}</finalName>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <annotationProcessorPaths>
            <annotationProcessorPath>
              <groupId>org.openjdk.jmh</groupId>
              <artifactId>jmh-generator-annprocess</artifactId>
              <version>${jmh.version}</version>
            </annotationProcessorPath>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>test-jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <reporting />
</project>
