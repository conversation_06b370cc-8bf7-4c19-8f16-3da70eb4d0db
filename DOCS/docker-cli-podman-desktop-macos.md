# Configuración de Docker CLI con Podman Desktop en macOS

Esta guía te mostrará cómo instalar y configurar Docker CLI para trabajar con Podman Desktop en macOS, utilizando el socket expuesto de Podman.

## Requisitos previos

- macOS (cualquier versión compatible con Homebrew)
- Homebrew instalado en tu sistema
- Permisos de administrador

## Paso 1: Instalar Docker CLI con Homebrew

Primero, instalamos la CLI de Docker usando Homebrew:

```bash
# Actualizar Homebrew
brew update

# Instalar Docker CLI
brew install docker
```

**Verificar la instalación:**
```bash
docker --version
```

## Paso 2: Instalar Podman Desktop con Homebrew

Instalamos Podman Desktop usando Homebrew Cask:

```bash
# Instalar Podman Desktop
brew install --cask podman-desktop
```

**Verificar la instalación:**
```bash
# Verificar que Podman está instalado
podman --version
```

## Paso 3: Configurar Podman Desktop

1. **<PERSON><PERSON><PERSON> Desktop** desde Aplicaciones o usando Spotlight
2. **Inicializar la máquina de <PERSON>dman:**
   - Ve a la sección "Settings" o "Configuración"
   - Busca la opción para crear/inicializar una máquina de Podman
   - Crea una nueva máquina con la configuración por defecto

3. **Iniciar la máquina:**
   ```bash
   podman machine start
   ```

## Paso 4: Exponer el Socket de Podman

Para que Docker CLI pueda comunicarse con Podman, necesitamos exponer el socket de Podman:

```bash
# Detener la máquina si está corriendo
podman machine stop

# Recrear la máquina con el socket expuesto
podman machine rm podman-machine-default

# Crear nueva máquina con socket expuesto
podman machine init --now --rootful
```

**Alternativamente, si ya tienes una máquina configurada:**

```bash
# Configurar el socket en la máquina existente
podman system connection default podman-machine-default-root
```

## Paso 5: Configurar la Variable de Entorno DOCKER_HOST

Ahora configuramos la variable de entorno para que Docker CLI use el socket de Podman:

### Opción A: Configuración temporal (sesión actual)

```bash
# Obtener la ruta del socket de Podman
export DOCKER_HOST="unix://$(podman machine inspect --format '{{.ConnectionInfo.PodmanSocket.Path}}')"
```

### Opción B: Configuración permanente

Agrega la configuración a tu archivo de perfil de shell:

**Para Bash (~/.bash_profile o ~/.bashrc):**
```bash
echo 'export DOCKER_HOST="unix://$(podman machine inspect --format '\''{{.ConnectionInfo.PodmanSocket.Path}}'\'')"' >> ~/.bash_profile
source ~/.bash_profile
```

**Para Zsh (~/.zshrc):**
```bash
echo 'export DOCKER_HOST="unix://$(podman machine inspect --format '\''{{.ConnectionInfo.PodmanSocket.Path}}'\'')"' >> ~/.zshrc
source ~/.zshrc
```

**Para Fish (~/.config/fish/config.fish):**
```bash
echo 'set -x DOCKER_HOST "unix://"(podman machine inspect --format '\''{{.ConnectionInfo.PodmanSocket.Path}}'\'')' >> ~/.config/fish/config.fish
source ~/.config/fish/config.fish
```

## Paso 6: Verificar la Configuración

Verifica que Docker CLI puede comunicarse con Podman:

```bash
# Verificar que Docker CLI funciona con Podman
docker version

# Verificar información del sistema
docker info

# Probar con un contenedor simple
docker run hello-world
```

## Paso 7: Configuración Adicional en Podman Desktop

1. **Abrir Podman Desktop**
2. **Ir a Settings/Configuración**
3. **En la sección "Resources" o "Recursos":**
   - Ajustar CPU y memoria según tus necesidades
   - Configurar volúmenes compartidos si es necesario

4. **En la sección "Docker Compatibility":**
   - Asegúrate de que esté habilitada la compatibilidad con Docker
   - Verifica que el socket esté expuesto

## Comandos Útiles

### Gestión de la máquina Podman:
```bash
# Ver estado de las máquinas
podman machine list

# Iniciar máquina
podman machine start

# Detener máquina
podman machine stop

# Ver información de la máquina
podman machine info
```

### Verificación de conectividad:
```bash
# Ver conexiones disponibles
podman system connection list

# Probar conexión
podman system connection default

# Ver información del socket
podman machine inspect --format '{{.ConnectionInfo.PodmanSocket.Path}}'
```

## Solución de Problemas

### Error: "Cannot connect to the Docker daemon"

1. **Verificar que Podman está corriendo:**
   ```bash
   podman machine list
   ```

2. **Verificar la variable DOCKER_HOST:**
   ```bash
   echo $DOCKER_HOST
   ```

3. **Reiniciar la máquina de Podman:**
   ```bash
   podman machine restart
   ```

### Error: "Permission denied"

1. **Verificar permisos del socket:**
   ```bash
   ls -la $(podman machine inspect --format '{{.ConnectionInfo.PodmanSocket.Path}}')
   ```

2. **Usar conexión rootful:**
   ```bash
   podman machine set --rootful
   ```

### Rendimiento lento

1. **Aumentar recursos de la máquina:**
   ```bash
   podman machine stop
   podman machine set --cpus 4 --memory 4096
   podman machine start
   ```

## Ventajas de esta Configuración

- ✅ **Compatibilidad completa** con comandos Docker
- ✅ **Mejor seguridad** (Podman no requiere daemon root)
- ✅ **Interfaz gráfica** con Podman Desktop
- ✅ **Gestión simplificada** de contenedores e imágenes
- ✅ **Soporte nativo** para Kubernetes pods

## Conclusión

Con esta configuración, tienes lo mejor de ambos mundos: la familiaridad de Docker CLI con la seguridad y características avanzadas de Podman. Puedes usar todos tus comandos Docker habituales mientras aprovechas las ventajas de Podman en el backend.

---

**Nota:** Esta configuración ha sido probada en macOS con las versiones más recientes de Docker CLI y Podman Desktop disponibles a través de Homebrew.
