# Configuración de Docker CLI con Podman Desktop en macOS (Sin Permisos de Admin)

Esta guía te mostrará cómo instalar y configurar Docker CLI para trabajar con Podman Desktop en macOS **sin permisos de administrador**, utilizando instalaciones en tu carpeta personal y el socket expuesto de Podman.

## Requisitos previos

- macOS (cualquier versión compatible con Homebrew)
- Homebrew instalado en tu sistema (instalación personal)
- Carpeta `~/Applications` creada en tu directorio personal
- **NO se requieren permisos de administrador**

## Configuración inicial para usuarios sin admin

### Crear estructura de directorios personal

```bash
# Crear carpeta Applications en tu directorio personal (si no existe)
mkdir -p ~/Applications

# Crear carpeta para binarios locales
mkdir -p ~/bin

# Agregar ~/bin al PATH (temporal)
export PATH="$HOME/bin:$PATH"
```

## Paso 1: Instalar Docker CLI con Homebrew (instalación personal)

```bash
# Actualizar Homebrew
brew update

# Instalar Docker CLI en tu instalación personal de Homebrew
brew install docker
```

**Verificar la instalación:**
```bash
docker --version
```

**Si docker no se encuentra en el PATH:**
```bash
# Encontrar la ubicación de docker
find $(brew --prefix) -name docker -type f 2>/dev/null

# Crear enlace simbólico en ~/bin
ln -sf $(brew --prefix)/bin/docker ~/bin/docker

# Verificar nuevamente
docker --version
```

## Paso 2: Instalar Podman Desktop (instalación personal)

### Opción A: Con Homebrew Cask (recomendado)

```bash
# Instalar Podman Desktop en ~/Applications
brew install --cask --appdir=~/Applications podman-desktop
```

### Opción B: Descarga manual (si Homebrew falla)

```bash
# Descargar la última versión desde GitHub
curl -L -o ~/Downloads/podman-desktop.dmg \
  "https://github.com/containers/podman-desktop/releases/latest/download/podman-desktop-*.dmg"

# Montar el DMG y copiar a ~/Applications
# (Esto requiere hacerlo manualmente desde Finder)
```

**Verificar la instalación:**
```bash
# Verificar que Podman está disponible
~/Applications/Podman\ Desktop.app/Contents/Resources/bin/podman --version

# Crear enlace simbólico para facilitar el uso
ln -sf ~/Applications/Podman\ Desktop.app/Contents/Resources/bin/podman ~/bin/podman

# Verificar nuevamente
podman --version
```

## Paso 3: Configurar variables de entorno permanentes

Agrega las rutas necesarias a tu archivo de perfil de shell:

### Para Zsh (~/.zshrc):
```bash
# Agregar al final del archivo ~/.zshrc
cat >> ~/.zshrc << 'EOF'

# Configuración personal para Docker/Podman
export PATH="$HOME/bin:$PATH"
export PATH="$HOME/Applications/Podman Desktop.app/Contents/Resources/bin:$PATH"

# Función para configurar DOCKER_HOST automáticamente
setup_docker_podman() {
    if command -v podman >/dev/null 2>&1; then
        export DOCKER_HOST="unix://$(podman machine inspect --format '{{.ConnectionInfo.PodmanSocket.Path}}' 2>/dev/null || echo '')"
    fi
}

# Ejecutar la configuración al iniciar la shell
setup_docker_podman
EOF

# Recargar la configuración
source ~/.zshrc
```

### Para Bash (~/.bash_profile):
```bash
# Agregar al final del archivo ~/.bash_profile
cat >> ~/.bash_profile << 'EOF'

# Configuración personal para Docker/Podman
export PATH="$HOME/bin:$PATH"
export PATH="$HOME/Applications/Podman Desktop.app/Contents/Resources/bin:$PATH"

# Función para configurar DOCKER_HOST automáticamente
setup_docker_podman() {
    if command -v podman >/dev/null 2>&1; then
        export DOCKER_HOST="unix://$(podman machine inspect --format '{{.ConnectionInfo.PodmanSocket.Path}}' 2>/dev/null || echo '')"
    fi
}

# Ejecutar la configuración al iniciar la shell
setup_docker_podman
EOF

# Recargar la configuración
source ~/.bash_profile
```

## Paso 4: Configurar Podman Desktop

1. **Abrir Podman Desktop** desde `~/Applications/Podman Desktop.app`
2. **Configuración inicial:**
   - La aplicación se ejecutará desde tu carpeta personal
   - Acepta los permisos necesarios cuando se soliciten
   - Ve a "Settings" o "Configuración"

3. **Crear máquina de Podman:**
   ```bash
   # Inicializar máquina con configuración personal
   podman machine init --now --rootful --cpus 2 --memory 2048
   ```

## Paso 5: Exponer el Socket de Podman

```bash
# Verificar que la máquina está corriendo
podman machine list

# Si no está corriendo, iniciarla
podman machine start

# Verificar la configuración del socket
podman machine inspect --format '{{.ConnectionInfo.PodmanSocket.Path}}'
```

## Paso 6: Configurar DOCKER_HOST

```bash
# Configurar DOCKER_HOST para la sesión actual
export DOCKER_HOST="unix://$(podman machine inspect --format '{{.ConnectionInfo.PodmanSocket.Path}}')"

# Verificar la configuración
echo $DOCKER_HOST
```

## Paso 7: Verificar la Configuración

```bash
# Verificar que Docker CLI funciona con Podman
docker version

# Verificar información del sistema
docker info

# Probar con un contenedor simple
docker run hello-world
```

## Comandos Útiles para Instalación Personal

### Gestión de aplicaciones personales:
```bash
# Listar aplicaciones en ~/Applications
ls -la ~/Applications/

# Verificar binarios personales
ls -la ~/bin/

# Ver PATH actual
echo $PATH | tr ':' '\n'
```

### Gestión de Podman:
```bash
# Ver estado de máquinas
podman machine list

# Ver información detallada
podman machine info

# Reiniciar máquina si hay problemas
podman machine restart
```

## Solución de Problemas Específicos

### Error: "Command not found"

1. **Verificar PATH:**
   ```bash
   echo $PATH | grep -E "(bin|Applications)"
   ```

2. **Recrear enlaces simbólicos:**
   ```bash
   ln -sf $(brew --prefix)/bin/docker ~/bin/docker
   ln -sf ~/Applications/Podman\ Desktop.app/Contents/Resources/bin/podman ~/bin/podman
   ```

### Error: "Permission denied" en ~/Applications

1. **Verificar permisos:**
   ```bash
   ls -la ~/Applications/
   chmod 755 ~/Applications/
   ```

### Podman Desktop no inicia

1. **Verificar la aplicación:**
   ```bash
   ls -la ~/Applications/Podman\ Desktop.app/
   ```

2. **Ejecutar desde terminal para ver errores:**
   ```bash
   open ~/Applications/Podman\ Desktop.app/
   ```

## Script de Instalación Automática

Puedes usar este script para automatizar la instalación:

```bash
#!/bin/bash
# install-docker-podman-personal.sh

echo "🚀 Instalando Docker CLI + Podman Desktop (sin permisos admin)"

# Crear directorios
mkdir -p ~/Applications ~/bin

# Instalar con Homebrew
brew update
brew install docker
brew install --cask --appdir=~/Applications podman-desktop

# Crear enlaces simbólicos
ln -sf $(brew --prefix)/bin/docker ~/bin/docker
ln -sf ~/Applications/Podman\ Desktop.app/Contents/Resources/bin/podman ~/bin/podman

# Configurar shell
if [[ $SHELL == *"zsh"* ]]; then
    SHELL_RC="$HOME/.zshrc"
else
    SHELL_RC="$HOME/.bash_profile"
fi

# Agregar configuración al archivo de shell
cat >> "$SHELL_RC" << 'EOF'

# Configuración Docker/Podman personal
export PATH="$HOME/bin:$PATH"
export PATH="$HOME/Applications/Podman Desktop.app/Contents/Resources/bin:$PATH"

setup_docker_podman() {
    if command -v podman >/dev/null 2>&1; then
        export DOCKER_HOST="unix://$(podman machine inspect --format '{{.ConnectionInfo.PodmanSocket.Path}}' 2>/dev/null || echo '')"
    fi
}

setup_docker_podman
EOF

echo "✅ Instalación completada. Reinicia tu terminal o ejecuta: source $SHELL_RC"
echo "📱 Abre Podman Desktop desde ~/Applications/ para completar la configuración"
```

## Ventajas de la Instalación Personal

- ✅ **No requiere permisos de administrador**
- ✅ **Instalación completamente aislada** en tu directorio personal
- ✅ **Control total** sobre las aplicaciones instaladas
- ✅ **Fácil de desinstalar** (solo eliminar carpetas)
- ✅ **No afecta** al sistema global
- ✅ **Portable** entre diferentes máquinas

## Conclusión

Esta configuración te permite usar Docker CLI con Podman Desktop sin necesidad de permisos de administrador, manteniendo todo en tu espacio personal de usuario. Es una solución perfecta para entornos corporativos o sistemas compartidos donde no tienes acceso root.

---

**Nota:** Esta configuración ha sido probada en macOS con instalaciones personales de Homebrew y aplicaciones en `~/Applications`.
