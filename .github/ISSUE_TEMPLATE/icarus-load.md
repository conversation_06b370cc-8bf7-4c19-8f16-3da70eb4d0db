---
name: Load testing
about: Run an ICaRUS load testing scenario.
title: '[Load Testing]'
labels: 'icarus'
assignees: ''

---

/test-load --action `ACTION` --scenario `SCENARIO_NAME`

### Write above your scenario to start or just run `stop` action to stop everything

---

### Examples
**Start load testing scenario**
> /test-load --action start --scenario redis-k6

**Stop all load testing**
> /test-load --action stop

### Further information
- [ChatBot run load testing](https://chatbot.docs.inditex.dev/chatbot/latest/commands/test-load.html)
