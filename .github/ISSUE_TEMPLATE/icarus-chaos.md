---
name: Chaos experiment
about: Run an ICaRUS chaos experiment.
title: '[Chaos Experiment]'
labels: 'icarus'
assignees: ''

---

/test-chaos --action `ACTION` --scenario `SCENARIO_NAME`

### Write above your scenario to start or just run `stop` action to stop everything

---

### Examples
**Start chaos experiment scenario**
> /test-chaos --action start --scenario za-purchase-prelt/chckt-cpu-prelt-high.yaml

**Stop all load testing**
> /test-chaos --action stop

### Further information
- [ChatBot run chaos experiment](https://chatbot.docs.inditex.dev/chatbot/latest/commands/test-chaos.html)
