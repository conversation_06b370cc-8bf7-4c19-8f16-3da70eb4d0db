---
name: Deploy PIPE Connector
about: Deploy PIPE Connector
title: '[Deploy PIPE Connector]'
labels: 'pipe'
assignees: ''

---

/deploy-pipe-connect --supraenv `supraenvironment` --notify-to `<EMAIL>`

### Write above the supraenvironment to deploy the connector and the user email to notify :rocket:

---

### Example

> /deploy-pipe-connect --supraenv pre --notify-to <EMAIL>


### More information
- [ChatBot to deploy PIPE topics](https://chatbot.docs.inditex.dev/chatbot/latest/commands/deploy-pipe-connect.html)
