---
name: pipe-deploy-connect
run-name: "Args: supraenv=${{ inputs.SUPRAENV }}, notify_to=${{ inputs.NOTIFY_TO }}"


on:
  workflow_dispatch:
    inputs:
      SUPRAENV:
        description: 'Supra environment to deploy connectors'
        required: true
        default: ''
      NOTIFY_TO:
        description: 'eMail address to notify connector deployment'
        required: true
        default: ''
      ISSUE_NUMBER:
        description: 'ID number (issue or pull request)'
        required: false
        default: ''

env:
  WORKFLOW_VERSION: 3.1.0
  DP_USERNAME: ${{ secrets.DIST_PLAT_READER_USERNAME }}
  DP_TOKEN: ${{ secrets.DIST_PLAT_READER_TOKEN }}

jobs:
  deploy-connect:
    name: Deploy PIPE Connect
    timeout-minutes: 30
    runs-on: ${{ fromJSON( vars.RUNSON_PIPE_ONPREM || '["self-hosted", "pipe"]' ) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Check and get supraenvironment
        id: supraenvironment
        shell: python
        run: |
          import os
          supraenv = '${{ github.event.inputs.SUPRAENV }}'.lower()
          environments = ['des', 'pre', 'pro']
          if supraenv not in environments:
              print(f"::error ::Supraenvironment '{supraenv}' not supported!")
              exit(1)
          github_output = os.getenv('GITHUB_OUTPUT')
          with open(github_output, "a") as gh_out:
            gh_out.write(f'env={supraenv}\n')

      - name: Get metadata
        id: metadata
        shell: python
        run: |
          import yaml
          import os
          with open('application.yml') as f:
            application = yaml.safe_load(f)

          github_output = os.getenv('GITHUB_OUTPUT')
          with open(github_output, "a") as gh_out:
            gh_out.write(f'project-key={ application["metadata"]["project_key"] }\n')
            gh_out.write(f'project-name={ application["metadata"]["project_name"] }\n')

      - name: Force lowercase project_key variable
        run: |
          echo "PROJECT_KEY=$(echo ${{ steps.metadata.outputs.project-key }} | tr '[:upper:]' '[:lower:]')" >> "$GITHUB_ENV"

      - name: Get Github gitUrl
        run: |
          echo "GITURL=https://github.com/${{github.repository}}/tree/${GITHUB_REF##*/}/pipe" >> "$GITHUB_ENV"

      - name: PIPE Verify Connector
        uses: inditex/gha-pipeactions/verify@v3
        with:
          supraenv: ${{ steps.supraenvironment.outputs.env }}
          components: "connects"
          project-dir: ${GITHUB_WORKSPACE}
          extra-features: "--extra-features ${{ github.event.inputs.ISSUE_NUMBER }} ${{ steps.metadata.outputs.project-name }}"
          github-token: ${{ secrets.GH_TOKEN_READER }}
        env:
          PROJECT_KEY: ${{ steps.metadata.outputs.project-key }}
          PROJECT_NAME: ${{ steps.metadata.outputs.project-name }}

      - name: Check PR Verify Result
        if: ${{ failure() && !cancelled() }}
        uses: inditex/gha-citool/add-comment@v0
        with:
          issue-number: "${{ github.event.inputs.ISSUE_NUMBER }}"
          body: |
            ### :warning: PIPE Connect PR verification failure
            - **Supraenvironments**: "${{ github.event.inputs.SUPRAENV }}"
            - **Action**: "[${{ github.workflow }}-${{ github.run_id }}](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})"
        env:
          GITHUB_TOKEN: ${{ secrets.CHATBOT_PR_PAT }}

      - name: Generate PIPE Summary and Coordinates
        if: ${{ !startsWith(env.PIPE_CODE_VERSION, '2') }}
        id: summary-generator
        uses: inditex/gha-pipeactions/analyze-differences@v2
        with:
          differences-file-path: "pipe/target"
          supraenv: ${{ github.event.inputs.SUPRAENV }}
          github-token: ${{ secrets.GH_TOKEN_READER }}

      - name: Shepherd validation
        if: ${{ steps.summary-generator.outputs.shepherd_regions && !startsWith(env.PIPE_CODE_VERSION, '2') }}
        uses: inditex/gha-pipeactions/shepherd-region-check@v2
        with:
          supraenvs: ${{ github.event.inputs.SUPRAENV }}
          regions: ${{ steps.summary-generator.outputs.shepherd_regions }}
          pr-number: ${{ github.event.inputs.ISSUE_NUMBER }}
          project-key: ${{ steps.metadata.outputs.project-key }}
          project-name: ${{ steps.metadata.outputs.project-name }}
          github-token: ${{ secrets.GH_TOKEN_READER }}

      - name: Setup paas-cli
        id: setup-cli
        uses: inditex/gha-paasdeployment/setup-cli@v0
        with:
          github_token: ${{ secrets.GH_TOKEN_READER }}

      - name: Deploy PIPE Connect
        uses: inditex/gha-pipeactions/deploy-connect@v3
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          paas-cli: ${{ steps.setup-cli.outputs.path }}
          supraenv: ${{ steps.supraenvironment.outputs.env }}
          email: ${{ github.event.inputs.NOTIFY_TO }}
          git-url: ${GITURL}
          project-dir: ${GITHUB_WORKSPACE}
          github-token: ${{ secrets.GH_TOKEN_READER }}
        env:
          PROJECT_KEY: ${{ steps.metadata.outputs.project-key }}
          PROJECT_NAME: ${{ steps.metadata.outputs.project-name }}

      - name: Create a Workflow Status Check to link this run to its corresponding PR
        if: always() && !cancelled() && github.event_name == 'workflow_dispatch'
        uses: inditex/gha-citool/create-status-check@v0
        with:
          app_id: ${{ vars.WFSC_APP_ID }}
          app_private_key: ${{ secrets.WFSC_APP_PKEY }}
          name: "pipe-deploy-connect-${{ github.event.inputs.SUPRAENV }}"
          summary: |
            This status check represents the conclusion (__${{ job.status }}__) of the last dispatched execution of workflow:
            `${{ github.workflow_ref	}}`
            You can find more details in the link below :arrow_down:
