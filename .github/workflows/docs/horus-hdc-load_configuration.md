# HORUS Deployment Control Load Configuration

[`horus-hdc-load_configuration.yml`](../horus-hdc-load_configuration.yml) workflow allows to **add** or **modify** the **Horus configuration** manually or automatically if the changes in the file are merged to `main` branch.

## Trigger

* `workflow_dispatch` manually we can execute this workflow to add or modify horus configuration
* Any pull request `merged` to `main` branch when there are changes into `horus` path, the trigger automatically will be start

### `input-params`

You can override default values with this input-params.

- **ISSUE_NUMBER**: ID number. It must be an issue or pull request

## Jobs

### `horus-hdc-load-configuration`

* Once the pull request is merged, this step is going to deploy the configuration changes in Horus deployment control service

### More information

Provides the set of files to configure Horus deployment control as code.

* [Quickstart documentation](https://horus.docs.inditex.dev/HORUS/latest/getting-started/hdc-quickstart.html)
* [HDC documentation](https://horus.docs.inditex.dev/HORUS/latest/resources/horusdepco/index.html)
* [Define configuration documentation](https://horus.docs.inditex.dev/HORUS/latest/resources/horusdepco/define-configuration.html)
* [Check catalog documentation](https://horus.docs.inditex.dev/HORUS/latest/resources/horusdepco/define-configuration.html)
