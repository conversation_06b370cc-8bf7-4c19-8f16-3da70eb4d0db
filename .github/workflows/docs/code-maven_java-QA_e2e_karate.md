# code-maven_java-QA_e2e_karate

[`code-maven_java-QA_e2e_karate.yml`](../code-maven_java-QA_e2e_karate.yml) workflow allows to **execute e2e tests** with the [Karate framework](https://github.com/intuit/karate). The test must be in the `e2e/karate` folder.

## Trigger
`workflow_dispatch` manually or invoked from a ChatBot command. See ChatBot official documentation here: [ChatBot documentation](https://chatbot.docs.inditex.dev/chatbot/latest/commands/test-e2e.html).

### `input-params`
You can override default values set in the `e2e/karate/src/test/resources/config_test.yml` file with these input-params.

- **ENV**: You can set this to customize run environment. Defaults to the value defined in `e2e/karate/src/test/resources/config_test.yml` in the `env` section.
- **KARATE_OPTIONS**: Options passed to the karate execution. Usually is used to narrow down the `test scope` to a certain features or scenarios using karate tags or paths, for example: "-t ~@ignore -t @JIRA_KEY-XXXX classpath:apis/ ... /ApiTest.feature". Defaults to the value defined in `e2e/karate/src/test/resources/config_test.yml` in the `karate.options` section.
- **KARATE_COVERAGE_ENABLED**: Enables or disables `jacoco coverage`. Only works with `env` set to "local". Defaults to the value defined in `code/config_test/config_test.yml` in the `karate.coverage.enabled` section.
- **XRAY_TESTPLANID**: Jira issue to be used as `xray test plan`. Defaults to the value defined in `e2e/karate/src/test/resources/config_test.yml` in the `xray.testplanid` section.
- **XRAY_ASSIGNEE**: Jira user to assign to the `executions`. Defaults to the value defined in `e2e/karate/src/test/resources/config_test.yml` in the `xray.assignee` section.
- **ATTACH_TARGET**: Target platform to upload the generated report, it does not export results if empty. Options: `jfrog` | `xray`.


## Where does it run?

GitHub cloud runners on `Ubuntu 20.04` on runs that build the artifact and execute the tests.

Self-hosted runners [iac-githubrunners/testing-ivm](https://githubrunners.docs.inditex.dev/githubrunners/latest/custom-runners/testing-ivm.html) on runs that attack to the remote application.

## Versions used

`asdf` and any `Java`, `Maven` and `Node` versions supported by [IVM's asdf plugin](https://github.com/inditex/cac-asdftooling).

## How does it work?

This workflow relies on [IVM's asdf plugin](https://github.com/inditex/cac-asdftooling) and the [`setup-environment` action](https://github.com/inditex/gha-ivmactions/tree/develop/setup-environment) to automatically **load a given tool version and any needed extra environment variable** (i.e. `$JAVA_HOME`) defined on the project's `code/.tool-versions` file.

## Jobs

- ### `preconditions-check`

What: Prepares the environment for the rest of the workflow.

Where: `self-hosted` runners.

When: `always`.

- **Steps**
  - Checkout of `source code`
  - Check if `e2e/karate/src/test/resources/config_test.yml` file exists, if so, it will take that file in consideration for the rest of the workflows.
  - Retrieve project `configuration` (key, name, version, etc...) using [resolvers/config-resolver](https://github.com/inditex/actions/tree/main/resolvers/config-resolver) taking in consideration the next files:
    - `application.yml`
    - `e2e/karate/src/test/resources/platform.yml`
    - `e2e/karate/src/test/resources/platform_test.yml`
    - `e2e/karate/src/test/resources/config_test.yml`
    - Workflow dispatch input vars
    This configuration will be used for the rest of the workflow.

- ### `e2e-karate-local`

What: Compiles the application and executes the e2e tests locally.

Where: Github cloud runners.

When: if configmap `env` value is `local`.

- **Steps**
  - Checkout of `source code`
  - Setup IVM in the runner
  - Check existence of docker-compose.yml file `/e2e/karate/src/test/resources/compose/docker-compose.yml`.
  - If docker-compose exists replaces the URL JFrog repository to Github internal repository to improve the download speed in the `/e2e/karate/src/test/resources/compose/docker-compose.yml` file.
  - Launch the `docker-compose.yml` services if file exists.
  - Compile and creates the application artifact.
  - Finds where is the application boot jar file.
  - Starts the application.
  - Run e2e karate tests.
  - Stops the application.
  - Generate `Surefire HTML report` (if enabled).
  - Display basic test info as `summary annotations` using [testing/results-summary-annotations](https://github.com/inditex/actions/tree/main/testing/results-summary-annotations) action.
  - Publish `test report` to [jfrog](https://inditex.jfrog.io/ui/repos/tree/General/distributables/gh-artifacts) using [upload-artifact](https://github.com/inditex/actions/tree/main/upload-artifact) action.
  - Create a workflow status check to link the execution with a PR.


- ### `e2e-karate-remote`

What: Launch the e2e karate tests to a remote environment.

Where: `self-hosted` runners.

When: if configmap `env` value is not `local`.

- **Steps**
  - Checkout of `source code`
  - Run e2e karate tests.
  - Generate `Cluecumber HTML report` (if enabled).
  - Generate `Surefire HTML report` (if enabled).
  - Display basic test info as `summary annotations` using [testing/results-summary-annotations](https://github.com/inditex/actions/tree/main/testing/results-summary-annotations) action.
  - Publish `test report` to [jfrog](https://inditex.jfrog.io/ui/repos/tree/General/distributables/gh-artifacts) using [upload-artifact](https://github.com/inditex/actions/tree/main/upload-artifact) action.
  - Publish test information to `JIRA` (if enabled) using [testing/xray-reporter](https://github.com/inditex/actions/tree/main/testing/xray-reporter).
  - Create a workflow status check to link the execution with a PR.
## Configuration files

- ### `e2e/karate/src/test/resources/platform_test.yml`

  Application configuration to start the application locally for the karate tests
  - Binary
    - Location of the boot jar without version number nor extension. For example if your jar is `code/cplayser-boot/target/cplayser-boot-2.1.0-SNAPSHOT.jar` the configuration should be `code/cplayser-boot/target/cplayser-boot`
  - Health Probe
    - Path of the endpoint to check the application started correctly, usually set to context/amiga/health. For example cplayser/amiga/health

  ```yaml
  app:
    binary: code/<APPKEY>-boot/target/<APPKEY>-boot
    health_probe:
      http_path: <APPKEY>/amiga/health
  ```

- ### `code/config_test/secret_test.yml`

  Credentials to be resolved from Cyberark

  ```yaml
  # Secrets file for QA environment

  # This is provided only as an example for creds-resolver action
  # cyberark.credentials.user = cyberark.safe.AccountName
  cyberark:
    credentials:
      <USER_1>: cyberark.<SAFE>.<ACCOUNT_NAME_1>
      <USER_2>: cyberark.<SAFE>.<ACCOUNT_NAME_2>
  ```

- ### `e2e/karate/src/test/resources/config_test.yml` (results)

  Location of the reports supported by this workflow, to be able to:
  - Display basic test info as `summary annotations`
  - Publish `test report` to `JFrog`

  Supported reports:
  - Karate
    - Default Karate report path
  - Surefire xml
    - Path to surefire xml's used for DEVINS publication - Not uploadable
  - Surefire html - OPTIONAL -
    - Requires **maven-surefire-report-plugin** and **maven-site-plugin** (See example configuration below)
  - Cucumber XRay
    - FIXED VALUE - Default karate cucumber output file for XRay
      - `e2e/karate/target/cucumber_result.json`
  - Jacoco e2e folder
    - FIXED VALUE - Mandatory if e2e coverage enabled, used internally by the workflow
      - `code/target/jacoco-e2e`
  - Cluecumber
    - Optional if defined in the pom
  - Allure
    - Optional if defined in the pom
  - App log
    - Internal fixed value for the logs of the application started locally
      - app.log

  ```yaml
  karate:
    # Folders/Paths where the results are stored or to be stored, from the root path of the repo
    results:
      karate_report_folder:
        path: "e2e/karate/target/karate-reports"
        artifact-name: "<APPKEY>-karate"
        upload: true
        summary-type: karate
      surefire_xml_report_path:
        path: "e2e/karate/target/surefire-reports"
        artifact-name: "<APPKEY>-karate"
        upload: true
      surefire_html_report_folder:
        path: "e2e/karate/target/surefire-html-report"
        artifact-name: "<APPKEY>-karate"
        upload: true
        summary-type: surefire
      cucumber_xray_file:
        path: "e2e/karate/target/cucumber_result.json"
        artifact-name: "<APPKEY>-karate"
        upload: true
      # jacoco_e2e_folder - Mandatory if e2e coverage enabled, used internally by the workflow
      jacoco_e2e_folder:
        path: "code/target/jacoco-e2e"
        artifact-name: "<APPKEY>-karate"
        upload: true
        summary-type: jacoco
      cluecumber_report_folder:
        path: "e2e/karate/target/cluecumber-report"
        artifact-name: "<APPKEY>-karate"
        upload: true
      allure_report_folder:
        path: "e2e/karate/target/allure-report"
        artifact-name: "<APPKEY>-karate"
        upload: true
      app_log_file:
        path: "app.log" # internal fixed value
        artifact-name: "<APPKEY>-karate"
        upload: true
      ...
  ```

  Configuration for **maven-surefire-report-plugin** and **maven-site-plugin**

  ```xml
  <plugin>
      <groupId>org.apache.maven.plugins</groupId>
      <artifactId>maven-surefire-report-plugin</artifactId>
      <version>${maven-surefire-report-plugin.version}</version>
      <configuration>
          <outputDirectory>${project.build.directory}/surefire-html-report</outputDirectory>
          <aggregate>true</aggregate>
      </configuration>
  </plugin>
  <plugin>
      <groupId>org.apache.maven.plugins</groupId>
      <artifactId>maven-site-plugin</artifactId>
      <version>${maven-site-plugin.version}</version>
      <configuration>
          <outputDirectory>${project.build.directory}/surefire-html-report</outputDirectory>
      </configuration>
  </plugin>
  ```

- ### `e2e/karate/src/test/resources/config_test.yml` (workflow default values)

  Default values for workflow input-params

  ```yaml
  env: local
  xray:
    testplanid: <JIRA_PROJECT_KEY-XXXX>
    assignee: <JIRA_USERNAME>
  karate:
    ...
    options: "-t ~@ignore"
    coverage:
      enabled: true
      ...
  ```

- ### `e2e/karate/src/test/resources/config_test.yml` (jacoco)

  Karate e2e code coverage configuration. For reference check Karate documentation, section "E2E coverage with jacoco"

  ```yaml
  karate:
    ...
    coverage:
      ...
      jacoco:
        version: X.Y.Z
        includes:
          - ...
        excludes:
          - ...
        sourcefiles:
          - ...
  ```
