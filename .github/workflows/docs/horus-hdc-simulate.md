# HORUS Deployment Control Simulate

[`horus-hdc-simulate.yml`](../horus-hdc-simulate.yml) workflow allows to simulate a Horus evaluation without the need to affect a deployment.

## Trigger

* `workflow_dispatch` manually we can execute this workflow to simulate <PERSON><PERSON> in any deployment

### `input-params`

- **ISSUE_NUMBER**: ID number. It must be an issue or pull request
- **environment**: Environment to simulate. Ex: des
- **supraenvironment**: Supraenvironment to simulate. Ex: des
- **platformId**: platformId to simulate. Ex: meccanoarteixo2
- **slot**: Slot to simulate. Default: default
- **platform**: Platform to simulate. Ex: Openshift-Meccano Interxion 2
- **tenant**: Tenant to simulate. Default: global

### More information

Provides the set of files to configure Horus deployment control as code.

* [Quickstart documentation](https://horus.docs.inditex.dev/HORUS/latest/getting-started/hdc-quickstart.html)
* [HDC documentation](https://horus.docs.inditex.dev/HORUS/latest/resources/horusdepco/index.html)
* [Define configuration documentation](https://horus.docs.inditex.dev/HORUS/latest/resources/horusdepco/define-configuration.html)
* [Check catalog documentation](https://horus.docs.inditex.dev/HORUS/latest/resources/horusdepco/define-configuration.html)
