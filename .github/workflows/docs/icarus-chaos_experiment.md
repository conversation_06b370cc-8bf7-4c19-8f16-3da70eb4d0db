# ICaRUS Chaos Experiment

[`icarus-chaos_experiment.yml`](../icarus-chaos_experiment.yml) workflow allows to
**start** and **stop** ICaRUS load tests on demand.

## Trigger

`workflow_dispatch` invoked from a ChatBot command `/test-chaos` as
described in [ChatBot documentation](https://chatbot.docs.inditex.dev/chatbot/latest/commands/test-chaos.html)

## Where does it run?

[testing github-runners](https://github.com/inditex/github-runners) self hosted.

## Jobs

- ### `chaos-experiment`

  - **Steps**
    - Extract metadata and branch information.
    - Execute [ICaRUS test-chaos action](https://github.com/inditex/actions/tree/main/sre/icarus/test-chaos).
    - Provide information about the execution as GitHub comments.
