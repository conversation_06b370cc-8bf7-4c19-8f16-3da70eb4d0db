# code-maven_java_npm_node-QA_newman

[`code-maven_java-QA_newman.yml`](../code-maven_java-QA_newman.yml) workflow allows to [**execute newman tests**](https://learning.postman.com/docs/running-collections/using-newman-cli/command-line-integration-with-newman/).

## Trigger
- Manually with `workflow_dispatch` invoked from a ChatBot command. See ChatBot offical documentation here: [ChatBot documentation](https://chatbot.docs.inditex.dev/chatbot/latest/commands/test-qa.html)

## Where does it run?

Self-hosted runner: [github-runners/testing](https://github.com/inditex/github-runners)

## Jobs
- ### `newman-config`
  - **Steps**
    - Checkout of `source code`
    - Store project version for saving the version of the application.
    - Retrieve project `configuration` (key, name, version, etc...) using [resolvers/config-resolver](https://github.com/inditex/actions/tree/main/resolvers/config-resolver) action
    - Check environment  is a step in order to ensure the environment we need to load for postman collections when running newman tests, because environment variable can be sent from different sources (payload, parameter...)

- ### `newman-tests`

  - **Steps**
    - Checkout of `source code`
    - Setup IVM environment is step for setting maven, node with versions indicated from file .tool-versions
    - Setup Node version for setting the node version to 16 in order to install newman dependencies.
    - Resolve credentials replace encrypted credentials for running collections using [testing/creds-resolver](https://github.com/inditex/actions/tree/main/testing/creds-resolver) action
    - Install Newman dependencies is step for preparing the newman command and run newman tests.
    - Check the existence of docker-compose.yml file at `/code/src/test/resources/docker-compose.yml`.
    - If docker-compose file exists, launch containers via `docker compose up` at `/code/src/test/resources/docker-compose.yml`.
    - Compile and create artifact (local) for preparing the artifact to run the application.
    - Start application run the java application
    - Start Newman Tests using [testing/start-newman-tests](https://github.com/inditex/actions/tree/main//testing/start-newman-tests) action
    - Start Newman Report using [testing/start-newman-report](https://github.com/inditex/actions/tree/main//testing/start-newman-report)
    - Attach results junit using [upload-artifact](https://github.com/inditex/actions/tree/main/upload-artifact/) action
    - Attach results html using [upload-artifact](https://github.com/inditex/actions/tree/main/upload-artifact/) action
    - Stop running application (local) is proccess for stopping application
    - Publish metrics information to `devins` using [clr-adam](https://github.com/inditex/clr-adam)

- ### `newman-tests-remote`

  - **Steps**
    - Checkout of `source code`
    - Setup IVM environment is step for setting maven, node with versions indicated from file .tool-versions
    - Setup Node version for setting the node version to 16 in order to install newman dependencies.
    - Resolve credentials replace encrypted credentials for running collections using [testing/creds-resolver](https://github.com/inditex/actions/tree/main/testing/creds-resolver) action
    - Install Newman dependencies is step for preparing the newman command and run newman tests.
    - Start Newman Tests using [testing/start-newman-tests](https://github.com/inditex/actions/tree/main//testing/start-newman-tests) action
    - Start Newman Report using [testing/start-newman-report](https://github.com/inditex/actions/tree/main//testing/start-newman-report)
    - Attach results junit using [upload-artifact](https://github.com/inditex/actions/tree/main/upload-artifact/) action
    - Attach results html using [upload-artifact](https://github.com/inditex/actions/tree/main/upload-artifact/) action
    - Publish metrics information to `devins` using [clr-adam](https://github.com/inditex/clr-adam)

## Configuration files

- ### `code/src/test/resources/config_test.yml`

  Location of the reports supported by this workflow, to be able to:
  - Display basic test info as `summary annotations`
  - Publish `test report` to `Action build execution`

  Supported reports:
  - Surefire xml
    - Path to surefire xml's used for DEVINS publication - Not uploadable
  - Surefire html

  ```yaml
  newman:
    html:
      results:
        jacoco_report_folder:
          path: "code/newman"
          artifact-name: "QANewman-html-unit"
          upload: true
          summary-type: html
    junit:
      results:
        jacoco_report_folder:
          path: "code/newman/junit"
          artifact-name: "QANewman-junit-unit"
          upload: true
          summary-type: junit
  ```

## Ways to run workflow
- ### `On demand`
It is possible to run workflow On Demand by indicating branch and environment.

If you pass environment "local". It will wun:
- Perses: to run pods run perses and prepare all the mappings configured into lego folder.
- Local Application: it will run your local api.
- Newman tests: it will run only your collections with prefix "LOCAL-"

If you pass environment different to "local". It will wun:
- Newman tests: it will run only your collections not containing prefix "LOCAL-"
