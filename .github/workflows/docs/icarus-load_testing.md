# icarus-load_testing

[`icarus-load_testing.yml`](../icarus-load_testing.yml) workflow allows to
**start** or **stop** ICaRUS load tests on demand.

## Trigger

`workflow_dispatch` manually or invoked from a ChatBot command. See ChatBot offical documentation here: [ChatBot documentation](https://chatbot.docs.inditex.dev/chatbot/latest/commands/test-load.html).

### `input-params`
You can override default values set in the `code/config_test/config_test.yml` file with these input-params.

- **ACTION**: Action to be performed. Options: start | stop
- **SCENARIO**: The scenario name from the test-plan.yml to be executed
- **ISSUE_NUMBER**: ID number. It must be an issue or pull request

## Where does it run?

Self-hosted runner: [github-runners/testing](https://github.com/inditex/github-runners)

## Jobs

- ### `load-testing`

  - **Steps**
    - Checkout of `source code`
    - Retrieve project `configuration` (key, name, version, etc...) using [resolvers/config-resolver](https://github.com/inditex/actions/tree/main/resolvers/config-resolver) action
    - Store `branch name` for latter usage
    - Execute [sre/icarus/test-load](https://github.com/inditex/actions/tree/main/sre/icarus/test-load) action.
    - Provide `information` about the start/stop/failed execution as `GitHub comments` using [add-comment](https://github.com/inditex/actions/tree/main/add-comment) action
    - Add `Grafana url link` as a Github annotation
    - Add `GrayLog url link` as a Github annotation
