# code-maven_java-QA_integration

[`code-maven_java-QA_integration.yml`](../code-maven_java-QA_integration.yml) workflow allows to **execute integration tests**.

## Trigger

- Manually with `workflow_dispatch` invoked from a ChatBot command.
See ChatBot offical documentation here: [ChatBot documentation](https://chatbot.docs.inditex.dev/chatbot/latest/commands/test-qa.html)

## Where does it run?

GitHub Cloud Runners (4 cores & 16 Gb RAM).

## Versions used

`asdf` and any `Java`, `Maven` versions supported by [IVM's asdf plugin](https://github.com/inditex/cac-asdftooling).

## Jobs

- ### `integration-tests`

  - **Steps**
    - Configure remote environment with Maven and Java versions via asdf.
    - Check the existence of docker-compose file at `/code/src/test/resources/docker-compose.yml`
    - If docker-compose file exists, launch containers via `docker compose up` at `/code/src/test/resources/docker-compose.yml`.
    - When docker-compose file exists, this step can be skipped by setting the [repo variable](https://github.blog/changelog/2023-01-10-github-actions-support-for-configuration-variables-in-workflows/) `LOCAL_CONTAINERS` to `false`.
    - Run `mvn verify` command to execute integration tests.
    - Publish integration tests results to DEVINS.
    - Report the status of the execution from status check in the pull request.
