---
name: pipe-service-account

on:
  workflow_dispatch:

env:
  WORKFLOW_VERSION: 3.1.0
  DP_USERNAME: ${{ secrets.DIST_PLAT_READER_USERNAME }}
  DP_TOKEN: ${{ secrets.DIST_PLAT_READER_TOKEN }}

jobs:
  service-account:
    name: PIPE / Create Service Account
    timeout-minutes: 30
    runs-on: ${{ fromJSON( vars.RUNSON_PIPE_ONPREM || '["self-hosted", "pipe"]' ) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get metadata
        id: metadata
        shell: python
        run: |
          import yaml
          import os
          with open('application.yml') as f:
            application = yaml.safe_load(f)

          github_output = os.getenv('GITHUB_OUTPUT')
          with open(github_output, "a") as gh_out:
            gh_out.write(f'project-key={ application["metadata"]["project_key"] }\n')

      - name: PIPE / Create Service Account
        uses: inditex/gha-pipeactions/service-account@v2
        with:
          project-dir: ${GITHUB_WORKSPACE}
          github-token: ${{ secrets.GH_TOKEN_READER }}
        env:
          PROJECT_KEY: ${{ steps.metadata.outputs.project-key }}
