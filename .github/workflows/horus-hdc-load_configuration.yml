---
name: horus-hdc-load-configuration
env:
  WORKFLOW_VERSION: 2.2.0
  ISSUE_NUMBER: ${{ github.event.number }}
on:
  workflow_dispatch:
    inputs:
      ISSUE_NUMBER:
        description: 'Pull request ID number'
        required: true
        default: ''
  pull_request:
    branches: ['main']
    paths: ['horus/**']
    types: [closed]

jobs:
  load-configuration:
    name: HorusDepco / Load configuration file
    if: github.event.pull_request.merged == true || github.event_name == 'workflow_dispatch'
    timeout-minutes: 5
    runs-on: ${{ fromJSON(vars.RUNSON_HORUS_ONPREM) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - name: Resolve Configuration
        id: config
        uses: inditex/actions/resolvers/config-resolver@main
        with:
          create-annotations: true
          files: |
            application.yml

      - name: Update issue number (only manual process)
        if: github.event_name == 'workflow_dispatch'
        run: echo "ISSUE_NUMBER=${{ github.event.inputs.ISSUE_NUMBER }}" >> "$GITHUB_ENV"

      - name: Print issue number
        run: echo "$ISSUE_NUMBER"

      - name: Run load configuration action
        uses: inditex/gha-sreactions/horusdepco/horusdepco-load-configuration-files@horus-latest
        id: loadConfig
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          project-key: ${{ fromJSON(steps.config.outputs.config).metadata.project_key }}
          project-name: ${{ fromJSON(steps.config.outputs.config).metadata.project_name }}
          issue-number: $ISSUE_NUMBER
          horusdepco-server-url: https://sscc.central.inditex.grp/horusdepco/api
          api-username: oauthhorusdepco
          api-password: cyberark.AX-N-SEG-OAU-IC-OPENAMPR.OAUTH2ClientsPRO_oauthhorusdepco
