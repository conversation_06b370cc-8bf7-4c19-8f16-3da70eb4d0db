---
name: code-maven-PR-verify

concurrency:
  group: code-PR-verify-${{ github.event.pull_request.number }}
  cancel-in-progress: true

on:
  pull_request:
    paths:
      - 'code/**'
      - '.github/workflows/code*'
    types: [opened, synchronize, ready_for_review, reopened]

env:
  WORKFLOW_VERSION: 2.35.0
  DP_USERNAME: ${{ secrets.DIST_PLAT_READER_USERNAME }}
  DP_TOKEN: ${{ secrets.DIST_PLAT_READER_TOKEN }}

jobs:
  unit-tests:
    name: Code / Verify
    runs-on: ${{ fromJSON(vars.RUNSON_CODE_MAVEN_JAVA_ONPREM || '["citool-icr__code-ubuntu24.04-large"]') }}
    if: ${{ (github.event.pull_request.draft == true && (vars.PR_VERIFY_RUNS_ON_DRAFT != 'false' || contains(github.event.pull_request.labels.*.name, 'pr-verify/force-on-draft'))) || github.event.pull_request.draft == false }}
    permissions:
      contents: write
      checks: write
      pull-requests: write
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Setup IVM environment
        uses: inditex/gha-ivmactions/setup-environment@v1
        with:
          tool-versions-directory: code
          distro-branch: ${{ vars.TOOLVER_DISTRO_BRANCH }}

      - name: Get runner resources
        continue-on-error: true
        uses: inditex/gha-citool/get-runner-resources@v0
        with:
          memory_magnitude: ${{ vars.RUNNER_TOTAL_MEMORY_MAGNITUDE || 'MB' }}

      - name: Get metadata
        id: metadata
        shell: python
        run: |
          import yaml
          import os
          with open('application.yml') as f:
            application = yaml.safe_load(f)

          github_output = os.getenv('GITHUB_OUTPUT')
          with open(github_output, "a") as gh_out:
            gh_out.write(f'project-key={ application["metadata"]["project_key"] }\n')
            gh_out.write(f'project-name={ application["metadata"]["project_name"] }\n')

      - name: Load custom secrets as environment variables
        id: creds-resolver
        uses: inditex/gha-citool/creds-resolver-cloud@v0
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          path-to-secrets: .github/secrets/secrets.yml
          output-format: kvequal
          install-paascli: true
          github_token: ${{ secrets.GH_TOKEN_READER }}

      - name: Store project version
        id: version
        run: |
          echo "app-version=$(xmllint --xpath "/*[name()='project']/*[name()='version']/text()" code/pom.xml)" >> "$GITHUB_OUTPUT"

      - name: Maven / Environment setup
        run: |
          echo "${MAVEN_HOME}/bin" >> "$GITHUB_PATH"

      - name: Maven / Set Maven Build Cache options
        id: maven-opts
        if: ${{ vars.MAVEN_BUILD_CACHE_REMOTE == 'true' && !contains(join(github.event.pull_request.labels.*.name, ', '), 'skip-build-cache') }}
        uses: inditex/gha-citool/set-maven-build-cache@v0
        with:
          save-remote-enabled: false
          skip-cache: ${{ startsWith(github.base_ref, 'main') && !contains(join(github.event.pull_request.labels.*.name, ', '), 'skip-release') && (contains(join(github.event.pull_request.labels.*.name, ', '), 'release-type') || vars.DEVELOPMENT_FLOW == 'trunk-based-development') }}

      - name: Maven / Verify artifact with coverage
        if: ${{ !(contains(github.event.pull_request.labels.*.name, 'autopublish/snapshot-binaries')) }}
        env:
          MVN_BUILD_CACHE_OPTS: ${{ steps.maven-opts.outputs.maven-build-cache-opts }}
        working-directory: code
        run: |
          # shellcheck disable=SC2086
          mvn -B clean verify -Djacoco.skip=false -Damiga.jacoco -DskipITs -DfailIfNoTests=false -Dmaven.test.failure.ignore=false $MVN_BUILD_CACHE_OPTS

      - name: Maven / Verify artifact with coverage and deploy PR-versioned SNAPSHOT binaries
        if: ${{ contains(github.event.pull_request.labels.*.name, 'autopublish/snapshot-binaries') }}
        working-directory: code
        env:
          DP_USERNAME: ${{ secrets.DIST_PLAT_DEPLOYER_USERNAME }}
          DP_TOKEN: ${{ secrets.DIST_PLAT_DEPLOYER_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          mvn -B build-helper:parse-version versions:set \
            -DnewVersion="\${parsedVersion.majorVersion}.\${parsedVersion.minorVersion}.\${parsedVersion.incrementalVersion}+PR${{ github.event.pull_request.number }}-SNAPSHOT"

          mvn -B clean deploy -Djacoco.skip=false -Damiga.jacoco -DskipITs -DfailIfNoTests=false -Dmaven.test.failure.ignore=false

          VERSION=${{ steps.version.outputs.app-version }}
          VERSION=${VERSION/-SNAPSHOT}+PR${{ github.event.pull_request.number }}-SNAPSHOT
          MESSAGE="## PR-versioned SNAPSHOT binaries published successfully.
          📦 The artifact version \`$VERSION\` has been published.
          💡 You can use this SNAPSHOT version in your dependent project setting this version of the dependency in your \`pom.xml\`:
          \`\`\`xml
          <version>$VERSION</version>
          \`\`\`

          *Make sure you have configured maven to force a check for updated snapshots (\`-U\`). See [reference configuration](https://github.com/inditex/lib-mavenhello/blob/develop/code/.mvn/maven.config).*
          "

          echo "$MESSAGE" >> "$GITHUB_STEP_SUMMARY"
          gh pr comment ${{ github.event.pull_request.number }} --body "$MESSAGE"

      - name: Maven / Process Surefire report and annotate PR
        if: ${{ always() && !cancelled() }}
        uses: scacap/action-surefire-report@a2911bd1a4412ec18dde2d93b1758b3e56d2a880
        with:
          fail_if_no_tests: false
          create_check: false
          check_name: "Code / Verify"

      - name: Sonar / Disable project-specific properties
        working-directory: ${{ github.workspace }}/code
        run: |
          find ./ -name pom.xml -exec sed -i 's/<sonar./<disabled./g' {} +
          find ./ -name pom.xml -exec sed -i 's/<\/sonar./<\/disabled./g' {} +

      - name: Sonar / Configure input parameters
        working-directory: ${{ github.workspace }}/code
        run: |
          if [[ -f './jacoco-report-aggregate/pom.xml' ]]; then JACOCO_PARAM="-pl !jacoco-report-aggregate -Dsonar.skip=false"; else JACOCO_PARAM="-Dsonar.skip=false"; fi; \
          echo "JACOCO_SONAR_PARAM=$JACOCO_PARAM" >> "$GITHUB_ENV"

      - name: Sonar / Check if the project is excluded of SonarCloud
        id: check-sonarqube-exclusion
        shell: python
        run: |
          import os
          path_find = "./.github/workflows/"
          hasExclusion = 1

          for file in os.listdir(path_find):
            print(f"Archivo: {file}")
            if file.startswith("code-") and file.endswith("-QA_unit.yml"):
                hasExclusion = 0
                print(f"El archivo {file} existe.")

          github_output = os.getenv('GITHUB_OUTPUT')
          with open(github_output, "a") as gh_out:
            gh_out.write(f'project-excluded={ hasExclusion }\n')

      - name: Sonar / Setup Node version
        if: ${{ steps.check-sonarqube-exclusion.outputs.project-excluded == 0 }}
        uses: inditex/gha-ivmactions/setup-tool@v1
        with:
          local-directory: code
          tool-name: ivm-node
          tool-version: 20

      - name: Sonar / Setup Java version
        if: ${{ steps.check-sonarqube-exclusion.outputs.project-excluded == 0 }}
        uses: inditex/gha-ivmactions/setup-tool@v1
        with:
          tool-name: ivm-java
          tool-version: openjdk-17

      - name: SonarCloud / Run Maven Sonar goal
        env:
          PR_HEAD_REF: ${{ github.head_ref }}
          LOGIN: ${{ secrets.SONARCLOUD_TOKEN }}
          SONAR_SCANNER_OPTS: ''
        if: ${{ steps.check-sonarqube-exclusion.outputs.project-excluded == 0 }}
        working-directory: code
        run: |
          JACOCO_REPORT_PATH="$GITHUB_WORKSPACE/code/jacoco-report-aggregate/target/site/jacoco-aggregate/jacoco.xml"
          mvn org.sonarsource.scanner.maven:sonar-maven-plugin:3.9.1.2184:sonar \
            -Dsonar.projectKey="inditexcodingfashion_${{ steps.metadata.outputs.project-key }}" \
            -Dsonar.projectName="${{ steps.metadata.outputs.project-name }}" \
            -Dsonar.projectVersion="${{ steps.version.outputs.app-version }}" \
            -Dsonar.host.url="https://sonarcloud.io/" \
            -Dsonar.organization=inditex-sonarcloud \
            -Dsonar.token="${LOGIN}" \
            -Dsonar.pullrequest.key=${{ github.event.pull_request.number }} \
            -Dsonar.pullrequest.branch="$PR_HEAD_REF" \
            -Dsonar.pullrequest.base=${{ github.base_ref }} \
            -Dsonar.scm.revision=${{ github.event.pull_request.head.sha }} \
            -Dsonar.qualitygate.wait=true \
            -Dsonar.qualitygate.timeout=300 \
            -Dsonar.pullrequest.provider=GitHub \
            -Dsonar.pullrequest.github.repository="${{ github.repository }}" \
            -Dsonar.pullrequest.github.summary_comment=true \
            -Dsonar.coverage.jacoco.xmlReportPaths="$JACOCO_REPORT_PATH"
