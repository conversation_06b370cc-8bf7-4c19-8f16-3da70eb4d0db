---
name: themis-database-generate-version

concurrency:
  group: themis-database-generate-version

on:
  pull_request:
    branches: ['main']
    types: [closed]

env:
  WORKFLOW_VERSION: 2.13.0

jobs:
  tagging:
    name: Database Tagging Merge
    timeout-minutes: 45
    if: github.event.pull_request.merged == true
    runs-on: ${{ fromJSON( vars.RUNSON_THEMIS_ONPREM || '["self-hosted", "database"]' ) }}
    steps:
      - name: Tagging Model
        uses: inditex/gha-themis/features/ci-cd/tag-on-merge@main
