---
name: monit-PR-verify

concurrency:
  group: monit-PR-verify-${{ github.event.pull_request.number }}
  cancel-in-progress: true

on:
  pull_request:
    paths: ['monit/**', '.github/workflows/monit**']
    types: [opened, synchronize, ready_for_review, reopened]

env:
  WORKFLOW_VERSION: 1.50.0

jobs:
  linter:
    name: Lin<PERSON>
    timeout-minutes: 30
    if: ${{ (github.event.pull_request.draft == true && (vars.PR_VERIFY_RUNS_ON_DRAFT != 'false' || contains(github.event.pull_request.labels.*.name, 'pr-verify/force-on-draft'))) || github.event.pull_request.draft == false }}
    runs-on: ${{ fromJSON(vars.RUNSON_ALERTHUB_DOCKER || '["ubuntu-24.04"]') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.sha }}

      - name: <PERSON><PERSON>
        uses: github/super-linter@v4.8.1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          FILTER_REGEX_INCLUDE: monit/.*
          VALIDATE_JSON: true


  alert_checker:
    name: Alerthub / Verify
    if: ${{ (github.event.pull_request.draft == true && (vars.PR_VERIFY_RUNS_ON_DRAFT != 'false' || contains(github.event.pull_request.labels.*.name, 'pr-verify/force-on-draft'))) || github.event.pull_request.draft == false }}
    runs-on: ${{ fromJSON(vars.RUNSON_ALERTHUB_ONPREM || '["citool-icr__code-ubuntu24.04-large"]') }}
    steps:

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Get metadata
        id: metadata
        shell: python
        run: |
          import yaml
          import os

          with open('application.yml') as f:
            application = yaml.safe_load(f)

          github_output = os.getenv('GITHUB_OUTPUT')
          with open(github_output, "a") as gh_out:
            gh_out.write(f'project-key={ application["metadata"]["project_key"] }\n')

      - name: Setup Java tool
        uses: inditex/gha-ivmactions/setup-tool@v1
        with:
          tool-name: ivm-java
          tool-version: openjdk-17

      - name: Comment
        uses: inditex/gha-alerthub/code/alerthub/pr_comment@main
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}

      - name: Monit
        uses: inditex/gha-alerthub/code@main
        with:
          project-key: ${{ steps.metadata.outputs.project-key }}
          pr-verify: true
        env:
          SRVC_ALERTHUBINTDESAX_USERNAME: ${{ secrets.SRVC_ALERTHUBINTDESAX_USERNAME }}
          SRVC_ALERTHUBINTDESAX_PASSWORD: ${{ secrets.SRVC_ALERTHUBINTDESAX_PASSWORD }}
          SRVC_ALERTHUBINTPREAX_USERNAME: ${{ secrets.SRVC_ALERTHUBINTPREAX_USERNAME }}
          SRVC_ALERTHUBINTPREAX_PASSWORD: ${{ secrets.SRVC_ALERTHUBINTPREAX_PASSWORD }}
          SRVC_ALERTHUBINTAX_USERNAME: ${{ secrets.SRVC_ALERTHUBINTAX_USERNAME }}
          SRVC_ALERTHUBINTAX_PASSWORD: ${{ secrets.SRVC_ALERTHUBINTAX_PASSWORD }}
