---
name: pipe-PR-verify
on:
  pull_request:
    paths: ['pipe/**', '.github/workflows/pipe**']
    types: [opened, synchronize, ready_for_review, reopened]

env:
  WORKFLOW_VERSION: 3.1.0
  DP_USERNAME: ${{ secrets.DIST_PLAT_READER_USERNAME }}
  DP_TOKEN: ${{ secrets.DIST_PLAT_READER_TOKEN }}

jobs:
  pipe-verify:
    name: Verify <PERSON><PERSON>
    timeout-minutes: 30
    if: ${{ (github.event.pull_request.draft == true && (vars.PR_VERIFY_RUNS_ON_DRAFT != 'false' || contains(github.event.pull_request.labels.*.name, 'pr-verify/force-on-draft'))) || github.event.pull_request.draft == false }}
    runs-on: ${{ fromJSON( vars.RUNSON_PIPE_ONPREM || '["self-hosted", "pipe"]' ) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Get metadata
        id: metadata
        shell: python
        run: |
          import yaml
          import os
          with open('application.yml') as f:
            application = yaml.safe_load(f)

          github_output = os.getenv('GITHUB_OUTPUT')
          with open(github_output, "a") as gh_out:
            gh_out.write(f'project-key={ application["metadata"]["project_key"] }\n')
            gh_out.write(f'project-name={ application["metadata"]["project_name"] }\n')

      - name: Verify Pipe
        uses: inditex/gha-pipeactions/verify@v3
        with:
          supraenv: "all"
          components: "both"
          project-dir: ${GITHUB_WORKSPACE}
          github-token: ${{ secrets.GH_TOKEN_READER }}
        env:
          PROJECT_KEY: ${{ steps.metadata.outputs.project-key }}
          PROJECT_NAME: ${{ steps.metadata.outputs.project-name }}
