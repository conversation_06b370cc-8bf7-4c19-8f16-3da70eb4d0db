---
name: code-maven-build-snapshot

concurrency:
  group: code-build-snapshot
  cancel-in-progress: ${{ vars.DEVELOPMENT_FLOW != 'trunk-based-development' }}

on:
  push:
    branches: ['develop', 'develop-*', 'main', 'main-*']
    paths: ['code/**', '.github/workflows/code**']
  workflow_dispatch:

env:
  WORKFLOW_VERSION: 2.35.0
  GITHUB_REGISTRY: ghcr.io
  ASDF_IMAGE_NAME: inditex/ci-agents/asdf
  DT_ENDPOINT: ${{ secrets.DT_ENDPOINT }}
  DT_SECRET: ${{ secrets.DT_SECRET }}
  NPM_EMAIL: ${{ secrets.DIST_PLAT_DEPLOYER_USERNAME }}
  NPM_AUTH: ${{ secrets.DIST_PLAT_DEPLOYER_NPM_AUTH }}

jobs:
  build-snapshot:
    name: Build Snapshot
    runs-on: ${{ fromJSON(vars.RUNSON_CODE_MAVEN_JAVA_GENERIC || '["ubuntu-24.04"]') }}
    if: ${{ (github.event_name == 'workflow_dispatch') || (vars.DEVELOPMENT_FLOW == 'trunk-based-development' && (github.ref_name == 'main' || startsWith(github.ref_name, 'main-'))) || (vars.DEVELOPMENT_FLOW != 'trunk-based-development' && (github.ref_name == 'develop' || startsWith(github.ref_name, 'develop-'))) }}
    permissions:
      contents: write
      id-token: write
    steps:
      - name: Get release commits
        id: release-commits
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          if [[ "${{ vars.DEVELOPMENT_FLOW }}" != "trunk-based-development" ]]; then
            echo "SKIP=false" >> "$GITHUB_OUTPUT"
          else
            COMMIT_MESSAGE=$(gh api -H "Accept: application/vnd.github.v3+json" "/repos/$GITHUB_REPOSITORY/commits/$GITHUB_SHA" | jq -cr '.commit.message')
            if [[ "$COMMIT_MESSAGE" == *"[maven-release-plugin] Prepare release"* ||
                  "$COMMIT_MESSAGE" == *"[maven-release-plugin] Prepare for next development iteration"* ||
                  "$COMMIT_MESSAGE" == *"[changelog-drafter] Prepare release"* ]]; then
              echo "SKIP=true" >> "$GITHUB_OUTPUT"
            else
              echo "SKIP=false" >> "$GITHUB_OUTPUT"
            fi
          fi
      - name: Checkout Branch Head
        if: ${{ steps.release-commits.outputs.SKIP == 'false' }}
        uses: actions/checkout@v4

      - name: Infer ci-agents asdf image
        if: ${{ steps.release-commits.outputs.SKIP == 'false' }}
        uses: inditex/gha-ivmactions/infer-ci-agent-asdf@v1
        env:
          ASDF_UBUNTU_IMAGE_TAG: 0.14.0-ubuntu24.04-r1
          ASDF_RHUBI_IMAGE_TAG: 0.14.0-rhubi-r5
          ASDF_CENTOS_IMAGE_TAG: 0.14.0-centos-r3
        with:
          tool-versions-directory: code
          github-token: ${{ secrets.GH_TOKEN_READER }}

      - name: Setup Maven Cache
        if: ${{ steps.release-commits.outputs.SKIP == 'false' }}
        uses: actions/cache@v4
        continue-on-error: true
        with:
          path: ~/.m2/repository
          key: ${{ env.ASDF_IMAGE_TAG }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ env.ASDF_IMAGE_TAG }}-maven-

      - name: Setup asdf Cache
        if: ${{ steps.release-commits.outputs.SKIP == 'false' }}
        uses: actions/cache@v4
        continue-on-error: true
        with:
          path: ~/.asdf/data
          key: ${{ env.ASDF_IMAGE_TAG }}-asdf-${{ hashFiles('**/.tool-versions') }}
          restore-keys: |
            ${{ env.ASDF_IMAGE_TAG }}-asdf-

      - name: Log in to GitHub Registry
        if: ${{ steps.release-commits.outputs.SKIP == 'false' }}
        run: echo "${{ secrets.GH_TOKEN_READER }}" | docker login "${GITHUB_REGISTRY}" -u none --password-stdin

      - name: Maven / Set Maven Build Cache options
        id: maven-opts
        if: ${{ vars.MAVEN_BUILD_CACHE_REMOTE == 'true' && !contains(join(github.event.pull_request.labels.*.name, ', '), 'skip-build-cache') && steps.release-commits.outputs.SKIP == 'false' }}
        uses: inditex/gha-citool/set-maven-build-cache@v0
        with:
          save-remote-enabled: true

      - name: Load custom secrets as environment variables
        id: creds-resolver
        uses: inditex/gha-citool/creds-resolver-cloud@v0
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          path-to-secrets: .github/secrets/secrets.yml
          output-format: kvequal
          install-paascli: true
          github_token: ${{ secrets.GH_TOKEN_READER }}

      - name: Get runner resources
        if: ${{ steps.release-commits.outputs.SKIP == 'false' }}
        continue-on-error: true
        uses: inditex/gha-citool/get-runner-resources@v0
        with:
          memory_magnitude: ${{ vars.RUNNER_TOTAL_MEMORY_MAGNITUDE || 'MB' }}

      - name: Build & Deploy Snapshots
        if: ${{ steps.release-commits.outputs.SKIP == 'false' }}
        env:
          MVN_BUILD_CACHE_OPTS: ${{ steps.maven-opts.outputs.maven-build-cache-opts }}
        run: |
          mkdir -p "$HOME"/.m2/repository
          mkdir -p "$HOME"/.asdf/data
          # shellcheck disable=SC2086
          docker run --workdir /app/code \
            -u "$(id -u)" \
            ${{ steps.creds-resolver.outputs.creds-docker }} \
            -e TOTAL_CPUS \
            -e TOTAL_MEMORY \
            -e DP_USERNAME=${{ secrets.DIST_PLAT_DEPLOYER_USERNAME }} \
            -e DP_TOKEN=${{ secrets.DIST_PLAT_DEPLOYER_TOKEN }} \
            -e DT_ENDPOINT -e DT_SECRET \
            -e NPM_EMAIL -e NPM_AUTH \
            -e GITHUB_TOKEN=${{ secrets.GH_TOKEN_READER }} \
            -e TOOLVER_DISTRO_BRANCH=${{ vars.TOOLVER_DISTRO_BRANCH }} \
            --mount type=bind,src="$HOME"/.m2/repository,dst=/home/<USER>/.m2/repository \
            --mount type=bind,src="$(pwd)",dst=/app \
            --mount type=bind,src="$HOME"/.asdf/data,dst=/opt/asdf/data \
            "${GITHUB_REGISTRY}/${ASDF_IMAGE_NAME}:${ASDF_IMAGE_TAG}" \
            mvn -B clean deploy -DskipTests -DskipUTs -DskipITs -Dbuild.type=snapshot $MVN_BUILD_CACHE_OPTS

      - name: Metrics / Report Size
        if: ${{ steps.release-commits.outputs.SKIP == 'false' }}
        uses: inditex/gha-pmsactions/size-reporter@v0
        continue-on-error: true
        with:
          REPOSITORY_PATH: code
          COMMIT_SHA: ${{ github.sha }}
          BRANCH: ${{ github.head_ref || github.ref_name }}
          BUILD_TYPE: SNAPSHOT
          TECHNOLOGY: maven_java
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_READER }}

      - name: Generate SBOM
        id: generate-sbom
        if: ${{ steps.release-commits.outputs.SKIP == 'false' }}
        uses: inditex/gha-oscodependencies/cdxgen-generator@v0
        continue-on-error: true
        env:
          MVN_BUILD_CACHE_OPTS: ${{ steps.maven-opts.outputs.maven-build-cache-opts }}
        with:
          cache-src: "$HOME/.m2/repository"
          cache-dst: "/home/<USER>/.m2/repository"
          code-src: ${{ github.workspace }}
          dp_user: ${{ secrets.DIST_PLAT_DEPLOYER_USERNAME }}
          dp_token: ${{ secrets.DIST_PLAT_DEPLOYER_TOKEN }}
          github_token: ${{ secrets.GH_TOKEN_READER }}
          command: "mvn -B org.cyclonedx:cyclonedx-maven-plugin:2.9.0:makeAggregateBom \
              -DprojectType=application \
              -DoutputFormat=json \
              -DoutputName=sbom \
              -DincludeTestScope=true \
              -DskipTests -DskipUTs -DskipITs -Dbuild.type=snapshot $MVN_BUILD_CACHE_OPTS"

      - name: Upload SBOM Artifact
        uses: actions/upload-artifact@v4
        continue-on-error: true
        with:
          name: sbom
          path: code/target/sbom.json
