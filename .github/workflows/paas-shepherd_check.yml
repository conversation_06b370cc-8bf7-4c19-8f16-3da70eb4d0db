---
name: paas-shepherd-check

on:
  workflow_call:
    inputs:
      PR_LIST:
        type: string
        required: true
env:
  WORKFLOW_VERSION: 3.22.1

jobs:
  get-details:
    name: <PERSON> / Get Details
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_ONPREM || '["cdtool-icr__paasfeature"]' ) }}
    outputs:
      matrix: ${{ steps.pr.outputs.matrix }}
    steps:
      - name: Parse PR information
        id: pr
        shell: bash
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          JSON="["
          pr_info_file="pr_info.json"
          pr_list="${{ inputs.PR_LIST }}"
          IFS="," read -r -a pr_array <<< "$pr_list"
          for pr_number in "${pr_array[@]}"
          do
            echo "::info ::Processing PR number ${pr_number}"
            gh api "/repos/${GITHUB_REPOSITORY}/pulls/${pr_number}" > ${pr_info_file}
            # Extract permutation from head reference
            permutation=$(jq -r .head.ref ${pr_info_file} | xargs basename)
            echo "permutation=${permutation}" >> "$GITHUB_OUTPUT"
            branch_name=$(jq -r .head.ref ${pr_info_file})
            JSONline="{\"pr_number\":\"$pr_number\",\"branch_name\":\"$branch_name\",\"permutation\":\"$permutation\"},"
            JSON="$JSON$JSONline"
          done
          if [[ $JSON == *, ]]; then
            JSON="${JSON%?}"
          fi
          JSON="$JSON]"
          echo "$JSON"

          # Set output
          echo "matrix=$JSON" >> "$GITHUB_OUTPUT"

  shepherd-check:
    name: Shepherd / Deployment Check
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_ONPREM || '["cdtool-icr__paasfeature"]' ) }}
    needs: get-details
    strategy:
      max-parallel: 1
      matrix:
        include: ${{fromJSON(needs.get-details.outputs.matrix)}}

    steps:
      - name: Checkout
        id: matrix-branch-checkout
        uses: actions/checkout@v4
        continue-on-error: true
        with:
          ref: ${{ matrix.branch_name }}

      - name: Set branch name
        id: set-branch-name
        run: |
          if ${{ steps.matrix-branch-checkout.outcome == 'failure' }}; then
            echo "branch_name=deployment" >> "$GITHUB_OUTPUT"
          else
            echo "branch_name=${{ matrix.branch_name }}" >> "$GITHUB_OUTPUT"
          fi

      - name: Checkout fallback
        if: steps.matrix-branch-checkout.outcome == 'failure'
        uses: actions/checkout@v4
        with:
          ref: ${{ steps.set-branch-name.outputs.branch_name }}

      - name: Parse info.yaml
        id: info
        shell: bash
        run: |
          permutation="${{ matrix.permutation }}"
          info_path="${permutation}/info.yml"

          project_key=$(yq e '.platform.values.metadata.project_key' "$info_path" | tr '[:upper:]' '[:lower:]')
          project_name=$(yq e '.platform.values.metadata.project_name' "$info_path" | tr '[:upper:]' '[:lower:]')

          echo "project_key=$project_key" >> "$GITHUB_OUTPUT"
          echo "project_name=$project_name" >> "$GITHUB_OUTPUT"

      - name: PaaS / Shepherd check
        uses: inditex/gha-paasdeployment/shepherd-deployment-check@v0
        id: shepherd
        with:
          project-key: ${{ steps.info.outputs.project_key }}
          repo-name: ${{ steps.info.outputs.project_name }}
          deployment-folder: ${{ matrix.permutation }}
          pr-number: ${{ matrix.pr_number }}
          branch-name: ${{ steps.set-branch-name.outputs.branch_name }}
          app-client-id: ${{ secrets.APPLICATION_CLIENT_ID }}
          app-client-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          url: 'https://cdmngmt.apps.axecocpci1.ecommerce.inditex.grp/cdmngmt/api/v1/shepherd/check'

      - name: Add comment when the workflow is failed
        if: ${{ failure() && !cancelled() && steps.shepherd.outputs.error-message == '' }}
        uses: inditex/gha-citool/add-comment@v0
        with:
          issue-number: '${{ github.event.inputs.ISSUE_NUMBER }}'
          body: |
            #### :x: An error has ocurred. See the [workflow log](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}).
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Add comment when the workflow is failed and there is an error message
        if: ${{ failure() && !cancelled() && steps.shepherd.outputs.error-message != '' }}
        uses: inditex/gha-citool/add-comment@v0
        with:
          issue-number: '${{ github.event.inputs.ISSUE_NUMBER }}'
          body: |
            #### :x: An error has ocurred.
            ${{ steps.shepherd.outputs.error-message }}
            See the [workflow log](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}).
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
