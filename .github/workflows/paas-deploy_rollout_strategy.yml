---
name: paas-deploy-rollout-strategy
run-name: "Args: action=${{ inputs.ACTION }}"

on:
  workflow_dispatch:
    inputs:
      ACTION:
        description: 'Action to run'
        required: true
        default: ''
      ISSUE_NUMBER:
        description: 'Pull request ID number'
        required: true
        default: ''
      STEP:
        description: 'Target steps to run the action'
        required: false
        default: ''
      SKIP:
        description: 'Steps to be skipped during rollout flow'
        required: false
        default: ''

env:
  WORKFLOW_VERSION: 3.22.1

jobs:
  rollout-strategy:
    name: PaaS / Deploy Rollout Strategy
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_ONPREM || '["cdtool-icr__paasfeature"]' ) }}
    steps:
      - name: PaaS / Deploy rollout strategy
        uses: inditex/gha-paasdeployment/deployment/rollout-strategy@v0
        id: rollout
        with:
          action: ${{ github.event.inputs.ACTION }}
          repository: ${{ github.repository }}
          pr-number: ${{ github.event.inputs.ISSUE_NUMBER }}
          step: ${{ github.event.inputs.STEP }}
          skip: ${{ github.event.inputs.SKIP }}
          app-client-id: ${{ secrets.APPLICATION_CLIENT_ID }}
          app-client-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}

      - name: Add comment when the workflow is failed
        if: ${{ failure() && !cancelled() && steps.rollout.outputs.error-message == '' }}
        uses: inditex/gha-citool/add-comment@v0
        with:
          issue-number: '${{ github.event.inputs.ISSUE_NUMBER }}'
          body: |
            #### :x: An error has ocurred. See the [workflow log](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}).
        env:
          GITHUB_TOKEN: ${{ secrets.CHATBOT_PR_PAT }}

      - name: Add comment when the workflow is failed and there is an error message
        if: ${{ failure() && !cancelled() && steps.rollout.outputs.error-message != '' }}
        uses: inditex/gha-citool/add-comment@v0
        with:
          issue-number: '${{ github.event.inputs.ISSUE_NUMBER }}'
          body: |
            #### :x: An error has ocurred.
            ${{ steps.rollout.outputs.error-message }}
            See the [workflow log](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}).
        env:
          GITHUB_TOKEN: ${{ secrets.CHATBOT_PR_PAT }}
