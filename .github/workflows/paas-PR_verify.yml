---
name: paas-PR-verify

concurrency:
  group: paas-PR-verify-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review, closed]
    paths:
      - "paas/**"
  workflow_dispatch:

env:
  WORKFLOW_VERSION: 3.22.1
  DIR_PATH_STATUS: "/tmp/check-secret-results"

jobs:
  check-changes-in-paths:
    name: Check for changes in corresponding paths
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    if: ${{ github.event.action != 'closed' && github.event_name != 'workflow_dispatch'
        && ((github.event.pull_request.draft == true && (vars.PR_VERIFY_RUNS_ON_DRAFT != 'false' || contains(github.event.pull_request.labels.*.name, 'pr-verify/force-on-draft'))) || github.event.pull_request.draft == false) }}
    outputs:
      yaml: ${{ steps.changes.outputs.yaml }}
      yaml-files: ${{ steps.changes.outputs.yaml_files }}
    steps:
      - name: Check for changed files in specific paths
        id: changes
        uses: dorny/paths-filter@ebc4d7e9ebcb0b1eb21480bb8f43113e996ac77a
        with:
          list-files: shell
          filters: |
            yaml:
              - added|modified: 'paas/**.yml'
              - added|modified: 'paas/**.yaml'
              - added|modified: 'paas/config_paas/**.yml'
              - added|modified: 'paas/config_paas/**.yaml'

  yaml-lint:
    name: PaaS / Verify
    timeout-minutes: 5
    needs: [check-changes-in-paths]
    if: needs.check-changes-in-paths.outputs.yaml == 'true'
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - name: YAML Lint
        run: yamllint -d relaxed ${{ needs.check-changes-in-paths.outputs.yaml-files }}

  check-changes-in-secrets:
    name: Check for changes secret*.yml
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    if: ${{ github.event.action != 'closed' && github.event_name != 'workflow_dispatch'
        && ((github.event.pull_request.draft == true && (vars.PR_VERIFY_RUNS_ON_DRAFT != 'false' || contains(github.event.pull_request.labels.*.name, 'pr-verify/force-on-draft'))) || github.event.pull_request.draft == false) }}
    outputs:
      secrets: ${{ steps.secrets-check-changes.outputs.secrets }}
      secrets-files-array: ${{ steps.secrets-check-changes.outputs.secrets-files-array }}
      ref-tag: ${{ steps.secrets-check-changes.outputs.ref-tag }}
      last-check-tag: ${{ steps.secrets-check-changes.outputs.last-check-tag }}

    steps:
      - name: PaaS / Check changes in secrets for PR event
        id: secrets-check-changes
        uses: inditex/gha-paasdeployment/paas-pr-verify/check-secret-changes@v0
        with:
          head-ref: ${{ github.event.pull_request.head.ref }}
          base-ref: ${{ github.event.pull_request.base.ref }}

  check-secrets:
    name: Check new secrets
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_ONPREM || '["cdtool-icr__paasfeature"]' ) }}
    needs: [check-changes-in-secrets]
    if: ${{ needs.check-changes-in-secrets.outputs.secrets == 'true' && github.event_name != 'workflow_dispatch' }}
    strategy:
      fail-fast: false
      max-parallel: 4
      matrix:
        secret-files: ${{ fromJson(needs.check-changes-in-secrets.outputs.secrets-files-array) }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0

      - name: Check secrets
        uses: inditex/gha-paasdeployment/check-all-secrets-proxy@v0
        with:
          secrets-file-path: ${{ matrix.secret-files }}
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          install-paascli: true
          github-token: ${{ secrets.GH_TOKEN_READER }}
          ref-tag: ${{ needs.check-changes-in-secrets.outputs.ref-tag }}
          idp-federate-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          idp-federate-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}

  publish-check-secrets-message:
    needs: [check-changes-in-secrets, check-secrets]
    if: ${{ failure() && needs.check-changes-in-secrets.outputs.secrets == 'true' && github.event_name != 'workflow_dispatch' }}
    name: "PaaS / Summary for check secrets action"
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    outputs:
      summary: ${{ steps.summary-check-secrets.outputs.summary }}

    steps:
      - name: PaaS / Summary for check secrets action
        id: summary-check-secrets
        uses: inditex/gha-paasdeployment/paas-pr-verify/publish-check-secrets-message@v0
        with:
          pr-number: "${{ github.event.pull_request.number }}"
          github-token: ${{ secrets.GITHUB_TOKEN }}

  create-check-secrets-tag:
    name: Create check-secrets tag
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    needs: [check-secrets]
    if: ${{ needs.check-secrets.result == 'success' && github.event_name != 'workflow_dispatch' }}

    steps:
      - name: PaaS / Create check-secrets tag
        id: secrets-create-check-tag
        uses: inditex/gha-paasdeployment/paas-pr-verify/create-check-secrets-tag@v0
        with:
          head-sha: ${{ github.event.pull_request.head.sha }}
          branch-name: ${{ github.event.pull_request.head.ref }}

  remove-check-secrets-tag:
    name: Remove check-secrets tag
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    if: ${{ github.event.action == 'closed' && github.event_name != 'workflow_dispatch' }}

    steps:
      - name: PaaS / Remove check-secrets tag
        id: secrets-remove-check-tag
        uses: inditex/gha-paasdeployment/paas-pr-verify/remove-check-secrets-tag@v0
        with:
          head-ref: ${{ github.event.pull_request.head.ref }}

  get-secret-files-list:
    name: Get secret files list
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_ONPREM || '["cdtool-icr__paasfeature"]' ) }}
    if: ${{ github.event_name == 'workflow_dispatch' }}
    outputs:
      files-found: ${{ steps.get-secret-files-list.outputs.files-found }}
      secrets-files-array: ${{ steps.get-secret-files-list.outputs.secrets-files-array }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref_name }}
          fetch-depth: 0

      - name: PaaS / Get secret files list
        id: get-secret-files-list
        uses: inditex/gha-paasdeployment/paas-pr-verify/get-secret-files-list@v0

  check-all-secrets:
    name: Check new secrets
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_ONPREM || '["cdtool-icr__paasfeature"]' ) }}
    needs: [get-secret-files-list]
    if: ${{ needs.get-secret-files-list.outputs.files-found == 'true' && github.event_name == 'workflow_dispatch' }}
    strategy:
      fail-fast: false
      max-parallel: 4
      matrix:
        secret-files: ${{ fromJson(needs.get-secret-files-list.outputs.secrets-files-array) }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref_name }}
          fetch-depth: 0

      - name: Check secrets
        uses: inditex/gha-paasdeployment/check-all-secrets-proxy@v0
        with:
          secrets-file-path: ${{ matrix.secret-files }}
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          install-paascli: true
          github-token: ${{ secrets.GH_TOKEN_READER }}
