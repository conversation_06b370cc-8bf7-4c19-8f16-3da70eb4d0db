---
name: amginspector-analyzer

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches:
      - main
      - develop
  pull_request:
    types: [opened, labeled, edited, synchronize]
  workflow_dispatch:
    inputs:
      DRY_RUN:
        required: false
        type: boolean
        default: false
        description: 'Run the workflow in dry-run mode'
      INCLUDE_MATCHES:
        required: false
        type: boolean
        default: false
        description: 'Include matches (only for dry-run mode)'

env:
  WORKFLOW_VERSION: 1.46.0
  TOOL_VERSIONS: |
    ivm-node 18.12.1

jobs:
  run-inspector:
    name: AmgInspector / Analyze
    timeout-minutes: 20
    if: |-
      ${{ github.event_name == 'workflow_dispatch' ||
        github.event_name == 'push' ||
        (github.event_name == 'pull_request' && contains( github.event.pull_request.labels.*.name, 'amiga-inspector'))
      }}
    runs-on: ${{ fromJSON(vars.RUNSON_AMGINSPECTOR_ONPREM || '["self-hosted", "inspector"]') }}
    env:
      ASDF_DATA_DIR: /tmp/asdf/data
    steps:

      - name: Checkout
        uses: actions/checkout@v4

      - name: Checkout Custom Rules
        uses: actions/checkout@v4
        with:
          repository: inditex/cac-amigainspectorrules
          path: cac-amigainspectorrules
          token: ${{ secrets.GH_TOKEN_READER }}

      - name: IVM / Create .tool-versions
        run: |
          echo "$TOOL_VERSIONS" > ${{ github.workspace }}/.tool-versions
      - name: IVM / Create ASDF_DATA_DIR
        run: |
          mkdir -p "$ASDF_DATA_DIR"
      - name: IVM / Setup plugins
        uses: inditex/gha-ivmactions/setup-plugins@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_READER }}
        with:
          tool-versions-directory: "."

      - name: IVM / Setup environment
        uses: inditex/gha-ivmactions/setup-environment@v1
        with:
          tool-versions-directory: "."

      - name: IVM / Source asdf
        run: |
          # shellcheck source=/dev/null
          source "$ASDF_DIR"/asdf.sh
          echo "$PATH" >> "$GITHUB_PATH"

      - name: IVM / Check versions
        run: |
          node --version
          npm --version

      - name: Get metadata
        id: metadata
        shell: python
        run: |
          import yaml
          import os

          with open('application.yml') as f:
            application = yaml.safe_load(f)

          github_output = os.getenv('GITHUB_OUTPUT')
          with open(github_output, "a") as gh_out:
            gh_out.write(f'project-key={ application["metadata"]["project_key"] }\n')
            gh_out.write(f'project-name={ application["metadata"]["project_name"] }\n')

      - name: Get global config
        id: config
        shell: python
        run: |
          import yaml
          import os

          with open('cac-amigainspectorrules/amginspector/config.yml') as f:
            config = yaml.safe_load(f)

          github_output = os.getenv('GITHUB_OUTPUT')
          with open(github_output, "a") as gh_out:
            gh_out.write(f'exclusions={ config["exclusions"] }\n')
            gh_out.write(f'process-timeout={ config["process-timeout"] }\n')
            gh_out.write(f'file-timeout={ config["file-timeout"] }\n')
            gh_out.write(f'ignore-default-rules={ config["ignore-default-rules"] }\n')
            gh_out.write(f'match-limit={ config["match-limit"] }\n')
            gh_out.write(f'react-scanner-enabled={ config["features"]["react-scanner"]["enabled"] }\n')

      - name: Get branch name (merge)
        if: github.event_name == 'push' || github.event_name == 'workflow_dispatch'
        shell: bash
        run: echo "BRANCH_NAME=${GITHUB_REF#refs/heads/}" >> "$GITHUB_ENV"

      - name: Get branch name (pull request)
        if: github.event_name == 'pull_request'
        shell: bash
        run: echo "BRANCH_NAME=${GITHUB_HEAD_REF}" >> "$GITHUB_ENV"

      - name: Get dryRun parameter (workflow_dispatch)
        if: github.event_name == 'workflow_dispatch'
        shell: bash
        run: echo "DRY_RUN=${{ inputs.DRY_RUN }}" >> "$GITHUB_ENV"

      - name: Get dryRun parameter (merge or pull request)
        if: github.event_name == 'push' || github.event_name == 'pull_request'
        shell: bash
        run: echo "DRY_RUN=false" >> "$GITHUB_ENV"

      - name: Get includeMatches parameter (workflow_dispatch)
        if: github.event_name == 'workflow_dispatch'
        shell: bash
        run: echo "INCLUDE_MATCHES=${{ inputs.INCLUDE_MATCHES }}" >> "$GITHUB_ENV"

      - name: Get includeMatches parameter (merge or pull request)
        if: github.event_name == 'push' || github.event_name == 'pull_request'
        shell: bash
        run: echo "INCLUDE_MATCHES=false" >> "$GITHUB_ENV"

      - name: Run AMIGA Inspector
        uses: inditex/gha-amigainspectoractions/run@main
        with:
          project-key: ${{ steps.metadata.outputs.project-key }}
          project-name: ${{ steps.metadata.outputs.project-name }}
          branch: ${{ env.BRANCH_NAME }}
          custom-rules-path: cac-amigainspectorrules/amginspector/rules
          timeout: ${{ steps.config.outputs.process-timeout }}
          file-timeout: ${{ steps.config.outputs.file-timeout }}
          exclusions: ${{ steps.config.outputs.exclusions }}
          ignore-default-rules: ${{ steps.config.outputs.ignore-default-rules }}
          match-limit: ${{ steps.config.outputs.match-limit }}
          react-scanner-enabled: ${{ steps.config.outputs.react-scanner-enabled }}
          dry-run: ${{ env.DRY_RUN }}
          include-matches: ${{ env.INCLUDE_MATCHES }}

        env:
          AMGINSPECT_USERNAME: ${{ secrets.AMGINSPECT_USER }}
          AMGINSPECT_PASSWORD: ${{ secrets.AMGINSPECT_PASS }}

      - name: Sanitize branch name
        if: ${{ inputs.DRY_RUN }}
        shell: bash
        run: echo "SANITIZED_BRANCH_NAME=${BRANCH_NAME//\//_}" >> "$GITHUB_ENV"

      - name: Upload application inspector results
        if: ${{ inputs.DRY_RUN }}
        uses: actions/upload-artifact@v4
        with:
          retention-days: 1
          name: application-inspector-result-${{ env.SANITIZED_BRANCH_NAME }}.json
          path: analysis-appinspector-result.json
          overwrite: true

      - name: Upload react scanner results
        if: ${{ inputs.DRY_RUN }}
        uses: actions/upload-artifact@v4
        with:
          retention-days: 1
          name: react-scanner-result-${{ env.SANITIZED_BRANCH_NAME }}.json
          path: analysis-reactscaner-result.json
          overwrite: true
