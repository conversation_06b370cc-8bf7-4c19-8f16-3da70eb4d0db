---
name: code-maven-snyk-analysis
run-name: "Snyk Analysis on branch '${{ github.head_ref || github.ref_name }}'"

permissions:
  contents: write
  issues: write
  actions: write
  pull-requests: write

concurrency:
  group: code-snyk-analysis-${{ github.head_ref || github.ref_name }}
  cancel-in-progress: true

on:
  workflow_dispatch:
  pull_request:
    types: [opened, synchronize, closed]
    paths: ['.github/workflows/code-*-snyk_analysis.yml', 'code/**/pom.xml']

env:
  WORKFLOW_VERSION: 3.10.0
  WORKING_DIR: code
  GITHUB_SNYK_API_TOKEN: ${{ secrets.SNYK_TOKEN }}

jobs:
  snyk-analysis-execution:
    runs-on: ${{ fromJSON( vars.RUNSON_SNYK_MAVEN_JAVA_ONPREM || '["citool-icr__code-ubuntu24.04-large"]' ) }}
    name: Snyk / Analysis execution
    if: ${{ (github.event.pull_request.draft == true && (vars.PR_VERIFY_RUNS_ON_DRAFT != 'false' || contains(github.event.pull_request.labels.*.name, 'pr-verify/force-on-draft'))) || github.event.pull_request.draft == false }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Setup IVM environment
        uses: inditex/gha-ivmactions/setup-environment@v1
        with:
          tool-versions-directory: code
          distro-branch: ${{ vars.TOOLVER_DISTRO_BRANCH }}

      - name: Load custom secrets as environment variables
        id: creds-resolver
        uses: inditex/gha-citool/creds-resolver-cloud@v0
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          path-to-secrets: .github/secrets/secrets.yml
          output-format: kvequal
          install-paascli: true
          github_token: ${{ secrets.GH_TOKEN_READER }}

      - name: Run Maven Install
        working-directory: ${{ env.WORKING_DIR }}
        run: |
          mvn -B install -DskipTests -DskipUTs -DskipITs -DskipEnforceSnapshots

      - name: Get Artifact metadata
        id: artifact-metadata
        uses: inditex/gha-defectdojoactions/metadata@main
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Get Snyk metadata
        id: snyk-metadata
        uses: inditex/gha-snykactions/metadata@main
        with:
          application-key: ${{ steps.artifact-metadata.outputs.application-key }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node version
        uses: inditex/gha-ivmactions/setup-tool@v1
        with:
          local-directory: code
          tool-name: ivm-node
          tool-version: 16

      - name: Snyk Test Execution
        uses: inditex/gha-snykactions/test@main
        with:
          project-key: ${{ steps.artifact-metadata.outputs.project-key }}
          application-org-slug: ${{ steps.snyk-metadata.outputs.application-org-slug }}
          working-directory: ${{ env.WORKING_DIR }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Snyk Monitor Execution
        uses: inditex/gha-snykactions/monitor@main
        with:
          project-key: ${{ steps.artifact-metadata.outputs.project-key }}
          application-org-slug: ${{ steps.snyk-metadata.outputs.application-org-slug }}
          working-directory: ${{ env.WORKING_DIR }}
          development-flow: ${{ vars.DEVELOPMENT_FLOW }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Import Snyk data to DefectDojo
        if: ${{ (github.event_name == 'workflow_dispatch') || (github.event.pull_request.merged == true && ( (vars.DEVELOPMENT_FLOW == 'trunk-based-development' && (github.ref_name == 'main' || startsWith(github.ref_name, 'main-'))) || (vars.DEVELOPMENT_FLOW != 'trunk-based-development' && (github.ref_name == 'develop' || startsWith(github.ref_name, 'develop-'))) ) ) }}
        uses: inditex/gha-defectdojoactions/import@main
        with:
          project-key: ${{ steps.artifact-metadata.outputs.project-key }}
          product-id: ${{ steps.artifact-metadata.outputs.product-id }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Snyk Quality Gate check
        id: snyk-qg-check
        uses: inditex/gha-snykactions/qualitygate@main
        with:
          project-key: ${{ steps.artifact-metadata.outputs.project-key }}
          snyk-report: ${{ github.workspace }}/${{ env.WORKING_DIR }}/snyk_text/${{ steps.artifact-metadata.outputs.project-key }}_test_output.json
          working-directory: ${{ env.WORKING_DIR }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Snyk Feedback
        uses: inditex/gha-snykactions/feedback@main
        if: ${{ always() && !cancelled() }}
        with:
          project-key: ${{ steps.artifact-metadata.outputs.project-key }}
          snyk-report: ${{ github.workspace }}/${{ env.WORKING_DIR }}/snyk_text/${{ steps.artifact-metadata.outputs.project-key }}_test_output.json
          quality-gate-result: ${{ steps.snyk-qg-check.outputs.result }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
