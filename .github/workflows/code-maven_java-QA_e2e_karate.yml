---
name: code-maven-QA-e2e-karate
run-name: "Args: env=${{ inputs.ENV }}, karate_options=${{ inputs.KARATE_OPTIONS }}"

on:
  workflow_dispatch:
    inputs:
      ENV:
        description: |
          ENV: Target environment for test execution.
          'DEFAULT' uses the value set in config_test "env".
        required: false
        default: 'DEFAULT'
      KARATE_OPTIONS:
        description: |
          KARATE_OPTIONS: Options passed to Karate
          'DEFAULT' uses the value set in config_test "karate.options".
        required: false
        default: 'DEFAULT'
      KARATE_COVERAGE_ENABLED:
        description: |
          KARATE_COVERAGE_ENABLED: Enables or disables jacoco coverage
          'DEFAULT' uses the value set in config_test "karate.coverage.enabled".
        required: false
        default: 'DEFAULT'
        type: choice
        options:
          - 'true'
          - 'false'
          - 'DEFAULT'
      XRAY_TESTPLANID:
        description: |
          XRAY_TESTPLANID: Jira issue to be used as xray test plan.
          'DEFAULT' uses the value set in config_test "xray.testplanid".
        required: false
      XRAY_ASSIGNEE:
        description: |
          XRAY_ASSIGNEE: JIRA User to assign to the executions.
          'DEFAULT' uses the value set in config_test "xray.assignee".
        required: false
        default: 'DEFAULT'
      ATTACH_TARGET:
        description: Target platform to upload the generated report, it does not export results if empty. (p.e. jfrog)
        required: false
      ADDITIONAL_PARAMETERS:
        description: |
          ADDITIONAL_PARAMETERS: Specify additional parameters for the workflow execution in the json format.
        required: false
        default: '{"REQUEST_URL":"", "PR_URL":""}'

env:
  WORKFLOW_VERSION: 7.5.1
  DP_USERNAME: ${{ secrets.DIST_PLAT_READER_USERNAME }}
  DP_TOKEN: ${{ secrets.DIST_PLAT_READER_TOKEN }}
  KARATE_FOLDER: "e2e/karate"
  GITHUB_REGISTRY: ghcr.io
  MAVEN_OPTS: "-Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn -Ddarwin-telemetry-maven-extension.output=json"
  JACOCO_ADDRESS: "localhost"
  JACOCO_PORT: 64535
  JIRA_USER: "bdmqualityeng"
jobs:
  preconditions-check:
    name: E2E Karate / Preconditions check
    runs-on: ${{ fromJSON(vars.RUNSON_E2E_MAVEN_JAVA_GENERIC || '["citool-icr-aks__code-ubuntu24.04-small"]') }}
    outputs:
      config: ${{ steps.config.outputs.config }}
      config-test-file: ${{ steps.config-test.outputs.file }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Find config_test.yml
        id: config-test
        env:
          CONFIG_TEST_FILE: ${{ env.KARATE_FOLDER }}/src/test/resources/config_test.yml
        run: |
          if [[ -f "${CONFIG_TEST_FILE}" ]]; then
            echo "file=${CONFIG_TEST_FILE}" >> "$GITHUB_OUTPUT"
          else
            echo "config_test.yml file not found"
          fi

      - name: Resolve Configuration
        id: config
        uses: inditex/actions/resolvers/config-resolver@main
        with:
          create-annotations: true
          files: |
            application.yml
            ${{ env.KARATE_FOLDER }}/src/test/resources/platform.yml
            ${{ env.KARATE_FOLDER }}/src/test/resources/platform_test.yml
            ${{ steps.config-test.outputs.file }}
          input-vars: "${{ toJSON(github.event.inputs) }}"

  e2e-karate-local:
    name: E2E Karate / Local
    needs: preconditions-check
    if: ${{ fromJSON(needs.preconditions-check.outputs.config).env == 'local' }}
    timeout-minutes: 60
    runs-on: ${{ fromJSON(vars.RUNSON_E2E_MAVEN_JAVA_DOCKER || '["citool-icr-aks__code-ubuntu24.04-large"]') }}
    env:
      APP_PORT: 8080
      ADAM_EXTERNAL_EXECUTION: 1
    outputs:
      upload-cucumber-report_e2e: ${{ steps.upload-cucumber-report_e2e.outcome }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get runner resources
        id: get-runner-resources
        uses: inditex/gha-citool/get-runner-resources@v0

      - name: Setup Maven Cache
        uses: actions/cache@v4
        continue-on-error: true
        with:
          path: ~/.m2/repository
          key: ${{ steps.get-runner-resources.outputs.RUNNER_OS_FULL_NAME }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ steps.get-runner-resources.outputs.RUNNER_OS_FULL_NAME }}-maven-

      - name: Export ASDF_DATA_DIR (Configure runner 1/5)
        run:
          echo "ASDF_DATA_DIR=$ASDF_DATA_DIR" >> "$GITHUB_ENV"

      - name: Setup asdf Cache (Configure runner 2/5)
        uses: actions/cache@v4
        continue-on-error: true
        with:
          path: ${{ env.ASDF_DATA_DIR }}
          key: ${{ steps.get-runner-resources.outputs.RUNNER_OS_FULL_NAME }}-asdf-${{ hashFiles('**/.tool-versions') }}
          restore-keys: |
            ${{ steps.get-runner-resources.outputs.RUNNER_OS_FULL_NAME }}-asdf-

      - name: Setup IVM plugins (Configure runner 3/5)
        uses: inditex/gha-ivmactions/setup-plugins@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_READER }}
        with:
          tool-versions-directory: code

      - name: Setup IVM environment (Configure runner 4/5)
        uses: inditex/gha-ivmactions/setup-environment@v1
        with:
          tool-versions-directory: code
          distro-branch: ${{ vars.TOOLVER_DISTRO_BRANCH }}

      - name: Check for docker-compose.yml file
        id: check-docker-compose
        working-directory: ${{ env.KARATE_FOLDER }}/src/test/resources
        run: |
          if [ ! -f compose/docker-compose.yml ]; then
            echo "docker-compose.yml not found!"
            echo "compose_file_exists=false" >> "$GITHUB_OUTPUT"
          else
            echo "docker-compose.yml found!"
            echo "compose_file_exists=true" >> "$GITHUB_OUTPUT"
          fi

      - name: Log in to GitHub Registry (Configure runner 5/5)
        if: ${{ steps.check-docker-compose.outputs.compose_file_exists == 'true' }}
        run: echo "${{ secrets.GH_TOKEN_READER }}" | docker login "${GITHUB_REGISTRY}" -u none --password-stdin

      - name: Replace Docker registry URLs
        if: ${{ steps.check-docker-compose.outputs.compose_file_exists == 'true' }}
        working-directory: ${{ env.KARATE_FOLDER }}/src/test/resources/compose
        run: |
          # This step replaces the Docker registry from JFrog to GitHub to reduce build times
          SOURCE_REGISTRY="inditex-docker.jfrog.io"
          TARGET_REGISTRY="ghcr.io\/inditex"

          # Find all the docker-compose files
          compose_files=$(find . -type f -name "docker-compose*.*")

          # Loop through the found files and replace the image names
          for file in $compose_files; do
            echo "+ Replacing image names in $file"
            sed -i "s/$SOURCE_REGISTRY/$TARGET_REGISTRY/g" "$file"
          done

      - name: Compile and create artifact
        working-directory: code
        run: |
          mvn -B clean install \
            -DskipTests -DskipITs -DskipUTs

      - name: Find the application jar file
        working-directory: code
        run: |
          jar_file=$(find . -type f -regex '.*/target\/.*-boot-.*-SNAPSHOT\.jar' -maxdepth 3)

          if [ -z "$jar_file" ]; then
            echo "+ ERROR No jar file found"
            exit 1
          fi

          echo "PATH_TO_JAR=$jar_file" >> "$GITHUB_ENV"

      - name: Launch containers
        id: launch-containers
        if: ${{ vars.LOCAL_CONTAINERS != 'false' && steps.check-docker-compose.outputs.compose_file_exists == 'true' }}
        working-directory: ${{ env.KARATE_FOLDER }}/src/test/resources/compose
        run: |
          echo "+ Launching containers"
          docker compose up -d --wait

          echo "+ List of running containers"
          docker ps

      - name: Download Jacoco Binary
        if: ${{ always() && !cancelled() && fromJSON(needs.preconditions-check.outputs.config).karate.coverage.enabled == true }}
        continue-on-error: true
        working-directory: code
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_READER }}
        run: |
          gh --repo inditex/clr-jacocokarate release download --pattern="jacoco_binary.tar.gz"
          tar -zxvf jacoco_binary.tar.gz

      - name: Start application
        id: start-application
        uses: inditex/gha-citool/run-application@v0
        env:
          HEALTH_CHECK_PATH: ${{ fromJSON(needs.preconditions-check.outputs.config).app.health_probe.http_path }}
        with:
          technology: java
          app-port: ${{ env.APP_PORT }}
          healthcheck-path: ${{ env.HEALTH_CHECK_PATH }}
          java-path-to-jar: ${{ env.PATH_TO_JAR }}
          java-extra-args: --spring.profiles.active=standalone,local
          working-directory: code
          use-jacoco-agent: ${{ fromJSON(needs.preconditions-check.outputs.config).karate.coverage.enabled }}
          jacoco-binary-path: "jacocoka/${{ fromJSON(needs.preconditions-check.outputs.config).karate.coverage.jacoco.version }}"
          jacoco-report-folder: ${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.jacoco_e2e_folder.path }}
          jacoco-includes: ${{ join(fromJSON(needs.preconditions-check.outputs.config).karate.coverage.jacoco.includes, ':') }}
          jacoco-excludes: ${{ join(fromJSON(needs.preconditions-check.outputs.config).karate.coverage.jacoco.excludes, ':') }}
          jacoco-address: ${{ env.JACOCO_ADDRESS }}
          jacoco-port: ${{ env.JACOCO_PORT }}

      - name: Setup IVM plugins
        uses: inditex/gha-ivmactions/setup-plugins@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_READER }}
        with:
          tool-versions-directory: ${{ env.KARATE_FOLDER }}

      - name: Setup IVM environment
        uses: inditex/gha-ivmactions/setup-environment@v1
        with:
          tool-versions-directory: ${{ env.KARATE_FOLDER }}

      - name: Run e2e karate tests
        id: run-tests
        working-directory: ${{ env.KARATE_FOLDER }}
        run: |
          mvn -B clean verify \
            -Dkarate.env="${{ fromJSON(needs.preconditions-check.outputs.config).env }}" \
            -Dkarate.options="${{ fromJSON(needs.preconditions-check.outputs.config).karate.options }}" \
            -DAPP_PORT="${{ env.APP_PORT }}"

      - name: Print logs if compose up or tests failed
        if: ${{ always() && (steps.launch-containers.outcome == 'failure' || steps.run-tests.outcome == 'failure') && steps.check-docker-compose.outputs.compose_file_exists == 'true' }}
        working-directory: ${{ env.KARATE_FOLDER }}/src/test/resources/compose
        run: |
          echo "Compose up failed, printing logs"
          docker compose logs
          mapfile -t services < <(docker --log-level ERROR compose config --services)
          mkdir compose-logs
          for service in "${services[@]}"; do
            docker compose logs "${service}" > "compose-logs/${service}.log"
          done
          echo "Logs have been saved to individual text files."

      - name: Upload logs if compose up or integration tests failed
        if: ${{ always() && (steps.launch-containers.outcome == 'failure' || steps.run-tests.outcome == 'failure') }}
        uses: actions/upload-artifact@v4
        with:
          name: compose-logs
          path: ${{ env.KARATE_FOLDER }}/src/test/resources/compose/compose-logs
          retention-days: 3

      - name: Setup IVM environment
        uses: inditex/gha-ivmactions/setup-environment@v1
        if: ${{ always() && !cancelled() }}
        with:
          tool-versions-directory: code

      - name: Dump jacoco execution data
        if: ${{ always() && fromJSON(needs.preconditions-check.outputs.config).karate.coverage.enabled == true }}
        continue-on-error: true
        working-directory: code
        run: |
          jacoco_e2e_folder_path=$(echo "${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.jacoco_e2e_folder.path }}" | sed -E "s/^code\///" )
          java -jar "jacocoka/${{ fromJSON(needs.preconditions-check.outputs.config).karate.coverage.jacoco.version }}/jacococli.jar" dump \
            --address ${{ env.JACOCO_ADDRESS }} --destfile "$jacoco_e2e_folder_path/jacoco-e2e.exec" \
            --port ${{ env.JACOCO_PORT }} --retry 3

      - name: Stop running application
        if: ${{ always() }}
        continue-on-error: true
        run: |
          kill ${{ steps.start-application.outputs.app-pid }}

      - name: Setup IVM environment
        uses: inditex/gha-ivmactions/setup-environment@v1
        if: ${{ always() && !cancelled() }}
        with:
          tool-versions-directory: ${{ env.KARATE_FOLDER }}

      - name: Generate Surefire HTML report
        if: ${{ always() && !cancelled() && fromJSON(needs.preconditions-check.outputs.config).karate.results.surefire_html_report_folder.path != null }}
        continue-on-error: true
        working-directory: ${{ env.KARATE_FOLDER }}
        run: |
          mvn -B surefire-report:report-only site:site \
            -DoutputName=index \
            -DalwaysGenerateSurefireReport=true \
            -DgenerateReports=false

      - name: Setup IVM environment
        uses: inditex/gha-ivmactions/setup-environment@v1
        if: ${{ always() && !cancelled() }}
        with:
          tool-versions-directory: code
      - name: Generate Jacoco Coverage Report
        if: ${{ always() && !cancelled() && fromJSON(needs.preconditions-check.outputs.config).karate.coverage.enabled == true }}
        working-directory: code
        run: |
          jacoco_e2e_folder_path=$(echo "${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.jacoco_e2e_folder.path }}" | sed -E "s/^code\///" )

          java \
          -jar jacocoka/${{ fromJSON(needs.preconditions-check.outputs.config).karate.coverage.jacoco.version }}/jacococli.jar \
          report "$jacoco_e2e_folder_path/jacoco-e2e.exec" \
          --classfiles "$jacoco_e2e_folder_path/classes" \
          --html "$jacoco_e2e_folder_path" \
          --sourcefiles ${{ join(fromJSON(needs.preconditions-check.outputs.config).karate.coverage.jacoco.sourcefiles, ' --sourcefiles ') }}

      - name: Attach results
        if: ${{ always() && !cancelled() && needs.preconditions-check.outputs.config-test-file != '' && contains(inputs.ATTACH_TARGET, 'jfrog') }}
        continue-on-error: true
        uses: inditex/actions/upload-artifact@main
        with:
          path: ${{ needs.preconditions-check.outputs.config-test-file }}
          keys: karate
        env:
          DP_ARTIFACTS_STORAGE_USER: ${{ secrets.DIST_PLAT_DEPLOYER_USERNAME }}
          DP_ARTIFACTS_STORAGE_TOKEN: ${{ secrets.DIST_PLAT_DEPLOYER_TOKEN }}

      - name: Download ADAM
        if: ${{ always() && !cancelled() }}
        continue-on-error: true
        working-directory: code
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_READER }}
        run: |
          gh --repo inditex/clr-adam release download --pattern="adam" -O adam
          chmod +x adam

      - name: Upload cucumber report
        id: upload-cucumber-report_e2e
        if: ${{ always() && !cancelled() && fromJSON(needs.preconditions-check.outputs.config).karate.coverage.enabled == true }}
        uses: actions/upload-artifact@v4
        with:
          name: "${{ github.workflow }}_${{github.run_id}}_${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.karate_report_folder.artifact-name }}"
          path: ${{ github.workspace }}/${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.cucumber_xray_file.path }}

      - name: Publish E2E test results to DEVINS
        if: ${{ always() && !cancelled() }}
        continue-on-error: true
        working-directory: code
        env:
          GITHUB_API_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          E2E_WF_ENVIRONMENT: ${{ fromJSON(needs.preconditions-check.outputs.config).env }}
          E2E_WF_PLATFORM_UPLOAD: "${{ fromJSON(needs.preconditions-check.outputs.config).xray.testplanid != null && fromJSON(needs.preconditions-check.outputs.config).xray.testplanid != '' && contains(inputs.ATTACH_TARGET, 'xray') }}"
        run: |
          ./adam publishE2ETest \
            --projectKey="${{ fromJSON(needs.preconditions-check.outputs.config).metadata.project_key }}" \
            --workingDir="${{ github.workspace }}/${{ env.KARATE_FOLDER }}" \
            --testType="KARATE" \
            --reportPath="${{ github.workspace }}/${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.cucumber_xray_file.path }}" \
            --surefireReportRootPath="${{ github.workspace }}/**/${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.surefire_xml_report_path.path }}" \
            --karateReportPathJacoco="${{ github.workspace }}/${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.jacoco_e2e_folder.path }}/index.html" \
            --additionalParams=${{ toJson(github.event.inputs.ADDITIONAL_PARAMETERS) }} \
            --testResult=${{ steps.run-tests.outcome }}

      - name: Display results
        if: ${{ always() && !cancelled() }}
        continue-on-error: true
        run: |
          if [ -f "code/message" ]
          then
              MESSAGE=$(cat "code/message" 2>/dev/null)
              MESSAGE=$(echo "$MESSAGE" | tr -s ' ')
              echo "::notice title=Results [jacoco] ::$MESSAGE"
          fi
          if [ -f "code/messageSurefire" ]
          then
              MESSAGE=$(cat "code/messageSurefire" 2>/dev/null)
              MESSAGE=$(echo "$MESSAGE" | tr -s ' ')
              echo "::notice title=Results [karate] ::$MESSAGE"
          fi

      - name: Create a Workflow Status Check to link this execution with a PR
        if: always() && !cancelled()
        continue-on-error: true
        uses: inditex/gha-citool/create-status-check@v0
        with:
          app_id: ${{ vars.WFSC_APP_ID }}
          app_private_key: ${{ secrets.WFSC_APP_PKEY }}
          name: "code-QA-e2e-karate-${{ fromJSON(needs.preconditions-check.outputs.config).env }}"
          summary: |
            This status check represents the conclusion (__${{ job.status }}__) of the last dispatched execution of workflow:
            `${{ github.workflow_ref }}`

            You can find more details in the link below :arrow_down:

  e2e-karate-local-reports:
    name: E2E Karate / Local (reports)
    needs: [preconditions-check, e2e-karate-local]
    if: ${{ always() && needs.e2e-karate-local.outputs.upload-cucumber-report_e2e == 'success' && fromJSON(needs.preconditions-check.outputs.config).env == 'local' && needs.preconditions-check.outputs.config-test-file != '' && fromJSON(needs.preconditions-check.outputs.config).xray.testplanid != null && fromJSON(needs.preconditions-check.outputs.config).xray.testplanid != '' }}
    timeout-minutes: 15
    runs-on: ${{ fromJSON(vars.RUNSON_E2E_MAVEN_JAVA_ONPREM || '["citool-icr__qa-ubuntu24.04"]') }}
    steps:
      - name: Download generated reports
        uses: actions/download-artifact@v4
        if: ${{ always() && needs.e2e-karate-local.outputs.upload-cucumber-report_e2e == 'success' && fromJSON(needs.preconditions-check.outputs.config).env == 'local' && needs.preconditions-check.outputs.config-test-file != '' && fromJSON(needs.preconditions-check.outputs.config).xray.testplanid != null && fromJSON(needs.preconditions-check.outputs.config).xray.testplanid != '' }}
        with:
          name: "${{ github.workflow }}_${{github.run_id}}_${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.karate_report_folder.artifact-name }}"
          path: "."

      - name: Prepare directory structure
        if: ${{ always() && !cancelled() }}
        continue-on-error: true
        working-directory: ${{ github.workspace }}
        run: |
          XRAY_REPORT_FOLDER="$(dirname ${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.cucumber_xray_file.path }})"
          XRAY_REPORT_FILE="$(basename ${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.cucumber_xray_file.path }})"
          mkdir -p "$XRAY_REPORT_FOLDER"
          mv "$XRAY_REPORT_FILE" "$XRAY_REPORT_FOLDER"

      - name: Get QA credentials
        if: ${{ always() && !cancelled() && needs.preconditions-check.outputs.config-test-file != '' && fromJSON(needs.preconditions-check.outputs.config).xray.testplanid != null && contains(inputs.ATTACH_TARGET, 'xray') }}
        continue-on-error: true
        uses: inditex/gha-testing/get-qa-credentials@main
        with:
          cyberark_oauth_clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark_oauth_secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          secrets_list: "JIRA_PASS"

      - name: Publish E2E test results to XRAY
        if: ${{ always() && !cancelled() && needs.preconditions-check.outputs.config-test-file != '' && fromJSON(needs.preconditions-check.outputs.config).xray.testplanid != null && contains(inputs.ATTACH_TARGET, 'xray') }}
        continue-on-error: true
        uses: inditex/gha-testing/xray-reporter@main
        with:
          path: ${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.cucumber_xray_file.path }}
          test-plan-id: ${{ fromJSON(needs.preconditions-check.outputs.config).xray.testplanid }}
          jira-username: ${{ env.JIRA_USER }}
          jira-password: ${{ env.CYBERARK_CREDENTIALS_JIRAPASSWORDQA }}
          assignee: ${{ fromJSON(needs.preconditions-check.outputs.config).xray.assignee }}
          description: "${{ fromJSON(needs.preconditions-check.outputs.config).metadata.project_key }}-${{github.workflow}}-${{ github.run_number }}-${{ fromJSON(needs.preconditions-check.outputs.config).env }}"
          testEnvironments: ${{ fromJSON(needs.preconditions-check.outputs.config).env }}

  e2e-karate-remote:
    name: E2E Karate / Remote
    needs: preconditions-check
    if: ${{ fromJSON(needs.preconditions-check.outputs.config).env != 'local' }}
    timeout-minutes: 60
    runs-on: ${{ fromJSON(vars.RUNSON_E2E_MAVEN_JAVA_ONPREM || '["citool-icr__qa-ubuntu24.04"]') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup IVM environment
        uses: inditex/gha-ivmactions/setup-environment@v1
        with:
          tool-versions-directory: ${{ env.KARATE_FOLDER }}

      - name: Resolve credentials
        uses: inditex/gha-paasdeployment/creds-resolver@v0
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          path-to-secrets: ${{ env.KARATE_FOLDER }}/src/test/resources/secret_test.yml
          output-format: maven

      - name: Run e2e karate tests
        id: run-tests
        working-directory: ${{ env.KARATE_FOLDER }}
        run: |
          mvn -B clean verify \
            -Dkarate.env="${{ fromJSON(needs.preconditions-check.outputs.config).env }}" \
            -Dkarate.options="${{ fromJSON(needs.preconditions-check.outputs.config).karate.options }}" \
            ${{ env.CREDS }}

      - name: Generate Cluecumber report
        if: ${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.cluecumber_report_folder.path != null }}
        continue-on-error: true
        working-directory: ${{ env.KARATE_FOLDER }}
        run: |
          mvn -B cluecumber-report:reporting

      - name: Generate Surefire HTML report
        if: ${{ always() && !cancelled() && fromJSON(needs.preconditions-check.outputs.config).karate.results.surefire_html_report_folder.path != null }}
        continue-on-error: true
        working-directory: ${{ env.KARATE_FOLDER }}
        run: |
          mvn -B surefire-report:report-only site:site \
            -DoutputName=index \
            -DalwaysGenerateSurefireReport=true \
            -DgenerateReports=false

      - name: Attach results
        if: ${{ always() && !cancelled() && needs.preconditions-check.outputs.config-test-file != '' && contains(inputs.ATTACH_TARGET, 'jfrog') }}
        continue-on-error: true
        uses: inditex/actions/upload-artifact@main
        with:
          path: ${{ needs.preconditions-check.outputs.config-test-file }}
          keys: karate
        env:
          DP_ARTIFACTS_STORAGE_USER: ${{ secrets.DIST_PLAT_DEPLOYER_USERNAME }}
          DP_ARTIFACTS_STORAGE_TOKEN: ${{ secrets.DIST_PLAT_DEPLOYER_TOKEN }}

      - name: Get QA credentials
        if: ${{ always() && !cancelled() && needs.preconditions-check.outputs.config-test-file != '' && fromJSON(needs.preconditions-check.outputs.config).xray.testplanid != null && contains(inputs.ATTACH_TARGET, 'xray') }}
        continue-on-error: true
        uses: inditex/gha-testing/get-qa-credentials@main
        with:
          cyberark_oauth_clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark_oauth_secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          secrets_list: "JIRA_PASS"

      - name: Publish E2E test results to XRAY
        if: ${{ always() && !cancelled() && needs.preconditions-check.outputs.config-test-file != '' && fromJSON(needs.preconditions-check.outputs.config).xray.testplanid != null && contains(inputs.ATTACH_TARGET, 'xray') }}
        continue-on-error: true
        uses: inditex/gha-testing/xray-reporter@main
        with:
          path: ${{ github.workspace }}/${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.cucumber_xray_file.path }}
          test-plan-id: ${{ fromJSON(needs.preconditions-check.outputs.config).xray.testplanid }}
          jira-username: ${{ env.JIRA_USER }}
          jira-password: ${{ env.CYBERARK_CREDENTIALS_JIRAPASSWORDQA }}
          assignee: ${{ fromJSON(needs.preconditions-check.outputs.config).xray.assignee }}
          description: "${{ fromJSON(needs.preconditions-check.outputs.config).metadata.project_key }}-${{github.workflow}}-${{ github.run_number }}-${{ fromJSON(needs.preconditions-check.outputs.config).env }}"
          testEnvironments: ${{ fromJSON(needs.preconditions-check.outputs.config).env }}

      - name: Publish E2E test results to DEVINS
        if: ${{ always() && !cancelled() }}
        working-directory: code
        continue-on-error: true
        env:
          GITHUB_API_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          E2E_WF_ENVIRONMENT: ${{ fromJSON(needs.preconditions-check.outputs.config).env }}
          E2E_WF_PLATFORM_UPLOAD: "${{ fromJSON(needs.preconditions-check.outputs.config).xray.testplanid != null && fromJSON(needs.preconditions-check.outputs.config).xray.testplanid != '' && contains(inputs.ATTACH_TARGET, 'xray') }}"
        run: |
          adam publishE2ETest \
            --projectKey="${{ fromJSON(needs.preconditions-check.outputs.config).metadata.project_key }}" \
            --workingDir="${{ github.workspace }}/${{ env.KARATE_FOLDER }}" \
            --testType="KARATE" \
            --reportPath="${{ github.workspace }}/${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.cucumber_xray_file.path }}" \
            --surefireReportRootPath="${{ github.workspace }}/**/${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.surefire_xml_report_path.path }}" \
            --karateReportPathJacoco="${{ github.workspace }}/${{ fromJSON(needs.preconditions-check.outputs.config).karate.results.jacoco_e2e_folder.path }}/index.html" \
            --additionalParams=${{ toJson(github.event.inputs.ADDITIONAL_PARAMETERS) }} \
            --testResult=${{ steps.run-tests.outcome }}

      - name: Display results
        if: ${{ always() && !cancelled() }}
        working-directory: code
        continue-on-error: true
        run: |
          if [ -f "message" ]
          then
              MESSAGE=$(cat "message" 2>/dev/null)
              MESSAGE=$(echo "$MESSAGE" | tr -s ' ')
              echo "::notice title=Results [jacoco] ::$MESSAGE"
          fi
          if [ -f "messageSurefire" ]
          then
              MESSAGE=$(cat "messageSurefire" 2>/dev/null)
              MESSAGE=$(echo "$MESSAGE" | tr -s ' ')
              echo "::notice title=Results [karate] ::$MESSAGE"
          fi

      - name: Create a Workflow Status Check to link this execution with a PR
        if: always() && !cancelled()
        continue-on-error: true
        uses: inditex/gha-citool/create-status-check@v0
        with:
          app_id: ${{ vars.WFSC_APP_ID }}
          app_private_key: ${{ secrets.WFSC_APP_PKEY }}
          name: "code-QA-e2e-karate-${{ fromJSON(needs.preconditions-check.outputs.config).env }}"
          summary: |
            This status check represents the conclusion (__${{ job.status }}__) of the last dispatched execution of workflow:
            `${{ github.workflow_ref }}`

            You can find more details in the link below :arrow_down:
