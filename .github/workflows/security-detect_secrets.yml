---
name: security-detect-secrets

on:
  workflow_dispatch:
  pull_request:
    paths:
      - '**'
    types: [opened, synchronize, closed]

env:
  SECRETS_FILE: secrets.json
  WORKFLOW_VERSION: 2.4.0
  CHANGED_FILES_FILENAME: dsecrets_changed_files.txt

jobs:
  detect-secrets-pr:
    if: ${{ github.event_name == 'pull_request' && github.event.pull_request.merged != true }}
    name: DSecrets / PR
    timeout-minutes: 30
    runs-on: ${{ fromJSON(vars.RUNSON_DSECRETS_ONPREM || '["self-hosted", "security"]') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.after }}

      - name: Check for changed files in PRs
        id: pr-files
        uses: dorny/paths-filter@ebc4d7e9ebcb0b1eb21480bb8f43113e996ac77a
        with:
          list-files: shell
          filters: |
            changed:
              - added|modified: '**'

      - name: Check changed files
        if: steps.pr-files.outputs.changed != 'true'
        run: |
          echo "::warning ::No added or modified files in this pull request!"

      - name: Create file with changed files
        id: list-filepath
        if: steps.pr-files.outputs.changed == 'true'
        shell: bash
        run: |
          echo "${{ steps.pr-files.outputs.changed_files }}" > "$CHANGED_FILES_FILENAME"

      - name: Detect secrets
        id: detect-secrets
        if: steps.pr-files.outputs.changed == 'true'
        uses: inditex/gha-dsecretsactions/partial-scan@main
        with:
          changed-files-path: ${{ env.CHANGED_FILES_FILENAME }}

      - name: Save artifacts
        if: ${{ always() && !cancelled() }}
        uses: actions/upload-artifact@v4
        with:
          name: detected-secrets
          path: ${{ env.SECRETS_FILE }}
          retention-days: 7
          if-no-files-found: ignore

      - name: Notify secrets
        if: ${{ always() && !cancelled() }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        uses: inditex/gha-dsecretsactions/feedback@main
        with:
          new-secrets: ${{ steps.detect-secrets.outputs.new-secrets }}

  dsecrets-analysis-dojo:
    if: ${{ (github.event_name == 'workflow_dispatch') || (github.event.pull_request.merged == true && ( (vars.DEVELOPMENT_FLOW == 'trunk-based-development' && (github.ref_name == 'main' || startsWith(github.ref_name, 'main-'))) || (vars.DEVELOPMENT_FLOW != 'trunk-based-development' && (github.ref_name == 'develop' || startsWith(github.ref_name, 'develop-'))) ) ) }}
    runs-on: ${{ fromJSON(vars.RUNSON_DSECRETS_ONPREM || '["self-hosted", "security"]') }}
    name: DSecrets / DefectDojo
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.repository.default_branch }}

      - name: Get Artifact metadata
        id: artifact-metadata
        uses: inditex/gha-defectdojoactions/metadata@main
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Secrets scanning
        uses: inditex/gha-dsecretsactions/scan@main
        with:
          project-key: ${{ steps.artifact-metadata.outputs.project-key }}

      - name: Import Detect Secrets data to DefectDojo
        uses: inditex/gha-defectdojoactions/import@main
        with:
          project-key: ${{ steps.artifact-metadata.outputs.project-key }}
          product-id: ${{ steps.artifact-metadata.outputs.product-id }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
