---
name: paas-gossip-analysis
run-name: gossip analysis with runner ${{ inputs.RUNNER || 'RELEASE' }}

concurrency:
  group: paas-gossip-analysis-${{ github.event.pull_request.number }}
  cancel-in-progress: true

on:
  pull_request:
    paths:
      - 'paas/**'
      - '.github/workflows/paas*'
      - 'code/**'
    types: [opened, synchronize, ready_for_review, reopened]
  workflow_dispatch:
    inputs:
      RUNNER:
        description: 'Runner'
        required: true
        type: choice
        options:
          - SNAPSHOT
          - RELEASE
        default: 'SNAPSHOT'

env:
  WORKFLOW_VERSION: 3.4.1
  SUPRAENVIRONMENT: pro
  APP_PATH: /opt/inditex/runner-volume/tools/gossip/releases/
  TMP_PATH: /tmp/gossip/sre
  CEPH_ACCESS_KEY_ID: cyberark.IE-N-DIG-INFRA-CEPH.Application-ITX-APP-500-SC-Generic-ieec1cs1rdgwlb1.central.inditex.grp-ieec-gossip_accesskey
  CEPH_SECRET_ACCESS_KEY: cyberark.IE-N-DIG-INFRA-CEPH.Application-ITX-APP-500-SC-Generic-ieec1cs1rdgwlb1.central.inditex.grp-ieec-gossip_secretkey
  SSCC_URL: https://apigw.apps.axsvocp1.service.inditex.grp/
  OAUTH_GOSSIP_USERNAME: oauthtoolgossippro
  OAUTH_GOSSIP_PASSWORD: cyberark.AX-N-PUBLIC-SRE-TOOLS.Application-ITX-APP-500-SC-Generic-OAUTH2ClientsPRO-oauthtoolgossippro

jobs:
  gossip-analysis:
    name: Gossip / Analysis
    timeout-minutes: 30
    if: ${{ (github.event.pull_request.draft == true && (vars.PR_VERIFY_RUNS_ON_DRAFT != 'false' || contains(github.event.pull_request.labels.*.name, 'pr-verify/force-on-draft'))) || github.event.pull_request.draft == false }}
    runs-on: ${{ fromJSON(vars.RUNSON_GOSSIP_ONPREM || '["sreteam-icr__sre-ivm"]') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Setup Paas CLI
        id: setup-cli
        uses: inditex/gha-paasdeployment/setup-cli@v0

      - name: "Setup Gossip env"
        id: gossip-setup
        uses: inditex/gha-sreactions/gossip/gossip-setup@gossip-v13
        with:
          version-type: ${{ github.event.inputs.RUNNER == 'SNAPSHOT' && 'SNAPSHOT' || 'RELEASE' }}

      - name: Set APP_PATH env variable
        run: echo "APP_PATH=${{ steps.gossip-setup.outputs.path }}" >> "$GITHUB_ENV"

      - name: "Check workflow appliance"
        uses: inditex/gha-sreactions/gossip/gossip-environment@gossip-v13
        with:
          workflow-type: "paas"

      - name: "Check pro environment"
        if: ${{ env.APPLYING_WORKFLOW }}
        shell: python
        run: |
          import yaml
          import os
          with open('paas/deployments.yml') as f:
            deployments = yaml.safe_load(f)
            env_list = deployments['supraenvironments']
            for current in env_list:
              if current['name'] == 'pro':
                env_file = os.getenv('GITHUB_ENV')
                with open(env_file, "a") as githubfile:
                  githubfile.write("HAS_PRO=true")

      - name: "Get metadata"
        id: metadata
        if: ${{ env.HAS_PRO && env.APPLYING_WORKFLOW }}
        shell: python
        run: |
          import yaml
          import os
          with open('application.yml') as f:
            application = yaml.safe_load(f)
          env_file = os.getenv('GITHUB_ENV')
          with open(env_file, "a") as githubfile:
             githubfile.write("PROJECT_KEY=" + application["metadata"]["project_key"])

      - name: "Launch Gossip Config Generator"
        if: ${{ env.HAS_PRO && env.APPLYING_WORKFLOW }}
        uses: inditex/gha-sreactions/gossip/gossip-config@gossip-v13
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          project-key: ${{ env.PROJECT_KEY }}
          ceph-access-key-id: ${{ env.CEPH_ACCESS_KEY_ID }}
          ceph-secret-access-key: ${{ env.CEPH_SECRET_ACCESS_KEY }}
          gossipsrv-url: ${{ env.SSCC_URL }}
          gossipsrv-user: ${{ env.OAUTH_GOSSIP_USERNAME }}
          gossipsrv-pass: "${{ env.OAUTH_GOSSIP_PASSWORD }}"
          tmp-path: ${{ env.TMP_PATH }}
          paas-cli-path: ${{ steps.setup-cli.outputs.path }}

      - name: "Merge Config Files"
        if: ${{ env.HAS_PRO && env.APPLYING_WORKFLOW }}
        run: |
          "$APP_PATH" merge -p "${GITHUB_WORKSPACE}/paas" -d "$TMP_PATH"/target -s "$SUPRAENVIRONMENT" -c "${GITHUB_WORKSPACE}/code"

      - name: "Download CEPH Rules"
        if: ${{ env.HAS_PRO && env.APPLYING_WORKFLOW }}
        run: |
          "$APP_PATH" download -i releases/latest.zip -d "$TMP_PATH"/rules

      - name: "Set variables for Release"
        if: ${{ github.base_ref == 'main' && env.APPLYING_WORKFLOW }}
        run: |
          echo "BLOCKED=--blocked" >> "$GITHUB_ENV"

      - name: "Gossip analyze"
        if: ${{ env.HAS_PRO && env.APPLYING_WORKFLOW }}
        run: |
          "$APP_PATH" analyze -r "$TMP_PATH"/rules -t "$TMP_PATH"/target -n "$RULE_NAMESPACE" -d "$TMP_PATH"/data.json -x "$TMP_PATH"/result -l "$TMP_PATH"/appreport.html.tmpl "$BLOCKED"

      - name: "Upload Gossip analysis results"
        if: ${{ always() && !cancelled() && env.HAS_PRO && env.APPLYING_WORKFLOW }}
        uses: actions/upload-artifact@v4
        with:
          name: Gossip Analysis
          path: ${{ env.TMP_PATH }}/result
          if-no-files-found: warn

      - name: "Send Report Event"
        if: ${{ always() && !cancelled() && env.HAS_PRO && env.APPLYING_WORKFLOW }}
        uses: inditex/gha-sreactions/gossip/gossip-event@gossip-v13
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          tool-report-url: ${{ env.SSCC_URL}}
          tool-report-username: ${{ env.OAUTH_GOSSIP_USERNAME }}
          tool-report-password: ${{ env.OAUTH_GOSSIP_PASSWORD}}
          result: ${{ env.TMP_PATH }}/result
          branch: ${{ github.base_ref || github.event.repository.default_branch }}
          paas-cli-path: ${{ steps.setup-cli.outputs.path }}
