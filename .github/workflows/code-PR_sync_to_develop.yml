---
name: code-PR-sync-to-develop

on:
  pull_request:
    types: [opened, closed]
    branches: ['main', 'main-*']
    paths-ignore: ['code/**', 'docs/**']

env:
  WORKFLOW_VERSION: 2.35.0

jobs:
  add-friendly-reminder:
    name: Add Friendly Reminder Comment
    if: github.head_ref != 'develop' && !startsWith(github.head_ref, 'develop-') && vars.DEVELOPMENT_FLOW != 'trunk-based-development'
    timeout-minutes: 30
    runs-on: ${{ fromJSON(vars.RUNSON_CODE_GENERIC || '["ubuntu-24.04"]') }}
    outputs:
      detected: ${{ steps.changes.outputs.paths }}
      develop-branch: ${{ steps.sync-branch.outputs.DEVELOP_BRANCH }}
      sync-branch: ${{ steps.sync-branch.outputs.SYNC_BRANCH }}
      main-branch: ${{ steps.sync-branch.outputs.MAIN_BRANCH }}
    steps:
      - name: Check for changed files in specific paths
        id: changes
        uses: dorny/paths-filter@ebc4d7e9ebcb0b1eb21480bb8f43113e996ac77a
        with:
          filters: |
            paths:
              - 'code/**'
              - 'docs/**'

      - name: Calculate SYNC, DEVELOP and MAIN branches
        id: sync-branch
        run: |
          BASELINE_BRANCH=${{ github.base_ref }}
          DEVELOP_BRANCH=${BASELINE_BRANCH/main/develop}
          {
            echo "DEVELOP_BRANCH=$DEVELOP_BRANCH"
            echo "SYNC_BRANCH=automated/sync-from-$BASELINE_BRANCH-to-$DEVELOP_BRANCH"
            echo "MAIN_BRANCH=$BASELINE_BRANCH"
          } >> "$GITHUB_OUTPUT"

      - name: Checkout
        if: steps.changes.outputs.paths == 'false' && github.event.pull_request.merged == false
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Add PR comment - Friendly reminder
        if: steps.changes.outputs.paths == 'false' && github.event.pull_request.merged == false
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          sync_branch="${{ steps.sync-branch.outputs.SYNC_BRANCH }}"
          develop_branch="${{ steps.sync-branch.outputs.DEVELOP_BRANCH }}"
          if [[ -z $(git ls-remote --heads origin $sync_branch) ]]; then
            body="
            ### :eyes: Friendly reminder
            - When this **pull request has been merged, its commits will be synchronized** from an automated pull request (\`$sync_branch → $develop_branch\`).
            "
          else
            pull_request=$(gh api "/repos/${{ github.repository }}/pulls" | jq -r ".[] | select(.head.ref==\"$sync_branch\") | .number")
            if [[ -n $pull_request ]]; then
              body="
            ### :eyes: Friendly reminder
            - When this **pull request has been merged, its commits will be synchronized** from an existent automated pull request [\`$sync_branch → $develop_branch\`](https://github.com/${{ github.repository }}/pull/$pull_request), rebasing the branch with the new changes introduced.
            "
            else
              body="
            ### :eyes: Friendly reminder
            - When this **pull request has been merged, its commits will be synchronized** from an automated pull request (\`$sync_branch → $develop_branch\`) rebasing the previous existent branch with the new changes introduced.
            "
            fi
          fi
          gh pr comment ${{ github.event.number }} --body "$body"

  sync-to-develop:
    name: Code / Sync To Develop Branch
    timeout-minutes: 30
    needs: add-friendly-reminder
    if: needs.add-friendly-reminder.outputs.detected == 'false' && github.event.pull_request.merged == true && vars.DEVELOPMENT_FLOW != 'trunk-based-development'
    runs-on: ${{ fromJSON(vars.RUNSON_CODE_GENERIC || '["ubuntu-24.04"]') }}
    concurrency:
      group: ${{ github.workflow }}-${{ github.job }}
      cancel-in-progress: true
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.base_ref }}

      - name: Get existent branches and pull requests from repository
        id: get-info
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          sync_branch="${{ needs.add-friendly-reminder.outputs.sync-branch }}"
          if [[ -n $(git ls-remote --heads origin $sync_branch) ]]; then
            pull_request=$(gh api "/repos/${{ github.repository }}/pulls" | jq -r ".[] | select(.head.ref==\"$sync_branch\") | .number")
            echo "PULL_REQUEST=$pull_request" >> "$GITHUB_OUTPUT"
          fi

      - name: Commit changes
        id: commit
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_PUSH }}
        run: |
          develop="${{ needs.add-friendly-reminder.outputs.develop-branch }}"
          if [[ -z $(git ls-remote --heads origin $develop) ]]; then
            # Avoid creating sync PR if the corresponding development branch does not exist
            echo "The $develop branch does not exist in remote. Skipping the creation of the sync PR"
          else
            sync_branch="${{ needs.add-friendly-reminder.outputs.sync-branch }}"
            main_branch="${{ needs.add-friendly-reminder.outputs.main-branch }}"

            if [[ -n $(git ls-remote --heads origin $sync_branch) ]]; then
              git checkout "$sync_branch"
              git rebase $main_branch --no-verify
              git push --no-verify -u origin HEAD
              echo "EXIST_BRANCH=True" >> "$GITHUB_OUTPUT"
            else
              git checkout -b "$sync_branch"
              git push --no-verify -u origin HEAD
              echo "EXIST_BRANCH=False" >> "$GITHUB_OUTPUT"
            fi
          fi

      - name: Create PR body
        id: pr-body
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          pr_body="**Automated Pull Request** related to:"
          pr_body="$pr_body"$'\n'"- #${{ github.event.pull_request.number }}"

          delimiter="$(openssl rand -hex 8)"
          echo "PR_BODY<<${delimiter}" >> "$GITHUB_OUTPUT"
          echo "$pr_body" >> "$GITHUB_OUTPUT"
          echo "${delimiter}" >> "${GITHUB_OUTPUT}"

      - name: Create Automated PR
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_PUSH }}
        run: |
          develop="${{ needs.add-friendly-reminder.outputs.develop-branch }}"
          pull_request="${{ steps.get-info.outputs.PULL_REQUEST }}"
          main_branch="${{ needs.add-friendly-reminder.outputs.main-branch }}"
          pr_body="${{ steps.pr-body.outputs.PR_BODY }}"

          if [[ $pull_request != "" ]]; then
              gh pr edit $pull_request -b "$pr_body"
          else
            gh pr create --base "$develop" \
              --title "Sync from $main_branch to $develop" \
              --label 'kind/internal' \
              --body "$pr_body"
          fi

      - name: Add PR comment - On Failure
        if: ${{ failure() && !cancelled() }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          gh pr comment ${{ github.event.number }} --body "
          ### :exclamation: :exclamation: :exclamation: Sync to develop failure
          - See the [workflow log](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}).
          "
