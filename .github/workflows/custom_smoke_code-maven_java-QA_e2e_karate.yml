name: custom_smoke-code-maven-QA-e2e-karate
on:
  workflow_dispatch:
env:
  WORKFLOW_VERSION: CUSTOM

jobs:
  code-maven-QA-e2e-karate:
    name: code-maven-QA-e2e-karate
    runs-on: [ self-hosted, testing-ivm ]
    steps:
      - name: code-maven-QA-e2e-karate
        uses: benc-uk/workflow-dispatch@4c044c1613fabbe5250deadc65452d54c4ad4fc7
        with:
          workflow: code-maven-QA-e2e-karate
          inputs: |
            {
              "ENV": "pro",
              "KARATE_OPTIONS": "-t @smoke"
            }
          token: ${{ secrets.GH_TOKEN_PUSH }}
          ref: ${{ github.base_ref }}
