---
name: code-maven-QA-newman

on:
  workflow_dispatch:
    inputs:
      ENV:
        description: |
          ENV: Target environment for Postman execution by selecting the env file.
          'local' is used by default
        required: false
        default: 'local'
      NUMBER_ITERATIONS:
        description: Specify the number of times for the collection to run.
        default: 1
        required: false
        type: number
      ADDITIONAL_PARAMETERS:
        description: |
          ADDITIONAL_PARAMETERS: Specify additional parameters for the workflow execution in the json format.
        required: false
        default: '{"REQUEST_URL":"", "PR_URL":""}'


concurrency:
  group: qa-api-testing-newman-${{ github.ref }}
  cancel-in-progress: true

env:
  WORKFLOW_VERSION: 6.4.0
  DP_USERNAME: ${{ secrets.DIST_PLAT_READER_USERNAME }}
  DP_TOKEN: ${{ secrets.DIST_PLAT_READER_TOKEN }}
  DT_ENDPOINT: ${{ secrets.DT_ENDPOINT }}
  DT_SECRET: ${{ secrets.DT_SECRET }}
  GITHUB_REGISTRY: ghcr.io
  RESOURCES_PATH: code/src/test/resources/compose
  CONFIG_PATH: code/src/test/resources
  NEWMAN_VERSION: 6.0.0
  NEWMAN_REPORTER_HTML-EXTRA_VERSION: 1.22.11
  NEWMAN_REPORTER_JUNIT-FULL_VERSION: 1.1.1

jobs:
  newman-settings:
    name: Newman Settings
    timeout-minutes: 30
    runs-on: ${{ fromJSON( vars.RUNSON_QADYNAMICTESTS_ONPREM || '["citool-icr__code-ubuntu24.04-large"]' ) }}
    outputs:
      config: ${{ steps.config.outputs.config }}
      environment: ${{ steps.checkenv.outputs.environment }}
      creds: ${{ env.CREDS }}
      app_port: ${{ fromJSON(steps.config.outputs.config).app.app_port }}
      http_path: ${{ fromJSON(steps.config.outputs.config).app.health_probe.http_path }}
      binary: ${{ fromJSON(steps.config.outputs.config).app.binary }}
      project_key: ${{ fromJSON(steps.config.outputs.config).metadata.project_key }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Find config_test.yml
        id: config-test
        env:
          CONFIG_TEST_FILE: ${{ env.CONFIG_PATH }}/config_test.yml
        run: |
          if [[ -f "${CONFIG_TEST_FILE}" ]]; then
            echo "file=${CONFIG_TEST_FILE}" >> "$GITHUB_OUTPUT"
          else
            echo "config_test.yml file not found"
          fi

      - name: Resolve Configuration
        id: config
        uses: inditex/actions/resolvers/config-resolver@main
        with:
          create-annotations: true
          files: |
            application.yml
            ${{ env.CONFIG_PATH }}/platform.yml
            ${{ env.CONFIG_PATH }}/platform_test.yml
            ${{ steps.config-test.outputs.file }}
          input-vars: "${{ toJSON(github.event.inputs) }}"

      - name: Check environment
        id: checkenv
        run: |
          if [[ "${{ github.event.inputs.env }}" != "" ]]; then
            env="${{ github.event.inputs.env }}"
          else
            env="local"
          fi
          echo "environment=$env" >> "$GITHUB_OUTPUT"

      - name: Validate number iterations input
        run: |
          if ! [[ "${{ github.event.inputs.NUMBER_ITERATIONS }}" =~ ^[0-9]+$ ]]; then
            echo "Error: Number iterations is not a valid integer. Please, set a value >= 1."
            exit 1
          fi
        shell: bash

  newman-tests:
    name: Newman Tests
    timeout-minutes: 30
    needs: newman-settings
    if: ${{ needs.newman-settings.outputs.environment == 'local'}}
    runs-on: ${{ fromJSON( vars.RUNSON_QADYNAMICTESTS_MAVEN_JAVA_DOCKER || '["ubuntu-24.04"]' ) }}
    env:
      ASDF_DIR: /opt/asdf
      ASDF_DATA_DIR: /opt/asdf/data
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install asdf
        env:
          ASDF_VERSION: v0.14.0
        run: |
          git clone --depth 1 --branch "$ASDF_VERSION" https://github.com/asdf-vm/asdf.git "$ASDF_DIR"
          echo "BASH_ENV=$ASDF_DIR/asdf.sh" >> "$GITHUB_ENV"
          echo "$ASDF_DIR/bin" >> "$GITHUB_PATH"
          echo "$ASDF_DIR/shims" >> "$GITHUB_PATH"
          mkdir -p "$ASDF_DATA_DIR"

      - name: Setup settings.xml
        run: |
          mkdir -p ~/.m2
          echo "${{ vars.M2_SETTINGS_TEMPLATE }}" > ~/.m2/settings.xml

      - name: Get runner resources
        id: get-runner-resources
        uses: inditex/gha-citool/get-runner-resources@v0

      - name: Setup Maven Cache
        uses: actions/cache@v4
        continue-on-error: true
        with:
          path: ~/.m2/repository
          key: ${{ steps.get-runner-resources.outputs.RUNNER_OS_FULL_NAME }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ steps.get-runner-resources.outputs.RUNNER_OS_FULL_NAME }}-maven-

      - name: Setup asdf Cache
        uses: actions/cache@v4
        continue-on-error: true
        with:
          path: ${{ env.ASDF_DATA_DIR }}
          key: ${{ steps.get-runner-resources.outputs.RUNNER_OS_FULL_NAME }}-asdf-${{ hashFiles('**/.tool-versions') }}
          restore-keys: |
            ${{ steps.get-runner-resources.outputs.RUNNER_OS_FULL_NAME }}-asdf-

      - name: Setup IVM plugins
        uses: inditex/gha-ivmactions/setup-plugins@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_READER }}
        with:
          tool-versions-directory: code

      - name: Setup IVM environment
        uses: inditex/gha-ivmactions/setup-environment@v1
        with:
          tool-versions-directory: code
          distro-branch: ${{ vars.TOOLVER_DISTRO_BRANCH }}

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 16

      - name: Install Newman extras
        run: |
          npm install -g \
            newman@${{ env.NEWMAN_VERSION }} \
            newman-reporter-htmlextra@${{ env.NEWMAN_REPORTER_HTML-EXTRA_VERSION }} \
            newman-reporter-junitfull@${{ env.NEWMAN_REPORTER_JUNIT-FULL_VERSION }}

      - name: Check for docker-compose.yml file
        id: check-docker-compose
        working-directory: ${{ env.RESOURCES_PATH }}
        run: |
          if [ ! -f docker-compose.yml ]; then
            echo "docker-compose.yml not found!"
            echo "compose_file_exists=false" >> "$GITHUB_OUTPUT"
          else
            echo "docker-compose.yml found!"
            echo "compose_file_exists=true" >> "$GITHUB_OUTPUT"
          fi

      - name: Log in to GitHub Registry
        run: echo "${{ secrets.GH_TOKEN_READER }}" | docker login "${GITHUB_REGISTRY}" -u none --password-stdin

      - name: Replace Docker registry URLs
        if: ${{ steps.check-docker-compose.outputs.compose_file_exists == 'true' }}
        working-directory: ${{ env.RESOURCES_PATH }}
        run: |
          # This step replaces the Docker registry from JFrog to GitHub to reduce build times
          SOURCE_REGISTRY="inditex-docker.jfrog.io"
          TARGET_REGISTRY="ghcr.io\/inditex"

          # Find all the docker-compose files
          compose_files=$(find . -type f -name "docker-compose*.*")

          # Loop through the found files and replace the image names
          for file in $compose_files; do
            echo "+ Replacing image names in $file"
            sed -i "s/$SOURCE_REGISTRY/$TARGET_REGISTRY/g" "$file"
          done

      - name: Compile and create artifact
        working-directory: code
        run: |
          mvn -B clean install -DskipTests -DskipITs -DskipUTs

      - name: Find application jar file
        working-directory: code
        run: |
          jar_file=$(find . -type f -regex '.*/target\/.*-boot-.*-SNAPSHOT\.jar' -maxdepth 3)
          if [ -z "$jar_file" ]; then
            echo "+ ERROR No jar file found"
            exit 1
          fi
          echo "PATH_TO_JAR=$jar_file" >> "$GITHUB_ENV"

      - name: Launch containers
        id: launch-containers
        if: ${{ steps.check-docker-compose.outputs.compose_file_exists == 'true' }}
        working-directory: ${{ env.RESOURCES_PATH }}
        run: |
          echo "+ Launching containers"
          docker compose up -d --wait

          echo "+ List of running containers"
          docker ps

      - name: Start application
        id: start-application
        uses: inditex/gha-citool/run-application@v0
        env:
          HEALTH_CHECK_PATH: ${{ fromJSON(needs.newman-settings.outputs.config).app.health_probe.http_path }}
          APP_PORT: ${{ fromJSON(needs.newman-settings.outputs.config).app.app_port }}
        with:
          technology: java
          app-port: ${{ env.APP_PORT }}
          healthcheck-path: ${{ env.HEALTH_CHECK_PATH }}
          java-path-to-jar: ${{ env.PATH_TO_JAR }}
          java-extra-args: --spring.profiles.active=standalone,local
          working-directory: code

      - name: Start Newman Tests
        id: start-newman-tests
        uses: inditex/gha-testing/start-newman-tests@main
        with:
          environment: ${{ needs.newman-settings.outputs.environment }}
          working-directory: "code"
          newman-config-path: "src/test/resources/newman"
          number-iterations: ${{ github.event.inputs.NUMBER_ITERATIONS }}

      - name: Print logs if compose up or tests failed
        if: ${{ always() && (steps.launch-containers.outcome == 'failure' || steps.start-newman-tests.outcome == 'failure') && steps.check-docker-compose.outputs.compose_file_exists == 'true' }}
        working-directory: ${{ env.RESOURCES_PATH }}
        run: |
          echo "Compose up failed, printing logs"
          docker compose logs
          mapfile -t services < <(docker --log-level ERROR compose config --services)
          mkdir compose-logs
          for service in "${services[@]}"; do
            docker compose logs "${service}" > "compose-logs/${service}.log"
          done
          echo "Logs have been saved to individual text files."

      - name: Upload logs if compose up or integration tests failed
        if: ${{ always() && (steps.launch-containers.outcome == 'failure' || steps.start-newman-tests.outcome == 'failure') }}
        uses: actions/upload-artifact@v4
        with:
          name: compose-logs
          path: ${{ env.RESOURCES_PATH }}/compose-logs
          retention-days: 3

      - name: Start Newman Report
        id: start-newman-report
        uses: inditex/gha-testing/start-newman-report@main
        with:
          working-directory: "code"
          newman-config-path: "src/test/resources/newman"

      - name: Upload report html to PR
        uses: actions/upload-artifact@v4
        with:
          name: report-html
          path: code/newman

      - name: Upload report junit to PR
        uses: actions/upload-artifact@v4
        with:
          name: report-junit
          path: code/newman/junit

      - name: Stop application
        continue-on-error: true
        run: |
          kill ${{ steps.start-application.outputs.app-pid }}

      - name: Download ADAM
        if: ${{ always() && !cancelled() }}
        continue-on-error: true
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_READER }}
        run: |
          gh --repo inditex/clr-adam release download --pattern="adam" -O /tmp/adam
          chmod +x /tmp/adam
          echo "/tmp" >> "$GITHUB_PATH"

      - name: Publish newman results to DEVINS
        env:
          ADAM_EXTERNAL_EXECUTION: 1
          GITHUB_API_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          E2E_WF_ENVIRONMENT: ${{ needs.newman-settings.outputs.environment }}
        run: |
          adam publishNewmanTest \
            --projectKey="${{ needs.newman-settings.outputs.project_key }}" \
            --workingDir="${{ github.workspace }}" \
            --testType="newman" \
            --reportPath="${{ github.workspace }}/code/newman/junit" \
            --additionalParams=${{ toJson(github.event.inputs.ADDITIONAL_PARAMETERS) }} \
            --testResult=${{ steps.start-newman-tests.outcome }}

      - name: Create a Workflow Status Check to link this execution with a PR
        if: always() && github.event_name == 'workflow_dispatch'
        uses: inditex/gha-citool/create-status-check@v0
        with:
          app_id: ${{ vars.WFSC_APP_ID }}
          app_private_key: ${{ secrets.WFSC_APP_PKEY }}
          name: "code-QA-newman-local"
          summary: |
            This status check represents the conclusion (__${{ job.status }}__) of the last dispatched execution of workflow:
            `${{ github.workflow_ref }}`
            You can find more details in the link below :arrow_down:

  newman-tests-remote:
    needs: [newman-settings]
    if: ${{ needs.newman-settings.outputs.environment != 'local'}}
    name: Newman Tests Remote
    timeout-minutes: 30
    runs-on: ${{ fromJSON( vars.RUNSON_QADYNAMICTESTS_ONPREM || '["citool-icr__code-ubuntu24.04-large"]' ) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup IVM environment
        uses: inditex/gha-ivmactions/setup-environment@v1
        with:
          tool-versions-directory: code
          distro-branch: ${{ vars.TOOLVER_DISTRO_BRANCH }}

      - name: Setup Node version
        uses: inditex/gha-ivmactions/setup-tool@v1
        with:
          local-directory: code
          tool-name: ivm-node
          tool-version: 16

      - name: Resolve credentials
        uses: inditex/gha-paasdeployment/creds-resolver@v0
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          path-to-secrets: ${{ env.CONFIG_PATH }}/secret_test.yml
          output-format: kvequal

      - name: Install Newman dependencies
        working-directory: code
        run: |
         npm install newman@${{ env.NEWMAN_VERSION }} newman-reporter-htmlextra newman-reporter-junitfull

      - name: Start Newman Tests
        id: start-newman-tests
        uses: inditex/gha-testing/start-newman-tests@main
        with:
          environment: ${{ needs.newman-settings.outputs.environment }}
          creds-params: ${{ env.CREDS }}
          working-directory: "code"
          newman-config-path: "src/test/resources/newman"
          number-iterations: ${{ github.event.inputs.NUMBER_ITERATIONS }}

      - name: Start Newman Report
        id: start-newman-report
        uses: inditex/gha-testing/start-newman-report@main
        with:
          working-directory: "code"
          newman-config-path: "src/test/resources/newman"

      - name: Upload report html to PR
        uses: actions/upload-artifact@v4
        with:
          name: report-html
          path: code/newman

      - name: Upload report junit to PR
        uses: actions/upload-artifact@v4
        with:
          name: report-junit
          path: code/newman/junit

      - name: Publish newman results to DEVINS
        env:
          GITHUB_API_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          E2E_WF_ENVIRONMENT: ${{ needs.newman-settings.outputs.environment }}
        run: |
          adam publishNewmanTest \
            --projectKey="${{ needs.newman-settings.outputs.project_key }}" \
            --workingDir="${{ github.workspace }}" \
            --testType="newman" \
            --reportPath="${{ github.workspace }}/code/newman/junit" \
            --additionalParams=${{ toJson(github.event.inputs.ADDITIONAL_PARAMETERS) }} \
            --testResult=${{ steps.start-newman-tests.outcome }}
