---
name: "oscos-check-compliance-snapshot"
on:
  workflow_run:
    workflows: [code-maven-build-snapshot]
    types:
      - completed

env:
  WORKFLOW_VERSION: 1.15.0

jobs:
  check-compliance:
    name: Check compliance using sbom and wsc-oscodeps
    if: ${{ github.event.workflow_run.conclusion == 'success' }}
    runs-on: ${{ fromJSON(vars.RUNSON_OSCOS_ONPREM || '["citool-icr__code-ubuntu24.04-large"]') }}
    steps:
      - name: Checkout
        id: action-checkout
        uses: actions/checkout@v4

      - name: Download SBOM Artifact
        id: download-sbom-artifact
        uses: actions/download-artifact@v4
        with:
          name: sbom
          github-token: ${{ secrets.GITHUB_TOKEN }}
          run-id: ${{ github.event.workflow_run.id }}
          path: ./sbom-artifact

      - name: Credentials resolver
        id: credentials-resolver
        uses: inditex/gha-oscodependencies/creds-resolver@v0
        with:
          cyberark_oauth_clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark_oauth_secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          secret_file_name: "secret_s3_storage.yml"

      - name: Get project key from repository metadata
        id: get-project-key
        shell: python
        run: |
          import yaml
          import os
          with open('application.yml') as f:
              application = yaml.safe_load(f)
          project_key = application["metadata"]["project_key"]
          github_env = os.getenv('GITHUB_OUTPUT')
          with open(github_env, "a") as env_file:
              env_file.write(f"project_key={project_key}\n")
              print(f"::notice::Project key = [{project_key}] written to GitHub environment output.")

      - name: Set unique S3 object name
        id: set-unique-s3-object-name
        run: |
          TIMESTAMP=$(date +"%Y%m%d%H%M%S")
          OBJECT_NAME="${{ steps.get-project-key.outputs.project_key }}_${TIMESTAMP}_sbom"
          echo "object_name=${OBJECT_NAME}" >> "$GITHUB_OUTPUT"
          echo "::notice::The generated S3 object name is: ${OBJECT_NAME}"

      - name: Upload file to an s3 bucket
        id: upload-s3-file
        uses: inditex/gha-oscodependencies/upload-s3@v0
        with:
          s3_access_key: ${{ steps.credentials-resolver.outputs.s3_access_key }}
          s3_secret_key: ${{ steps.credentials-resolver.outputs.s3_secret_key }}
          file_path: ${{ github.workspace }}/sbom-artifact/sbom.json
          object_name: ${{ steps.set-unique-s3-object-name.outputs.object_name }}

      - name: Check if curation.yml exists
        id: check-curation-file
        run: |
          curation_file="${GITHUB_WORKSPACE}/code/curation.yml"
          oscos_curation_file="${GITHUB_WORKSPACE}/code/oscos-curation.yml"

          is_effectively_empty() {
            if grep -qE '^[[:space:]]*[^#[:space:]]' "$1"; then
              return 1
            else
              return 0
            fi
          }

          if [ -f "${oscos_curation_file}" ]; then
            if [ ! -s "${oscos_curation_file}" ] || is_effectively_empty "${oscos_curation_file}"; then
              rm "${oscos_curation_file}"
              echo "curation_exists=false" >> "$GITHUB_OUTPUT"
              echo "::notice::oscos-curation.yml file exists but is empty or only contains comments. File removed."
            else
              echo "curation_exists=true" >> "$GITHUB_OUTPUT"
              echo "curation_path=${oscos_curation_file}" >> "$GITHUB_OUTPUT"
              echo "::notice::oscos-curation.yml file exists."
            fi
          elif [ -f "${curation_file}" ]; then
            echo "curation_exists=true" >> "$GITHUB_OUTPUT"
            echo "curation_path=${curation_file}" >> "$GITHUB_OUTPUT"
            echo "::notice::curation.yml file exists."
          else
            echo "curation_exists=false" >> "$GITHUB_OUTPUT"
            echo "::notice::No curation file exists."
          fi

      - name: Validation project curation file
        id: validation-project-curation-file
        if: ${{ steps.check-curation-file.outputs.curation_exists == 'true' }}
        uses: inditex/gha-oscodependencies/file-validation@v0
        with:
          schema-name: curation_schema.json
          file-path: ${{ steps.check-curation-file.outputs.curation_path }}

      - name: Check compliance with OSCODEP API
        id: check-compliance
        uses: inditex/gha-oscodependencies/sbom-api-client@v0
        with:
          use-case: SBOM_COMPLIANCE
          sbom-id: ${{ steps.set-unique-s3-object-name.outputs.object_name }}
          curation-path: ${{ steps.check-curation-file.outputs.curation_path }}
          vcs-url: https://github.com/${{ github.repository }}
          project-key: ${{ steps.get-project-key.outputs.project_key }}

      - name: Check result with OSCODEP API
        id: check-result
        uses: inditex/gha-oscodependencies/sbom-api-client@v0
        with:
          use-case: SBOM_STATUS_CHECK
          task-id: ${{ steps.check-compliance.outputs.task_id }}

      - name: Generate Compliance Summary
        id: generate-summary
        uses: inditex/gha-oscodependencies/compliance-result-renderer@v0
        with:
          result-json-path: ${{ steps.check-result.outputs.compliance_result_path }}
          compliance-result: ${{ steps.check-result.outputs.compliance_result }}
          project-key: ${{ steps.get-project-key.outputs.project_key }}

      - name: Action status exist
        id: action-status-exist
        if: ${{ steps.check-result.outputs.compliance_result == 'FAILURE' || steps.check-result.outputs.invocation_status == 'error' }}
        run: |
          exit 1
