---
name: paas-promote
run-name: "Args: version=${{ inputs.VERSION || 'latest' }}, supraenvs=${{ inputs.SUPRAENVS }}, envs=${{ inputs.ENVS || 'all' }}, platforms=${{ inputs.PLATFORMS || 'all' }}, tenants=${{ inputs.TENANTS || 'all' }}, slots=${{ inputs.SLOTS || 'all' }}, issue_number=${{ inputs.ISSUE_NUMBER || 'none' }}"

concurrency:
  group: "promote-${{ github.ref_name }}-${{ inputs.VERSION }}"

on:
  workflow_call:
    inputs:
      SUPRAENVS:
        required: true
        type: string
      ENVS:
        required: false
        default: ''
        type: string
      PLATFORMS:
        required: false
        default: ''
        type: string
      TENANTS:
        required: false
        default: ''
        type: string
      SLOTS:
        required: false
        default: 'default'
        type: string
      LABELS:
        required: true
        type: string
      VERSION:
        required: false
        default: ''
        type: string
      ISSUE_NUMBER:
        required: false
        default: ''
        type: string
      ADDITIONAL_ARGS:
        required: false
        type: string
        default: '{"STRATEGY":"","PERCENTAGE":"","CONFIG_REF":"","TRIGGER":"","TARGET_BRANCH":""}'
  workflow_dispatch:
    inputs:
      SUPRAENVS:
        description: 'Supra environments to generate manifest files'
        required: true
        default: ''
      ENVS:
        description: 'Environments to generate manifest files'
        required: false
        default: ''
      PLATFORMS:
        description: 'Platforms to generate manifest files'
        required: false
        default: ''
      TENANTS:
        description: 'Tenants to generate manifest files'
        required: false
        default: ''
      SLOTS:
        description: 'Slots to generate manifest files'
        required: false
        default: 'default'
      LABELS:
        description: 'Labels to set in PR to deployment branch'
        required: true
        default: ''
      VERSION:
        description: 'Version to deploy'
        required: false
        default: ''
      ISSUE_NUMBER:
        description: 'ID number (issue or pull request)'
        required: false
        default: ''
      ADDITIONAL_ARGS:
        description: 'Additional args not common to all pipelines'
        required: false
        type: string
        default: '{"STRATEGY":"","PERCENTAGE":"","CONFIG_REF":"","TRIGGER":""}'

env:
  WORKFLOW_VERSION: 3.22.1
  DP_USERNAME: ${{ secrets.DIST_PLAT_READER_USERNAME }}
  DP_TOKEN: ${{ secrets.DIST_PLAT_READER_TOKEN }}

permissions:
  id-token: write  # This is required for requesting the JWT
  contents: write  # This is required for merging pull request
  issues: write
  pull-requests: write
  packages: read

jobs:
  promote:
    name: Promote & Deployment PRs
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_ONPREM || '["cdtool-icr__paasfeature"]' ) }}
    outputs:
      pr-list: ${{ steps.create-pr-manifest.outputs.pr-list }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ fromJson(inputs.ADDITIONAL_ARGS).CONFIG_REF }}

      - name: Annotate the origin trigger
        if: ${{ inputs.VERSION != 'latest' && inputs.ISSUE_NUMBER != '' }}
        run: |
          echo "::notice title=Deployment triggered from: ::https://github.com/${GITHUB_REPOSITORY}/pull/${{ inputs.ISSUE_NUMBER }}"

      - name: Compute vars
        id: compute-vars
        uses: inditex/gha-paasdeployment/paas-promote/compute-vars@v0
        with:
          issue-number: ${{ inputs.ISSUE_NUMBER }}
          token: ${{ secrets.GITHUB_TOKEN }}
          version: ${{ inputs.VERSION }}

      - name: Reset error label if the origin is an issue
        if: ${{ steps.compute-vars.outputs.is-issue == 'true' }}
        run: |
          label=$(gh issue view ${{ env.ISSUE_NUMBER }} --repo ${{ github.repository }} --json labels | jq -r '.labels[].name | select(. == "${{ env.LABEL_TO_REMOVE }}")')
          if [[ "${label}" == "${{ env.LABEL_TO_REMOVE }}" ]]; then
            echo "Removing label '${{ env.LABEL_TO_REMOVE }}' from previous failed execution"
            gh issue edit ${{ env.ISSUE_NUMBER }} --remove-label ${{ env.LABEL_TO_REMOVE }} --repo ${{ github.repository }}
          else
            echo "Label '${{ env.LABEL_TO_REMOVE }}' not found in issue"
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ISSUE_NUMBER: ${{ inputs.ISSUE_NUMBER }}
          LABEL_TO_REMOVE: "status/failed"

      - name: Add comment with started status
        if: ${{ inputs.ISSUE_NUMBER != '' }}
        uses: inditex/gha-citool/add-comment@v0
        id: starting-comment
        with:
          issue-number: '${{ inputs.ISSUE_NUMBER }}'
          body: |
            #### :gear: The `paas-promote` workflow is currently running

            Deployment templates are being generated and Docker image is being promoted to PaaS registries.
            The pull request(s) related to this task will be opened shortly...

            See the [workflow log](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}) for more details.
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup paas-cli
        id: setup-cli
        uses: inditex/gha-paasdeployment/setup-cli@v0
        with:
          github_token: ${{ secrets.GH_TOKEN_READER }}

      - name: Validate slots
        id: validate-slots
        uses: inditex/gha-paasdeployment/deployment/validate-slots@v0
        with:
          slots: ${{ inputs.SLOTS }}
          strategy: ${{ fromJson(inputs.ADDITIONAL_ARGS).STRATEGY }}

      - name: Stop if slots are not valid
        if: ${{ steps.validate-slots.outputs.is-valid != 'true' }}
        run: |
          echo "::error ::Error validating the slots. ${{ steps.validate-slots.outputs.error-message }}" | tr -s '\n' ' '
          exit 1

      - name: Configure ghcr registry credentials
        if: vars.PAAS_DOCKER_SNAPSHOT_REGISTRY == 'ghcr.io' && steps.compute-vars.outputs.deployment-version == 'latest'
        run: |
          {
            echo "CONTAINER_REGISTRY_SNAPSHOT_USER=USERNAME"
            echo "CONTAINER_REGISTRY_SNAPSHOT_PASSWORD=${{ secrets.GITHUB_TOKEN }}"
            echo "CONTAINER_REGISTRY_SNAPSHOT_BASE_URL=ghcr.io/inditex"
          } >> "$GITHUB_ENV"

      - name: Sentinel - Generate manifests
        uses: inditex/gha-paasdeployment/sentinel@v0
        id: sentinel
        with:
          supraenvs: ${{ inputs.SUPRAENVS }}
          envs: ${{ inputs.ENVS }}
          platforms: ${{ inputs.PLATFORMS }}
          tenants: ${{ inputs.TENANTS }}
          slots: ${{ inputs.SLOTS }}
          deployment-version: ${{ steps.compute-vars.outputs.deployment-version }}
          strategy: ${{ fromJson(inputs.ADDITIONAL_ARGS).STRATEGY }}
          percentage: ${{ fromJson(inputs.ADDITIONAL_ARGS).PERCENTAGE }}
          working-directory: paas
          token: ${{ secrets.GH_TOKEN_READER }}
          trigger: ${{ fromJson(inputs.ADDITIONAL_ARGS).TRIGGER }}
          config-ref: ${{ fromJson(inputs.ADDITIONAL_ARGS).CONFIG_REF }}
          target-branch: ${{ fromJson(inputs.ADDITIONAL_ARGS).TARGET_BRANCH }}
          is-issue: ${{ steps.compute-vars.outputs.is-issue }}
          issue-number: ${{ inputs.ISSUE_NUMBER }}
        env:
          MERGED: ${{ github.event.pull_request.merged }}

      - name: Promote images to internal registries from ghcr
        if: ${{ steps.sentinel.outputs.no-deployments-needed != 'True' && vars.PAAS_DOCKER_SNAPSHOT_REGISTRY == 'ghcr.io' && steps.compute-vars.outputs.deployment-version == 'latest' }}
        uses: inditex/gha-paasdeployment/deployment/promote@v0
        with:
          working-directory: paas
          ghcr-username: ${{ env.CONTAINER_REGISTRY_SNAPSHOT_USER }}
          ghcr-token: ${{ env.CONTAINER_REGISTRY_SNAPSHOT_PASSWORD }}
          ghcr-url: ${{ env.CONTAINER_REGISTRY_SNAPSHOT_BASE_URL }}

      - name: Promote images to internal registries from JFrog
        if: ${{ steps.sentinel.outputs.no-deployments-needed != 'True' && ( vars.PAAS_DOCKER_SNAPSHOT_REGISTRY != 'ghcr.io' || steps.compute-vars.outputs.deployment-version != 'latest' ) }}
        uses: inditex/gha-paasdeployment/deployment/promote@v0
        with:
          working-directory: paas

      - name: Promote ML Models
        if: ${{ steps.sentinel.outputs.no-deployments-needed != 'True' && steps.compute-vars.outputs.is-mlops == 'true' }}
        uses: inditex/gha-mlopsactions/actions/paas-promote-models@v0
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          model-list-json-path: configmap.values.amiga.mlserver.models
          overwrite-when-version-exists: "false"
          copy-only-this-list-of-model-versions: ""
          remove-all-versions-before-copying: "true"

      - name: BatchAsCode / Copy job definitions
        if: ${{ steps.sentinel.outputs.no-deployments-needed != 'True' && steps.compute-vars.outputs.is-batch == 'true' }}
        run: |
          if [ -d '../../jobs' ]; then
            find . -maxdepth 1 -mindepth 1 -type d | while read -r dir; do
              echo "Adding 'jobs' folder in $dir"
              cp -r ../../jobs "$dir"/
            done
          else
            echo "::error ::No jobs folder to copy!"
          fi
        working-directory: paas/target_tpl

      - name: Traffic Parrot / Promote mappings to s3 bucket
        if: ${{ steps.sentinel.outputs.no-deployments-needed != 'True' }}
        uses: inditex/gha-paastol/paasmarket/trafficparrot/promote-mappings@promote-mappings-v2
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          source: paas/config_paas/trafficparrot_mappings/
          working-dir: paas/target_tpl
          delete: true

      - name: Create PIPE users for affected environments
        if: ${{ steps.sentinel.outputs.no-deployments-needed != 'True' }}
        uses: inditex/gha-pipeactions/deploy-client-paas@v1
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          paas-cli: ${{ steps.setup-cli.outputs.path }}
          working-directory: paas
          token: ${{ secrets.GH_TOKEN_READER }}

      - name: Create PR to deploy manifests into deployment branch
        id: create-pr-manifest
        if: ${{ steps.sentinel.outputs.no-deployments-needed != 'True' }}
        uses: inditex/gha-paasdeployment/deployment/create-pr-manifests@v0
        with:
          working-directory: paas
          token: ${{ secrets.CHATBOT_PR_PAT }}
          version: ${{ steps.compute-vars.outputs.deployment-version }}
          strategy: ${{ fromJson(inputs.ADDITIONAL_ARGS).STRATEGY }}
          label: ${{ inputs.LABELS }}
          force-token-usage: ${{ steps.compute-vars.outputs.force-token-usage }}
        env:
          ISSUE_NUMBER: ${{ inputs.ISSUE_NUMBER }}
          SCM_COMMITTER_PGP_KEY: ${{ secrets.BUTLER_PGP_KEY }}

      - name: Delete deployment start comment
        if: ${{ always() && !cancelled() && inputs.ISSUE_NUMBER != '' }}
        id: delete-comment
        run: |
          gh api --method DELETE -H "Accept: application/vnd.github.v3+json" /repos/inditex/${{ github.event.repository.name }}/issues/comments/${{ steps.starting-comment.outputs.comment_id }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Add label on failure if the origin is an issue
        if: ${{ failure() && !cancelled() && (steps.compute-vars.outputs.is-issue == 'true') }}
        run: |
          gh issue edit ${{ inputs.ISSUE_NUMBER }} --add-label "status/failed" --repo ${{ github.repository }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Add comment when deployment fails and issue or PR exists
        if: ${{ failure() && !cancelled() && (inputs.ISSUE_NUMBER != '' || github.event.pull_request.number != '') }}
        uses: inditex/gha-citool/add-comment@v0
        with:
          issue-number: '${{ inputs.ISSUE_NUMBER || github.event.pull_request.number }}'
          body: |
            #### :x: An error has ocurred deploying the application.

            ${{ steps.validate-slots.outputs.error-message }}

            The `${{ github.workflow }}` workflow has failed at the `${{ github.job }}` job.

            See the [workflow log](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}) for more details.
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Create a Workflow Status Check to link this execution with a PR
        if: always() && !cancelled()
        uses: inditex/gha-citool/create-status-check@v0
        with:
          app_id: ${{ vars.WFSC_APP_ID }}
          app_private_key: ${{ secrets.WFSC_APP_PKEY }}
          name: "paas-promote-${{ inputs.SUPRAENVS }}"
          summary: |
            This status check represents the conclusion (__${{ job.status }}__) of the last dispatched execution of workflow:
            `${{ github.workflow_ref }}`
            You can find more details in the link below :arrow_down:
  shepherd-check:
    name: Shepherd check
    needs: promote
    if: needs.promote.outputs.pr-list != ''
    uses: ./.github/workflows/paas-shepherd_check.yml
    with:
      PR_LIST: ${{ needs.promote.outputs.pr-list }}
    secrets: inherit
