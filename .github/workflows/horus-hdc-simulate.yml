---
name: horus-hdc-simulate
run-name: "Args: env=${{ inputs.environment }}, supraenv=${{ inputs.supraenvironment }}, platformId=${{ inputs.platformId }}, slot=${{ inputs.slot }}, platform=${{ inputs.platform }}, tenant=${{ inputs.tenant }}"


on:
  workflow_dispatch:
    inputs:
      ISSUE_NUMBER:
        description: 'Pull request ID number'
        required: true
      environment:
        description: 'Environment to simulate'
        required: true
        default: pro
      supraenvironment:
        description: 'Supraenvironment to simulate'
        required: true
        default: pro
      platformId:
        description: 'platformId to simulate'
        required: true
      slot:
        description: 'Slot to simulate'
        required: true
        default: default
      platform:
        description: 'Platform to simulate'
        required: true
      tenant:
        description: 'Tenant to simulate'
        required: true
        default: global

env:
  WORKFLOW_VERSION: 2.2.0
  ISSUE_NUMBER: ${{ github.event.number }}

jobs:
  simulate-horus:
    name: HorusDepco / Simulate Horus
    timeout-minutes: 5
    runs-on: ${{ fromJSON(vars.RUNSON_HORUS_ONPREM) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Resolve Configuration
        id: config
        uses: inditex/actions/resolvers/config-resolver@main
        with:
          create-annotations: true
          files: |
            application.yml

      - name: Run simulate horus action
        uses: inditex/gha-sreactions/horusdepco/horusdepco-simulate-horus@horus-latest
        id: simulateHorus
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          project-key: ${{ fromJSON(steps.config.outputs.config).metadata.project_key }}
          project-name: ${{ fromJSON(steps.config.outputs.config).metadata.project_name }}
          issue-number: ${{ github.event.inputs.ISSUE_NUMBER }}
          horusdepco-server-url: https://sscc.central.inditex.grp/horusdepco/api
          api-username: oauthhorusdepco
          api-password: cyberark.AX-N-SEG-OAU-IC-OPENAMPR.OAUTH2ClientsPRO_oauthhorusdepco
          environment: ${{ github.event.inputs.environment }}
          supraenvironment: ${{ github.event.inputs.supraenvironment }}
          platformId: ${{ github.event.inputs.platformId }}
          slot: ${{ github.event.inputs.slot }}
          platform: ${{ github.event.inputs.platform }}
          tenant: ${{ github.event.inputs.tenant }}
