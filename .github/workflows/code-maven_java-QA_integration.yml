---
name: code-maven-QA-integration

on:
  workflow_dispatch:
    inputs:
      ATTACH_TARGET:
        description: Target platform to upload the generated report, it does not export results if empty. (p.e. jfrog)
        required: false

concurrency:
  group: qa-integration-${{ github.ref }}
  cancel-in-progress: true

env:
  WORKFLOW_VERSION: 6.4.0
  DP_USERNAME: ${{ secrets.DIST_PLAT_READER_USERNAME }}
  DP_TOKEN: ${{ secrets.DIST_PLAT_READER_TOKEN }}
  ADAM_EXTERNAL_EXECUTION: 1
  NPM_EMAIL: ${{ secrets.DIST_PLAT_READER_USERNAME }}
  NPM_AUTH: ${{ secrets.DIST_PLAT_READER_NPM_AUTH }}
  NPM_REGISTRY: https://inditex.jfrog.io/artifactory/api/npm/node-public/

jobs:
  integration-tests-containers:
    name: Integration Tests / Containers
    timeout-minutes: 45
    runs-on: ${{ fromJSON( vars.RUNSON_QADYNAMICTESTS_MAVEN_JAVA_DOCKER || '["citool-icr-aks__code-ubuntu24.04-large"]' ) }}
    env:
      GITHUB_REGISTRY: ghcr.io
      MAVEN_OPTS: "-Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=warn -Ddarwin-telemetry-maven-extension.output=json"
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Find config_test.yml
        id: config-test
        env:
          CONFIG_TEST_FILE: code/src/test/resources/config_test.yml
        run: |
          if [[ -f "${CONFIG_TEST_FILE}" ]]; then
            echo "file=${CONFIG_TEST_FILE}" >> "$GITHUB_OUTPUT"
          else
            echo "config_test.yml file not found"
          fi

      - name: Resolve Configuration
        id: config
        uses: inditex/actions/resolvers/config-resolver@main
        with:
          create-annotations: true
          files: |
            application.yml
            ${{ steps.config-test.outputs.file }}

      - name: Get runner resources
        id: get-runner-resources
        uses: inditex/gha-citool/get-runner-resources@v0

      - name: Setup Maven Cache
        uses: actions/cache@v4
        continue-on-error: true
        with:
          path: ~/.m2/repository
          key: ${{ steps.get-runner-resources.outputs.RUNNER_OS_FULL_NAME }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ steps.get-runner-resources.outputs.RUNNER_OS_FULL_NAME }}-maven-

      - name: Export ASDF_DATA_DIR (Configure runner 1/5)
        run: echo "ASDF_DATA_DIR=$ASDF_DATA_DIR" >> "$GITHUB_ENV"

      - name: Setup asdf Cache (Configure runner 2/5)
        uses: actions/cache@v4
        continue-on-error: true
        with:
          path: ${{ env.ASDF_DATA_DIR }}
          key: ${{ steps.get-runner-resources.outputs.RUNNER_OS_FULL_NAME }}-asdf-${{ hashFiles('**/.tool-versions') }}
          restore-keys: |
            ${{ steps.get-runner-resources.outputs.RUNNER_OS_FULL_NAME }}-asdf-

      - name: Setup IVM plugins (Configure runner 3/5)
        uses: inditex/gha-ivmactions/setup-plugins@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_READER }}
        with:
          tool-versions-directory: code

      - name: Setup IVM environment (Configure runner 4/5)
        uses: inditex/gha-ivmactions/setup-environment@v1
        with:
          tool-versions-directory: code
          distro-branch: ${{ vars.TOOLVER_DISTRO_BRANCH }}

      - name: Check for docker-compose.yml file
        id: check-docker-compose
        working-directory: code
        run: |
          if [ ! -f src/test/resources/compose/docker-compose.yml ]; then
            echo "docker-compose.yml not found!"
            echo "compose_file_exists=false" >> "$GITHUB_OUTPUT"
          else
            echo "docker-compose.yml found!"
            echo "compose_file_exists=true" >> "$GITHUB_OUTPUT"
          fi

      - name: Log in to GitHub Registry (Configure runner 5/5)
        run: echo "${{ secrets.GH_TOKEN_READER }}" | docker login "${GITHUB_REGISTRY}" -u none --password-stdin

      - name: Replace Docker registry URLs
        if: ${{ steps.check-docker-compose.outputs.compose_file_exists == 'true' }}
        working-directory: code
        run: |
          # This step replaces the Docker registry from JFrog to GitHub to reduce build times

          shopt -s globstar

          SOURCE_REGISTRY="inditex-docker.jfrog.io"
          TARGET_REGISTRY="ghcr.io\/inditex"

          # Find all the docker-compose files
          compose_files=$(find ./**/src/test -type f -name "docker-compose*.*")

          # Loop through the found files and replace the image names
          for file in $compose_files; do
            echo "+ Replacing image names in $file"
            sed -i "s/$SOURCE_REGISTRY/$TARGET_REGISTRY/g" "$file"
          done

      - name: Launch containers
        id: launch-containers
        if: ${{ vars.LOCAL_CONTAINERS != 'false' && steps.check-docker-compose.outputs.compose_file_exists == 'true' }}
        working-directory: code/src/test/resources/compose
        run: |
          echo "+ Launching containers"
          docker compose up -d --wait
          echo "+ List of running containers"
          docker ps
          echo "containers_up=true" >> "$GITHUB_OUTPUT"

      - name: Run integration tests
        id: run-tests
        env:
          LOCAL_CONTAINERS: ${{ steps.launch-containers.outputs.containers_up || 'false' }}
        working-directory: code
        run: |
          mvn -B install \
            -Djacoco.skip=false -Damiga.jacoco \
            -DskipUTs -Dskip.unit.tests=true -DfailIfNoTests=false -Dmaven.test.failure.ignore=false

      - name: Print logs if compose up or tests failed
        if: ${{ always() && (steps.launch-containers.outcome == 'failure' || steps.run-tests.outcome == 'failure') }}
        working-directory: code/src/test/resources/compose
        run: |
          echo "Compose up failed, printing logs"
          docker compose logs
          mapfile -t services < <(docker --log-level ERROR compose config --services)
          mkdir compose-logs
          for service in "${services[@]}"; do
            docker compose logs "${service}" > "compose-logs/${service}.log"
          done
          echo "Logs have been saved to individual text files."

      - name: Upload logs if compose up or integration tests failed
        if: ${{ always() && (steps.launch-containers.outcome == 'failure' || steps.run-tests.outcome == 'failure') && steps.check-docker-compose.outputs.compose_file_exists == 'true'}}
        uses: actions/upload-artifact@v4
        with:
          name: compose-logs
          path: code/src/test/resources/compose/compose-logs
          retention-days: 3

      - name: Generate Surefire report and annotate PR
        if: ${{ always() && !cancelled() }}
        uses: scacap/action-surefire-report@a2911bd1a4412ec18dde2d93b1758b3e56d2a880
        with:
          fail_if_no_tests: false
          create_check: false
          check_name: "Integration Tests / Containers"

      - name: Generate Failsafe HTML report
        if: ${{ always() && !cancelled() && steps.config-test.outputs.file != '' }}
        continue-on-error: true
        working-directory: code
        run: |
          mvn -B surefire-report:failsafe-report-only site:site \
            -DoutputName=index \
            -DalwaysGenerateFailsafeReport=true \
            -DgenerateReports=false

      - name: Attach results
        if: ${{ always() && !cancelled() && steps.config-test.outputs.file != '' && contains(inputs.ATTACH_TARGET, 'jfrog') }}
        continue-on-error: true
        uses: inditex/actions/upload-artifact@main
        with:
          path: ${{ steps.config-test.outputs.file }}
          keys: junit.integration
        env:
          DP_ARTIFACTS_STORAGE_USER: ${{ secrets.DIST_PLAT_DEPLOYER_USERNAME }}
          DP_ARTIFACTS_STORAGE_TOKEN: ${{ secrets.DIST_PLAT_DEPLOYER_TOKEN }}

      - name: Store project version
        if: ${{ always() && !cancelled() }}
        continue-on-error: true
        id: version
        working-directory: code
        run: |
          echo "app-version=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)" >> "$GITHUB_OUTPUT"

      - name: Download ADAM
        if: ${{ always() && !cancelled() }}
        continue-on-error: true
        working-directory: code
        env:
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_READER }}
        run: |
          gh --repo inditex/clr-adam release download --pattern="adam" -O adam
          chmod +x adam

      - name: Publish integration test results to DEVINS
        if: ${{ always() && !cancelled() }}
        continue-on-error: true
        working-directory: code
        run: |
          ./adam publishIntegrationTest \
            --projectKey="${{ fromJSON(steps.config.outputs.config).metadata.project_key }}" \
            --workspace="${{ github.workspace }}" \
            --version="${{ steps.version.outputs.app-version }}" \
            --failsafePath="${{ github.workspace }}/**/${{ fromJSON(steps.config.outputs.config).junit.integration.results.failsafe_xml_report_path.path }}" \
            --jacocoPath="${{ github.workspace }}/${{ fromJSON(steps.config.outputs.config).junit.integration.results.jacoco_report_folder.path }}/index.html"

      - name: Check Quality Gate Conditions
        if: ${{ always() && !cancelled() }}
        working-directory: code
        env:
          SONARCLOUD_TOKEN: ${{ secrets.SONARCLOUD_TOKEN }}
          GITHUB_API_TOKEN: ${{ secrets.GH_TOKEN_READER }}
        run: |
          ./adam checkStatusConditions \
            --projectKey="${{ fromJSON(steps.config.outputs.config).metadata.project_key }}" \
            --repository="${{ fromJSON(steps.config.outputs.config).metadata.project_name }}" \
            --branch="${{ github.base_ref || github.ref_name }}" \
            --workingDir="${{ github.workspace }}/code" \
            --statusCheck="code-QA-integration"

      - name: Display results
        if: ${{ always() && !cancelled() }}
        continue-on-error: true
        run: |
          if [ -f "code/message" ]
          then
              MESSAGE=$(cat "code/message" 2>/dev/null)
              MESSAGE=$(echo "$MESSAGE" | tr -s ' ')
              echo "::notice title=Results [jacoco] ::$MESSAGE"
          fi
          if [ -f "code/messageSurefire" ]
          then
              MESSAGE=$(cat "code/messageSurefire" 2>/dev/null)
              MESSAGE=$(echo "$MESSAGE" | tr -s ' ')
              echo "::notice title=Results [karate] ::$MESSAGE"
          fi
          if [ -f "code/messageStatusGate" ]
          then
              MESSAGE=$(cat "code/messageStatusGate" 2>/dev/null)
              MESSAGE=$(echo "$MESSAGE" | tr -s ' ')
              echo "::notice title=Status check ::$MESSAGE"
          fi

      - name: Create a Workflow Status Check to link this execution with a PR
        if: ${{ always() && !cancelled() }}
        continue-on-error: true
        uses: inditex/gha-citool/create-status-check@v0
        with:
          app_id: ${{ vars.WFSC_APP_ID }}
          app_private_key: ${{ secrets.WFSC_APP_PKEY }}
          name: "code-QA-integration"
          summary: |
            This status check represents the conclusion (__${{ job.status }}__) of the last dispatched execution of workflow:
            `${{ github.workflow_ref }}`

            You can find more details in the link below :arrow_down:
