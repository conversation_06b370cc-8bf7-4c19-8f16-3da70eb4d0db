---
name: pipe-deploy-topic
run-name: "Args: supraenv=${{ inputs.SUPRAENV }}"

on:
  workflow_dispatch:
    inputs:
      SUPRAENV:
        description: 'Supra environment to deploy topics'
        required: true
        default: ''
      ISSUE_NUMBER:
        description: 'ID number (issue or pull request)'
        required: false
        default: ''

env:
  WORKFLOW_VERSION: 3.1.0
  DP_USERNAME: ${{ secrets.DIST_PLAT_READER_USERNAME }}
  DP_TOKEN: ${{ secrets.DIST_PLAT_READER_TOKEN }}

jobs:
  verify-topic:
    name: Verify Deploy PIPE Topic
    timeout-minutes: 30
    runs-on: ${{ fromJSON( vars.RUNSON_PIPE_ONPREM || '["self-hosted", "pipe"]' ) }}
    outputs:
      sensitive: ${{ steps.summary-generator.outputs.sensitive }}
      supraenv: ${{ steps.supraenvironment.outputs.env }}
      project-key: ${{ steps.metadata.outputs.project-key }}
      project-name: ${{ steps.metadata.outputs.project-name }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check and get supraenvironment
        id: supraenvironment
        shell: python
        run: |
          import os
          supraenv = '${{ github.event.inputs.SUPRAENV }}'.lower()
          environments = ['des', 'pre', 'pro']
          if supraenv not in environments:
              print(f"::error ::Supraenvironment '{supraenv}' not supported!")
              exit(1)
          github_output = os.getenv('GITHUB_OUTPUT')
          with open(github_output, "a") as gh_out:
            gh_out.write(f'env={supraenv}\n')

      - name: Get metadata
        id: metadata
        shell: python
        run: |
          import yaml
          import os
          with open('application.yml') as f:
            application = yaml.safe_load(f)
          github_output = os.getenv('GITHUB_OUTPUT')
          with open(github_output, "a") as gh_out:
            gh_out.write(f'project-key={ application["metadata"]["project_key"] }\n')
            gh_out.write(f'project-name={ application["metadata"]["project_name"] }\n')

      - name: PIPE Verify Topic
        id: pr_verify
        uses: inditex/gha-pipeactions/verify@v3
        with:
          supraenv: ${{ steps.supraenvironment.outputs.env }}
          components: "topics"
          project-dir: ${GITHUB_WORKSPACE}
          extra-features: "--extra-features ${{ github.event.inputs.ISSUE_NUMBER }} ${{ steps.metadata.outputs.project-name }}"
          github-token: ${{ secrets.GH_TOKEN_READER }}
        env:
          PROJECT_KEY: ${{ steps.metadata.outputs.project-key }}
          PROJECT_NAME: ${{ steps.metadata.outputs.project-name }}

      - name: Check PR Verify Result
        if: ${{ failure() && !cancelled() }}
        uses: inditex/gha-citool/add-comment@v0
        with:
          issue-number: "${{ github.event.inputs.ISSUE_NUMBER }}"
          body: |
            ### :warning: PIPE Topic PR verification failure
            - **Supraenvironments**: "${{ github.event.inputs.SUPRAENV }}"
            - **Action**: "[${{ github.workflow }}-${{ github.run_id }}](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})"
        env:
          GITHUB_TOKEN: ${{ secrets.CHATBOT_PR_PAT }}

      - name: Check if environment exists
        if: ${{ !startsWith(env.PIPE_CODE_VERSION, '2') }}
        id: check-environment
        run: |
          ENVIRONMENT_NAME="pipe-deploy-topic-environment"
          REPO=${{ github.repository }}
          RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" \
            -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/$REPO/environments/$ENVIRONMENT_NAME)
          if [ "$RESPONSE" -ne 200 ]; then
            echo "Environment $ENVIRONMENT_NAME does not exist. Contact PIPE team."
            exit 1
          fi

      - name: Generate PIPE Summary and Coordinates
        if: ${{ !startsWith(env.PIPE_CODE_VERSION, '2') }}
        id: summary-generator
        uses: inditex/gha-pipeactions/analyze-differences@v2
        with:
          differences-file-path: "pipe/target"
          supraenv: ${{ github.event.inputs.SUPRAENV }}
          github-token: ${{ secrets.GH_TOKEN_READER }}

      - name: Check PR Verify Operations Summary
        if: ${{ !startsWith(env.PIPE_CODE_VERSION, '2') }}
        uses: inditex/gha-citool/add-comment@v0
        with:
          issue-number: "${{ github.event.inputs.ISSUE_NUMBER }}"
          body: |
            ### :rocket: PIPE Topic PR summary
            ${{ steps.summary-generator.outputs.no_sensitive_operations_summary }}
            ${{ steps.summary-generator.outputs.sensitive_operations_summary }}
            - **Supraenvironments**: ${{ github.event.inputs.SUPRAENV }}

            See the [workflow log](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}) for more details.
        env:
          GITHUB_TOKEN: ${{ secrets.CHATBOT_PR_PAT }}

      - name: Shepherd validation
        if: ${{ steps.summary-generator.outputs.shepherd_regions && !startsWith(env.PIPE_CODE_VERSION, '2') }}
        uses: inditex/gha-pipeactions/shepherd-region-check@v2
        with:
          supraenvs: ${{ github.event.inputs.SUPRAENV }}
          regions: ${{ steps.summary-generator.outputs.shepherd_regions }}
          pr-number: ${{ github.event.inputs.ISSUE_NUMBER }}
          project-key: ${{ steps.metadata.outputs.project-key }}
          project-name: ${{ steps.metadata.outputs.project-name }}
          github-token: ${{ secrets.GH_TOKEN_READER }}

      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: pipe-content
          path: "pipe"
          retention-days: 3

  ask-for-approval:
    name: Ask for approval
    runs-on: ubuntu-latest
    if: needs.verify-topic.outputs.sensitive == 'True'
    environment: pipe-deploy-topic-environment
    needs: verify-topic
    steps:
      - id: approval
        run: echo "Approval step"

  deploy-topic:
    name: Deploy PIPE Topic
    timeout-minutes: 30
    runs-on: ${{ fromJSON( vars.RUNSON_PIPE_ONPREM || '["self-hosted", "pipe"]' ) }}
    if: ${{ always() && (needs.verify-topic.result == 'success') && (needs.ask-for-approval.result == 'success' || needs.ask-for-approval.result == 'skipped') }}
    needs: [verify-topic, ask-for-approval]
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4
        with:
          name: pipe-content
          path: pipe

      - name: Setup paas-cli
        id: setup-cli
        uses: inditex/gha-paasdeployment/setup-cli@v0
        with:
          github_token: ${{ secrets.GH_TOKEN_READER }}

      - name: Deploy No PaaS Client
        id: deploy-no-paas
        uses: inditex/gha-pipeactions/deploy-client-no-paas@v2
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          paas-cli: ${{ steps.setup-cli.outputs.path }}
          token: ${{ secrets.CHATBOT_PR_PAT }}
          working-directory: pipe
          github-token: ${{ secrets.GH_TOKEN_READER }}

      - name: Deploy PIPE Topic
        uses: inditex/gha-pipeactions/deploy-topic@v3
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          paas-cli: ${{ steps.setup-cli.outputs.path }}
          supraenv: ${{ needs.verify-topic.outputs.supraenv }}
          project-dir: ${GITHUB_WORKSPACE}
          github-token: ${{ secrets.GH_TOKEN_READER }}
        env:
          PROJECT_KEY: ${{ needs.verify-topic.outputs.project-key }}
          PROJECT_NAME: ${{ needs.verify-topic.outputs.project-name }}

      - name: Add comment with deployment information
        uses: inditex/gha-citool/add-comment@v0
        with:
          issue-number: "${{ github.event.inputs.ISSUE_NUMBER }}"
          body: |
            ### :rocket: PIPE Topic deployed
            - **Supraenvironments**: "${{ github.event.inputs.SUPRAENV }}"
            - **Action**: "[${{ github.workflow }}-${{ github.run_id }}](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})"
        env:
          GITHUB_TOKEN: ${{ secrets.CHATBOT_PR_PAT }}

      - name: Create a Workflow Status Check to link this run to its corresponding PR
        if: always() && !cancelled() && github.event_name == 'workflow_dispatch'
        uses: inditex/gha-citool/create-status-check@v0
        with:
          app_id: ${{ vars.WFSC_APP_ID }}
          app_private_key: ${{ secrets.WFSC_APP_PKEY }}
          name: "pipe-deploy-topic-${{ github.event.inputs.SUPRAENV }}"
          summary: |
            This status check represents the conclusion (__${{ job.status }}__) of the last dispatched execution of workflow:
            `${{ github.workflow_ref	}}`
            You can find more details in the link below :arrow_down:
