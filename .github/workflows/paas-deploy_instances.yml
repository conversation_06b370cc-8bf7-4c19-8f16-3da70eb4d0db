---
name: paas-deploy-instances
run-name: "Args: action=${{ inputs.ACTION }}"

on:
  workflow_dispatch:
    inputs:
      ISSUE_NUMBER:
        description: "Related issue number"
        required: true
      ISSUE_COMMENT_ID:
        description: "Issue comment ID that triggered the action"
        required: true
      ACTION:
        description: "Action to run"
        required: true
        type: choice
        options:
          - abort
          - approve
          - cancel
          - continue
          - merge
          - restore
          - retry
          - rollback
      SKIP:
        description: 'Steps to be skipped during rollout flow'
        required: false
        default: ''
      SUPRAENVS:
        description: 'Supra environments used during action execution'
        required: false
        default: ''
      ENVS:
        description: 'Environments used during action execution'
        required: false
        default: ''
      PLATFORMS:
        description: 'Platforms used during action execution'
        required: false
        default: ''
      TENANTS:
        description: 'Tenants used during action execution'
        required: false
        default: ''
      SLOTS:
        description: 'Slots used during action execution'
        required: false
        default: ''

env:
  WORKFLOW_VERSION: 3.22.1
  GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
  DIR_PATH_STATUS: "/tmp/artifact-results"

jobs:

  get-affected-prs:
    name: PaaS / Get affected PRs
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    outputs:
      affected-prs: ${{ steps.affected-prs-by-issue.outputs.affected-prs }}

    steps:
      - name: PaaS / Get affected PRs by issue
        id: affected-prs-by-issue
        uses: inditex/gha-paasdeployment/affected-prs-by-issue@v0
        with:
          issue-number: ${{ github.event.inputs.ISSUE_NUMBER }}
          token: ${{ secrets.GITHUB_TOKEN }}

  validate-permutations:
    name: PaaS / Validate permutations
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    needs: [get-affected-prs]
    outputs:
      filtered-permutations: ${{ steps.get-filtered-permutations.outputs.filtered-permutations }}
      error-message: ${{ steps.validate-slots.outputs.error-message }}

    steps:
      - name: Validate slots
        if: ${{ github.event.inputs.SLOTS != '' }}
        id: validate-slots
        uses: inditex/gha-paasdeployment/deployment/validate-slots@v0
        with:
          slots: ${{ github.event.inputs.SLOTS }}
          strategy: default

      - name: Stop if slots are not valid
        if: ${{ (github.event.inputs.SLOTS != '') && (steps.validate-slots.outputs.is-valid != 'true') }}
        run: |
          echo "::error::Error validating the slots. ${{ steps.validate-slots.outputs.error-message }}" | tr -s '\n' ' '
          exit 1

      - name: PaaS / Filter permutations by coordinates
        id: get-filtered-permutations
        uses: inditex/gha-paasdeployment/get-filtered-permutations@v0
        with:
          pr-list: '${{ needs.get-affected-prs.outputs.affected-prs }}'
          supraenvs: ${{ github.event.inputs.SUPRAENVS }}
          envs: ${{ github.event.inputs.ENVS }}
          platforms: ${{ github.event.inputs.PLATFORMS }}
          tenants: ${{ github.event.inputs.TENANTS }}
          slots: ${{ github.event.inputs.SLOTS }}

  rollout:
    name: PaaS / Run rollout command in PRs
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    needs: [validate-permutations, get-affected-prs]
    if: ${{ !contains(fromJSON('["merge", "cancel", "restore", "approve"]'), github.event.inputs.ACTION) }}
    outputs:
      summary: ${{ steps.run-rollout.outputs.summary }}
    steps:

      - name: "PaaS / Run rollout action ${{ github.event.inputs.ACTION}}"
        id: run-rollout
        uses: inditex/gha-paasdeployment/paas-deploy-instances/rollout-command@v0
        with:
          action: ${{ github.event.inputs.ACTION}}
          issue-number: "${{ github.event.inputs.ISSUE_NUMBER }}"
          skip: ${{ github.event.inputs.SKIP }}
          github-token: ${{ secrets.GITHUB_TOKEN }}
          affected-prs: '${{ needs.get-affected-prs.outputs.affected-prs }}'
          permutations: '${{ needs.validate-permutations.outputs.filtered-permutations }}'
          chatbot-token: ${{ secrets.CHATBOT_PR_PAT }}

  restore-init:
    name: PaaS / Restore Init
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    needs: [validate-permutations, get-affected-prs]
    if: ${{ github.event.inputs.ACTION == 'restore' }}
    outputs:
      permutations-data: ${{ steps.init-restore.outputs.permutations-data }}
      restore-issue-number: ${{ steps.init-restore.outputs.restore-issue-number }}

    steps:
      - name: PaaS / Init Restore action
        id: init-restore
        uses: inditex/gha-paasdeployment/paas-deploy-instances/init-restore@v0
        with:
          issue-number: "${{ github.event.inputs.ISSUE_NUMBER }}"
          validate-permutations-error-message: ${{ needs.validate-permutations.outputs.error-message }}
          affected-prs: '${{ needs.get-affected-prs.outputs.affected-prs }}'
          filtered-permutations: '${{ needs.validate-permutations.outputs.filtered-permutations }}'
          github-token: ${{ secrets.GITHUB_TOKEN }}

  restore:
    name: Restore affected PRs
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    needs: [restore-init]
    if: ${{ fromJson(needs.restore-init.outputs.permutations-data) }}
    strategy:
      fail-fast: false
      max-parallel: 4
      matrix:
        permutations: ${{ fromJson(needs.restore-init.outputs.permutations-data) }}

    steps:
      - name: PaaS / Execute restore action
        id: execute-restore
        uses: inditex/gha-paasdeployment/paas-deploy-instances/execute-restore@v0
        with:
          issue-number: ${{ github.event.inputs.ISSUE_NUMBER }}
          permutation-json: '${{ toJson(matrix.permutations) }}'
          restore-issue-number: ${{ needs.restore-init.outputs.restore-issue-number }}
          artifact-path: ${{ env.DIR_PATH_STATUS }}
          github-token: ${{ secrets.GITHUB_TOKEN }}
          chatbot-token: ${{ secrets.CHATBOT_PR_PAT }}
          committer-pgp-key: ${{ secrets.BUTLER_PGP_KEY }}

  restore-summary:
    if: ${{ success() && !cancelled() && (github.event.inputs.ISSUE_NUMBER != '') }}
    name: Restore summary
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    needs: [restore, restore-init]
    outputs:
      summary: ${{ steps.summary-restore.outputs.summary }}

    steps:
      - name: PaaS / Summary restore action
        id: summary-restore
        uses: inditex/gha-paasdeployment/paas-deploy-instances/summary-restore@v0
        with:
          issue-number: ${{ github.event.inputs.ISSUE_NUMBER }}
          restore-issue-number: ${{ needs.restore-init.outputs.restore-issue-number }}
          permutations-data: ${{ needs.restore-init.outputs.permutations-data }}
          artifact-path: ${{ env.DIR_PATH_STATUS }}
          github-token: ${{ secrets.GITHUB_TOKEN }}

  mimic-init:
    name: "PaaS / Init Mimic action ${{ github.event.inputs.ACTION}}"
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    needs: [validate-permutations, get-affected-prs]
    if: contains(fromJSON('["approve", "merge" ,"cancel"]'), github.event.inputs.ACTION)
    outputs:
      permutations-data: ${{ steps.init-mimic.outputs.permutations-data }}
      login: ${{ steps.init-mimic.outputs.login }}
    permissions:
      id-token: write
      issues: write
      contents: write
    steps:
      - name: "PaaS / Init Mimic action ${{ github.event.inputs.ACTION}}"
        id: init-mimic
        uses: inditex/gha-paasdeployment/paas-deploy-instances/init-mimic@v0
        with:
          action: ${{ github.event.inputs.ACTION}}
          issue-number: "${{ github.event.inputs.ISSUE_NUMBER }}"
          issue-comment-id: ${{ github.event.inputs.ISSUE_COMMENT_ID }}
          github-token: ${{ secrets.GITHUB_TOKEN }}
          affected-prs: '${{ needs.get-affected-prs.outputs.affected-prs }}'
          permutations: '${{ needs.validate-permutations.outputs.filtered-permutations }}'

  mimic-exec:
    name: "PaaS / Execute action ${{ github.event.inputs.ACTION}} in affected PRs with MIMIC"
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    needs: [mimic-init]
    if: ${{ needs.mimic-init.outputs.permutations-data != '' && needs.mimic-init.outputs.permutations-data != '[]' }}
    permissions:
      id-token: write
      issues: write
      contents: write
    strategy:
      fail-fast: false
      max-parallel: 1
      matrix:
        permutations: ${{ fromJson(needs.mimic-init.outputs.permutations-data) }}
    steps:
      - name: "PaaS / Execute mimic action ${{ github.event.inputs.ACTION }}"
        id: execute-mimic
        uses: inditex/gha-paasdeployment/paas-deploy-instances/execute-mimic@v0
        with:
          action: ${{ github.event.inputs.ACTION }}
          user-login: ${{ needs.mimic-init.outputs.login }}
          branch: ${{ matrix.permutations.headRefName }}
          github-token: ${{ secrets.GITHUB_TOKEN }}
          pr-number: ${{ matrix.permutations.number }}
          mimic-app-id: ${{ vars.MIMIC_APP_ID }}
          artifact-path: ${{ env.DIR_PATH_STATUS }}

  mimic-summary:
    if: ${{ !failure() && !cancelled() && (github.event.inputs.ISSUE_NUMBER != '') && contains(fromJSON('["approve", "merge" ,"cancel"]'), github.event.inputs.ACTION) }}
    name: "PaaS / Summary mimic action ${{ github.event.inputs.ACTION}}"
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    needs: [mimic-init, mimic-exec]
    outputs:
      summary: ${{ steps.summary-mimic.outputs.summary }}
    steps:
      - name: PaaS / Summary mimic action
        id: summary-mimic
        uses: inditex/gha-paasdeployment/paas-deploy-instances/summary-mimic@v0
        with:
          issue-number: "${{ github.event.inputs.ISSUE_NUMBER }}"
          action: ${{ github.event.inputs.ACTION}}
          github-token: ${{ secrets.GITHUB_TOKEN }}
          artifact-path: ${{ env.DIR_PATH_STATUS }}
          permutations-data: ${{needs.mimic-init.outputs.permutations-data}}

  summary:
    name: PaaS / Summary
    runs-on: ${{ fromJSON( vars.RUNSON_PAAS_GENERIC || '["ubuntu-24.04"]' ) }}
    if: ${{ !failure() && !cancelled() }}
    needs: [rollout, restore-summary, mimic-summary]
    steps:
      - name: PaaS / Publish summary comment
        id: publish-summary-comment
        uses: inditex/gha-paasdeployment/paas-deploy-instances/publish-summary-comment@v0
        with:
          issue-number: "${{ github.event.inputs.ISSUE_NUMBER }}"
          issue-comment-id: ${{ github.event.inputs.ISSUE_COMMENT_ID }}
          message: '${{ needs.rollout.outputs.summary || needs.restore-summary.outputs.summary || needs.mimic-summary.outputs.summary }}'
          chatbot-token: ${{ secrets.CHATBOT_PR_PAT }}
