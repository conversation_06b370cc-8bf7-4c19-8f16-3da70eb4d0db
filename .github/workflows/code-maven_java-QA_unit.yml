---
name: code-maven-QA-unit
run-name: QA unit testing in ${{ github.base_ref || github.ref_name }} branch

concurrency:
  group: qa-unit-${{ github.ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:
    inputs:
      ATTACH_TARGET:
        description: Target platform to upload the generated report, it does not export results if empty. (p.e. jfrog)
        required: false
  pull_request:
    types: [closed]
    branches: ['develop', 'develop-*', 'main', 'main-*']
    paths: ['code/**', '.github/workflows/code-*-QA_unit.yml']
  release:
    types:
      - published

env:
  WORKFLOW_VERSION: 2.29.0
  REF: ${{ github.base_ref || github.ref_name }}
  DP_USERNAME: ${{ secrets.DIST_PLAT_READER_USERNAME }}
  DP_TOKEN: ${{ secrets.DIST_PLAT_READER_TOKEN }}
  DT_ENDPOINT: ${{ secrets.DT_ENDPOINT }}
  DT_SECRET: ${{ secrets.DT_SECRET }}

jobs:
  unit-tests:
    name: SonarCloud / Unit Tests
    if: ${{ (github.event.pull_request.merged == true && (vars.DEVELOPMENT_FLOW != 'trunk-based-development' && (github.base_ref == 'develop' || startsWith(github.base_ref, 'develop-'))) ||
        (vars.DEVELOPMENT_FLOW == 'trunk-based-development' && (github.base_ref == 'main' || startsWith(github.base_ref, 'main-')))) ||
        github.event_name == 'workflow_dispatch' ||
        github.event_name == 'release' }}
    timeout-minutes: 30
    runs-on: ${{ fromJSON( vars.RUNSON_SONARQUBE_MAVEN_JAVA_ONPREM || '["citool-icr__code-ubuntu24.04-large"]' ) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Check where config_test is
        id: check-where-config-test-is
        env:
          CONFIG_TEST_FILE: code/src/test/resources/config_test.yml
        run: |
          if [[ -f "${CONFIG_TEST_FILE}" ]]; then
            echo "config-test-directory=${CONFIG_TEST_FILE}" >> "$GITHUB_OUTPUT"
          elif [[ -f "code/config_test/config_test.yml" ]]; then
            echo "config-test-directory=code/config_test/config_test.yml" >> "$GITHUB_OUTPUT"
          else
            echo "config_test.yml file not found"
          fi

      - name: Setup IVM environment
        uses: inditex/gha-ivmactions/setup-environment@v1
        with:
          tool-versions-directory: code
          distro-branch: ${{ vars.TOOLVER_DISTRO_BRANCH }}

      - name: Resolve Configuration
        id: config
        uses: inditex/actions/resolvers/config-resolver@main
        with:
          create-annotations: true
          files: |
            application.yml
            ${{ steps.check-where-config-test-is.outputs.config-test-directory }}

      - name: Store project version
        id: pom-data
        run: |
          echo "app-version=$(xmllint --xpath "/*[name()='project']/*[name()='version']/text()" code/pom.xml)" >> "$GITHUB_OUTPUT"

      - name: Load custom secrets as environment variables
        id: creds-resolver
        uses: inditex/gha-citool/creds-resolver-cloud@v0
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          path-to-secrets: .github/secrets/secrets.yml
          output-format: kvequal
          install-paascli: true
          github_token: ${{ secrets.GH_TOKEN_READER }}

      - name: Run unit tests with release event
        if: github.event_name == 'release'
        working-directory: code
        run: |
          mvn -B clean verify -DskipEnforceSnapshots -Djacoco.skip=false -Damiga.jacoco -DskipITs -DfailIfNoTests=false -Dmaven.test.failure.ignore=false

      - name: Run unit tests
        if: github.event_name != 'release'
        working-directory: code
        run: |
          mvn -B clean verify -Djacoco.skip=false -Damiga.jacoco -DskipITs -DfailIfNoTests=false -Dmaven.test.failure.ignore=false

      - name: Sonar / Disable project-specific properties
        working-directory: ${{ github.workspace }}/code
        run: |
          find ./ -name pom.xml -exec sed -i 's/<sonar./<disabled./g' {} +
          find ./ -name pom.xml -exec sed -i 's/<\/sonar./<\/disabled./g' {} +

      - name: Sonar / Configure input parameters
        working-directory: ${{ github.workspace }}/code
        run: |
          if [[ -f './jacoco-report-aggregate/pom.xml' ]]; then JACOCO_PARAM="-pl !jacoco-report-aggregate -Dsonar.skip=false"; else JACOCO_PARAM="-Dsonar.skip=false"; fi; \
          echo "JACOCO_SONAR_PARAM=$JACOCO_PARAM" >> "$GITHUB_ENV"

      - name: Sonar / Setup Node version
        uses: inditex/gha-ivmactions/setup-tool@v1
        with:
          local-directory: code
          tool-name: ivm-node
          tool-version: 20

      - name: Sonar / Setup Java version
        uses: inditex/gha-ivmactions/setup-tool@v1
        with:
          tool-name: ivm-java
          tool-version: openjdk-17

      - name: SonarCloud / Run Maven Sonar goal with release event
        env:
          LOGIN: ${{ secrets.SONARCLOUD_TOKEN }}
          SONAR_SCANNER_OPTS: ''
        if: ${{ github.event_name == 'release' }}
        working-directory: code
        run: |
          JACOCO_REPORT_PATH="$GITHUB_WORKSPACE/code/jacoco-report-aggregate/target/site/jacoco-aggregate/jacoco.xml"
          mvn org.sonarsource.scanner.maven:sonar-maven-plugin:3.9.1.2184:sonar \
            -Dsonar.projectKey=inditexcodingfashion_"${{ fromJSON(steps.config.outputs.config).metadata.project_key }}" \
            -Dsonar.projectName="${{ fromJSON(steps.config.outputs.config).metadata.project_name }}" \
            -Dsonar.projectVersion="${{ github.event.release.tag_name }}" \
            -Dsonar.branch.name="release/${{ github.event.release.tag_name }}" \
            -Dsonar.host.url="https://sonarcloud.io/" \
            -Dsonar.organization=inditex-sonarcloud \
            -Dsonar.token="${LOGIN}" \
            -Dsonar.coverage.jacoco.xmlReportPaths="$JACOCO_REPORT_PATH"

      - name: SonarCloud / Run Maven Sonar goal
        env:
          LOGIN: ${{ secrets.SONARCLOUD_TOKEN }}
          SONAR_SCANNER_OPTS: ''
        if: ${{ github.event_name != 'release' }}
        working-directory: code
        run: |
          JACOCO_REPORT_PATH="$GITHUB_WORKSPACE/code/jacoco-report-aggregate/target/site/jacoco-aggregate/jacoco.xml"
          mvn org.sonarsource.scanner.maven:sonar-maven-plugin:3.9.1.2184:sonar \
            -Dsonar.projectKey=inditexcodingfashion_"${{ fromJSON(steps.config.outputs.config).metadata.project_key }}" \
            -Dsonar.projectName="${{ fromJSON(steps.config.outputs.config).metadata.project_name }}" \
            -Dsonar.projectVersion="${{ steps.pom-data.outputs.app-version }}" \
            -Dsonar.branch.name="${{ env.REF }}" \
            -Dsonar.host.url="https://sonarcloud.io/" \
            -Dsonar.organization=inditex-sonarcloud \
            -Dsonar.token="${LOGIN}" \
            -Dsonar.coverage.jacoco.xmlReportPaths="$JACOCO_REPORT_PATH"


      - name: Generate Surefire HTML report
        if: ${{ always() && !cancelled() && fromJSON(steps.config.outputs.config).junit.unit.results.surefire_html_report_folder.path != null }}
        working-directory: code
        continue-on-error: true
        run: |
          mvn surefire-report:report-only site:site -DoutputName=index -DalwaysGenerateSurefireReport=true -DgenerateReports=false

      - name: Attach results
        if: ${{ always() && !cancelled() && contains(inputs.ATTACH_TARGET, 'jfrog') }}
        continue-on-error: true
        uses: inditex/actions/upload-artifact@main
        with:
          path: ${{ steps.check-where-config-test-is.outputs.config-test-directory }}
          keys: junit.unit
        env:
          DP_ARTIFACTS_STORAGE_USER: ${{ secrets.DIST_PLAT_DEPLOYER_USERNAME }}
          DP_ARTIFACTS_STORAGE_TOKEN: ${{ secrets.DIST_PLAT_DEPLOYER_TOKEN }}

      - name: Publish unit test results to DEVINS
        if: github.event_name != 'release'
        env:
          SONARCLOUD_TOKEN: ${{ secrets.SONARCLOUD_TOKEN }}
        run: |
          adam publishUnitTest \
            --projectKey="${{ fromJSON(steps.config.outputs.config).metadata.project_key }}" \
            --workspace="${{ github.workspace }}" \
            --version="${{ steps.pom-data.outputs.app-version }}" \
            --surefirePath="${{ github.workspace }}/**/${{ fromJSON(steps.config.outputs.config).junit.unit.results.surefire_xml_report_path.path }}" \
            --jacocoPath="${{ github.workspace }}/${{ fromJSON(steps.config.outputs.config).junit.unit.results.jacoco_report_folder.path }}/index.html"
          adam publishSonarTest \
            --taskPath="${{ github.workspace }}/code/target/sonar/report-task.txt" \
            --version="${{ steps.pom-data.outputs.app-version }}"
          adam publishExclusions \
            --projectKey="${{ fromJSON(steps.config.outputs.config).metadata.project_key }}" \
            --workspace="${{ github.workspace }}"

      - name: Publish unit test results to DEVINS
        if: github.event_name == 'release'
        env:
          SONARCLOUD_TOKEN: ${{ secrets.SONARCLOUD_TOKEN }}
        run: |
          adam publishUnitTest \
            --projectKey="${{ fromJSON(steps.config.outputs.config).metadata.project_key }}" \
            --workspace="${{ github.workspace }}" \
            --version="${{ github.event.release.tag_name }}" \
            --surefirePath="${{ github.workspace }}/**/${{ fromJSON(steps.config.outputs.config).junit.unit.results.surefire_xml_report_path.path }}" \
            --jacocoPath="${{ github.workspace }}/${{ fromJSON(steps.config.outputs.config).junit.unit.results.jacoco_report_folder.path }}/index.html"
          adam publishSonarTest \
            --taskPath="${{ github.workspace }}/code/target/sonar/report-task.txt" \
            --version="${{ github.event.release.tag_name }}"
          adam publishExclusions \
            --projectKey="${{ fromJSON(steps.config.outputs.config).metadata.project_key }}" \
            --workspace="${{ github.workspace }}"

      - name: Display results
        if: ${{ always() && !cancelled() }}
        continue-on-error: true
        run: |
          if [ -f "message" ]
          then
              MESSAGE=$(cat "message" 2>/dev/null)
              MESSAGE=$(echo "$MESSAGE" | tr -s ' ')
              echo "::notice title=Results [jacoco] ::$MESSAGE"
          fi
          if [ -f "messageSurefire" ]
          then
              MESSAGE=$(cat "messageSurefire" 2>/dev/null)
              MESSAGE=$(echo "$MESSAGE" | tr -s ' ')
              echo "::notice title=Results [karate] ::$MESSAGE"
          fi
          if [ -f "messageSonar" ]
          then
              MESSAGE=$(cat "messageSonar" 2>/dev/null)
              MESSAGE=$(echo "$MESSAGE" | tr -s ' ')
              echo "::notice title=Results [sonar] ::$MESSAGE"
          fi

      - name: Create a Workflow Status Check to link this execution with a PR
        if: always() && !cancelled() && github.event_name == 'workflow_dispatch'
        uses: inditex/gha-citool/create-status-check@v0
        with:
          app_id: ${{ vars.WFSC_APP_ID }}
          app_private_key: ${{ secrets.WFSC_APP_PKEY }}
          name: "code-QA-unit"
          summary: |
            This status check represents the conclusion (__${{ job.status }}__) of the last dispatched execution of workflow:
            `${{ github.workflow_ref }}`

            You can find more details in the link below :arrow_down:
