name: scheduled-code-maven-QA-unit
on:
  schedule:
    # Cron syntax has five fields separated by a space, and each field represents a unit of time.
    #
    # ┌───────────── minute (0 - 59)
    # │ ┌───────────── hour (0 - 23)
    # │ │ ┌───────────── day of the month (1 - 31)
    # │ │ │ ┌───────────── month (1 - 12 or JAN-DEC)
    # │ │ │ │ ┌───────────── day of the week (0 - 6 or SUN-SAT)
    # │ │ │ │ │
    # │ │ │ │ │
    # │ │ │ │ │
    # * * * * *
    - cron:  '12 04 * * 1-5'  # runs since Monday to Friday at 04:12 (UTC)
env:
  WORKFLOW_VERSION: SCHEDULED

jobs:
  code-maven-QA-unit:
    name: code-maven-QA-unit
    runs-on: [ self-hosted, testing-ivm ]
    steps:
      - name: code-maven-QA-unit
        uses: benc-uk/workflow-dispatch@4c044c1613fabbe5250deadc65452d54c4ad4fc7
        with:
          workflow: code-maven-QA-unit
          token: ${{ secrets.GH_TOKEN_PUSH }}
          ref: ${{ github.base_ref }}
