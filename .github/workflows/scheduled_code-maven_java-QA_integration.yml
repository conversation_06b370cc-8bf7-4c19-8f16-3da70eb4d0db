name: scheduled-code-maven-QA-integration
on:
  schedule:
    # Cron syntax has five fields separated by a space, and each field represents a unit of time.
    #
    # ┌───────────── minute (0 - 59)
    # │ ┌───────────── hour (0 - 23)
    # │ │ ┌───────────── day of the month (1 - 31)
    # │ │ │ ┌───────────── month (1 - 12 or JAN-DEC)
    # │ │ │ │ ┌───────────── day of the week (0 - 6 or SUN-SAT)
    # │ │ │ │ │
    # │ │ │ │ │
    # │ │ │ │ │
    # * * * * *
    - cron:  '32 04 * * 1-5'  # runs since Monday to Friday at 04:32 (UTC)
env:
  WORKFLOW_VERSION: SCHEDULED

jobs:
  code-maven-QA-integration:
    name: code-maven-QA-integration
    runs-on: [ self-hosted, testing-ivm ]
    steps:
      - name: code-maven-QA-integration
        uses: benc-uk/workflow-dispatch@4c044c1613fabbe5250deadc65452d54c4ad4fc7
        with:
          workflow: code-maven-QA-integration
          token: ${{ secrets.GH_TOKEN_PUSH }}
          ref: ${{ github.base_ref }}
