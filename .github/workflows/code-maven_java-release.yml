---
name: code-maven-release
run-name: "Release labeled ${{ inputs.RELEASE_TYPE || 'in PR' }}"

concurrency: code-release-${{ github.ref }}

on:
  pull_request:
    types: [closed]
    branches: ['main', 'main-*']
    paths: ['code/**', '.github/workflows/code**']
  workflow_dispatch:
    inputs:
      BASELINE:
        description: 'Baseline branch'
        required: true
        default: 'main'
      RELEASE_TYPE:
        description: 'Release type to use'
        required: true
        default: 'release-type/current'
        type: choice
        options:
          - 'release-type/current'
          - 'release-type/hotfix'
          - 'release-type/multi-hotfix'
          - 'release-type/major'
          - 'release-type/minor'
          - 'release-type/patch'
      RELEASE_MODE:
        description: 'Special release mode to use (OPTIONAL)'
        type: choice
        required: false
        default: 'none'
        options:
          - 'none'
          - 'release-mode/skip-changelog'
          - 'release-mode/yank-previous'
      FORCE:
        description: 'Force the generation of the release, skipping the pre-checks (OPTIONAL)'
        required: false
        default: false
        type: boolean

env:
  WORKFLOW_VERSION: 2.35.0
  GITHUB_REGISTRY: ghcr.io
  DP_TOKEN: ${{ secrets.DIST_PLAT_DEPLOYER_TOKEN }}
  DP_USERNAME: ${{ secrets.DIST_PLAT_DEPLOYER_USERNAME }}
  JFROG_REGISTRY: inditex-docker.jfrog.io
  ASDF_IMAGE_NAME: inditex/ci-agents/asdf
  CHANGELOG_DRAFTER_IMAGE_NAME: inditex/ci-agents/changelog-drafter
  CHANGELOG_DRAFTER_IMAGE_TAG: 2.20.4-node-12-ubuntu-r1
  SCM_EMAIL: <EMAIL>
  SCM_TOKEN: ${{ secrets.GH_TOKEN_PUSH }}
  SCM_USERNAME: butler-ci-bot
  SCM_COMMITTER_PGP_KEY: ${{ secrets.BUTLER_PGP_KEY }}
  NPM_EMAIL: ${{ secrets.DIST_PLAT_DEPLOYER_USERNAME }}
  NPM_AUTH: ${{ secrets.DIST_PLAT_DEPLOYER_NPM_AUTH }}

jobs:
  release:
    name: Release
    if: github.event_name == 'workflow_dispatch'
      || (github.event.pull_request.merged == true && !contains(join(github.event.pull_request.labels.*.name, ', '), 'skip-release')
        && (contains(join(github.event.pull_request.labels.*.name, ', '), 'release-type')
        || vars.DEVELOPMENT_FLOW == 'trunk-based-development' && !contains(join(github.event.pull_request.labels.*.name, ', '), 'butler/feature-provision')))
    runs-on: ${{ fromJSON(vars.RUNSON_CODE_MAVEN_JAVA_GENERIC || '["ubuntu-24.04"]') }}
    permissions:
      actions: write
      attestations: write
      checks: write
      contents: write
      deployments: write
      discussions: write
      issues: write
      packages: write
      pages: write
      id-token: write
      pull-requests: write
      repository-projects: write
      security-events: write
      statuses: write
    steps:
      - name: Get input parameters
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            RELEASE_LABELS="${{ github.event.inputs.RELEASE_TYPE }}, ${{ github.event.inputs.RELEASE_MODE }}"
            if [[ "${{ github.event.inputs.FORCE }}" == "true" ]]; then
              RELEASE_LABELS="$RELEASE_LABELS, release-mode/force"
            fi
          else
            RELEASE_LABELS="${{ join(github.event.pull_request.labels.*.name, ', ') }}"
            if [[ $RELEASE_LABELS != *release-type/* ]]; then
              RELEASE_LABELS="$RELEASE_LABELS, release-type/current"
            fi
          fi
          echo "RELEASE_LABELS=$RELEASE_LABELS" >> "$GITHUB_ENV"
          BASELINE_BRANCH=${{ github.event.inputs.BASELINE || github.ref }}
          echo "BASELINE_BRANCH=${BASELINE_BRANCH#refs/heads/}" >> "$GITHUB_ENV"

      - name: Setup current release type version
        if: contains(env.RELEASE_LABELS, 'release-type/current')
        run: echo "CD_ARGS=release" >> "$GITHUB_ENV"

      - name: Setup patch release type version
        if: contains(env.RELEASE_LABELS, 'release-type/hotfix')
          || contains(env.RELEASE_LABELS, 'release-type/multi-hotfix')
          || contains(env.RELEASE_LABELS, 'release-type/patch')
        run: |
          echo "CD_ARGS=release --patch" >> "$GITHUB_ENV"
          echo "MVN_RELEASE_TYPE=-DreleaseType=patch" >> "$GITHUB_ENV"

      - name: Setup minor release type version
        if: contains(env.RELEASE_LABELS, 'release-type/minor')
        run: |
          echo "CD_ARGS=release --minor" >> "$GITHUB_ENV"
          echo "MVN_RELEASE_TYPE=-DreleaseType=minor" >> "$GITHUB_ENV"

      - name: Setup major release type version
        if: contains(env.RELEASE_LABELS, 'release-type/major')
        run: |
          echo "CD_ARGS=release --major" >> "$GITHUB_ENV"
          echo "MVN_RELEASE_TYPE=-DreleaseType=major" >> "$GITHUB_ENV"

      - name: Setup force option
        if: contains(env.RELEASE_LABELS, 'release-mode/force')
        run: |
          echo "CD_ARGS=$CD_ARGS --force-release-mode" >> "$GITHUB_ENV"
          echo "MVN_RELEASE_ARGS=-DreleaseForce" >> "$GITHUB_ENV"

      - name: Force release if the yank-previous label is present
        if: contains(env.RELEASE_LABELS, 'release-mode/yank-previous')
        run: |
          echo "CD_ARGS=$CD_ARGS --force-release-mode" >> "$GITHUB_ENV"
          echo "MVN_RELEASE_ARGS=-DreleaseForce" >> "$GITHUB_ENV"

      - name: Checkout merge commit
        uses: actions/checkout@v4
        with:
          ref: ${{ env.BASELINE_BRANCH }}
          fetch-depth: 0
          persist-credentials: false

      - name: Infer ci-agents asdf image
        uses: inditex/gha-ivmactions/infer-ci-agent-asdf@v1
        env:
          ASDF_UBUNTU_IMAGE_TAG: 0.14.0-ubuntu24.04-r1
          ASDF_RHUBI_IMAGE_TAG: 0.14.0-rhubi-r5
          ASDF_CENTOS_IMAGE_TAG: 0.14.0-centos-r3
        with:
          tool-versions-directory: code
          github-token: ${{ secrets.GH_TOKEN_READER }}

      - name: Setup Maven Cache
        uses: actions/cache@v4
        continue-on-error: true
        with:
          path: ~/.m2/repository
          key: ${{ env.ASDF_IMAGE_TAG }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ env.ASDF_IMAGE_TAG }}-maven-

      - name: Setup asdf Cache
        uses: actions/cache@v4
        continue-on-error: true
        with:
          path: ~/.asdf/data
          key: ${{ env.ASDF_IMAGE_TAG }}-asdf-${{ hashFiles('**/.tool-versions') }}
          restore-keys: |
            ${{ env.ASDF_IMAGE_TAG }}-asdf-

      - name: Log in to GitHub Registry
        run: echo "${{ secrets.GH_TOKEN_READER }}" | docker login "${GITHUB_REGISTRY}" -u none --password-stdin

      - name: Log in to JFrog Registry
        run: echo "${DP_TOKEN}" | docker login "${JFROG_REGISTRY}" -u "${DP_USERNAME}" --password-stdin

      - name: Prepare committer information
        run: |
          echo "$SCM_COMMITTER_PGP_KEY" | gpg --import
          git config user.name "$SCM_USERNAME"
          git config user.email "$SCM_EMAIL"
          git config commit.gpgsign true

      - name: Yank previous release
        if: contains(env.RELEASE_LABELS, 'release-mode/yank-previous')
        run: |
          # shellcheck disable=SC2086
          docker run --workdir /app/code \
            -u "$(id -u)" \
            -e GIT_AUTHOR_NAME=${SCM_USERNAME} -e GIT_AUTHOR_EMAIL=${SCM_EMAIL} -e EMAIL=${SCM_EMAIL} \
            -e ISSUE_TRACKER_PASSWORD=${{ secrets.GITHUB_TOKEN }} \
            --mount type=bind,src="$(pwd)",dst=/app \
            --mount type=bind,src="$HOME/.gnupg",dst=/home/<USER>/.gnupg \
            "${GITHUB_REGISTRY}/${CHANGELOG_DRAFTER_IMAGE_NAME}:${CHANGELOG_DRAFTER_IMAGE_TAG}" \
            changelog-drafter yank --headless --commit --drafterconfig-path .ci-drafterconfig.yml > /tmp/.yanked-version
          echo "MVN_RELEASE_ARGS=$MVN_RELEASE_ARGS -DyankRelease=$(cat /tmp/.yanked-version)" >> "$GITHUB_ENV"

      - name: Load custom secrets as environment variables
        id: creds-resolver
        uses: inditex/gha-citool/creds-resolver-cloud@v0
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          path-to-secrets: .github/secrets/secrets.yml
          output-format: kvequal
          install-paascli: true
          github_token: ${{ secrets.GH_TOKEN_READER }}

      - name: Update CHANGELOG.md
        if: ${{ !contains(env.RELEASE_LABELS, 'release-mode/skip-changelog') }}
        run: |
          # shellcheck disable=SC2086
          docker run --workdir /app/code \
            -u "$(id -u)" \
            -e ISSUE_TRACKER_JIRA_USERNAME=${{ secrets.SRVCJIRAGITHUBAX_USERNAME }} \
            -e ISSUE_TRACKER_JIRA_SECRET=${{ secrets.SRVCJIRAGITHUBAX_PASSWORD }} \
            -e ISSUE_TRACKER_EXTRAHEADERS='{"itx-apikey":"${{ secrets.JANUS_JIRASIST_APIKEY }}"}' \
            -e ISSUE_TRACKER_GITHUB_SECRET="${{ secrets.GITHUB_TOKEN }}" \
            --mount type=bind,src="$(pwd)",dst=/app \
            --mount type=bind,src="$HOME"/.gnupg,dst=/home/<USER>/.gnupg \
            "${GITHUB_REGISTRY}/${CHANGELOG_DRAFTER_IMAGE_NAME}:${CHANGELOG_DRAFTER_IMAGE_TAG}" \
            changelog-drafter $CD_ARGS --headless --commit --drafterconfig-path .ci-drafterconfig.yml > /tmp/.version
          echo "MVN_RELEASE_ARGS=$MVN_RELEASE_ARGS -DreleaseVersion=$(cat /tmp/.version)" >> "$GITHUB_ENV"
          echo "RELEASE_VERSION=$(cat /tmp/.version)" >> "$GITHUB_ENV"

      - name: Maven / Set Maven Build Cache options
        id: maven-opts
        if: ${{ vars.MAVEN_BUILD_CACHE_REMOTE == 'true' }}
        uses: inditex/gha-citool/set-maven-build-cache@v0
        with:
          save-remote-enabled: false
          skip-cache: true

      - name: Get runner resources
        continue-on-error: true
        uses: inditex/gha-citool/get-runner-resources@v0
        with:
          memory_magnitude: ${{ vars.RUNNER_TOTAL_MEMORY_MAGNITUDE || 'MB' }}

      - name: Release Artifact
        env:
          MVN_BUILD_CACHE_OPTS: ${{ steps.maven-opts.outputs.maven-build-cache-opts }}
        run: |
          mkdir -p "$HOME"/.m2/repository
          mkdir -p "$HOME"/.asdf/data
          # shellcheck disable=SC2086
          docker run --workdir /app/code \
            -u "$(id -u)" \
            ${{ steps.creds-resolver.outputs.creds-docker }} \
            -e TOTAL_CPUS \
            -e TOTAL_MEMORY \
            -e SCM_USERNAME -e SCM_TOKEN \
            -e DP_USERNAME -e DP_TOKEN \
            -e NPM_EMAIL -e NPM_AUTH \
            -e GITHUB_TOKEN=${{ secrets.GH_TOKEN_READER }} \
            -e TOOLVER_DISTRO_BRANCH=${{ vars.TOOLVER_DISTRO_BRANCH }} \
            --mount type=bind,src="$(pwd)",dst=/app \
            --mount type=bind,src="$HOME"/.m2/repository,dst=/home/<USER>/.m2/repository \
            --mount type=bind,src="$HOME"/.gnupg,dst=/home/<USER>/.gnupg \
            --mount type=bind,src="$HOME"/.asdf/data,dst=/opt/asdf/data \
            "${GITHUB_REGISTRY}/${ASDF_IMAGE_NAME}:${ASDF_IMAGE_TAG}" \
            mvn -B release:prepare release:perform $MVN_RELEASE_TYPE $MVN_RELEASE_ARGS $MVN_BUILD_CACHE_OPTS

      - name: GitHub Release / Get final version number from latest tag in branch
        if: ${{ contains(env.RELEASE_LABELS, 'release-mode/skip-changelog') }}
        run: echo "RELEASE_VERSION=$(git describe --tags --abbrev=0)" >> "$GITHUB_ENV"

      - name: Github Release / Create
        uses: ncipollo/release-action@2c591bcc8ecdcd2db72b97d6147f871fcd833ba5
        id: github-release
        continue-on-error: true
        with:
          name: ${{ env.RELEASE_VERSION }}
          tag: ${{ env.RELEASE_VERSION }}
          token: ${{ secrets.GH_TOKEN_PUSH }}
          body: |
            Check out the [changelog](code/CHANGELOG.md) for version ${{ env.RELEASE_VERSION }}

      - name: Metrics / Report Size
        uses: inditex/gha-pmsactions/size-reporter@v0
        continue-on-error: true
        with:
          REPOSITORY_PATH: code
          COMMIT_SHA: ${{ github.sha }}
          BRANCH: ${{ github.base_ref || github.head_ref || github.ref_name }}
          BUILD_TYPE: RELEASE
          TECHNOLOGY: maven_java
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN_READER }}
          VERSION: ${{ env.RELEASE_VERSION }}

      - name: Comment in PR / Sync PR creation failed
        if: ${{ vars.DEVELOPMENT_FLOW != 'trunk-based-development' }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          DEVELOP=${BASELINE_BRANCH/main/develop}
          git remote set-url origin "https://x-access-token:$<EMAIL>/$GITHUB_REPOSITORY"
          # shellcheck disable=SC2140
          if ! git ls-remote --exit-code --heads origin automated/sync-release-"${{ env.RELEASE_VERSION }}"-to-"$DEVELOP"; then
            gh pr comment "${{ github.event.number }}" --body "An error occurred creating the \`sync\` branch that synchronizes the \`$BASELINE_BRANCH\` and \`$DEVELOP\` branches.
            Please create a branch from \`$BASELINE_BRANCH\` (e.g. \`internal/sync-$BASELINE_BRANCH-with-$DEVELOP\`) and then create a pull request against \`$DEVELOP\` to finish the release process."
          fi

      - name: Comment in PR / Release creation failed
        if: ${{ steps.github-release.outcome == 'failure' }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: >
          gh pr comment ${{ github.event.number }} --body "An error occurred creating the Github Release.
          Don't panic! Your artifacts were successfully uploaded to the Distribution Platform and the new release tag was created.
          You can manually complete the release by creating it in the [releases](https://github.com/inditex/%7Brepo%7D/releases)
          page or opening up an issue at [inditex/community](https://github.com/inditex/community/) where you will have assistance."
