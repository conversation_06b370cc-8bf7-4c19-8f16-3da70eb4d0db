---
name: icarus-load-testing
run-name: "Args: action=${{ inputs.ACTION }}, scenario=${{ inputs.SCENARIO }}"

on:
  workflow_dispatch:
    inputs:
      ACTION:
        description: 'Action to be performed'
        required: true
        type: choice
        options:
          - start
          - stop
      SCENARIO:
        description: "The name of the test-plan.yml scenario to run"
        required: false
      ISSUE_NUMBER:
        description: 'ID number (issue or pull request)'
        required: false

env:
  WORKFLOW_VERSION: 2.4.0

jobs:
  load-testing:
    name: ICaRUS / Load testing
    timeout-minutes: 5
    runs-on: ${{ fromJSON(vars.RUNSON_ICARUS_ONPREM) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Resolve Configuration
        id: config
        uses: inditex/actions/resolvers/config-resolver@main
        with:
          create-annotations: true
          files: |
            application.yml
          input-vars: "${{ toJSON(github.event.inputs) }}"


      - name: Get branch name
        id: branch-name
        shell: python
        run: |
          import os
          ref = "${{ github.event.ref }}"
          prefix = "refs/heads/"

          if prefix not in ref:
            print(f'::error ::The reference "{ ref }" is not a branch!')
            exit(1)

          branch = ref[len(prefix):]

          github_output = os.getenv('GITHUB_OUTPUT')
          with open(github_output, "a") as gh_out:
            gh_out.write(f'branch={ branch }\n')

      - name: Run load testing action
        uses: inditex/gha-sreactions/icarus/test-load@icarus-latest
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          project-key: ${{ fromJSON(steps.config.outputs.config).metadata.project_key }}
          action: ${{ github.event.inputs.ACTION }}
          scenario-name: ${{ github.event.inputs.SCENARIO }}
          branch-name: ${{ steps.branch-name.outputs.branch }}
          api-username: oauthicaconser
          api-password: cyberark.AX-N-PUBLIC-SRE-TOOLS.OAUTH2ClientsPRO_oauthicaconser
          github-issue-id: ${{ github.event.inputs.ISSUE_NUMBER }}
          ceph-server-url: https://axprergwlb1.central.inditex.grp
          ceph-bucket-name: preint-icarus-storage
          ceph-access-key-id: cyberark.AX-N-DIG-INFRA-CEPH.Application-ITX-APP-500-SC-Generic-axprergwlb1.central.inditex.grp-pre-icarus_accesskey
          ceph-secret-access-key: cyberark.AX-N-DIG-INFRA-CEPH.Application-ITX-APP-500-SC-Generic-axprergwlb1.central.inditex.grp-pre-icarus_secretkey

      - name: Add comment when the workflow is failed
        if: ${{ fromJSON(steps.config.outputs.inputs).ISSUE_NUMBER && failure() && !cancelled() }}
        uses: inditex/gha-citool/add-comment@v0
        with:
          issue-number: "${{ github.event.inputs.ISSUE_NUMBER }}"
          body: |
            #### :x: An error has ocurred. See the [workflow log](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}).
        env:
          GITHUB_TOKEN: ${{ secrets.CHATBOT_PR_PAT }}
