---
name: paas-gossip-upload-config

on:
  push:
    branches: [main]

env:
  WORKFLOW_VERSION: 3.4.1
  GOSSIP_SERVER_URL: https://apigw.apps.axsvocp1.service.inditex.grp/
  GOSSIP_SERVER_USER: oauthtoolgossippro
  GOSSIP_SERVER_PASS: cyberark.AX-N-PUBLIC-SRE-TOOLS.Application-ITX-APP-500-SC-Generic-OAUTH2ClientsPRO-oauthtoolgossippro

jobs:
  upload-config:
    name: Upload Gossip configuration
    timeout-minutes: 30
    runs-on: ${{ fromJSON(vars.RUNSON_GOSSIP_ONPREM || '["sreteam-icr__sre-ivm"]') }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Paas CLI
        id: setup-cli
        uses: inditex/gha-paasdeployment/setup-cli@v0

      - name: Launch Gossip Server request
        uses: inditex/gha-sreactions/gossip/gossip-upload@gossip-v13
        with:
          cyberark-oauth-clientid: ${{ secrets.APPLICATION_CLIENT_ID }}
          cyberark-oauth-secret: ${{ secrets.APPLICATION_CLIENT_SECRET }}
          gossip-server-url: ${{ env.GOSSIP_SERVER_URL }}
          gossip-server-username: ${{ env.GOSSIP_SERVER_USER }}
          gossip-server-password: "${{ env.GOSSIP_SERVER_PASS }}"
          paas-cli-path: ${{ steps.setup-cli.outputs.path }}
