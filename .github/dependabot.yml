---
version: 2

registries:
  inditex-npm:
    type: npm-registry
    url: https://inditex.jfrog.io/artifactory/api/npm/node-public
    username: ${{secrets.DIST_PLAT_READER_USERNAME}}
    password: ${{secrets.DIST_PLAT_READER_TOKEN}}
    replaces-base: true
  inditex-maven:
    type: maven-repository
    url: https://inditex.jfrog.io/artifactory/maven-public
    username: ${{secrets.DIST_PLAT_READER_USERNAME}}
    password: ${{secrets.DIST_PLAT_READER_TOKEN}}
  inditex-poetry:
    type: python-index
    url: https://inditex.jfrog.io/artifactory/api/pypi/python-public/simple/
    username: ${{secrets.DIST_PLAT_READER_USERNAME}}
    password: ${{secrets.DIST_PLAT_READER_TOKEN}}
updates:
  # AMIGA Fwk Node.js (WSC/WEB)
  # AMIGA Fwk Web (SPA)
  - package-ecosystem: npm
    rebase-strategy: "disabled"
    directory: /code
    schedule:
      interval: "weekly"
      day: "sunday"
      time: "02:00"
      timezone: UTC
    registries:
      - inditex-npm
    groups:
      amiga-web-dependencies:
        patterns:
          - "@amiga-fwk-web/*"
        update-types:
          - "minor"
          - "patch"
        exclude-patterns:
          - "@amiga-fwk-web/components-data-viz"
      amiga-node-dependencies:
        patterns:
          - "@amiga-fwk-nodejs/*"
        update-types:
          - "minor"
          - "patch"
    allow:
      # Eslint config for Inditex
      - dependency-name: "@inditex/eslint-config"
        dependency-type: direct
      # AMIGA Fwk Web Data Viz
      - dependency-name: "@amiga-fwk-web/components-data-viz"
        dependency-type: direct
      # AMIGA Fwk Node.js
      - dependency-name: "@amiga-fwk-nodejs/*"
        dependency-type: direct
      # AMIGA Fwk Web
      - dependency-name: "@amiga-fwk-web/*"
        dependency-type: direct
    ignore:
      # Ignore major version update
      - dependency-name: "@amiga-fwk-web/components-data-viz"
        update-types: ["version-update:semver-major"]
    labels:
      - kind/dependency
  # AMIGA Tech Docs (DOCS)
  - package-ecosystem: npm
    rebase-strategy: "disabled"
    directory: /docs
    schedule:
      interval: "weekly"
      day: "sunday"
      time: "02:00"
      timezone: UTC
    registries:
      - inditex-npm
    allow:
      # Eslint config for Inditex
      - dependency-name: "@inditex/eslint-config"
        dependency-type: direct
      # AMIGA Tech Docs
      - dependency-name: "@amigatechdocs/*"
        dependency-type: direct
    groups:
      amiga-tech-docs-dependencies:
        patterns:
          - "@amigatechdocs/*"
        update-types:
          - "minor"
          - "patch"
    labels:
      - kind/dependency
  # AMIGA Fwk Java (WSC/WEB)
  - package-ecosystem: "maven"
    rebase-strategy: "disabled"
    directory: "/code"
    schedule:
      interval: "weekly"
      day: "sunday"
      time: "02:00"
      timezone: UTC
    registries:
      - inditex-maven
    labels:
      - kind/dependency
    allow:
      - dependency-name: "com.inditex.aqsw.framework.amiga:amiga-framework"
        dependency-type: direct
      - dependency-name: "com.inditex.aqsw.amiga.javaformat:amiga-javaformat-maven-plugin"
        dependency-type: direct
      - dependency-name: "com.inditex.libamfmt:amiga-javaformat-maven-plugin"
        dependency-type: direct
      - dependency-name: "com.inditex.aqsw.pipeclie:pipe-starter-kafka"
        dependency-type: direct
      - dependency-name: "com.inditex.aqsw.amgjdbce*"
        dependency-type: direct
      - dependency-name: "com.inditex.aqsw.amiga.rules*"
        dependency-type: direct
      - dependency-name: "com.inditex.amigafwk:amiga-framework"
        dependency-type: direct
      # Darwin Telemetry and Maven Release strategies from @inditex/productivity-team
      - dependency-name: "com.inditex.darwin.telemetry:darwin-telemetry-maven-extension"
        dependency-type: direct
      - dependency-name: "com.inditex.maven.release:maven-release-strategies"
        dependency-type: direct
      - dependency-name: "org.apache.maven.extensions:maven-build-cache-extension"
        dependency-type: direct
      # API artifacts version update
      - dependency-name: "com.inditex.api*"
        dependency-type: direct
      # Core platform artifacts version update
      - dependency-name: "com.inditex.openplatform*"
        dependency-type: direct
    ignore:
      # Ignore major version update
      - dependency-name: "com.inditex.aqsw.framework.amiga*"
        update-types: ["version-update:semver-major"]
      - dependency-name: "com.inditex.darwin.telemetry:darwin-telemetry-maven-extension"
        update-types: ["version-update:semver-major"]
      - dependency-name: "com.inditex.maven.release:maven-release-strategies"
        update-types: ["version-update:semver-major"]
      - dependency-name: "org.apache.maven.extensions:maven-build-cache-extension"
        update-types: ["version-update:semver-major"]
      - dependency-name: "com.inditex.openplatform*"
        update-types: ["version-update:semver-major"]
  - package-ecosystem: "pip"
    rebase-strategy: "disabled"
    directory: "/code"
    insecure-external-code-execution: allow
    schedule:
      interval: "weekly"
      day: "sunday"
      time: "03:00"
      timezone: UTC
    registries:
      - inditex-poetry
    labels:
      - kind/dependency
    allow:
      # AMIGA Fwk Python
      - dependency-name: "fwk-amigapython"
        dependency-type: direct
      - dependency-name: "amigapythonci"
        dependency-type: direct
      - dependency-name: "amigamlserver"
        dependency-type: direct
      # Version Catalog
      - dependency-name: "version_catalog"
        dependency-type: direct
    ignore:
      # Ignore major version update
      - dependency-name: "fwk-amigapython"
        update-types: ["version-update:semver-major"]
      - dependency-name: "amigapythonci"
        update-types: ["version-update:semver-major"]
      - dependency-name: "amigamlserver"
        update-types: ["version-update:semver-major"]
